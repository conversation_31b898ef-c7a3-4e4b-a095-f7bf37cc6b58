-- Drop notification_preferences Table
-- This script safely removes the redundant notification_preferences table
-- 
-- IMPORTANT: This table is safe to remove because:
-- 1. It's only used in migration files (not active code)
-- 2. It's only used in unused PWA methods
-- 3. The main system uses user_notification_preferences table
-- 4. No active controllers or views reference it

-- Check if table exists first
SELECT 'Checking if notification_preferences table exists...' as status;

-- Show table info if it exists
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    CREATE_TIME,
    UPDATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notification_preferences';

-- Show any data in the table (if it exists)
SELECT 'Current data in notification_preferences table:' as info;
SELECT COUNT(*) as total_records FROM notification_preferences;

-- Drop the table
SELECT 'Dropping notification_preferences table...' as status;
DROP TABLE IF EXISTS notification_preferences;

-- Verify table is gone
SELECT 'Verifying table removal...' as status;
SELECT COUNT(*) as tables_found 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'notification_preferences';

-- Show remaining notification-related tables
SELECT 'Remaining notification tables:' as info;
SELECT TABLE_NAME, TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE '%notification%'
ORDER BY TABLE_NAME;

SELECT '✅ notification_preferences table cleanup completed!' as result;
SELECT 'Your system now uses only the unified notification system:' as info;
SELECT '• notification_settings (global settings)' as table1;
SELECT '• user_notification_preferences (user settings)' as table2;
SELECT '• messages (unified messaging)' as table3;
