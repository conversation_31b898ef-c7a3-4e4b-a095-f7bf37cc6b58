<?php
/**
 * Debug Email System - Comprehensive Test
 * 
 * This script tests the email system to see what's happening
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

echo "<h1>Email System Debug Test</h1>\n";

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/UnifiedMessageModel.php';

try {
    echo "<h2>1. Testing Database Connection</h2>\n";
    $db = new Database();
    echo "✅ Database connection successful<br>\n";
    
    echo "<h2>2. Checking Messages Table Structure</h2>\n";
    $db->query("DESCRIBE messages");
    $columns = $db->resultSet();
    
    $hasFolderId = false;
    echo "<table border='1'><tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
    foreach ($columns as $column) {
        echo "<tr><td>{$column->Field}</td><td>{$column->Type}</td><td>{$column->Null}</td><td>{$column->Key}</td><td>{$column->Default}</td></tr>\n";
        if ($column->Field === 'folder_id') {
            $hasFolderId = true;
        }
    }
    echo "</table>\n";
    
    if ($hasFolderId) {
        echo "✅ folder_id column exists<br>\n";
    } else {
        echo "❌ folder_id column MISSING - need to run email_folders.sql<br>\n";
    }
    
    echo "<h2>3. Checking Email Folders Table</h2>\n";
    try {
        $db->query("SELECT COUNT(*) as count FROM email_folders");
        $folderCount = $db->single();
        echo "Email folders count: {$folderCount->count}<br>\n";
        
        if ($folderCount->count > 0) {
            $db->query("SELECT * FROM email_folders ORDER BY id");
            $folders = $db->resultSet();
            echo "<table border='1'><tr><th>ID</th><th>Name</th><th>Color</th><th>Icon</th><th>System</th></tr>\n";
            foreach ($folders as $folder) {
                echo "<tr><td>{$folder->id}</td><td>{$folder->name}</td><td>{$folder->color}</td><td>{$folder->icon}</td><td>{$folder->is_system}</td></tr>\n";
            }
            echo "</table>\n";
        }
    } catch (Exception $e) {
        echo "❌ email_folders table doesn't exist - need to run email_folders.sql<br>\n";
    }
    
    echo "<h2>4. Checking Existing Email Messages</h2>\n";
    $db->query("SELECT COUNT(*) as count FROM messages WHERE message_type = 'email'");
    $emailCount = $db->single();
    echo "Email messages count: {$emailCount->count}<br>\n";
    
    if ($emailCount->count > 0) {
        $db->query("SELECT id, subject, message_type, folder_id, created_at FROM messages WHERE message_type = 'email' ORDER BY created_at DESC LIMIT 5");
        $emails = $db->resultSet();
        echo "<table border='1'><tr><th>ID</th><th>Subject</th><th>Type</th><th>Folder ID</th><th>Created</th></tr>\n";
        foreach ($emails as $email) {
            echo "<tr><td>{$email->id}</td><td>{$email->subject}</td><td>{$email->message_type}</td><td>{$email->folder_id}</td></tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h2>5. Testing UnifiedMessageModel</h2>\n";
    $messageModel = new UnifiedMessageModel();
    
    // Test creating an email message
    echo "Testing createEmailMessage...<br>\n";
    $testMessageId = $messageModel->createEmailMessage(
        1, // from admin
        1, // to admin  
        "Test Email Message " . date('Y-m-d H:i:s'),
        "This is a test email message to verify the system is working.",
        "<EMAIL>", // original sender
        null, // no show
        'normal'
    );
    
    if ($testMessageId) {
        echo "✅ Test email message created with ID: $testMessageId<br>\n";
        
        // Check if it was created properly
        $db->query("SELECT * FROM messages WHERE id = ?");
        $db->bind(1, $testMessageId);
        $testMessage = $db->single();
        
        if ($testMessage) {
            echo "✅ Message found in database<br>\n";
            echo "Subject: {$testMessage->subject}<br>\n";
            echo "Message Type: {$testMessage->message_type}<br>\n";
            echo "Folder ID: {$testMessage->folder_id}<br>\n";
            echo "Original Sender: {$testMessage->original_sender_email}<br>\n";
        } else {
            echo "❌ Message not found in database<br>\n";
        }
    } else {
        echo "❌ Failed to create test email message<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>\n";
    echo "Stack trace:<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>Test Complete</h2>\n";
echo "Run this script to diagnose email system issues.<br>\n";
?>
