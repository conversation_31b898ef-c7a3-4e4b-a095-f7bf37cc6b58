# Show Role Assignment System

## Overview

The Show Role Assignment System allows coordinators and administrators to assign temporary, show-specific roles to users while preserving their primary system roles. This system includes an approval workflow for coordinator assignments and automatic cleanup of expired assignments.

## Key Features

- **Per-Show Role Assignments**: Users can have different roles for different shows
- **Primary Role Preservation**: Users keep their main system role (admin, coordinator, user, etc.)
- **Approval Workflow**: Coordinator assignments require user approval; admin assignments are immediate
- **User ID Assignment**: Coordinators assign roles using user IDs for privacy
- **Admin User Search**: Administrators can search users by name, email, or ID
- **Automatic Cleanup**: Expired assignments are automatically removed 1 week after show end date
- **Notification System**: Users receive notifications for requests, approvals, and removals

## Installation

### 1. Database Setup

Choose one of the SQL scripts based on your database configuration:

**With Foreign Key Constraints:**
```bash
mysql -u username -p database_name < sql/create_show_role_system.sql
```

**Without Foreign Key Constraints:**
```bash
mysql -u username -p database_name < sql/create_show_role_system_no_fk.sql
```

### 2. Update Notification Categories

The system extends the existing notification system. Ensure your `notification_queue` table supports the new `role_assignment` category.

### 3. Set Up Cron Job

Add the following cron job to run daily cleanup:

```bash
# Run daily at 2 AM
0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_show_roles.php
```

## Database Schema

### Tables Created

#### `show_role_requests`
Stores role assignment requests awaiting user approval.

| Column | Type | Description |
|--------|------|-------------|
| id | int(11) | Primary key |
| show_id | int(11) | Show ID |
| user_id | int(11) | User being assigned |
| requested_role | enum | Role being requested (coordinator, judge, staff) |
| requested_by | int(11) | User making the request |
| status | enum | Request status (pending, approved, declined, expired) |
| request_message | text | Optional message from requester |
| response_message | text | Optional response from user |
| requested_at | datetime | When request was made |
| responded_at | datetime | When user responded |
| expires_at | datetime | When request expires |

#### `show_role_assignments`
Stores active, approved role assignments.

| Column | Type | Description |
|--------|------|-------------|
| id | int(11) | Primary key |
| show_id | int(11) | Show ID |
| user_id | int(11) | Assigned user |
| assigned_role | enum | Assigned role (coordinator, judge, staff) |
| assigned_by | int(11) | User who made the assignment |
| request_id | int(11) | Reference to original request (nullable) |
| is_active | tinyint(1) | Whether assignment is active |
| assigned_at | datetime | When assignment was created |
| expires_at | datetime | Show end date + 1 day |
| auto_cleanup_date | datetime | Show end date + 1 week |

### Views Created

#### `user_show_roles`
Easy querying of active user show roles with details.

#### `pending_role_requests`
View of pending role requests with user and show details.

## Usage

### For Administrators

1. **Navigate to Show Management**: Go to any show's edit page
2. **Access Role Management**: Click "Manage Roles" button
3. **Search and Assign**: Use the search box to find users by name, email, or ID
4. **Immediate Assignment**: Admin assignments are active immediately (no approval needed)
5. **Remove Assignments**: Click "Remove" next to any assignment to deactivate it

### For Coordinators

1. **Navigate to Show Management**: Go to your show's edit page
2. **Access Role Management**: Click "Manage Roles" button
3. **Request Assignment**: Enter the user's ID number (user must provide this)
4. **Wait for Approval**: User receives notification and must approve the assignment
5. **Monitor Requests**: View pending requests in the management interface

### For Users

1. **View Requests**: Navigate to "My Show Role Assignments" from your dashboard
2. **Respond to Requests**: Accept or decline role assignment requests
3. **View Current Assignments**: See all your active show role assignments
4. **Access Show Features**: Use assigned roles to access show management features

## Role Hierarchy and Permissions

### Role Types
- **Coordinator**: Full access to show management features
- **Judge**: Access to judging and scoring features
- **Staff**: Access to registration and check-in features

### Permission Checking

The system extends existing authentication methods:

```php
// Check if user has role for specific show
$auth->hasShowRole($showId, 'judge');

// Get all user's roles for a show
$roles = $auth->getUserShowRoles($showId);

// Check if user is assigned to show in any capacity
$auth->isAssignedToShow($showId);
```

### Backward Compatibility

The system maintains full backward compatibility with existing code:
- All existing role checking continues to work unchanged
- Old staff assignments are still recognized
- Judge assignments work with both old and new systems

## Workflow Examples

### Coordinator Assignment Workflow

1. Coordinator enters user ID and selects role
2. System creates pending request with 7-day expiration
3. User receives email notification
4. User logs in and accepts/declines request
5. If accepted, active assignment is created
6. Assignment expires 1 day after show end date
7. Assignment is automatically cleaned up 1 week after show end date

### Admin Assignment Workflow

1. Admin searches for user by name, email, or ID
2. Admin selects role and submits assignment
3. Active assignment is created immediately
4. User receives notification about direct assignment
5. Assignment expires and is cleaned up automatically

## Automatic Cleanup

The cron job `cron/cleanup_show_roles.php` runs daily and:

- Deactivates assignments past their cleanup date
- Marks expired requests as "expired"
- Sends email notifications to administrators
- Maintains cleanup logs
- Cleans up old log entries (keeps 30 days)

## Notifications

The system sends notifications for:

- **Role Requests**: When coordinator requests role assignment
- **Direct Assignments**: When admin assigns role directly
- **Request Responses**: When user accepts/declines request
- **Assignment Removals**: When assignment is manually removed
- **Cleanup Reports**: Daily cleanup summary to administrators

## Security Features

- **User ID Privacy**: Coordinators can't browse user database
- **Permission Validation**: All actions require proper authorization
- **Request Expiration**: Requests expire after 7 days
- **Automatic Cleanup**: No manual intervention needed for expired assignments
- **Audit Trail**: All assignments tracked with who assigned and when

## Troubleshooting

### Common Issues

1. **Notifications not sending**: Check notification queue table and email settings
2. **Cleanup not running**: Verify cron job is configured and running
3. **Permission denied**: Ensure user has proper role for the show
4. **Expired requests**: Requests expire after 7 days and must be recreated

### Log Files

- **Cleanup logs**: `logs/show_role_cleanup.log`
- **Error logs**: `logs/cron_errors.log`
- **Application logs**: Check your application's error log

### Database Queries for Debugging

```sql
-- Check active assignments for a show
SELECT * FROM user_show_roles WHERE show_id = 123;

-- Check pending requests
SELECT * FROM pending_role_requests;

-- Check assignments needing cleanup
SELECT * FROM show_role_assignments 
WHERE auto_cleanup_date <= NOW() AND is_active = 1;
```

## Migration from Old System

The new system is fully compatible with existing staff assignments. To migrate:

1. Install the new system
2. Existing staff assignments continue to work
3. New assignments use the new system
4. Gradually transition to new system as needed
5. Old `staff_assignments` table can be kept for historical data

## API Integration

The system provides AJAX endpoints:

- `POST /show_roles/assign` - Create role assignment
- `POST /show_roles/remove` - Remove role assignment
- `POST /show_roles/respond` - Respond to role request
- `GET /show_roles/searchUsers` - Search users (admin only)

## Future Enhancements

Potential future improvements:
- Bulk role assignments
- Role templates for common assignments
- Integration with calendar system
- Mobile app notifications
- Role assignment analytics
