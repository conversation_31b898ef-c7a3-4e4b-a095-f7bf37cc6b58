<?php
/**
 * Clean up old FCM tokens and test with fresh subscriptions
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Prevent direct access without security key
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'cleanup_fcm_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/helpers/fcm_v1_helper.php';

echo "=== FCM TOKEN CLEANUP & TEST ===\n\n";

try {
    $userId = 3;
    $db = new Database();
    
    echo "1. CURRENT SUBSCRIPTION STATUS\n";
    
    // Get all subscriptions
    $db->query("SELECT COUNT(*) as total FROM push_subscriptions WHERE user_id = ?");
    $db->bind(1, $userId);
    $db->execute();
    $total = $db->single()->total;
    
    $db->query("SELECT COUNT(*) as active FROM push_subscriptions WHERE user_id = ? AND active = 1");
    $db->bind(1, $userId);
    $db->execute();
    $active = $db->single()->active;
    
    echo "   Total subscriptions: $total\n";
    echo "   Active subscriptions: $active\n";
    
    if (isset($_GET['cleanup']) && $_GET['cleanup'] === '1') {
        echo "\n2. CLEANING UP OLD SUBSCRIPTIONS\n";
        
        // Mark old subscriptions as inactive
        $db->query("UPDATE push_subscriptions SET active = 0 WHERE user_id = ? AND created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $db->bind(1, $userId);
        $db->execute();
        $cleaned = $db->rowCount();
        
        echo "   Marked $cleaned old subscriptions as inactive\n";
        
        // Get remaining active subscriptions
        $db->query("SELECT COUNT(*) as remaining FROM push_subscriptions WHERE user_id = ? AND active = 1");
        $db->bind(1, $userId);
        $db->execute();
        $remaining = $db->single()->remaining;
        
        echo "   Remaining active subscriptions: $remaining\n";
    }
    
    echo "\n3. GETTING RECENT SUBSCRIPTIONS\n";
    
    // Get most recent subscriptions (likely to be valid)
    $db->query("SELECT * FROM push_subscriptions WHERE user_id = ? AND active = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE) ORDER BY created_at DESC LIMIT 2");
    $db->bind(1, $userId);
    $db->execute();
    $recentSubs = $db->resultSet();
    
    echo "   Recent subscriptions (last 30 min): " . count($recentSubs) . "\n";
    
    if (empty($recentSubs)) {
        echo "\n   ⚠️ NO RECENT SUBSCRIPTIONS FOUND\n";
        echo "   You need to create a fresh subscription:\n";
        echo "   1. Clear browser data completely\n";
        echo "   2. Visit your site fresh\n";
        echo "   3. Allow notifications when prompted\n";
        echo "   4. Run this test again\n\n";
        
        echo "   BROWSER REFRESH SCRIPT:\n";
        echo "   Run this in your browser console:\n";
        echo "   \n";
        echo "   // Remove all old subscriptions\n";
        echo "   navigator.serviceWorker.getRegistration().then(reg => {\n";
        echo "       if (reg) {\n";
        echo "           reg.pushManager.getSubscription().then(sub => {\n";
        echo "               if (sub) {\n";
        echo "                   sub.unsubscribe().then(() => {\n";
        echo "                       console.log('Old subscription removed');\n";
        echo "                       location.reload();\n";
        echo "                   });\n";
        echo "               }\n";
        echo "           });\n";
        echo "       }\n";
        echo "   });\n\n";
        
        exit;
    }
    
    echo "\n4. TESTING WITH RECENT TOKENS\n";
    
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    $fcm = new FCMv1Helper($serviceAccountPath);
    
    $fcmTokens = [];
    foreach ($recentSubs as $sub) {
        $token = FCMv1Helper::extractFCMToken($sub->endpoint);
        if ($token) {
            $fcmTokens[] = $token;
            echo "   Recent token: " . substr($token, 0, 20) . "... (created: {$sub->created_at})\n";
        }
    }
    
    if (empty($fcmTokens)) {
        echo "   No FCM tokens in recent subscriptions\n";
        exit;
    }
    
    echo "\n5. SENDING TO RECENT TOKENS\n";
    
    $title = "✅ Fresh FCM Token Test";
    $body = "This notification uses a fresh FCM token from " . date('H:i:s');
    $data = [
        'url' => '/',
        'event_id' => '0',
        'event_type' => 'fresh_token_test'
    ];
    
    $results = $fcm->sendToMultipleTokens($fcmTokens, $title, $body, $data);
    
    echo "\n6. RESULTS\n";
    
    $successCount = 0;
    $failCount = 0;
    
    foreach ($results as $i => $result) {
        echo "   Token " . ($i + 1) . ": ";
        
        if ($result['success']) {
            echo "✅ SUCCESS!\n";
            $successCount++;
        } else {
            echo "❌ FAILED - " . $result['error'] . "\n";
            $failCount++;
        }
    }
    
    echo "\n7. SUMMARY\n";
    echo "   Successful: $successCount\n";
    echo "   Failed: $failCount\n";
    
    if ($successCount > 0) {
        echo "\n🎉 FCM v1 API WORKING WITH FRESH TOKENS!\n";
        echo "   Check your browser for notifications\n";
        echo "   The solution works - just need fresh subscriptions\n";
        echo "   Ready to integrate into NotificationService\n";
    } else {
        echo "\n❌ Still failing - tokens may be too old\n";
        echo "   Need completely fresh browser subscription\n";
    }
    
    echo "\n8. NEXT STEPS\n";
    
    if ($successCount > 0) {
        echo "   ✅ FCM v1 API is working!\n";
        echo "   1. Integrate into NotificationService.php\n";
        echo "   2. Clean up old subscriptions regularly\n";
        echo "   3. Use only recent subscriptions for notifications\n";
    } else {
        echo "   🔄 Need fresh subscription:\n";
        echo "   1. Clear browser data completely\n";
        echo "   2. Visit site and allow notifications\n";
        echo "   3. Test immediately with fresh token\n";
    }
    
    echo "\n9. CLEANUP OPTION\n";
    if (!isset($_GET['cleanup'])) {
        echo "   To clean up old subscriptions, visit:\n";
        echo "   https://events.rowaneliterides.com/cleanup_and_test_fcm.php?test_key=cleanup_fcm_2025&cleanup=1\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== END TEST ===\n";
?>
