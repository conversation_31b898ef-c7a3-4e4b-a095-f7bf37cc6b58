/**
 * Calendar Advanced Filtering System
 * 
 * This script provides advanced filtering functionality for the calendar views
 * (month, week, day, list, and map).
 */

console.log('=== CALENDAR-FILTERS.JS LOADED - VERSION 3.47.0 - TIMEZONE IMPLEMENTATION ===');

document.addEventListener('DOMContentLoaded', function() {
    // Debug mode detection
    const DEBUG_MODE = typeof window.DEBUG_MODE !== 'undefined' ? window.DEBUG_MODE : false;
    
    if (DEBUG_MODE) {
        console.log('Calendar filters system initializing...');
    }
    
    // Initialize filter state
    const activeFilters = {
        startDate: null,
        endDate: null,
        calendars: [],
        state: '',
        city: '',
        venue: '',
        clubs: [],
        radius: document.getElementById('radius-filter') ? parseInt(document.getElementById('radius-filter').value) : 50,
        lat: null,
        lng: null,
        keyword: '',
        categories: [],
        tags: [],
        priceMin: 0,
        priceMax: 500,
        currentView: 'calendar' // 'calendar' or 'map'
    };

    // Set default start date for map view to exclude past events
    const isMapPage = window.location.pathname.includes('/calendar/map');
    if (isMapPage) {
        const today = new Date();
        const todayString = today.getFullYear() + '-' +
                           String(today.getMonth() + 1).padStart(2, '0') + '-' +
                           String(today.getDate()).padStart(2, '0');
        activeFilters.startDate = todayString;

        if (DEBUG_MODE) {
            console.log('Map view detected: Set default start date to', todayString);
        }
    }

    // Initialize filter elements
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const stateFilter = document.getElementById('state-filter');
    const cityFilter = document.getElementById('city-filter');
    const venueFilter = document.getElementById('venue-filter');
    const clubFilter = document.getElementById('club-filter');
    const radiusFilter = document.getElementById('radius-filter');
    const radiusValue = document.getElementById('radius-value');
    const locationSearch = document.getElementById('location-search');
    // Search location button removed - location search now integrated with Apply Filters button
    const keywordFilter = document.getElementById('keyword-filter');
    const categoryFilter = document.getElementById('category-filter');
    const tagFilter = document.getElementById('tag-filter');
    const priceRangeValue = document.getElementById('price-range-value');
    const priceMinInput = document.getElementById('price-min');
    const priceMaxInput = document.getElementById('price-max');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');

    // Initialize event listeners
    initEventListeners();

    // Initialize calendar filters
    updateCalendarFilters();

    // Load filter data
    loadFilterData();

    /**
     * Initialize event listeners for filter controls
     */
    function initEventListeners() {
        // Calendar checkboxes
        document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateCalendarFilters);
        });

        // State filter
        if (stateFilter) {
            stateFilter.addEventListener('change', function() {
                if (DEBUG_MODE) {
                    console.log('State filter changed to:', this.value);
                    console.log('State filter value is empty:', this.value === '');
                }
                activeFilters.state = this.value;
                
                // When state changes, reset city filter to prevent stale city values
                if (DEBUG_MODE) {
                    console.log('Resetting city filter due to state change');
                    console.log('Previous city filter value:', activeFilters.city);
                }
                activeFilters.city = '';
                if (cityFilter) {
                    cityFilter.selectedIndex = 0; // Reset to "All" option
                }
                
                loadCities(this.value);
                loadVenues(this.value, null);
                
                if (DEBUG_MODE) {
                    console.log('City filter reset to:', activeFilters.city);
                }
            });
        }

        // City filter
        if (cityFilter) {
            cityFilter.addEventListener('change', function() {
                if (DEBUG_MODE) {
                    console.log('City filter changed to:', this.value);
                    console.log('City filter value is empty:', this.value === '');
                }
                activeFilters.city = this.value;
                loadVenues(activeFilters.state, this.value);
            });
        }

        // Venue filter
        if (venueFilter) {
            venueFilter.addEventListener('change', function() {
                activeFilters.venue = this.value;
            });
        }

        // Club filter
        if (clubFilter) {
            clubFilter.addEventListener('change', function() {
                activeFilters.clubs = Array.from(this.selectedOptions).map(option => option.value).filter(value => value !== '');
            });
        }

        // Radius filter
        if (radiusFilter && radiusValue) {
            radiusFilter.addEventListener('input', function() {
                radiusValue.textContent = this.value;
                activeFilters.radius = parseInt(this.value);
            });
        }

        // Location search - no search button, will be processed when Apply Filters is clicked
        if (locationSearch) {
            locationSearch.addEventListener('input', function() {
                // Clear previous location coordinates when user types new location
                activeFilters.lat = null;
                activeFilters.lng = null;
                if (DEBUG_MODE) {
                    console.log('Location search input changed, cleared coordinates');
                }
            });
        }

        // Keyword filter
        if (keywordFilter) {
            keywordFilter.addEventListener('input', function() {
                activeFilters.keyword = this.value;
            });
        }

        // Apply filters button
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', applyFilters);
        }

        // Reset filters button
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', resetFilters);
        }

        // Date inputs
        if (startDateInput) {
            startDateInput.addEventListener('change', function() {
                activeFilters.startDate = this.value;
            });

            // Set default start date for map view if not already set
            if (activeFilters.startDate && !startDateInput.value) {
                startDateInput.value = activeFilters.startDate;
            }
        }

        if (endDateInput) {
            endDateInput.addEventListener('change', function() {
                activeFilters.endDate = this.value;
            });
        }
        
        // Category filter
        if (categoryFilter) {
            categoryFilter.addEventListener('change', function() {
                activeFilters.categories = Array.from(this.selectedOptions).map(option => option.value).filter(value => value !== '');
            });
            
            // Load categories
            loadCategories();
        }
        
        // Tag filter
        if (tagFilter) {
            tagFilter.addEventListener('change', function() {
                activeFilters.tags = Array.from(this.selectedOptions).map(option => option.value).filter(value => value !== '');
            });
            
            // Load tags
            loadTags();
        }
        
        // Initialize price range slider if jQuery UI is available
        if (typeof $ !== 'undefined' && typeof $.fn.slider !== 'undefined' && document.getElementById('price-range-slider')) {
            $('#price-range-slider').slider({
                range: true,
                min: 0,
                max: 500,
                values: [0, 500],
                slide: function(event, ui) {
                    activeFilters.priceMin = ui.values[0];
                    activeFilters.priceMax = ui.values[1];
                    
                    if (priceMinInput) priceMinInput.value = ui.values[0];
                    if (priceMaxInput) priceMaxInput.value = ui.values[1];
                    
                    if (priceRangeValue) {
                        priceRangeValue.textContent = `$${ui.values[0]} - $${ui.values[1]}`;
                    }
                }
            });
        }
    }

    /**
     * Load filter data (states, clubs)
     */
    function loadFilterData() {
        // Load states
        loadStates();
        
        // Load clubs
        loadClubs();
    }

    /**
     * Update calendar filters based on checkboxes
     */
    function updateCalendarFilters() {
        const selectedCalendars = [];
        
        document.querySelectorAll('.calendar-checkbox:checked').forEach(checkbox => {
            selectedCalendars.push(checkbox.value);
        });
        
        activeFilters.calendars = selectedCalendars;
        
        // Log the selected calendars for debugging
        console.log('Selected calendars (advanced filter):', selectedCalendars);
        

    }

    /**
     * Load states for the filter
     */
    function loadStates() {
        if (!stateFilter) return;
        
        fetch(URLROOT + '/calendar/getStates?_cb=' + Date.now())
            .then(response => response.json())
            .then(data => {
                // Clear existing options except the first one
                while (stateFilter.options.length > 1) {
                    stateFilter.remove(1);
                }
                
                // Add states to the dropdown
                data.forEach(state => {
                    const option = document.createElement('option');
                    option.value = state.state;
                    option.textContent = `${state.state} (${state.event_count})`;
                    stateFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading states:', error);
            });
    }

    /**
     * Load cities for the filter based on selected state
     * 
     * @param {string} state Selected state
     */
    function loadCities(state) {
        if (!cityFilter) return;
        
        // Clear existing options except the first one
        while (cityFilter.options.length > 1) {
            cityFilter.remove(1);
        }
        
        if (!state) {
            return;
        }
        
        fetch(URLROOT + '/calendar/getCities?state=' + encodeURIComponent(state) + '&_cb=' + Date.now())
            .then(response => response.json())
            .then(data => {
                // Add cities to the dropdown
                data.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.city;
                    option.textContent = `${city.city} (${city.event_count})`;
                    cityFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading cities:', error);
            });
    }

    /**
     * Load venues for the filter based on selected state and city
     * 
     * @param {string} state Selected state
     * @param {string} city Selected city
     */
    function loadVenues(state, city) {
        if (!venueFilter) return;
        
        // Clear existing options except the first one
        while (venueFilter.options.length > 1) {
            venueFilter.remove(1);
        }
        
        let url = URLROOT + '/calendar/getVenues';
        const params = [];
        
        if (state) {
            params.push('state=' + encodeURIComponent(state));
        }
        
        if (city) {
            params.push('city=' + encodeURIComponent(city));
        }
        
        // Add cache busting parameter
        params.push('_cb=' + Date.now());
        
        if (params.length > 0) {
            url += '?' + params.join('&');
        }
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                // Add venues to the dropdown
                data.forEach(venue => {
                    const option = document.createElement('option');
                    option.value = venue.id;
                    option.textContent = `${venue.name} (${venue.event_count})`;
                    venueFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading venues:', error);
            });
    }

    /**
     * Load clubs for the filter
     */
    function loadClubs() {
        if (!clubFilter) return;
        
        fetch(URLROOT + '/calendar/getClubs?_cb=' + Date.now())
            .then(response => response.json())
            .then(data => {
                // Clear existing options except the first one
                while (clubFilter.options.length > 1) {
                    clubFilter.remove(1);
                }
                
                // Add clubs to the dropdown
                data.forEach(club => {
                    const option = document.createElement('option');
                    option.value = club.id;
                    option.textContent = `${club.name} (${club.event_count})`;
                    clubFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading clubs:', error);
            });
    }
    
    /**
     * Load categories for the filter
     */
    function loadCategories() {
        if (!categoryFilter) return;
        
        // Disable the category filter while loading
        categoryFilter.disabled = true;
        
        fetch(URLROOT + '/calendar/getCategories?_cb=' + Date.now())
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (categoryFilter.options.length > 1) {
                    categoryFilter.remove(1);
                }
                
                // Add categories to the dropdown
                if (Array.isArray(data) && data.length > 0) {
                    data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = `${category.name} (${category.event_count})`;
                        categoryFilter.appendChild(option);
                    });
                    // Enable the filter since we have categories
                    categoryFilter.disabled = false;
                } else {
                    // If no categories, add a placeholder option and keep disabled
                    const option = document.createElement('option');
                    option.disabled = true;
                    option.textContent = 'No categories available';
                    categoryFilter.appendChild(option);
                    
                    // Keep the filter disabled
                    categoryFilter.disabled = true;
                    
                    // Remove category from active filters
                    activeFilters.categories = [];
                    
                    if (DEBUG_MODE) {
                        console.log('No categories available, disabling category filter');
                    }
                }
            })
            .catch(error => {
                console.error('Error loading categories:', error);
                
                // Add a placeholder option on error
                const option = document.createElement('option');
                option.disabled = true;
                option.textContent = 'Failed to load categories';
                
                // Clear existing options except the first one
                while (categoryFilter.options.length > 1) {
                    categoryFilter.remove(1);
                }
                
                categoryFilter.appendChild(option);
                
                // Keep the filter disabled
                categoryFilter.disabled = true;
                
                // Remove category from active filters
                activeFilters.categories = [];
            });
    }
    
    /**
     * Load tags for the filter
     */
    function loadTags() {
        if (!tagFilter) return;
        
        // Disable the tag filter while loading
        tagFilter.disabled = true;
        
        fetch(URLROOT + '/calendar/getTags')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (tagFilter.options.length > 1) {
                    tagFilter.remove(1);
                }
                
                // Add tags to the dropdown
                if (Array.isArray(data) && data.length > 0) {
                    data.forEach(tag => {
                        const option = document.createElement('option');
                        option.value = tag.id;
                        option.textContent = `${tag.name} (${tag.event_count})`;
                        tagFilter.appendChild(option);
                    });
                    // Enable the filter since we have tags
                    tagFilter.disabled = false;
                } else {
                    // If no tags, add a placeholder option and keep disabled
                    const option = document.createElement('option');
                    option.disabled = true;
                    option.textContent = 'No tags available';
                    tagFilter.appendChild(option);
                    
                    // Keep the filter disabled
                    tagFilter.disabled = true;
                    
                    // Remove tags from active filters
                    activeFilters.tags = [];
                    
                    if (DEBUG_MODE) {
                        console.log('No tags available, disabling tag filter');
                    }
                }
            })
            .catch(error => {
                console.error('Error loading tags:', error);
                
                // Add a placeholder option on error
                const option = document.createElement('option');
                option.disabled = true;
                option.textContent = 'Failed to load tags';
                
                // Clear existing options except the first one
                while (tagFilter.options.length > 1) {
                    tagFilter.remove(1);
                }
                
                tagFilter.appendChild(option);
                
                // Keep the filter disabled
                tagFilter.disabled = true;
                
                // Remove tags from active filters
                activeFilters.tags = [];
            });
    }

    /**
     * Geocode a location and then apply filters
     */
    function geocodeLocationAndApplyFilters(locationInput) {
        if (DEBUG_MODE) {
            console.log('=== GEOCODING LOCATION ===');
            console.log('Location input:', locationInput);
        }
        
        // Use the appropriate geocoding service based on the map provider
        if (typeof geocoder !== 'undefined') {
            geocoder.geocode({ address: locationInput }, function(results, status) {
                const okStatus = typeof google !== 'undefined' ? google.maps.GeocoderStatus.OK : 'OK';
                
                if (status === okStatus && results[0]) {
                    const location = results[0].geometry.location;
                    const lat = typeof location.lat === 'function' ? location.lat() : location.lat;
                    const lng = typeof location.lng === 'function' ? location.lng() : location.lng;
                    
                    if (DEBUG_MODE) {
                        console.log('Geocoding successful:', { lat, lng });
                    }
                    
                    // Update filters with location
                    activeFilters.lat = lat;
                    activeFilters.lng = lng;
                    
                    // Now apply the filters with the geocoded location
                    applyFiltersWithLocation();
                } else {
                    console.error('Geocoding failed:', status);
                    alert('Location not found. Please try a different search term or leave the location field empty.');
                    
                    // Apply filters without location
                    applyFiltersWithLocation();
                }
            });
        } else if (typeof fetch !== 'undefined') {
            // Fallback to a simple geocoding service if Google Maps is not available
            const geocodeUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(locationInput)}&countrycodes=us&limit=5`;
            
            fetch(geocodeUrl)
                .then(response => response.json())
                .then(data => {
                    if (data && data.length > 0) {
                        if (DEBUG_MODE) {
                            console.log('Nominatim geocoding results:', data);
                        }
                        
                        // If multiple results, try to find the best match
                        let selectedResult = data[0]; // Default to first result
                        
                        // If we have multiple results, apply smart prioritization
                        if (data.length > 1) {
                            // Check if user specified a state in their search
                            const searchLower = locationInput.toLowerCase();
                            const hasStateInSearch = /\b(al|alabama|ak|alaska|az|arizona|ar|arkansas|ca|california|co|colorado|ct|connecticut|de|delaware|fl|florida|ga|georgia|hi|hawaii|id|idaho|il|illinois|in|indiana|ia|iowa|ks|kansas|ky|kentucky|la|louisiana|me|maine|md|maryland|ma|massachusetts|mi|michigan|mn|minnesota|ms|mississippi|mo|missouri|mt|montana|ne|nebraska|nv|nevada|nh|new hampshire|nj|new jersey|nm|new mexico|ny|new york|nc|north carolina|nd|north dakota|oh|ohio|ok|oklahoma|or|oregon|pa|pennsylvania|ri|rhode island|sc|south carolina|sd|south dakota|tn|tennessee|tx|texas|ut|utah|vt|vermont|va|virginia|wa|washington|wv|west virginia|wi|wisconsin|wy|wyoming)\b/.test(searchLower);
                            
                            if (hasStateInSearch) {
                                // User specified a state, so respect their choice - use first result
                                selectedResult = data[0];
                                if (DEBUG_MODE) {
                                    console.log('User specified state in search, using first result');
                                }
                            } else {
                                // Ambiguous search (no state specified), apply NC area priority
                                const statePreferences = [
                                    'North Carolina', 'NC',  // Home base - highest priority
                                    'South Carolina', 'SC',  // Neighboring states
                                    'Virginia', 'VA',
                                    'Tennessee', 'TN',
                                    'Georgia', 'GA'
                                ];
                                
                                for (const preference of statePreferences) {
                                    const match = data.find(result => 
                                        result.display_name && 
                                        result.display_name.includes(preference)
                                    );
                                    if (match) {
                                        selectedResult = match;
                                        if (DEBUG_MODE) {
                                            console.log(`Applied NC area priority, selected result from ${preference}`);
                                        }
                                        break;
                                    }
                                }
                            }
                            
                            if (DEBUG_MODE) {
                                console.log('Selected geocoding result:', selectedResult);
                            }
                        }
                        
                        const lat = parseFloat(selectedResult.lat);
                        const lng = parseFloat(selectedResult.lon);
                        
                        if (DEBUG_MODE) {
                            console.log('Nominatim geocoding successful:', { lat, lng, display_name: selectedResult.display_name });
                        }
                        
                        // Update filters with location
                        activeFilters.lat = lat;
                        activeFilters.lng = lng;
                        
                        // Now apply the filters with the geocoded location
                        applyFiltersWithLocation();
                    } else {
                        console.error('Nominatim geocoding failed: no results');
                        alert('Location not found. Please try a different search term or leave the location field empty.');
                        
                        // Apply filters without location
                        applyFiltersWithLocation();
                    }
                })
                .catch(error => {
                    console.error('Geocoding error:', error);
                    alert('Error searching for location. Please try again or leave the location field empty.');
                    
                    // Apply filters without location
                    applyFiltersWithLocation();
                });
        } else {
            // No geocoding service available
            console.warn('No geocoding service available');
            alert('Location search is not available. Please use state/city filters instead.');
            
            // Apply filters without location
            applyFiltersWithLocation();
        }
    }

    // Flag to prevent infinite loops
    let isApplyingFilters = false;
    
    /**
     * Apply filters to the current view
     */
    function applyFilters() {
        console.log('=== APPLY FILTERS FUNCTION CALLED ===');
        
        // Prevent infinite loops
        if (isApplyingFilters) {
            console.warn('=== PREVENTING INFINITE LOOP - FILTERS ALREADY BEING APPLIED ===');
            return;
        }
        
        isApplyingFilters = true;
        
        // Safety timeout to reset flag in case something goes wrong
        setTimeout(() => {
            if (isApplyingFilters) {
                console.warn('=== SAFETY TIMEOUT - RESETTING FILTER FLAG ===');
                isApplyingFilters = false;
            }
        }, 10000); // 10 second timeout
        
        // Check if we need to geocode a location first
        const locationInput = locationSearch ? locationSearch.value.trim() : '';
        const needsGeocoding = locationInput && (!activeFilters.lat || !activeFilters.lng);
        
        if (needsGeocoding) {
            console.log('=== GEOCODING LOCATION BEFORE APPLYING FILTERS ===');
            console.log('Location to geocode:', locationInput);
            
            // Geocode the location first, then apply filters
            geocodeLocationAndApplyFilters(locationInput);
            isApplyingFilters = false; // Reset flag since we're delegating to geocoding
            return;
        }
        
        // If no location input, clear location coordinates
        if (!locationInput) {
            activeFilters.lat = null;
            activeFilters.lng = null;
            if (DEBUG_MODE) {
                console.log('No location input, cleared coordinates');
            }
        }
        
        // Continue with normal filter application
        applyFiltersWithLocation();
    }
    
    /**
     * Apply filters with location already resolved
     */
    function applyFiltersWithLocation() {
        console.log('=== APPLY FILTERS WITH LOCATION FUNCTION CALLED ===');
        
        // Track filter reset scenario for debugging
        const isFilterReset = (!activeFilters.state || activeFilters.state === '') && 
                             (!activeFilters.city || activeFilters.city === '');
        console.log('=== FILTER RESET DETECTION ===');
        console.log('Is filter reset (no state/city):', isFilterReset);
        console.log('State filter:', activeFilters.state || 'EMPTY');
        console.log('City filter:', activeFilters.city || 'EMPTY');
        console.log('Location coordinates:', { lat: activeFilters.lat, lng: activeFilters.lng });
        console.log('=== END FILTER RESET DETECTION ===');
        
        if (DEBUG_MODE) {
            console.log('=== APPLY FILTERS STARTED ===');
            console.log('Current event sources count:', window.calendar ? window.calendar.options.eventSources.length : 'Calendar not available');
        }
        
        // Initialize calendar filters first
        updateCalendarFilters();
        
        if (DEBUG_MODE) {
            console.log('Applying filters with calendars:', activeFilters.calendars);
            console.log('All active filters:', activeFilters);
        }
        
        // Check if any calendars are selected
        if (activeFilters.calendars.length === 0) {
            if (DEBUG_MODE) {
                console.log('No calendars selected, clearing calendar and upcoming events');
            }
            
            // Clear the calendar
            const calendarInstance = typeof calendar !== 'undefined' ? calendar : window.calendar;
            if (calendarInstance) {
                if (typeof calendarInstance.clearEvents === 'function') {
                    // Use the new clearEvents method for proper cleanup
                    calendarInstance.clearEvents();
                    calendarInstance.renderView();
                } else if (typeof calendarInstance.getEvents === 'function') {
                    // FullCalendar implementation - clear events
                    const events = calendarInstance.getEvents();
                    events.forEach(event => event.remove());
                } else if (typeof calendarInstance.events !== 'undefined') {
                    // CustomCalendar implementation - clear events array
                    calendarInstance.events = [];
                    calendarInstance.renderView();
                }
            }
            
            // Update upcoming events section
            const upcomingEventsContainer = document.getElementById('upcoming-events');
            if (upcomingEventsContainer) {
                upcomingEventsContainer.innerHTML = '<div class="p-3">No events to display. Please select at least one calendar.</div>';
            }
            
            // Remove any loading indicators
            const calendarEl = document.getElementById('calendar');
            if (calendarEl) {
                calendarEl.classList.remove('loading');
                const loadingIndicator = calendarEl.querySelector('.calendar-loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }
            }
            
            if (DEBUG_MODE) {
                console.log('Calendar and upcoming events cleared successfully');
            }
            
            return;
        }
        
        // Build the filter URL for debugging
        if (DEBUG_MODE) {
            const params = getFilterParams();
            console.log('=== FILTER DEBUG INFO ===');
            console.log('Active filters object:', activeFilters);
            console.log('Generated filter params:', params);
            console.log('Filter URL would be: ' + URLROOT + '/calendar/getEvents?' + params);
            console.log('State filter value:', activeFilters.state, 'empty:', activeFilters.state === '');
            console.log('City filter value:', activeFilters.city, 'empty:', activeFilters.city === '');
            console.log('========================');
        }
        
        // Determine which view we're in and call the appropriate function
        if (activeFilters.currentView === 'map') {
            if (typeof loadEvents === 'function') {
                loadEvents();
            }
        } else {
            // For calendar views - handle different calendar implementations
            if (DEBUG_MODE) {
                console.log('Calendar object available:', typeof calendar !== 'undefined');
                if (typeof calendar !== 'undefined') {
                    console.log('Calendar methods available:', {
                        loadEvents: typeof calendar.loadEvents,
                        refetchEvents: typeof calendar.refetchEvents,
                        events: typeof calendar.events
                    });
                }
            }
            
            const calendarInstance = typeof calendar !== 'undefined' ? calendar : window.calendar;
            if (DEBUG_MODE) {
                console.log('=== CALENDAR INSTANCE DEBUG ===');
                console.log('Calendar instance found:', !!calendarInstance);
                if (calendarInstance) {
                    console.log('Calendar instance type:', calendarInstance.constructor.name);
                    console.log('Available methods:', {
                        loadEventsDirectly: typeof calendarInstance.loadEventsDirectly,
                        refetchEvents: typeof calendarInstance.refetchEvents,
                        loadEvents: typeof calendarInstance.loadEvents,
                        clearEvents: typeof calendarInstance.clearEvents
                    });
                }
                console.log('=== END CALENDAR INSTANCE DEBUG ===');
            }
            
            if (calendarInstance) {
                // Check which refresh method is available
                if (typeof calendarInstance.refetchEvents === 'function') {
                    // FullCalendar implementation
                    calendarInstance.refetchEvents();
                } else if (typeof calendarInstance.loadEvents === 'function') {
                    // CustomCalendar implementation
                    if (DEBUG_MODE) {
                        console.log('Applying filters to CustomCalendar implementation');
                    }
                    
                    // Show loading indicator
                    const calendarEl = document.getElementById('calendar');
                    if (calendarEl) {
                        calendarEl.classList.add('loading');
                        // Remove any existing loading indicator first
                        const existingIndicator = calendarEl.querySelector('.calendar-loading-indicator');
                        if (existingIndicator) {
                            existingIndicator.remove();
                        }
                        const loadingIndicator = document.createElement('div');
                        loadingIndicator.className = 'calendar-loading-indicator';
                        loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
                        calendarEl.appendChild(loadingIndicator);
                    }
                    
                    // Create a new event source function with the current filters
                    const newEventSource = (fetchInfo, successCallback, failureCallback) => {
                        console.log('=== EVENT SOURCE FUNCTION CALLED ===');
                        if (DEBUG_MODE) {
                            console.log('fetchInfo:', fetchInfo);
                        }
                        
                        // Get the filter parameters as a string
                        let filterParams = getFilterParams();
                        
                        // Add date range from fetchInfo if not already present
                        const urlParams = new URLSearchParams(filterParams);
                        if (!urlParams.has('start') && fetchInfo.startStr) {
                            urlParams.set('start', fetchInfo.startStr);
                        }
                        if (!urlParams.has('end') && fetchInfo.endStr) {
                            urlParams.set('end', fetchInfo.endStr);
                        }
                        
                        // Add a timestamp to force refresh and prevent caching
                        urlParams.set('_t', new Date().getTime());
                        
                        // Add a filter reset indicator to help with debugging
                        const hasLocationFilters = activeFilters.state || activeFilters.city;
                        if (DEBUG_MODE) {
                            console.log('Has location filters:', hasLocationFilters);
                            console.log('State filter:', activeFilters.state || 'empty');
                            console.log('City filter:', activeFilters.city || 'empty');
                        }
                        
                        // Build the URL
                        const url = `${URLROOT}/calendar/getEvents?${urlParams.toString()}`;
                        
                        if (DEBUG_MODE) {
                            console.log('Fetching filtered events from:', url);
                        }
                        
                        // Fetch the events
                        fetch(url)
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP error! Status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (DEBUG_MODE) {
                                    console.log(`Received ${data.length} filtered events from server`);
                                }
                                
                                if (typeof successCallback === 'function') {
                                    successCallback(data);
                                }
                                return data;
                            })
                            .catch(error => {
                                console.error('Error loading filtered events:', error);
                                if (typeof failureCallback === 'function') {
                                    failureCallback(error);
                                }
                                return [];
                            });
                    };
                    
                    // CRITICAL: Completely clear existing events and DOM elements first
                    console.log('=== CLEARING CALENDAR BEFORE APPLYING FILTERS ===');
                    if (typeof calendarInstance.clearEvents === 'function') {
                        if (DEBUG_MODE) {
                            console.log('Using clearEvents method to clear calendar');
                        }
                        calendarInstance.clearEvents();
                    } else {
                        if (DEBUG_MODE) {
                            console.log('Using fallback method to clear calendar');
                        }
                        // Fallback for older calendar versions
                        calendarInstance.events = [];
                        const calendarContainer = document.getElementById('calendar');
                        if (calendarContainer) {
                            const eventElements = calendarContainer.querySelectorAll('.calendar-event');
                            eventElements.forEach(el => el.remove());
                            // Also clear any event-related classes
                            const eventCells = calendarContainer.querySelectorAll('.has-events');
                            eventCells.forEach(cell => {
                                cell.classList.remove('has-events');
                                cell.removeAttribute('data-events');
                            });
                        }
                    }
                    
                    // FORCE REFRESH: Always create a new event source to ensure fresh data
                    // This is critical for the filter reset scenario
                    if (DEBUG_MODE) {
                        console.log('=== FORCING CALENDAR REFRESH ===');
                        console.log('Previous event sources count:', calendarInstance.options.eventSources.length);
                    }
                    
                    // Clear existing event sources completely
                    calendarInstance.options.eventSources = [];
                    
                    // Add the new event source
                    calendarInstance.options.eventSources = [newEventSource];
                    
                    if (DEBUG_MODE) {
                        console.log('New event sources count:', calendarInstance.options.eventSources.length);
                        console.log('=== CALENDAR REFRESH FORCED ===');
                    }
                    
                    // Check if this is a Monthly Event Chart or regular calendar
                    if (typeof calendarInstance.loadEventsDirectly === 'function') {
                        // Monthly Event Chart - load events directly
                        console.log('=== FILTER SYSTEM: Detected Monthly Event Chart ===');
                        if (DEBUG_MODE) {
                            console.log('Loading filtered events for Event chart');
                        }
                        
                        // Fetch events directly and load them
                        const filterParams = getFilterParams();
                        const urlParams = new URLSearchParams(filterParams);
                        
                        // Add current month date range
                        const currentDate = calendarInstance.currentDate || new Date();
                        const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                        const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
                        
                        urlParams.set('start', monthStart.toISOString().split('T')[0]);
                        urlParams.set('end', monthEnd.toISOString().split('T')[0]);
                        urlParams.set('_t', Date.now());
                        
                        const url = `${URLROOT}/calendar/getEvents?${urlParams.toString()}`;
                        
                        if (DEBUG_MODE) {
                            console.log('Fetching filtered events for Event chart from:', url);
                            console.log('Filter parameters:', filterParams);
                        }
                        
                        fetch(url)
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP error! Status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(events => {
                                console.log('=== FILTER SYSTEM: Events fetched successfully ===');
                                console.log(`Loaded ${events.length} filtered events for Event chart`);
                                if (DEBUG_MODE && events.length > 0) {
                                    console.log('Sample events:', events.slice(0, 3).map(e => ({
                                        title: e.title,
                                        start: e.start,
                                        end: e.end
                                    })));
                                }
                                
                                console.log('=== FILTER SYSTEM: Calling loadEventsDirectly ===');
                                calendarInstance.loadEventsDirectly(events);
                                
                                // Dispatch event for compatibility
                                window.dispatchEvent(new CustomEvent('calendarEventsLoaded', {
                                    detail: { events: events }
                                }));
                                console.log('=== FILTER SYSTEM: Event loading completed ===');
                            })
                            .catch(error => {
                                console.error('Error loading filtered events for Event chart:', error);
                                // Clear the Event chart on error
                                if (typeof calendarInstance.clearEvents === 'function') {
                                    calendarInstance.clearEvents();
                                }
                            });
                    } else if (typeof calendarInstance.refetchEvents === 'function') {
                        // FullCalendar implementation
                        if (DEBUG_MODE) {
                            console.log('Using FullCalendar refetchEvents');
                        }
                        calendarInstance.refetchEvents();
                    } else if (typeof calendarInstance.loadEvents === 'function') {
                        // Custom calendar implementation
                        if (DEBUG_MODE) {
                            console.log('Using custom calendar loadEvents');
                        }
                        calendarInstance.loadEvents();
                    } else {
                        console.warn('No compatible event loading method found');
                    }
                    
                    if (DEBUG_MODE) {
                        console.log('Calendar event sources updated successfully');
                    }
                    
                    // Remove loading indicator
                    if (calendarEl) {
                        calendarEl.classList.remove('loading');
                        const loadingIndicator = calendarEl.querySelector('.calendar-loading-indicator');
                        if (loadingIndicator) {
                            loadingIndicator.remove();
                        }
                    }
                    

                    
                    return; // Exit early - the new event source will handle loading
                } else {
                    console.error('No compatible calendar refresh method found');
                    // Instead of reloading, show an error message
                    const calendarEl = document.getElementById('calendar');
                    if (calendarEl) {
                        calendarEl.innerHTML = '<div class="alert alert-warning">Unable to refresh calendar. Please reload the page manually.</div>';
                    }
                }
            } else {
                console.error('Calendar object not found - neither local nor global calendar available');
                if (DEBUG_MODE) {
                    console.log('Available calendar references:', {
                        localCalendar: typeof calendar,
                        globalCalendar: typeof window.calendar,
                        windowCalendarInstance: typeof window.calendarInstance
                    });
                }
            }
        }
        
        // Reset the flag to allow future filter applications
        isApplyingFilters = false;
        
        if (DEBUG_MODE) {
            console.log('=== APPLY FILTERS COMPLETED ===');
        }
    }

    /**
     * Reset all filters to default values
     */
    function resetFilters() {
        // Reset date inputs
        if (startDateInput) startDateInput.value = '';
        if (endDateInput) endDateInput.value = '';
        
        // Reset state, city, venue filters
        if (stateFilter) stateFilter.selectedIndex = 0;
        if (cityFilter) {
            while (cityFilter.options.length > 1) {
                cityFilter.remove(1);
            }
            cityFilter.selectedIndex = 0;
        }
        if (venueFilter) {
            while (venueFilter.options.length > 1) {
                venueFilter.remove(1);
            }
            venueFilter.selectedIndex = 0;
        }
        
        // Reset club filter
        if (clubFilter) {
            for (let i = 0; i < clubFilter.options.length; i++) {
                clubFilter.options[i].selected = false;
            }
        }
        
        // Reset radius filter
        if (radiusFilter) {
            radiusFilter.value = 50;
            if (radiusValue) radiusValue.textContent = '50';
        }
        
        // Reset location search
        if (locationSearch) locationSearch.value = '';
        
        // Reset keyword filter
        if (keywordFilter) keywordFilter.value = '';
        
        // Check all calendar checkboxes
        document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        
        // Reset category filter
        if (categoryFilter) {
            for (let i = 0; i < categoryFilter.options.length; i++) {
                categoryFilter.options[i].selected = false;
            }
        }
        
        // Reset tag filter
        if (tagFilter) {
            for (let i = 0; i < tagFilter.options.length; i++) {
                tagFilter.options[i].selected = false;
            }
        }
        
        // Reset price range slider
        if (typeof $ !== 'undefined' && typeof $.fn.slider !== 'undefined' && $('#price-range-slider').length) {
            $('#price-range-slider').slider('values', [0, 500]);
            if (priceRangeValue) priceRangeValue.textContent = '$0 - $500';
            if (priceMinInput) priceMinInput.value = 0;
            if (priceMaxInput) priceMaxInput.value = 500;
        }
        
        // Reset active filters object
        activeFilters.startDate = null;
        activeFilters.endDate = null;
        activeFilters.state = '';
        activeFilters.city = '';
        activeFilters.venue = '';
        activeFilters.clubs = [];
        activeFilters.radius = 50;
        activeFilters.lat = null;
        activeFilters.lng = null;
        activeFilters.keyword = '';
        activeFilters.categories = [];
        activeFilters.tags = [];
        activeFilters.priceMin = 0;
        activeFilters.priceMax = 500;
        
        // Update calendar filters
        updateCalendarFilters();
        
        // Apply the reset filters
        applyFilters();
    }

    /**
     * Get the current filter parameters as a query string
     * 
     * @returns {string} Query string with filter parameters
     */
    function getFilterParams() {
        console.log('=== GET FILTER PARAMS CALLED ===');
        console.log('activeFilters.state:', activeFilters.state, 'empty:', activeFilters.state === '');
        console.log('activeFilters.city:', activeFilters.city, 'empty:', activeFilters.city === '');
        
        const params = [];
        
        if (activeFilters.startDate) {
            params.push(`start=${activeFilters.startDate}`);
        }
        
        if (activeFilters.endDate) {
            params.push(`end=${activeFilters.endDate}`);
        }
        
        // Always include calendar_id parameter, even if empty
        // This allows the server to know when no calendars are selected
        params.push(`calendar_id=${activeFilters.calendars.join(',')}`);
        
        // Log the calendar filter for debugging
        console.log('Calendar filter:', activeFilters.calendars.join(','));
        if (DEBUG_MODE) {
            console.log('Calendar IDs being sent:', activeFilters.calendars);
            console.log('Calendar parameter string:', activeFilters.calendars.join(','));
        }
        
        if (activeFilters.state) {
            console.log('Adding state parameter:', activeFilters.state);
            params.push(`state=${encodeURIComponent(activeFilters.state)}`);
        } else {
            console.log('NOT adding state parameter (empty or falsy)');
        }
        
        if (activeFilters.city) {
            console.log('Adding city parameter:', activeFilters.city);
            params.push(`city=${encodeURIComponent(activeFilters.city)}`);
        } else {
            console.log('NOT adding city parameter (empty or falsy)');
        }
        
        if (activeFilters.venue) {
            params.push(`venue_id=${activeFilters.venue}`);
        }
        
        if (activeFilters.clubs.length > 0) {
            params.push(`club_id=${activeFilters.clubs.join(',')}`);
        }
        
        if (activeFilters.keyword) {
            params.push(`keyword=${encodeURIComponent(activeFilters.keyword)}`);
            if (DEBUG_MODE) {
                console.log('Adding keyword parameter:', activeFilters.keyword);
            }
        }
        
        if (activeFilters.lat && activeFilters.lng && activeFilters.radius) {
            params.push(`lat=${activeFilters.lat}`);
            params.push(`lng=${activeFilters.lng}`);
            params.push(`radius=${activeFilters.radius}`);
            if (DEBUG_MODE) {
                console.log('Adding location parameters:', {
                    lat: activeFilters.lat,
                    lng: activeFilters.lng,
                    radius: activeFilters.radius
                });
            }
        }
        
        if (activeFilters.categories.length > 0) {
            params.push(`category=${activeFilters.categories.join(',')}`);
        }
        
        if (activeFilters.tags.length > 0) {
            params.push(`tag=${activeFilters.tags.join(',')}`);
        }
        
        if (activeFilters.priceMin > 0 || activeFilters.priceMax < 500) {
            params.push(`price_range=${activeFilters.priceMin}-${activeFilters.priceMax}`);
        }
        
        const result = params.join('&');
        console.log('=== FINAL FILTER PARAMS ===', result);
        return result;
    }

    // Expose functions and objects to global scope
    window.calendarFilters = {
        activeFilters: activeFilters,
        getFilterParams: getFilterParams,
        applyFilters: applyFilters,
        resetFilters: resetFilters,
        updateCalendarFilters: updateCalendarFilters,
        geocodeLocationAndApplyFilters: geocodeLocationAndApplyFilters,
        get isApplyingFilters() { return isApplyingFilters; }
    };
});