<?php
/**
 * Comprehensive notification system fix
 * This will process the backlog and test both immediate and queued notifications
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load core classes
require_once APPROOT . '/core/Database.php';

// Load notification helper
require_once APPROOT . '/helpers/notification_helper.php';

echo "<h1>🔧 Notification System Fix</h1>";

$userId = 3; // Test user ID

try {
    require_once APPROOT . '/models/NotificationService.php';
    require_once APPROOT . '/models/NotificationModel.php';
    
    $notificationService = new NotificationService();
    $notificationModel = new NotificationModel();
    $db = new Database();
    
    echo "<h2>1. Current System Status</h2>";
    
    // Check pending notifications
    $db->query("SELECT COUNT(*) as total FROM notification_queue WHERE status = 'pending'");
    $result = $db->single();
    $totalPending = $result->total;
    
    echo "<p><strong>Pending notifications:</strong> $totalPending</p>";
    
    // Check user preferences
    $userPrefs = $notificationModel->getUserPreferences($userId);
    echo "<h3>User $userId Preferences:</h3>";
    if ($userPrefs) {
        echo "<ul>";
        echo "<li>Email: " . ($userPrefs->email_notifications ? "✅ Enabled" : "❌ Disabled") . "</li>";
        echo "<li>SMS: " . ($userPrefs->sms_notifications ? "✅ Enabled" : "❌ Disabled") . "</li>";
        echo "<li>Push: " . ($userPrefs->push_notifications ? "✅ Enabled" : "❌ Disabled") . "</li>";
        echo "<li>Toast: " . ($userPrefs->toast_notifications ? "✅ Enabled" : "❌ Disabled") . "</li>";
        echo "</ul>";
    } else {
        echo "<p>❌ No preferences found for user $userId</p>";
    }
    
    // Check push subscriptions
    $subscriptions = $notificationModel->getUserPushSubscriptions($userId);
    echo "<p><strong>Push subscriptions:</strong> " . count($subscriptions) . "</p>";
    
    echo "<h2>2. Processing Notification Backlog</h2>";
    
    if ($totalPending > 0) {
        echo "<p>Processing $totalPending pending notifications...</p>";
        
        $batchSize = 50;
        $totalProcessed = 0;
        $totalSent = 0;
        $totalFailed = 0;
        $batchNumber = 1;
        
        while ($totalPending > 0 && $batchNumber <= 10) {
            echo "<h4>Batch $batchNumber</h4>";
            
            $results = $notificationService->processPendingNotifications($batchSize);
            
            echo "<p>Processed: {$results['processed']}, Sent: {$results['sent']}, Failed: {$results['failed']}</p>";
            
            $totalProcessed += $results['processed'];
            $totalSent += $results['sent'];
            $totalFailed += $results['failed'];
            
            if (!empty($results['errors'])) {
                echo "<details><summary>Errors</summary><ul>";
                foreach (array_slice($results['errors'], 0, 5) as $error) {
                    echo "<li>" . htmlspecialchars($error) . "</li>";
                }
                echo "</ul></details>";
            }
            
            // Check remaining
            $db->query("SELECT COUNT(*) as total FROM notification_queue WHERE status = 'pending'");
            $result = $db->single();
            $totalPending = $result->total;
            
            echo "<p>Remaining: $totalPending</p>";
            $batchNumber++;
            
            if ($results['processed'] == 0) {
                echo "<p>⚠️ No notifications processed in this batch, stopping to prevent infinite loop</p>";
                break;
            }
        }
        
        echo "<h3>Backlog Processing Summary</h3>";
        echo "<p>Total Processed: $totalProcessed</p>";
        echo "<p>Total Sent: $totalSent</p>";
        echo "<p>Total Failed: $totalFailed</p>";
    } else {
        echo "<p>✅ No pending notifications to process</p>";
    }
    
    echo "<h2>3. Testing Immediate Notifications</h2>";
    
    echo "<h3>Testing Helper Functions</h3>";
    
    // Test immediate push notification
    echo "<h4>Push Notification Test</h4>";
    $pushResult = sendPushNotification($userId, "Immediate Push Test", "This push notification was sent immediately at " . date('H:i:s'));
    echo "<p>Push result: " . ($pushResult ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
    
    // Test immediate toast notification
    echo "<h4>Toast Notification Test</h4>";
    $toastResult = sendToastNotification($userId, "Immediate Toast Test", "This toast notification was sent immediately at " . date('H:i:s'));
    echo "<p>Toast result: " . ($toastResult ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
    
    // Test all enabled types
    echo "<h4>All Enabled Types Test</h4>";
    $allResult = sendNotification($userId, "All Types Test", "This notification was sent to all enabled types at " . date('H:i:s'));
    echo "<p>All types result:</p>";
    echo "<pre>" . print_r($allResult, true) . "</pre>";
    
    echo "<h2>4. Testing Queued Notifications</h2>";
    
    // Queue some test notifications
    echo "<h3>Queueing Test Notifications</h3>";
    
    $queuedCount = 0;
    
    // Queue a push notification
    $queued = $notificationModel->queueTestNotification(
        $userId,
        'push',
        'Queued Push Test',
        'This is a queued push notification test at ' . date('H:i:s'),
        null // immediate processing
    );
    if ($queued) {
        $queuedCount++;
        echo "<p>✅ Queued push notification</p>";
    }
    
    // Queue a toast notification
    $queued = $notificationModel->queueTestNotification(
        $userId,
        'toast',
        'Queued Toast Test',
        'This is a queued toast notification test at ' . date('H:i:s'),
        null // immediate processing
    );
    if ($queued) {
        $queuedCount++;
        echo "<p>✅ Queued toast notification</p>";
    }
    
    echo "<p>Queued $queuedCount test notifications</p>";
    
    // Process the queued notifications
    if ($queuedCount > 0) {
        echo "<h3>Processing Queued Test Notifications</h3>";
        $results = $notificationService->processPendingNotifications(10);
        echo "<p>Processed: {$results['processed']}, Sent: {$results['sent']}, Failed: {$results['failed']}</p>";
        
        if (!empty($results['errors'])) {
            echo "<details><summary>Processing Errors</summary><ul>";
            foreach ($results['errors'] as $error) {
                echo "<li>" . htmlspecialchars($error) . "</li>";
            }
            echo "</ul></details>";
        }
    }
    
    echo "<h2>5. Final System Status</h2>";
    
    // Final status check
    $db->query("SELECT status, COUNT(*) as count FROM notification_queue GROUP BY status");
    $statusCounts = $db->resultSet();
    
    echo "<h3>Notification Queue Status</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f2f2f2;'><th>Status</th><th>Count</th></tr>";
    foreach ($statusCounts as $status) {
        echo "<tr><td>{$status->status}</td><td>{$status->count}</td></tr>";
    }
    echo "</table>";
    
    // Check recent notifications in user tables
    echo "<h3>Recent User Notifications</h3>";
    
    $db->query("SELECT COUNT(*) as count FROM user_push_notifications WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $db->bind(1, $userId);
    $db->execute();
    $recentPush = $db->single()->count;
    
    $db->query("SELECT COUNT(*) as count FROM user_toast_notifications WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $db->bind(1, $userId);
    $db->execute();
    $recentToast = $db->single()->count;
    
    echo "<p>Recent push notifications (last hour): $recentPush</p>";
    echo "<p>Recent toast notifications (last hour): $recentToast</p>";
    
    echo "<h2>6. Recommendations</h2>";
    
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ System Status</h4>";
    
    if ($totalPending == 0) {
        echo "<p>✅ No pending notifications - backlog cleared!</p>";
    } else {
        echo "<p>⚠️ $totalPending notifications still pending - may need manual review</p>";
    }
    
    if ($pushResult && $toastResult) {
        echo "<p>✅ Immediate notifications working</p>";
    } else {
        echo "<p>❌ Immediate notifications have issues</p>";
    }
    
    if (count($subscriptions) > 0) {
        echo "<p>✅ User has push subscriptions</p>";
    } else {
        echo "<p>⚠️ User needs to enable push notifications in browser</p>";
    }
    
    echo "<h4>🔧 Next Steps</h4>";
    echo "<ol>";
    echo "<li>Ensure cron job is running: <code>/cron/process_notifications.php</code></li>";
    echo "<li>Test user flow: enable notifications in user preferences</li>";
    echo "<li>Check browser notifications are allowed</li>";
    echo "<li>Monitor notification queue for new issues</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<details><summary>Stack Trace</summary><pre>" . $e->getTraceAsString() . "</pre></details>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Notification System Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        h1, h2, h3 { color: #333; }
        h1 { border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
        details { margin: 10px 0; }
        summary { cursor: pointer; font-weight: bold; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <p><strong>This script:</strong> Processes notification backlog, tests immediate notifications, tests queued notifications, and provides system status.</p>
</body>
</html>
