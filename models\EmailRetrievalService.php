<?php
/**
 * Email Retrieval Service
 * 
 * Handles POP3/IMAP email fetching for the unified messaging system
 * Supports both POP3 and IMAP protocols with SSL/TLS encryption
 */
class EmailRetrievalService {
    private $db;
    private $settingsModel;
    private $connection;
    private $protocol;
    private $host;
    private $port;
    private $username;
    private $password;
    private $encryption;
    private $deleteAfterProcessing;
    
    public function __construct() {
        $this->db = new Database();
        require_once APPROOT . '/models/SettingsModel.php';
        $this->settingsModel = new SettingsModel();
        $this->loadSettings();
    }
    
    /**
     * Load email server settings from database
     */
    private function loadSettings() {
        $this->protocol = $this->settingsModel->getSetting('email_server_protocol', 'imap');
        $this->host = $this->settingsModel->getSetting('email_server_host', '');
        $this->port = $this->settingsModel->getSetting('email_server_port', '993');
        $this->username = $this->settingsModel->getSetting('email_server_username', '');
        $this->password = $this->settingsModel->getSetting('email_server_password', '');
        $this->encryption = $this->settingsModel->getSetting('email_server_encryption', 'ssl');
        $this->deleteAfterProcessing = $this->settingsModel->getSetting('email_delete_after_processing', '1') === '1';
    }
    
    /**
     * Check if email retrieval is properly configured
     */
    public function isConfigured() {
        return !empty($this->host) && !empty($this->username) && !empty($this->password);
    }
    
    /**
     * Test email server connection
     */
    public function testConnection() {
        if (!$this->isConfigured()) {
            return ['success' => false, 'message' => 'Email server not configured'];
        }
        
        try {
            $this->connect();
            $this->disconnect();
            return ['success' => true, 'message' => 'Connection successful'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Connection failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Connect to email server
     */
    private function connect() {
        if ($this->connection) {
            return true;
        }
        
        if (!$this->isConfigured()) {
            throw new Exception('Email server not configured');
        }
        
        // Build connection string
        $connectionString = '{' . $this->host . ':' . $this->port;
        
        if ($this->protocol === 'imap') {
            $connectionString .= '/imap';
        } else {
            $connectionString .= '/pop3';
        }
        
        if ($this->encryption === 'ssl') {
            $connectionString .= '/ssl';
        } elseif ($this->encryption === 'tls') {
            $connectionString .= '/tls';
        }
        
        $connectionString .= '/novalidate-cert}INBOX';
        
        // Attempt connection
        $this->connection = imap_open($connectionString, $this->username, $this->password);
        
        if (!$this->connection) {
            throw new Exception('Failed to connect to email server: ' . imap_last_error());
        }
        
        return true;
    }
    
    /**
     * Disconnect from email server
     */
    private function disconnect() {
        if ($this->connection) {
            imap_close($this->connection);
            $this->connection = null;
        }
    }
    
    /**
     * Fetch new emails from server
     */
    public function fetchEmails($limit = 50) {
        try {
            $this->connect();
            
            // Get message count
            $messageCount = imap_num_msg($this->connection);
            
            if ($messageCount === 0) {
                $this->disconnect();
                return ['success' => true, 'emails' => [], 'message' => 'No new emails'];
            }
            
            // Calculate range to fetch (newest first)
            $start = max(1, $messageCount - $limit + 1);
            $end = $messageCount;
            
            $emails = [];
            $processedMessages = [];

            for ($i = $end; $i >= $start; $i--) {
                try {
                    $email = $this->parseEmail($i);
                    if ($email) {
                        $emails[] = $email;
                        $processedMessages[] = $i; // Track successfully processed messages
                    }
                } catch (Exception $e) {
                    error_log("EmailRetrievalService::fetchEmails - Error parsing email $i: " . $e->getMessage());
                    continue;
                }
            }

            // Delete processed emails from server if configured to do so
            if ($this->deleteAfterProcessing && !empty($processedMessages)) {
                try {
                    foreach ($processedMessages as $messageNumber) {
                        $this->markForDeletion($messageNumber);
                    }
                    $this->expungeDeleted();
                    error_log("EmailRetrievalService::fetchEmails - Deleted " . count($processedMessages) . " processed emails from server");
                } catch (Exception $e) {
                    error_log("EmailRetrievalService::fetchEmails - Error deleting emails: " . $e->getMessage());
                }
            }

            $this->disconnect();
            
            return [
                'success' => true, 
                'emails' => $emails, 
                'message' => 'Fetched ' . count($emails) . ' emails'
            ];
            
        } catch (Exception $e) {
            $this->disconnect();
            error_log("EmailRetrievalService::fetchEmails - Error: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Parse individual email
     */
    private function parseEmail($messageNumber) {
        $header = imap_headerinfo($this->connection, $messageNumber);
        $body = imap_body($this->connection, $messageNumber);
        
        if (!$header) {
            throw new Exception("Failed to get email header for message $messageNumber");
        }
        
        // Extract basic information
        $email = [
            'message_number' => $messageNumber,
            'message_id' => $header->message_id ?? '',
            'date' => $this->parseEmailDateToUTC($header->date ?? null),
            'subject' => $this->decodeHeader($header->subject ?? ''),
            'from_email' => $this->extractEmail($header->from ?? []),
            'from_name' => $this->extractName($header->from ?? []),
            'to_email' => $this->extractEmail($header->to ?? []),
            'reply_to' => $this->extractEmail($header->reply_to ?? []),
            'body' => $this->decodeBody($body),
            'size' => $header->Size ?? 0,
            'attachments' => [],
            'is_multipart' => false
        ];
        
        // Check for multipart message (attachments)
        $structure = imap_fetchstructure($this->connection, $messageNumber);
        if (isset($structure->parts) && count($structure->parts) > 0) {
            $email['is_multipart'] = true;
            $email['attachments'] = $this->extractAttachments($messageNumber, $structure);
        }
        
        // Extract threading information
        $email['in_reply_to'] = $header->in_reply_to ?? '';
        $email['references'] = $header->references ?? '';
        
        return $email;
    }
    
    /**
     * Decode email header (handles encoding)
     */
    private function decodeHeader($header) {
        $decoded = imap_mime_header_decode($header);
        $result = '';
        
        foreach ($decoded as $part) {
            $result .= $part->text;
        }
        
        return trim($result);
    }
    
    /**
     * Extract email address from header object
     */
    private function extractEmail($headerArray) {
        if (empty($headerArray) || !is_array($headerArray)) {
            return '';
        }
        
        $first = $headerArray[0];
        if (isset($first->mailbox) && isset($first->host)) {
            return $first->mailbox . '@' . $first->host;
        }
        
        return '';
    }
    
    /**
     * Extract name from header object
     */
    private function extractName($headerArray) {
        if (empty($headerArray) || !is_array($headerArray)) {
            return '';
        }
        
        $first = $headerArray[0];
        return isset($first->personal) ? $this->decodeHeader($first->personal) : '';
    }
    
    /**
     * Decode email body - handle multipart MIME emails properly
     */
    private function decodeBody($body) {
        // Check if this is a multipart email
        if (strpos($body, 'Content-Type:') !== false && strpos($body, 'boundary=') !== false) {
            return $this->parseMultipartBody($body);
        }

        // Handle simple email body
        $decoded = quoted_printable_decode($body);

        // Remove excessive whitespace and normalize line endings
        $decoded = preg_replace('/\r\n|\r|\n/', "\n", $decoded);
        $decoded = preg_replace('/\n{3,}/', "\n\n", $decoded);

        return trim($decoded);
    }

    /**
     * Parse multipart email body to extract plain text content
     */
    private function parseMultipartBody($body) {
        // Extract the boundary from the body
        $boundary = null;
        // Look for boundary patterns like ------sinikael-?=_1-17528869465410.7023045675010756
        if (preg_match('/^(--+[^\r\n]+)/m', $body, $matches)) {
            $boundary = trim($matches[1]);
        }
        
        // Also try to extract from Content-Type header if present
        if (!$boundary && preg_match('/boundary=(["\']?)([^"\'\r\n;]+)\1/i', $body, $matches)) {
            $boundary = '--' . $matches[2];
        }
        
        if (!$boundary) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("EmailParser: No boundary found, using simple text extraction");
            }
            // Fallback to simple text extraction
            return $this->extractSimpleText($body);
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("EmailParser: Found boundary: " . $boundary);
        }
        
        // Split by boundary
        $parts = explode($boundary, $body);
        
        // Look for text/plain part first (preferred)
        foreach ($parts as $part) {
            if (strpos($part, 'Content-Type: text/plain') !== false) {
                $text = $this->extractTextFromPart($part);
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("EmailParser: Found text/plain part, extracted: " . substr($text, 0, 100) . "...");
                }
                return $text;
            }
        }
        
        // Fallback: look for any part with readable content
        foreach ($parts as $part) {
            $text = $this->extractTextFromPart($part);
            if (!empty($text) && strlen($text) > 10) {
                return $text;
            }
        }

        // Final fallback: try to extract any readable text
        return $this->extractSimpleText($body);
    }
    
    /**
     * Extract text content from a MIME part
     */
    private function extractTextFromPart($part) {
        // Find the content after headers (look for double newline or single newline followed by content)
        $headerEndPos = strpos($part, "\n\n");
        if ($headerEndPos === false) {
            $headerEndPos = strpos($part, "\r\n\r\n");
        }
        
        // If no double newline found, look for single newline after last header
        if ($headerEndPos === false) {
            $lines = explode("\n", $part);
            $headerEndLine = 0;
            foreach ($lines as $i => $line) {
                if (preg_match('/^[A-Za-z-]+:\s/', trim($line))) {
                    $headerEndLine = $i;
                } else if (trim($line) === '' && $headerEndLine > 0) {
                    $headerEndPos = strpos($part, $line, strpos($part, $lines[$headerEndLine]));
                    break;
                }
            }
        }
        
        if ($headerEndPos === false) {
            // No clear header separation, try to find first non-header line
            $lines = explode("\n", $part);
            $contentStart = 0;
            foreach ($lines as $i => $line) {
                $trimmed = trim($line);
                if (!empty($trimmed) && !preg_match('/^[A-Za-z-]+:\s/', $trimmed)) {
                    $contentStart = $i;
                    break;
                }
            }
            $headers = implode("\n", array_slice($lines, 0, $contentStart));
            $content = implode("\n", array_slice($lines, $contentStart));
        } else {
            $headers = substr($part, 0, $headerEndPos);
            $content = substr($part, $headerEndPos + 2);
        }
        
        // Determine encoding
        $encoding = '7bit'; // default
        if (preg_match('/Content-Transfer-Encoding:\s*([^\r\n]+)/i', $headers, $matches)) {
            $encoding = strtolower(trim($matches[1]));
        }
        
        // Decode based on encoding
        switch ($encoding) {
            case 'quoted-printable':
                $content = quoted_printable_decode($content);
                break;
            case 'base64':
                $content = base64_decode($content);
                break;
            case '7bit':
            case '8bit':
            default:
                // No decoding needed
                break;
        }
        
        // Clean up the content
        $content = trim($content);
        
        // If this is HTML content, strip tags but preserve line breaks
        if (strpos($headers, 'Content-Type: text/html') !== false) {
            $content = strip_tags($content);
        }
        
        // Remove excessive whitespace
        $content = preg_replace('/\r\n|\r|\n/', "\n", $content);
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        
        return trim($content);
    }
    
    /**
     * Simple text extraction fallback
     */
    private function extractSimpleText($body) {
        $lines = explode("\n", $body);
        $content = [];
        $skipNext = false;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                continue;
            }
            
            // Skip boundaries
            if (preg_match('/^--+/', $line)) {
                continue;
            }
            
            // Skip headers
            if (preg_match('/^[A-Za-z-]+:\s/', $line)) {
                continue;
            }
            
            // Skip HTML tags
            if (preg_match('/^<[^>]+>/', $line)) {
                continue;
            }
            
            // This looks like actual content
            if (strlen($line) > 2) {
                $content[] = $line;
            }
        }
        
        return implode("\n", $content);
    }
    
    /**
     * Extract attachments from multipart message
     */
    private function extractAttachments($messageNumber, $structure) {
        $attachments = [];
        
        if (!isset($structure->parts)) {
            return $attachments;
        }
        
        foreach ($structure->parts as $partNumber => $part) {
            if (isset($part->disposition) && strtolower($part->disposition) === 'attachment') {
                $attachment = [
                    'part_number' => $partNumber + 1,
                    'filename' => '',
                    'size' => $part->bytes ?? 0,
                    'type' => $part->type ?? 0,
                    'subtype' => $part->subtype ?? '',
                    'encoding' => $part->encoding ?? 0
                ];
                
                // Extract filename
                if (isset($part->dparameters)) {
                    foreach ($part->dparameters as $param) {
                        if (strtolower($param->attribute) === 'filename') {
                            $attachment['filename'] = $this->decodeHeader($param->value);
                            break;
                        }
                    }
                }
                
                if (empty($attachment['filename']) && isset($part->parameters)) {
                    foreach ($part->parameters as $param) {
                        if (strtolower($param->attribute) === 'name') {
                            $attachment['filename'] = $this->decodeHeader($param->value);
                            break;
                        }
                    }
                }
                
                $attachments[] = $attachment;
            }
        }
        
        return $attachments;
    }
    
    /**
     * Download attachment content
     */
    public function downloadAttachment($messageNumber, $partNumber, $encoding) {
        if (!$this->connection) {
            throw new Exception('Not connected to email server');
        }
        
        $data = imap_fetchbody($this->connection, $messageNumber, $partNumber);
        
        // Decode based on encoding
        switch ($encoding) {
            case 1: // 8bit
                break;
            case 2: // binary
                break;
            case 3: // base64
                $data = base64_decode($data);
                break;
            case 4: // quoted-printable
                $data = quoted_printable_decode($data);
                break;
        }
        
        return $data;
    }
    
    /**
     * Mark email for deletion
     */
    public function markForDeletion($messageNumber) {
        if (!$this->connection) {
            throw new Exception('Not connected to email server');
        }
        
        return imap_delete($this->connection, $messageNumber);
    }
    
    /**
     * Expunge deleted emails
     */
    public function expungeDeleted() {
        if (!$this->connection) {
            throw new Exception('Not connected to email server');
        }
        
        return imap_expunge($this->connection);
    }

    /**
     * Convert email server timestamp to UTC
     * Email server is in Pacific Time, need to convert to UTC for database storage
     */
    private function convertEmailTimestampToUTC($emailDate) {
        if (empty($emailDate)) {
            // If no date provided, use current UTC time
            return gmdate('Y-m-d H:i:s');
        }

        try {
            // Parse the email date string
            $timestamp = strtotime($emailDate);
            if ($timestamp === false) {
                // If parsing fails, use current UTC time
                return gmdate('Y-m-d H:i:s');
            }

            // Create DateTime object from the timestamp
            // Email server is in Pacific Time (America/Los_Angeles)
            $pacificTime = new DateTime();
            $pacificTime->setTimestamp($timestamp);
            $pacificTime->setTimezone(new DateTimeZone('America/Los_Angeles'));

            // Convert to UTC
            $utcTime = clone $pacificTime;
            $utcTime->setTimezone(new DateTimeZone('UTC'));

            // Return in MySQL datetime format
            return $utcTime->format('Y-m-d H:i:s');

        } catch (Exception $e) {
            error_log("EmailRetrievalService::convertEmailTimestampToUTC - Error converting timestamp: " . $e->getMessage());
            // Fallback to current UTC time
            return gmdate('Y-m-d H:i:s');
        }
    }

    /**
     * Parse email date header to UTC for database storage
     * Follow the same pattern as calendar system - store in UTC, display with timezone helpers
     */
    private function parseEmailDateToUTC($emailDate) {
        if (empty($emailDate)) {
            // If no date provided, use current UTC time
            return gmdate('Y-m-d H:i:s');
        }

        try {
            // Parse the email date string with timezone info
            // strtotime() properly handles email date formats with timezone
            $timestamp = strtotime($emailDate);

            if ($timestamp === false) {
                error_log("EmailRetrievalService::parseEmailDateToUTC - Failed to parse email date: '$emailDate'");
                return gmdate('Y-m-d H:i:s');
            }

            // Convert timestamp to UTC for database storage (like calendar system)
            return gmdate('Y-m-d H:i:s', $timestamp);

        } catch (Exception $e) {
            error_log("EmailRetrievalService::parseEmailDateToUTC - Error parsing email date '$emailDate': " . $e->getMessage());

            // Final fallback: current UTC time
            return gmdate('Y-m-d H:i:s');
        }
    }

    /**
     * Destructor - ensure connection is closed
     */
    public function __destruct() {
        $this->disconnect();
    }
}
