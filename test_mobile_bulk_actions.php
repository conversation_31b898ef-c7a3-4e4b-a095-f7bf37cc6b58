<?php
/**
 * Test Mobile Bulk Actions Fix
 * 
 * Tests the improved mobile bulk action buttons
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🔘 Test Mobile Bulk Actions Fix</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Bulk Actions Improvements</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Issues Fixed:</h3>";
    echo "<ul>";
    echo "<li><strong>Horizontal layout:</strong> Changed from btn-group-vertical to btn-group</li>";
    echo "<li><strong>Larger buttons:</strong> Increased padding from 2px to 6-8px (4px+ larger)</li>";
    echo "<li><strong>Better spacing:</strong> Added margin between buttons</li>";
    echo "<li><strong>Consistent sizing:</strong> Both main bulk actions and message actions improved</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Messages</h2>";
        echo "<p>Creating multiple test messages to demonstrate bulk actions...</p>";
        
        $subjects = [
            "🔘 Bulk Action Test 1",
            "🔘 Bulk Action Test 2", 
            "🔘 Bulk Action Test 3"
        ];
        
        $createdCount = 0;
        foreach ($subjects as $subject) {
            $message = "This tests the improved mobile bulk action buttons with larger, horizontal layout.";
            $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
            if ($result) {
                $createdCount++;
            }
        }
        
        if ($createdCount > 0) {
            echo "<p style='color: green;'>✅ Created {$createdCount} test messages successfully!</p>";
            $unreadCount = $createdCount;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test messages</p>";
        }
    }
    
    echo "<h2>🧪 Mobile Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile Bulk Actions Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile device</strong></li>";
    echo "<li><strong>Navigate to Messages</strong> (notification center)</li>";
    echo "<li><strong>Select multiple messages:</strong> Check the checkboxes</li>";
    echo "<li><strong>Check bulk actions:</strong> Should appear horizontally at top</li>";
    echo "<li><strong>Test button size:</strong> Should be larger and easier to tap</li>";
    echo "<li><strong>Check message actions:</strong> Individual message buttons should also be horizontal</li>";
    echo "<li><strong>Verify spacing:</strong> Buttons should have proper spacing between them</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Mobile Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Element</th><th>Before</th><th>After</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Main Bulk Actions</strong></td>";
    echo "<td style='color: red;'>❌ Stacked vertically, small buttons</td>";
    echo "<td style='color: green;'>✅ Horizontal row, larger buttons (8px padding)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message Actions</strong></td>";
    echo "<td style='color: red;'>❌ Vertical stack, tiny buttons (2px padding)</td>";
    echo "<td style='color: green;'>✅ Horizontal row, larger buttons (6px padding)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Button Spacing</strong></td>";
    echo "<td style='color: red;'>❌ No spacing, cramped appearance</td>";
    echo "<td style='color: green;'>✅ 2px margins, proper spacing</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Tap Target Size</strong></td>";
    echo "<td style='color: red;'>❌ Too small for mobile tapping</td>";
    echo "<td style='color: green;'>✅ 4+ pixels larger, easier to tap</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Changes Made</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔘 Main Bulk Actions:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- BEFORE: Small vertical buttons -->
<div class=\"btn-group-vertical btn-group-sm\">
    <button class=\"btn btn-sm\" style=\"padding: 2px 6px;\">

<!-- AFTER: Larger horizontal buttons -->
<div class=\"btn-group btn-group-sm\">
    <button class=\"btn\" style=\"padding: 8px 12px;\">✅</button>
    <button class=\"btn\" style=\"padding: 8px 12px;\">📦</button>
    <button class=\"btn\" style=\"padding: 8px 12px;\">🗑️</button>
    <button class=\"btn\" style=\"padding: 8px 12px;\">❌</button>
</div>";
    echo "</pre>";
    
    echo "<h3>🔘 Message Action Buttons:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- BEFORE: Vertical stack -->
<div class=\"btn-group-vertical btn-group-sm\">
    <button style=\"padding: 2px 6px; font-size: 11px;\">

<!-- AFTER: Horizontal row -->
<div class=\"btn-group btn-group-sm\" style=\"flex-wrap: wrap;\">
    <button style=\"padding: 6px 8px; font-size: 12px; margin-bottom: 2px;\">👁️</button>
    <button style=\"padding: 6px 8px; font-size: 12px; margin-bottom: 2px;\">✅</button>
    <button style=\"padding: 6px 8px; font-size: 12px; margin-bottom: 2px;\">📦</button>
</div>";
    echo "</pre>";
    
    echo "<h3>🎨 CSS Improvements:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "/* Mobile bulk actions */
.d-md-none .btn-group .btn {
    padding: 8px 12px !important;
    font-size: 14px !important;
    margin-right: 2px;
}

/* Mobile message action buttons */
.d-md-none .message-item .btn-group .btn {
    padding: 6px 8px !important;
    font-size: 12px !important;
    margin-right: 2px;
    margin-bottom: 2px;
}";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>📏 Button Size Comparison</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📐 Padding Increases:</h3>";
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Button Type</th><th>Before</th><th>After</th><th>Increase</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Main Bulk Actions</strong></td>";
    echo "<td>0.25rem 0.5rem (4px 8px)</td>";
    echo "<td>8px 12px</td>";
    echo "<td style='color: green;'>+4px vertical, +4px horizontal</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message Actions</strong></td>";
    echo "<td>2px 6px</td>";
    echo "<td>6px 8px</td>";
    echo "<td style='color: green;'>+4px vertical, +2px horizontal</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Bulk Actions Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>What you should see on mobile:</strong></p>";
        echo "<ul>";
        echo "<li>🔘 <strong>Horizontal bulk actions:</strong> ✅ 📦 🗑️ ❌ in a row at top</li>";
        echo "<li>📏 <strong>Larger buttons:</strong> 4+ pixels bigger, easier to tap</li>";
        echo "<li>🔘 <strong>Horizontal message actions:</strong> 👁️ ✅ 📦 in a row per message</li>";
        echo "<li>📐 <strong>Proper spacing:</strong> 2px margins between buttons</li>";
        echo "<li>👆 <strong>Better usability:</strong> Much easier to tap on mobile</li>";
        echo "</ul>";
        echo "<p><strong>The bulk actions should now be much more mobile-friendly!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated file:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/notification_center/index.php</code> - Fixed bulk action layout and sizing</li>";
    echo "</ul>";
    echo "<p><strong>Changes made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Changed btn-group-vertical to btn-group (horizontal)</li>";
    echo "<li>✅ Increased padding: 2px→6px, 4px→8px (4+ pixels larger)</li>";
    echo "<li>✅ Added proper margins between buttons</li>";
    echo "<li>✅ Improved CSS for consistent mobile button sizing</li>";
    echo "<li>✅ Added flex-wrap for better button wrapping</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Mobile bulk actions test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
