<?php
/**
 * FCM Token Cleanup Cron Job
 * 
 * This script should be run daily via cron to clean up old inactive FCM tokens
 * 
 * Cron schedule example (run daily at 2 AM):
 * 0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_fcm_tokens.php
 */

// Prevent direct web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line');
}

require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/core/Database.php';
require_once dirname(__DIR__) . '/models/NotificationModel.php';

echo "[" . date('Y-m-d H:i:s') . "] Starting FCM token cleanup...\n";

try {
    $notificationModel = new NotificationModel();
    $db = new Database();
    
    // Check if fcm_tokens table exists
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    if (!$db->single()) {
        echo "[" . date('Y-m-d H:i:s') . "] No fcm_tokens table found, skipping cleanup\n";
        exit(0);
    }
    
    // Get current statistics
    $db->query("SELECT 
                    COUNT(*) as total,
                    SUM(active) as active,
                    COUNT(*) - SUM(active) as inactive
                FROM fcm_tokens");
    $beforeStats = $db->single();
    
    echo "[" . date('Y-m-d H:i:s') . "] Before cleanup: {$beforeStats->total} total, {$beforeStats->active} active, {$beforeStats->inactive} inactive\n";
    
    // Cleanup strategies with different timeframes
    $cleanupStrategies = [
        ['days' => 7, 'description' => 'Very old inactive tokens (7+ days)'],
        ['days' => 30, 'description' => 'Old inactive tokens (30+ days)'],
        ['days' => 90, 'description' => 'Ancient inactive tokens (90+ days)']
    ];
    
    $totalCleaned = 0;
    
    foreach ($cleanupStrategies as $strategy) {
        $cleaned = $notificationModel->cleanupInactiveTokens($strategy['days']);
        $totalCleaned += $cleaned;
        
        if ($cleaned > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] Cleaned {$cleaned} {$strategy['description']}\n";
        }
    }
    
    // Additional cleanup: Remove tokens for users that no longer exist
    echo "[" . date('Y-m-d H:i:s') . "] Checking for orphaned tokens...\n";
    
    $db->query("DELETE fcm_tokens FROM fcm_tokens 
                LEFT JOIN users ON fcm_tokens.user_id = users.id 
                WHERE users.id IS NULL");
    
    if ($db->execute()) {
        $orphanedCleaned = $db->rowCount();
        if ($orphanedCleaned > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] Cleaned {$orphanedCleaned} orphaned tokens (users no longer exist)\n";
            $totalCleaned += $orphanedCleaned;
        }
    }
    
    // Additional cleanup: Remove duplicate tokens (same user + same token)
    echo "[" . date('Y-m-d H:i:s') . "] Checking for duplicate tokens...\n";
    
    $db->query("DELETE t1 FROM fcm_tokens t1
                INNER JOIN fcm_tokens t2 
                WHERE t1.id > t2.id 
                AND t1.user_id = t2.user_id 
                AND t1.token = t2.token");
    
    if ($db->execute()) {
        $duplicatesCleaned = $db->rowCount();
        if ($duplicatesCleaned > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] Cleaned {$duplicatesCleaned} duplicate tokens\n";
            $totalCleaned += $duplicatesCleaned;
        }
    }
    
    // Get final statistics
    $db->query("SELECT 
                    COUNT(*) as total,
                    SUM(active) as active,
                    COUNT(*) - SUM(active) as inactive
                FROM fcm_tokens");
    $afterStats = $db->single();
    
    echo "[" . date('Y-m-d H:i:s') . "] After cleanup: {$afterStats->total} total, {$afterStats->active} active, {$afterStats->inactive} inactive\n";
    echo "[" . date('Y-m-d H:i:s') . "] Total tokens cleaned: {$totalCleaned}\n";
    
    // Log cleanup statistics to database for monitoring
    if ($totalCleaned > 0) {
        $db->query("INSERT INTO system_logs (log_type, message, created_at) 
                    VALUES ('fcm_cleanup', :message, NOW())");
        $db->bind(':message', "Cleaned {$totalCleaned} FCM tokens. Before: {$beforeStats->total} total, After: {$afterStats->total} total");
        $db->execute();
    }
    
    // Check for potential issues
    $warnings = [];
    
    // Warn if too many active tokens per user (might indicate cleanup issues)
    $db->query("SELECT user_id, COUNT(*) as token_count 
                FROM fcm_tokens 
                WHERE active = 1 
                GROUP BY user_id 
                HAVING COUNT(*) > 5 
                ORDER BY token_count DESC 
                LIMIT 5");
    $heavyUsers = $db->resultSet();
    
    if (!empty($heavyUsers)) {
        $warnings[] = "Users with many active tokens: " . implode(', ', array_map(function($u) {
            return "User {$u->user_id} ({$u->token_count} tokens)";
        }, $heavyUsers));
    }
    
    // Warn if inactive tokens are accumulating too quickly
    if ($afterStats->inactive > 100) {
        $warnings[] = "High number of inactive tokens: {$afterStats->inactive}";
    }
    
    if (!empty($warnings)) {
        echo "[" . date('Y-m-d H:i:s') . "] WARNINGS:\n";
        foreach ($warnings as $warning) {
            echo "[" . date('Y-m-d H:i:s') . "] - {$warning}\n";
        }
    }
    
    echo "[" . date('Y-m-d H:i:s') . "] FCM token cleanup completed successfully\n";
    
} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] ERROR: " . $e->getMessage() . "\n";
    echo "[" . date('Y-m-d H:i:s') . "] Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}

exit(0);
?>
