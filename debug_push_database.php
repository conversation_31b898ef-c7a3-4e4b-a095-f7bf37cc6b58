<?php
/**
 * Debug Push Notifications Database
 * 
 * Check what's actually in the push notifications table
 */

require_once 'config/config.php';
require_once 'core/Database.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$userId = $_SESSION['user_id'];
$db = new Database();

echo "<h2>Push Notifications Database Debug</h2>";
echo "<p><strong>User ID:</strong> $userId</p>";

try {
    // Check all push notifications for this user
    echo "<h3>All Push Notifications for User $userId</h3>";
    $db->query('SELECT * FROM user_push_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 20');
    $db->bind(':user_id', $userId);
    $allPush = $db->resultSet();
    
    echo "<p><strong>Total Push Notifications:</strong> " . count($allPush) . "</p>";
    
    if (!empty($allPush)) {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Read</th><th>Sent At</th><th>Created</th></tr>";
        foreach ($allPush as $push) {
            $readStatus = $push->is_read ? 'Yes' : 'No';
            $sentAt = $push->sent_at ?? 'NULL';
            echo "<tr>";
            echo "<td>{$push->id}</td>";
            echo "<td>" . htmlspecialchars($push->title ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars(substr($push->message ?? '', 0, 50)) . "...</td>";
            echo "<td style='color: " . ($push->is_read ? 'green' : 'red') . ";'>{$readStatus}</td>";
            echo "<td>{$sentAt}</td>";
            echo "<td>{$push->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check unread count
    echo "<h3>Unread Push Notifications</h3>";
    $db->query('SELECT COUNT(*) as unread_count FROM user_push_notifications WHERE user_id = :user_id AND is_read = 0');
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult->unread_count ?? 0;
    
    echo "<p><strong>Unread Count:</strong> $unreadCount</p>";
    
    if ($unreadCount > 0) {
        echo "<h4>Unread Push Notifications Details</h4>";
        $db->query('SELECT * FROM user_push_notifications WHERE user_id = :user_id AND is_read = 0 ORDER BY created_at DESC');
        $db->bind(':user_id', $userId);
        $unreadPush = $db->resultSet();
        
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Event Type</th><th>Created</th></tr>";
        foreach ($unreadPush as $push) {
            echo "<tr>";
            echo "<td>{$push->id}</td>";
            echo "<td>" . htmlspecialchars($push->title ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($push->message ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($push->event_type ?? 'NULL') . "</td>";
            echo "<td>{$push->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test the API endpoint directly
    echo "<h3>API Endpoint Test</h3>";
    require_once 'models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    $apiResult = $notificationModel->getUnreadNotifications($userId);
    
    echo "<p><strong>API getUnreadNotifications() Result:</strong></p>";
    echo "<pre>";
    echo "Push notifications: " . count($apiResult['push']) . "\n";
    echo "Toast notifications: " . count($apiResult['toast']) . "\n";
    echo "\nPush notifications data:\n";
    print_r($apiResult['push']);
    echo "\nToast notifications data:\n";
    print_r($apiResult['toast']);
    echo "</pre>";
    
    // Check database table structure
    echo "<h3>Database Table Structure</h3>";
    $db->query('DESCRIBE user_push_notifications');
    $structure = $db->resultSet();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($structure as $field) {
        echo "<tr>";
        echo "<td>{$field->Field}</td>";
        echo "<td>{$field->Type}</td>";
        echo "<td>{$field->Null}</td>";
        echo "<td>{$field->Key}</td>";
        echo "<td>{$field->Default}</td>";
        echo "<td>{$field->Extra}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/test_push_fix.php'>→ Test Push Fix</a></p>";
echo "<p><a href='/admin/dashboard'>← Back to Admin Dashboard</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Push Notifications Database Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
</body>
</html>