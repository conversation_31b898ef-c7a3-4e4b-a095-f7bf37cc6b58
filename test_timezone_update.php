<?php
/**
 * Test script to verify timezone update functionality and validation
 */

// Include the application bootstrap
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UserModel.php';
require_once 'helpers/timezone_helper.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Timezone Update Test & Debug</h1>";

// Test timezone validation first
echo "<h2>1. Testing Timezone Validation</h2>";

$testTimezones = [
    'America/Los_Angeles',
    'America/New_York',
    'America/Chicago',
    'Invalid/Timezone',
    '',
    null
];

foreach ($testTimezones as $tz) {
    $isValid = isValidUSATimezone($tz);
    $tzDisplay = $tz === null ? 'NULL' : ($tz === '' ? 'EMPTY STRING' : $tz);
    $status = $isValid ? '✓ VALID' : '✗ INVALID';
    $color = $isValid ? 'green' : 'red';
    echo "<p style='color: $color;'><strong>$tzDisplay:</strong> $status</p>";
}

// Create database connection
$db = new Database();

// Test user ID (you'll need to replace this with a valid user ID)
$testUserId = 1; // Change this to a valid user ID

echo "<h2>2. Current User Data</h2>";

// Get current user data
$db->query('SELECT id, name, email, timezone FROM users WHERE id = :id');
$db->bind(':id', $testUserId);
$currentUser = $db->single();

if ($currentUser) {
    echo "<p><strong>User ID:</strong> {$currentUser->id}</p>";
    echo "<p><strong>Name:</strong> {$currentUser->name}</p>";
    echo "<p><strong>Email:</strong> {$currentUser->email}</p>";
    echo "<p><strong>Current Timezone:</strong> " . ($currentUser->timezone ?? 'NULL') . "</p>";
} else {
    echo "<p style='color: red;'>User not found with ID: $testUserId</p>";
    exit;
}

echo "<h2>3. Testing Form Data Processing</h2>";

// Simulate form submission data
$_POST = [
    'name' => $currentUser->name,
    'phone' => '',
    'address' => '',
    'city' => '',
    'state' => '',
    'zip' => '',
    'timezone' => 'America/Los_Angeles',
    'password' => '',
    'confirm_password' => ''
];

echo "<p><strong>Simulated POST data:</strong></p>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

// Test the controller's data processing logic
$data = [
    'id' => $testUserId,
    'name' => trim($_POST['name']),
    'phone' => isset($_POST['phone']) ? trim($_POST['phone']) : null,
    'address' => isset($_POST['address']) ? trim($_POST['address']) : null,
    'city' => isset($_POST['city']) ? trim($_POST['city']) : null,
    'state' => isset($_POST['state']) ? trim($_POST['state']) : null,
    'zip' => isset($_POST['zip']) ? trim($_POST['zip']) : null,
    'timezone' => isset($_POST['timezone']) ? trim($_POST['timezone']) : 'America/New_York',
    'password' => trim($_POST['password']),
    'confirm_password' => trim($_POST['confirm_password']),
    'name_err' => '',
    'password_err' => '',
    'confirm_password_err' => '',
    'timezone_err' => ''
];

echo "<p><strong>Processed data array:</strong></p>";
echo "<pre>" . print_r($data, true) . "</pre>";

// Test validation
if (!empty($data['timezone']) && !isValidUSATimezone($data['timezone'])) {
    $data['timezone_err'] = 'Please select a valid timezone';
    echo "<p style='color: red;'><strong>Timezone validation failed!</strong></p>";
} else {
    echo "<p style='color: green;'><strong>Timezone validation passed!</strong></p>";
}

echo "<h2>4. Testing Database Update</h2>";

// Create UserModel instance
$userModel = new UserModel();

echo "<p><strong>Test timezone value:</strong> {$data['timezone']}</p>";

// Perform the update
$updateResult = $userModel->updateProfile($data);

echo "<p><strong>Update result:</strong> " . ($updateResult ? 'SUCCESS' : 'FAILED') . "</p>";

echo "<h2>5. Verify Update</h2>";

// Get updated user data
$db->query('SELECT id, name, email, timezone FROM users WHERE id = :id');
$db->bind(':id', $testUserId);
$updatedUser = $db->single();

if ($updatedUser) {
    echo "<p><strong>Updated Timezone:</strong> " . ($updatedUser->timezone ?? 'NULL') . "</p>";

    if ($updatedUser->timezone === $data['timezone']) {
        echo "<p style='color: green;'><strong>✓ SUCCESS:</strong> Timezone was updated correctly!</p>";
    } else {
        echo "<p style='color: red;'><strong>✗ FAILED:</strong> Timezone was not updated correctly.</p>";
        echo "<p>Expected: {$data['timezone']}</p>";
        echo "<p>Actual: " . ($updatedUser->timezone ?? 'NULL') . "</p>";
    }
} else {
    echo "<p style='color: red;'>Could not retrieve updated user data.</p>";
}

echo "<h2>4. Restore Original Timezone</h2>";

// Restore original timezone
$restoreData = [
    'id' => $testUserId,
    'name' => $currentUser->name,
    'phone' => null,
    'address' => null,
    'city' => null,
    'state' => null,
    'zip' => null,
    'timezone' => $currentUser->timezone,
    'password' => ''
];

$restoreResult = $userModel->updateProfile($restoreData);
echo "<p><strong>Restore result:</strong> " . ($restoreResult ? 'SUCCESS' : 'FAILED') . "</p>";

// Verify restoration
$db->query('SELECT timezone FROM users WHERE id = :id');
$db->bind(':id', $testUserId);
$restoredUser = $db->single();

if ($restoredUser && $restoredUser->timezone === $currentUser->timezone) {
    echo "<p style='color: green;'><strong>✓ SUCCESS:</strong> Original timezone restored!</p>";
} else {
    echo "<p style='color: orange;'><strong>⚠ WARNING:</strong> Could not restore original timezone.</p>";
}

echo "<h2>5. Check Error Logs</h2>";
echo "<p>Check your error logs for debug messages starting with 'UserModel::updateProfile' and 'UserController::profile'</p>";
echo "<p>If DEBUG_MODE is enabled, you should see detailed logging of the update process.</p>";

?>
