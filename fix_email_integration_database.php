<?php
/**
 * Fix Email Integration Database Issues
 * 
 * This script fixes the database schema issues from the initial setup
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "=== Fixing Email Integration Database Issues ===\n\n";

try {
    $db = new Database();
    
    echo "1. Checking and updating messages table...\n";
    
    // Check if messages table exists
    $db->query("SHOW TABLES LIKE 'messages'");
    if (!$db->single()) {
        echo "   - Messages table doesn't exist, creating it...\n";
        
        $createMessagesTable = "
        CREATE TABLE `messages` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `from_user_id` int(10) UNSIGNED NOT NULL,
          `to_user_id` int(10) UNSIGNED NOT NULL,
          `subject` varchar(255) NOT NULL,
          `message` text NOT NULL,
          `show_id` int(11) DEFAULT NULL COMMENT 'Related show if applicable',
          `parent_message_id` int(11) DEFAULT NULL COMMENT 'For reply threading',
          `message_type` enum('direct','system','judging','event','admin','notification','email') NOT NULL DEFAULT 'direct',
          `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
          `is_read` tinyint(1) DEFAULT 0,
          `is_archived` tinyint(1) DEFAULT 0,
          `requires_reply` tinyint(1) DEFAULT 0,
          `allows_reply` tinyint(1) DEFAULT 1,
          `reply_used` tinyint(1) DEFAULT 0,
          `ticket_number` varchar(50) DEFAULT NULL COMMENT 'Unique ticket number for email threading',
          `email_message_id` varchar(255) DEFAULT NULL COMMENT 'Original email Message-ID header',
          `original_sender_email` varchar(255) DEFAULT NULL COMMENT 'Original email sender for non-user emails',
          `folder_id` int(11) DEFAULT NULL COMMENT 'Admin folder assignment',
          `owned_by_admin_id` int(11) DEFAULT NULL COMMENT 'Admin who owns this message',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `read_at` timestamp NULL DEFAULT NULL,
          `replied_at` timestamp NULL DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `idx_to_user` (`to_user_id`),
          KEY `idx_from_user` (`from_user_id`),
          KEY `idx_show` (`show_id`),
          KEY `idx_parent` (`parent_message_id`),
          KEY `idx_unread` (`to_user_id`, `is_read`),
          KEY `idx_created` (`created_at` DESC),
          KEY `idx_type` (`message_type`),
          KEY `idx_ticket` (`ticket_number`),
          KEY `idx_email_id` (`email_message_id`),
          KEY `idx_folder` (`folder_id`),
          KEY `idx_owner` (`owned_by_admin_id`),
          UNIQUE KEY `unique_ticket` (`ticket_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($createMessagesTable);
        $db->execute();
        echo "   ✓ Messages table created\n";
    } else {
        echo "   - Messages table exists, checking for missing columns...\n";
        
        // Check and add missing columns
        $columnsToAdd = [
            'ticket_number' => "ADD COLUMN `ticket_number` varchar(50) DEFAULT NULL COMMENT 'Unique ticket number for email threading'",
            'email_message_id' => "ADD COLUMN `email_message_id` varchar(255) DEFAULT NULL COMMENT 'Original email Message-ID header'",
            'original_sender_email' => "ADD COLUMN `original_sender_email` varchar(255) DEFAULT NULL COMMENT 'Original email sender for non-user emails'",
            'folder_id' => "ADD COLUMN `folder_id` int(11) DEFAULT NULL COMMENT 'Admin folder assignment'",
            'owned_by_admin_id' => "ADD COLUMN `owned_by_admin_id` int(11) DEFAULT NULL COMMENT 'Admin who owns this message'"
        ];
        
        foreach ($columnsToAdd as $column => $alterStatement) {
            try {
                $db->query("SHOW COLUMNS FROM messages LIKE '$column'");
                if (!$db->single()) {
                    $db->query("ALTER TABLE messages $alterStatement");
                    $db->execute();
                    echo "   ✓ Added column: $column\n";
                } else {
                    echo "   - Column already exists: $column\n";
                }
            } catch (Exception $e) {
                echo "   Warning: Could not add column $column: " . $e->getMessage() . "\n";
            }
        }
        
        // Add missing indexes
        $indexesToAdd = [
            'idx_ticket' => "ADD INDEX `idx_ticket` (`ticket_number`)",
            'idx_email_id' => "ADD INDEX `idx_email_id` (`email_message_id`)",
            'idx_folder' => "ADD INDEX `idx_folder` (`folder_id`)",
            'idx_owner' => "ADD INDEX `idx_owner` (`owned_by_admin_id`)"
        ];
        
        foreach ($indexesToAdd as $index => $alterStatement) {
            try {
                $db->query("SHOW INDEX FROM messages WHERE Key_name = '$index'");
                if (!$db->single()) {
                    $db->query("ALTER TABLE messages $alterStatement");
                    $db->execute();
                    echo "   ✓ Added index: $index\n";
                } else {
                    echo "   - Index already exists: $index\n";
                }
            } catch (Exception $e) {
                echo "   Warning: Could not add index $index: " . $e->getMessage() . "\n";
            }
        }
        
        // Update message_type enum to include 'email'
        try {
            $db->query("ALTER TABLE messages MODIFY COLUMN `message_type` enum('direct','system','judging','event','admin','notification','email') NOT NULL DEFAULT 'direct'");
            $db->execute();
            echo "   ✓ Updated message_type enum to include 'email'\n";
        } catch (Exception $e) {
            echo "   Warning: Could not update message_type enum: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n2. Creating email integration tables...\n";
    
    // Create admin_email_folders table
    echo "   - Creating admin_email_folders table...\n";
    try {
        $db->query("DROP TABLE IF EXISTS admin_email_folders");
        $db->execute();
        
        $createFoldersTable = "
        CREATE TABLE `admin_email_folders` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `admin_user_id` int(11) NOT NULL COMMENT 'Admin who owns this folder',
          `name` varchar(100) NOT NULL COMMENT 'Folder name',
          `color` varchar(7) DEFAULT '#007bff' COMMENT 'Folder color (hex)',
          `sort_order` int(11) DEFAULT 0 COMMENT 'Display order',
          `is_system` tinyint(1) DEFAULT 0 COMMENT 'System folder (cannot be deleted)',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_admin_user` (`admin_user_id`),
          KEY `idx_sort_order` (`admin_user_id`, `sort_order`),
          UNIQUE KEY `unique_admin_folder` (`admin_user_id`, `name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($createFoldersTable);
        $db->execute();
        echo "   ✓ admin_email_folders table created\n";
    } catch (Exception $e) {
        echo "   Warning: " . $e->getMessage() . "\n";
    }
    
    // Create message_reminders table
    echo "   - Creating message_reminders table...\n";
    try {
        $db->query("DROP TABLE IF EXISTS message_reminders");
        $db->execute();
        
        $createRemindersTable = "
        CREATE TABLE `message_reminders` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `message_id` int(11) NOT NULL,
          `admin_user_id` int(11) NOT NULL COMMENT 'Admin who set the reminder',
          `reminder_time` datetime NOT NULL COMMENT 'When to send the reminder',
          `note` text DEFAULT NULL COMMENT 'Admin note for the reminder',
          `is_sent` tinyint(1) DEFAULT 0 COMMENT 'Whether reminder has been sent',
          `sent_at` timestamp NULL DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_message` (`message_id`),
          KEY `idx_admin_user` (`admin_user_id`),
          KEY `idx_reminder_time` (`reminder_time`, `is_sent`),
          KEY `idx_pending` (`is_sent`, `reminder_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($createRemindersTable);
        $db->execute();
        echo "   ✓ message_reminders table created\n";
    } catch (Exception $e) {
        echo "   Warning: " . $e->getMessage() . "\n";
    }
    
    // Create email_templates table
    echo "   - Creating email_templates table...\n";
    try {
        $db->query("DROP TABLE IF EXISTS email_templates");
        $db->execute();
        
        $createTemplatesTable = "
        CREATE TABLE `email_templates` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL COMMENT 'Template name',
          `subject` varchar(255) NOT NULL COMMENT 'Email subject template',
          `body` text NOT NULL COMMENT 'Email body template',
          `is_active` tinyint(1) DEFAULT 1,
          `created_by` int(11) NOT NULL COMMENT 'Admin who created the template',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_active` (`is_active`),
          KEY `idx_created_by` (`created_by`),
          UNIQUE KEY `unique_template_name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($createTemplatesTable);
        $db->execute();
        echo "   ✓ email_templates table created\n";
    } catch (Exception $e) {
        echo "   Warning: " . $e->getMessage() . "\n";
    }
    
    // Create other tables
    $otherTables = [
        'email_processing_log' => "
        CREATE TABLE `email_processing_log` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `email_message_id` varchar(255) NOT NULL COMMENT 'Email Message-ID header',
          `sender_email` varchar(255) NOT NULL COMMENT 'Email sender address',
          `subject` varchar(500) NOT NULL COMMENT 'Email subject',
          `ticket_number` varchar(50) DEFAULT NULL COMMENT 'Generated or extracted ticket number',
          `message_id` int(11) DEFAULT NULL COMMENT 'Created message ID',
          `processing_status` enum('pending','processed','failed','ignored') DEFAULT 'pending',
          `error_message` text DEFAULT NULL COMMENT 'Error details if processing failed',
          `email_size` int(11) DEFAULT NULL COMMENT 'Email size in bytes',
          `attachment_count` int(11) DEFAULT 0 COMMENT 'Number of attachments',
          `spam_score` decimal(3,2) DEFAULT NULL COMMENT 'Spam score (0.00-1.00)',
          `is_auto_reply` tinyint(1) DEFAULT 0 COMMENT 'Detected as auto-reply',
          `processed_at` timestamp NULL DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_email_message_id` (`email_message_id`),
          KEY `idx_sender` (`sender_email`),
          KEY `idx_ticket` (`ticket_number`),
          KEY `idx_status` (`processing_status`),
          KEY `idx_created` (`created_at` DESC),
          UNIQUE KEY `unique_email_message` (`email_message_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'ticket_numbers' => "
        CREATE TABLE `ticket_numbers` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `ticket_number` varchar(50) NOT NULL COMMENT 'Full ticket number (e.g., RER-2025-001)',
          `prefix` varchar(10) NOT NULL COMMENT 'Ticket prefix (e.g., RER)',
          `year` int(4) NOT NULL COMMENT 'Year component',
          `sequence` int(11) NOT NULL COMMENT 'Sequence number',
          `message_id` int(11) DEFAULT NULL COMMENT 'Associated message ID',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_ticket_number` (`ticket_number`),
          KEY `idx_year_sequence` (`year`, `sequence`),
          KEY `idx_message` (`message_id`),
          UNIQUE KEY `unique_ticket` (`ticket_number`),
          UNIQUE KEY `unique_year_sequence` (`prefix`, `year`, `sequence`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'message_attachments' => "
        CREATE TABLE `message_attachments` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `message_id` int(11) NOT NULL,
          `filename` varchar(255) NOT NULL COMMENT 'Original filename',
          `stored_filename` varchar(255) NOT NULL COMMENT 'Filename on disk',
          `file_path` varchar(500) NOT NULL COMMENT 'Full path to file',
          `file_size` int(11) NOT NULL COMMENT 'File size in bytes',
          `mime_type` varchar(100) NOT NULL COMMENT 'MIME type',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_message` (`message_id`),
          KEY `idx_filename` (`filename`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    foreach ($otherTables as $tableName => $createStatement) {
        echo "   - Creating $tableName table...\n";
        try {
            $db->query("DROP TABLE IF EXISTS $tableName");
            $db->execute();
            
            $db->query($createStatement);
            $db->execute();
            echo "   ✓ $tableName table created\n";
        } catch (Exception $e) {
            echo "   Warning: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n3. Inserting default data...\n";
    
    // Insert default email templates
    echo "   - Creating default email templates...\n";
    $defaultTemplates = [
        [
            'name' => 'Auto Reply Confirmation',
            'subject' => 'Re: {{subject}} [{{ticket_number}}]',
            'body' => "Hello,\n\nThank you for contacting us. We have received your message and assigned it ticket number {{ticket_number}}.\n\nYour message:\nSubject: {{subject}}\nReceived: {{date}}\n\nWe will review your message and respond as soon as possible. Please reference the ticket number {{ticket_number}} in any future correspondence regarding this matter.\n\nBest regards,\n{{site_name}} Support Team\n\n---\nThis is an automated response. Please do not reply to this email."
        ],
        [
            'name' => 'Welcome Message',
            'subject' => 'Welcome to {{site_name}}',
            'body' => "Hello {{name}},\n\nWelcome to {{site_name}}! We're excited to have you join our community.\n\nIf you have any questions or need assistance, please don't hesitate to contact us.\n\nBest regards,\n{{site_name}} Team"
        ],
        [
            'name' => 'General Response',
            'subject' => 'Re: {{subject}} [{{ticket_number}}]',
            'body' => "Hello,\n\nThank you for your message regarding {{subject}}.\n\n{{response_content}}\n\nIf you have any additional questions, please feel free to contact us and reference ticket number {{ticket_number}}.\n\nBest regards,\n{{admin_name}}\n{{site_name}} Team"
        ]
    ];
    
    foreach ($defaultTemplates as $template) {
        try {
            $db->query("INSERT IGNORE INTO email_templates (name, subject, body, created_by) VALUES (:name, :subject, :body, 1)");
            $db->bind(':name', $template['name']);
            $db->bind(':subject', $template['subject']);
            $db->bind(':body', $template['body']);
            $db->execute();
            echo "   ✓ Created template: {$template['name']}\n";
        } catch (Exception $e) {
            echo "   Warning: Could not create template {$template['name']}: " . $e->getMessage() . "\n";
        }
    }
    
    // Create default folders for admins
    echo "   - Creating default folders for admins...\n";
    $db->query("SELECT id, name FROM users WHERE role = 'admin' AND status = 'active'");
    $admins = $db->resultSet();
    
    $defaultFolders = [
        ['name' => 'Inbox', 'color' => '#007bff', 'sort_order' => 1, 'is_system' => 1],
        ['name' => 'Sent', 'color' => '#28a745', 'sort_order' => 2, 'is_system' => 1],
        ['name' => 'Archive', 'color' => '#6c757d', 'sort_order' => 3, 'is_system' => 1],
        ['name' => 'Trash', 'color' => '#dc3545', 'sort_order' => 4, 'is_system' => 1]
    ];
    
    foreach ($admins as $admin) {
        foreach ($defaultFolders as $folder) {
            try {
                $db->query("INSERT IGNORE INTO admin_email_folders (admin_user_id, name, color, sort_order, is_system) VALUES (:admin_id, :name, :color, :sort_order, :is_system)");
                $db->bind(':admin_id', $admin->id);
                $db->bind(':name', $folder['name']);
                $db->bind(':color', $folder['color']);
                $db->bind(':sort_order', $folder['sort_order']);
                $db->bind(':is_system', $folder['is_system']);
                $db->execute();
            } catch (Exception $e) {
                // Ignore duplicate errors
            }
        }
        echo "   ✓ Created folders for admin: {$admin->name}\n";
    }
    
    echo "\n=== Database Fix Complete! ===\n\n";
    echo "✓ All database issues have been resolved\n";
    echo "✓ Email integration tables created successfully\n";
    echo "✓ Default templates and folders added\n\n";
    
    echo "IMPORTANT: PHP IMAP Extension Missing\n";
    echo "The system detected that the PHP IMAP extension is not loaded.\n";
    echo "This is REQUIRED for email processing to work.\n\n";
    echo "To install it, contact your hosting provider or run:\n";
    echo "- On Ubuntu/Debian: sudo apt-get install php-imap\n";
    echo "- On CentOS/RHEL: sudo yum install php-imap\n";
    echo "- On cPanel: Enable in PHP Extensions\n\n";
    echo "After installing, restart your web server.\n\n";
    echo "You can now proceed to configure the email server settings!\n";
    
} catch (Exception $e) {
    echo "✗ Database fix failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
