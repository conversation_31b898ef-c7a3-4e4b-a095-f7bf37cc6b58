<?php
/**
 * Unified Messaging System - PHP Code Examples
 * 
 * This file contains comprehensive examples of how to use the unified messaging system
 * for sending messages, handling replies, and managing conversations.
 */

require_once APPROOT . '/models/UnifiedMessageModel.php';

// Initialize the messaging model
$messageModel = new UnifiedMessageModel();

echo "<h1>🔔 Unified Messaging System - PHP Examples</h1>";

// =============================================================================
// BASIC MESSAGE SENDING
// =============================================================================

echo "<h2>📤 Basic Message Sending</h2>";

/**
 * Example 1: Simple Direct Message
 */
echo "<h3>Example 1: Simple Direct Message</h3>";
echo "<pre>";
echo htmlspecialchars('
// Send a simple message between users
$messageId = $messageModel->sendMessage(
    $_SESSION[\'user_id\'],  // From current user
    $recipientUserId,       // To recipient
    "Hello!",               // Subject
    "This is a test message.", // Message content
    null,                   // No show relation
    \'direct\',              // Message type
    false,                  // No reply required
    null                    // Not a reply
);

if ($messageId) {
    echo "Message sent successfully! ID: $messageId";
} else {
    echo "Failed to send message";
}
');
echo "</pre>";

/**
 * Example 2: System Notification
 */
echo "<h3>Example 2: System Notification</h3>";
echo "<pre>";
echo htmlspecialchars('
// Send a system notification (from system user ID 1)
$messageId = $messageModel->sendMessage(
    1,                      // From system user
    $userId,                // To user
    "Registration Approved", // Subject
    "Your vehicle registration has been approved for the show.", // Message
    $showId,                // Related show
    \'system\',              // System message type
    false,                  // No reply needed
    null                    // Not a reply
);
');
echo "</pre>";

// =============================================================================
// ADVANCED MESSAGE TYPES
// =============================================================================

echo "<h2>🎯 Advanced Message Types</h2>";

/**
 * Example 3: Judge Communication
 */
echo "<h3>Example 3: Judge Communication</h3>";
echo "<pre>";
echo htmlspecialchars('
// Send a message to a judge with reply requirement
$messageId = $messageModel->sendMessage(
    $_SESSION[\'user_id\'],  // From coordinator/admin
    $judgeUserId,           // To judge
    "Judging Assignment",   // Subject
    "Please review category \'Best in Show\'. [reply] required by tomorrow.", // Message with reply marker
    $showId,                // Related show
    \'judging\',             // Judge message type
    true,                   // Requires reply
    null                    // Not a reply
);
');
echo "</pre>";

/**
 * Example 4: Event Notification
 */
echo "<h3>Example 4: Event Notification</h3>";
echo "<pre>";
echo htmlspecialchars('
// Send an event notification
$messageId = $messageModel->sendMessage(
    1,                      // From system
    $userId,                // To user
    "Event Reminder",       // Subject
    "Your event \'Car Show 2025\' starts in 1 hour!", // Message
    $eventId,               // Related event
    \'event\',               // Event message type
    false,                  // No reply needed
    null                    // Not a reply
);
');
echo "</pre>";

// =============================================================================
// CONVERSATION THREADING AND REPLIES
// =============================================================================

echo "<h2>💬 Conversation Threading and Replies</h2>";

/**
 * Example 5: Reply to a Message
 */
echo "<h3>Example 5: Reply to a Message</h3>";
echo "<pre>";
echo htmlspecialchars('
// Reply to an existing message
$originalMessageId = 123; // ID of message being replied to
$originalMessage = $messageModel->getMessageById($originalMessageId);

if ($originalMessage) {
    $replyId = $messageModel->sendMessage(
        $_SESSION[\'user_id\'],     // From current user
        $originalMessage->from_user_id, // To original sender
        "Re: " . $originalMessage->subject, // Reply subject
        "Thank you for your message. I will handle this request.", // Reply content
        $originalMessage->show_id,  // Same show relation
        \'direct\',                  // Direct reply
        false,                      // No reply needed to reply
        $originalMessageId          // Parent message for threading
    );
    
    if ($replyId) {
        echo "Reply sent successfully! ID: $replyId";
    }
}
');
echo "</pre>";

/**
 * Example 6: Conversation Thread
 */
echo "<h3>Example 6: Get Conversation Thread</h3>";
echo "<pre>";
echo htmlspecialchars('
// Get all messages in a conversation thread
$messageId = 123;
$conversation = $messageModel->getConversationThread($messageId);

foreach ($conversation as $message) {
    echo "From: " . $message->from_user_name . "\n";
    echo "Subject: " . $message->subject . "\n";
    echo "Message: " . $message->message . "\n";
    echo "Date: " . $message->created_at . "\n";
    echo "---\n";
}
');
echo "</pre>";

// =============================================================================
// BULK MESSAGING AND NOTIFICATIONS
// =============================================================================

echo "<h2>📢 Bulk Messaging and Notifications</h2>";

/**
 * Example 7: Send to Multiple Users
 */
echo "<h3>Example 7: Send to Multiple Users</h3>";
echo "<pre>";
echo htmlspecialchars('
// Send the same message to multiple users
$userIds = [10, 15, 22, 35]; // Array of user IDs
$subject = "Show Update";
$message = "Important update about the upcoming car show.";

foreach ($userIds as $userId) {
    $messageId = $messageModel->sendMessage(
        $_SESSION[\'user_id\'],  // From current user
        $userId,                // To each user
        $subject,               // Same subject
        $message,               // Same message
        $showId,                // Related show
        \'admin\',               // Admin announcement
        false,                  // No reply needed
        null                    // Not a reply
    );
    
    if ($messageId) {
        echo "Message sent to user $userId: ID $messageId\n";
    }
}
');
echo "</pre>";

/**
 * Example 8: Send to All Judges
 */
echo "<h3>Example 8: Send to All Judges</h3>";
echo "<pre>";
echo htmlspecialchars('
// Send message to all judges for a show
$showId = 45;
$judges = $messageModel->getJudgesForShow($showId); // Custom method to get judges

$subject = "Judging Instructions";
$message = "Please review the judging criteria document attached. [reply] to confirm receipt.";

foreach ($judges as $judge) {
    $messageId = $messageModel->sendMessage(
        $_SESSION[\'user_id\'],  // From coordinator
        $judge->user_id,        // To each judge
        $subject,               // Same subject
        $message,               // Same message
        $showId,                // Related show
        \'judging\',             // Judge message type
        true,                   // Requires reply
        null                    // Not a reply
    );
}
');
echo "</pre>";

// =============================================================================
// MESSAGE MANAGEMENT
// =============================================================================

echo "<h2>📋 Message Management</h2>";

/**
 * Example 9: Mark Messages as Read
 */
echo "<h3>Example 9: Mark Messages as Read</h3>";
echo "<pre>";
echo htmlspecialchars('
// Mark specific messages as read
$messageIds = [123, 124, 125];
$result = $messageModel->markMessagesAsRead($messageIds, $_SESSION[\'user_id\']);

if ($result) {
    echo "Messages marked as read successfully";
}

// Mark all messages as read for a user
$result = $messageModel->markAllMessagesAsRead($_SESSION[\'user_id\']);
');
echo "</pre>";

/**
 * Example 10: Archive Messages
 */
echo "<h3>Example 10: Archive Messages</h3>";
echo "<pre>";
echo htmlspecialchars('
// Archive specific messages
$messageIds = [123, 124, 125];
$result = $messageModel->archiveMessages($messageIds, $_SESSION[\'user_id\']);

if ($result) {
    echo "Messages archived successfully";
}

// Get archived messages
$archivedMessages = $messageModel->getArchivedMessages($_SESSION[\'user_id\']);
');
echo "</pre>";

// =============================================================================
// DELIVERY TRACKING
// =============================================================================

echo "<h2>📊 Delivery Tracking</h2>";

/**
 * Example 11: Check Delivery Status
 */
echo "<h3>Example 11: Check Delivery Status</h3>";
echo "<pre>";
echo htmlspecialchars('
// Get delivery status for a message
$messageId = 123;
$deliveries = $messageModel->getMessageDeliveries($messageId);

foreach ($deliveries as $delivery) {
    echo "Method: " . $delivery->delivery_method . "\n";
    echo "Status: " . $delivery->status . "\n";
    echo "Sent: " . $delivery->sent_at . "\n";
    echo "Attempts: " . $delivery->attempts . "\n";
    if ($delivery->error_message) {
        echo "Error: " . $delivery->error_message . "\n";
    }
    echo "---\n";
}
');
echo "</pre>";

// =============================================================================
// USER PREFERENCES AND SETTINGS
// =============================================================================

echo "<h2>⚙️ User Preferences and Settings</h2>";

/**
 * Example 12: Check User Preferences
 */
echo "<h3>Example 12: Check User Preferences</h3>";
echo "<pre>";
echo htmlspecialchars('
// Get user notification preferences
$userId = $_SESSION[\'user_id\'];
$preferences = $messageModel->getUserNotificationPreferences($userId);

echo "Email enabled: " . ($preferences->email_enabled ? "Yes" : "No") . "\n";
echo "SMS enabled: " . ($preferences->sms_enabled ? "Yes" : "No") . "\n";
echo "Push enabled: " . ($preferences->push_enabled ? "Yes" : "No") . "\n";
echo "Toast enabled: " . ($preferences->toast_enabled ? "Yes" : "No") . "\n";
');
echo "</pre>";

// =============================================================================
// ERROR HANDLING AND VALIDATION
// =============================================================================

echo "<h2>⚠️ Error Handling and Validation</h2>";

/**
 * Example 13: Comprehensive Error Handling
 */
echo "<h3>Example 13: Comprehensive Error Handling</h3>";
echo "<pre>";
echo htmlspecialchars('
try {
    // Validate inputs
    if (empty($toUserId) || empty($subject) || empty($message)) {
        throw new Exception("Required fields missing");
    }
    
    // Check if recipient exists
    if (!$messageModel->userExists($toUserId)) {
        throw new Exception("Recipient user not found");
    }
    
    // Send message
    $messageId = $messageModel->sendMessage(
        $_SESSION[\'user_id\'],
        $toUserId,
        $subject,
        $message,
        $showId,
        \'direct\',
        false,
        null
    );
    
    if (!$messageId) {
        throw new Exception("Failed to send message");
    }
    
    echo "Message sent successfully! ID: $messageId";
    
} catch (Exception $e) {
    error_log("Message sending error: " . $e->getMessage());
    echo "Error: " . $e->getMessage();
}
');
echo "</pre>";

echo "<hr>";
echo "<p><em>Unified Messaging System Examples - Complete Reference Guide</em></p>";
?>
