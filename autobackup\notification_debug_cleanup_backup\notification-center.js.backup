/**
 * Notification Center JavaScript
 * Handles notification center functionality including real-time updates
 */
class NotificationCenter {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.updateInterval = 30000; // 30 seconds
        this.intervalId = null;
        
        this.init();
    }
    
    /**
     * Initialize the notification center
     */
    init() {
        // Start periodic updates
        this.startPeriodicUpdates();
        
        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPeriodicUpdates();
            } else {
                this.startPeriodicUpdates();
            }
        });
        
        // Update count immediately
        this.updateNotificationCount();
    }
    
    /**
     * Start periodic notification count updates
     */
    startPeriodicUpdates() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        
        this.intervalId = setInterval(() => {
            this.updateNotificationCount();
        }, this.updateInterval);
    }
    
    /**
     * Stop periodic updates
     */
    stopPeriodicUpdates() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    /**
     * Update notification count in header
     */
    async updateNotificationCount() {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/getUnreadCount`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.updateBadge(data.total_unread);
                
                // Update tab counts if we're on the notification center page
                if (data.conversationCounts && window.location.pathname.includes('notification_center')) {
                    const allTabCount = document.getElementById('all-tab-count');
                    const unreadTabCount = document.getElementById('unread-tab-count');
                    const archivedTabCount = document.getElementById('archived-tab-count');
                    
                    if (allTabCount) allTabCount.textContent = data.conversationCounts.all;
                    if (unreadTabCount) unreadTabCount.textContent = data.conversationCounts.unread;
                    if (archivedTabCount) archivedTabCount.textContent = data.conversationCounts.archived;
                }
                
                // Trigger custom event for other components
                window.dispatchEvent(new CustomEvent('notificationCountUpdated', {
                    detail: {
                        totalUnread: data.total_unread,
                        counts: data.counts,
                        conversationCounts: data.conversationCounts
                    }
                }));
            }
            
        } catch (error) {
            if (window.DEBUG_MODE) {
                console.error('[NotificationCenter] Failed to update count:', error);
            }
        }
    }
    
    /**
     * Update the notification badge
     */
    updateBadge(count) {
        const badges = document.querySelectorAll('.notification-badge, .notification-count');
        
        badges.forEach(badge => {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'inline-block';
                badge.classList.add('badge-pulse');
                
                // Remove pulse animation after a short delay
                setTimeout(() => {
                    badge.classList.remove('badge-pulse');
                }, 1000);
            } else {
                badge.style.display = 'none';
            }
        });
        
        // Update page title if there are unread notifications
        this.updatePageTitle(count);
    }
    
    /**
     * Update page title with unread count
     */
    updatePageTitle(count) {
        const baseTitle = document.title.replace(/^\(\d+\)\s*/, '');
        
        if (count > 0) {
            document.title = `(${count}) ${baseTitle}`;
        } else {
            document.title = baseTitle;
        }
    }
    
    /**
     * Mark a message as read
     */
    async markAsRead(messageId) {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/markAsRead`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `message_id=${messageId}`
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Update the UI to reflect read status
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                if (messageElement) {
                    messageElement.classList.remove('unread');
                    messageElement.classList.add('read');
                }
                
                // Update notification count
                this.updateNotificationCount();
            }
            
            return data.success;
            
        } catch (error) {
            console.error('[NotificationCenter] Failed to mark as read:', error);
            return false;
        }
    }
    
    /**
     * Archive a conversation
     */
    async archiveConversation(messageId) {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/archive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `message_id=${messageId}`
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Remove the conversation from the current view
                const conversationElement = document.querySelector(`[data-conversation-id="${messageId}"]`);
                if (conversationElement) {
                    conversationElement.remove();
                }
                
                // Update notification count
                this.updateNotificationCount();
                
                // Show success message
                this.showToast('Conversation archived successfully', 'success');
            } else {
                this.showToast(data.message || 'Failed to archive conversation', 'error');
            }
            
            return data.success;
            
        } catch (error) {
            console.error('[NotificationCenter] Failed to archive:', error);
            this.showToast('Failed to archive conversation', 'error');
            return false;
        }
    }
    
    /**
     * Unarchive a conversation
     */
    async unarchiveConversation(messageId) {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/unarchive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `message_id=${messageId}`
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Remove the conversation from the archived view
                const conversationElement = document.querySelector(`[data-conversation-id="${messageId}"]`);
                if (conversationElement) {
                    conversationElement.remove();
                }
                
                // Update notification count
                this.updateNotificationCount();
                
                // Show success message
                this.showToast('Conversation unarchived successfully', 'success');
            } else {
                this.showToast(data.message || 'Failed to unarchive conversation', 'error');
            }
            
            return data.success;
            
        } catch (error) {
            console.error('[NotificationCenter] Failed to unarchive:', error);
            this.showToast('Failed to unarchive conversation', 'error');
            return false;
        }
    }
    
    /**
     * Delete a conversation
     */
    async deleteConversation(messageId) {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `message_id=${messageId}`
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Remove the conversation from the current view
                const conversationElement = document.querySelector(`[data-conversation-id="${messageId}"]`);
                if (conversationElement) {
                    conversationElement.remove();
                }
                
                // Update notification count
                this.updateNotificationCount();
                
                // Show success message
                this.showToast('Conversation deleted successfully', 'success');
            } else {
                this.showToast(data.message || 'Failed to delete conversation', 'error');
            }
            
            return data.success;
            
        } catch (error) {
            console.error('[NotificationCenter] Failed to delete:', error);
            this.showToast('Failed to delete conversation', 'error');
            return false;
        }
    }
    
    /**
     * Show a toast notification
     */
    showToast(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // Add to toast container
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // Initialize and show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
    
    /**
     * Destroy the notification center
     */
    destroy() {
        this.stopPeriodicUpdates();
    }
}

// Initialize notification center when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.notificationCenter = new NotificationCenter();
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', () => {
    if (window.notificationCenter) {
        window.notificationCenter.destroy();
    }
});