# Current System Backup - Before Unified Message System

## Files Being Modified
- models/NotificationCenterModel.php
- controllers/JudgeManagementController.php  
- views/notification_center/index.php
- models/NotificationService.php

## Database Tables Involved
- notification_center_items (unified display)
- user_messages (direct messages)
- user_push_notifications (push notifications)
- user_toast_notifications (toast notifications)
- notification_queue (scheduled delivery)
- notification_settings (user preferences)

## Current Issues to Fix
1. Push notifications broken
2. Data duplication across tables
3. Complex routing for different notification types
4. User confusion with artificial type distinctions

## Target Solution
- Single messages table for all communications
- Delivery tracking table for methods used
- Unified user interface showing all as "messages"
- Preserved delivery methods (email, SMS, push, toast)
- Working push notifications