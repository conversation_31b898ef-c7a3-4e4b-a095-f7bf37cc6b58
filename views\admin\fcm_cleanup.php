<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">
                <i class="fas fa-broom me-2"></i>FCM Token Cleanup
            </h1>
            <p class="text-muted">Manage and clean up Firebase Cloud Messaging tokens</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings_notifications" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Notifications
            </a>
        </div>
    </div>

    <?php flash('fcm_cleanup'); ?>

    <!-- Current Statistics -->
    <?php if (isset($fcm_stats) && $fcm_stats): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <h5 class="card-title">Current FCM Token Statistics</h5>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="stat-box">
                                <h3 class="text-primary"><?php echo $fcm_stats->total ?? 0; ?></h3>
                                <p class="text-muted mb-0">Total Tokens</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-box">
                                <h3 class="text-success"><?php echo $fcm_stats->active ?? 0; ?></h3>
                                <p class="text-muted mb-0">Active Tokens</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-box">
                                <h3 class="text-warning"><?php echo $fcm_stats->inactive ?? 0; ?></h3>
                                <p class="text-muted mb-0">Inactive Tokens</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Cleanup Actions -->
    <div class="row g-4 mb-4">
        <!-- Cleanup Inactive Tokens -->
        <div class="col-md-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body text-center">
                    <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-clock text-warning fa-2x"></i>
                    </div>
                    <h5 class="card-title">Cleanup Inactive Tokens</h5>
                    <p class="card-text text-muted">Remove tokens that have been inactive for a specified number of days</p>
                    
                    <form method="post" action="<?php echo BASE_URL; ?>/admin/fcm_cleanup">
                        <input type="hidden" name="action" value="cleanup_inactive">
                        <div class="mb-3">
                            <label class="form-label">Days old:</label>
                            <input type="number" name="days" value="30" min="1" max="365" class="form-control">
                            <div class="form-text">Tokens inactive for this many days will be removed</div>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-clock me-2"></i>Clean Inactive Tokens
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Cleanup Orphaned Tokens -->
        <div class="col-md-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body text-center">
                    <div class="icon-box bg-danger bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-user-slash text-danger fa-2x"></i>
                    </div>
                    <h5 class="card-title">Cleanup Orphaned Tokens</h5>
                    <p class="card-text text-muted">Remove tokens belonging to users that no longer exist in the system</p>
                    
                    <form method="post" action="<?php echo BASE_URL; ?>/admin/fcm_cleanup">
                        <input type="hidden" name="action" value="cleanup_orphaned">
                        <div class="mb-3">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                This will permanently remove tokens for deleted users
                            </div>
                        </div>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-user-slash me-2"></i>Clean Orphaned Tokens
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Cleanup Duplicates -->
        <div class="col-md-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body text-center">
                    <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-copy text-info fa-2x"></i>
                    </div>
                    <h5 class="card-title">Cleanup Duplicate Tokens</h5>
                    <p class="card-text text-muted">Remove duplicate tokens where the same user has identical token values</p>
                    
                    <form method="post" action="<?php echo BASE_URL; ?>/admin/fcm_cleanup">
                        <input type="hidden" name="action" value="cleanup_duplicates">
                        <div class="mb-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Keeps the newest token, removes older duplicates
                            </div>
                        </div>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-copy me-2"></i>Clean Duplicate Tokens
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Automatic Cleanup Information -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-robot text-primary me-2"></i>
                        Automatic Cleanup System
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Built-in Cleanup</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>10% chance during token registration</li>
                                <li><i class="fas fa-check text-success me-2"></i>Removes tokens inactive for 30+ days</li>
                                <li><i class="fas fa-check text-success me-2"></i>Safe operation (only inactive tokens)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Recommended Cron Job</h6>
                            <div class="bg-light p-3 rounded">
                                <code>0 2 * * * /usr/bin/php /path/to/site/cron/cleanup_fcm_tokens.php</code>
                            </div>
                            <small class="text-muted">Runs daily at 2 AM for comprehensive cleanup</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-box {
    padding: 1rem;
}
.stat-box h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}
.icon-box {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
