-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1deb1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 17, 2025 at 04:22 PM
-- Server version: 11.4.5-MariaDB-deb12
-- PHP Version: 8.2.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `sql24006_events`
--

-- --------------------------------------------------------

--
-- Table structure for table `show_role_assignments`
--

CREATE TABLE `show_role_assignments` (
  `id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `assigned_role` enum('coordinator','judge','staff') NOT NULL,
  `assigned_by` int(11) NOT NULL COMMENT 'User ID of coordinator/admin who made the assignment',
  `request_id` int(11) DEFAULT NULL COMMENT 'Reference to the original request',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `assigned_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL COMMENT 'Show end date + 1 week for automatic cleanup',
  `auto_cleanup_date` datetime NOT NULL COMMENT 'Date when this assignment will be automatically removed',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci COMMENT='Stores active show-specific role assignments';

--
-- Dumping data for table `show_role_assignments`
--

INSERT INTO `show_role_assignments` (`id`, `show_id`, `user_id`, `assigned_role`, `assigned_by`, `request_id`, `is_active`, `assigned_at`, `expires_at`, `auto_cleanup_date`, `created_at`, `updated_at`) VALUES
(1, 5, 3, 'staff', 3, NULL, 0, '2025-07-10 21:45:34', '2025-07-08 23:59:59', '2025-07-14 23:59:59', '2025-07-10 21:45:34', '2025-07-10 21:46:08'),
(2, 113, 3, 'judge', 2406, 1, 1, '2025-07-10 21:52:36', '2025-11-27 23:59:59', '2025-12-03 23:59:59', '2025-07-10 21:52:36', '2025-07-10 21:52:36'),
(7, 9, 3, 'judge', 3, NULL, 1, '2025-07-11 11:37:15', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '2025-07-11 11:37:15', '2025-07-11 11:37:15');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `show_role_assignments`
--
ALTER TABLE `show_role_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_active_assignment` (`show_id`,`user_id`,`assigned_role`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `assigned_by` (`assigned_by`),
  ADD KEY `request_id` (`request_id`),
  ADD KEY `is_active` (`is_active`),
  ADD KEY `auto_cleanup_date` (`auto_cleanup_date`),
  ADD KEY `expires_at` (`expires_at`),
  ADD KEY `idx_show_role_assignments_active` (`is_active`,`show_id`),
  ADD KEY `idx_show_role_assignments_cleanup` (`auto_cleanup_date`,`is_active`),
  ADD KEY `id` (`id`,`show_id`,`user_id`,`assigned_role`,`assigned_by`,`request_id`,`is_active`,`assigned_at`,`expires_at`,`auto_cleanup_date`,`created_at`,`updated_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `show_role_assignments`
--
ALTER TABLE `show_role_assignments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
