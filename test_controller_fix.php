<?php
/**
 * Test Controller Fix
 * 
 * This script tests if the NotificationCenterController method conflict is resolved
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'core/Controller.php';
require_once 'core/Auth.php';

echo "<h1>Controller Fix Test</h1>";

try {
    echo "<h2>1. Testing Base Controller Class</h2>";
    
    // Test if Controller class exists
    if (class_exists('Controller')) {
        echo "<p>✅ Controller base class exists</p>";
        
        // Check Controller methods
        $controllerMethods = get_class_methods('Controller');
        echo "<p>Controller methods: " . implode(', ', $controllerMethods) . "</p>";
        
        if (in_array('view', $controllerMethods)) {
            echo "<p>✅ Controller::view() method exists</p>";
        } else {
            echo "<p>❌ Controller::view() method not found</p>";
        }
    } else {
        echo "<p>❌ Controller base class not found</p>";
    }
    
    echo "<h2>2. Testing NotificationCenterController</h2>";
    
    // Test if we can load the NotificationCenterController
    $controllerFile = 'controllers/NotificationCenterController.php';
    if (file_exists($controllerFile)) {
        echo "<p>✅ NotificationCenterController file exists</p>";
        
        try {
            require_once $controllerFile;
            echo "<p>✅ NotificationCenterController loaded successfully</p>";
            
            if (class_exists('NotificationCenterController')) {
                echo "<p>✅ NotificationCenterController class exists</p>";
                
                // Check methods
                $methods = get_class_methods('NotificationCenterController');
                echo "<p>NotificationCenterController methods:</p>";
                echo "<ul>";
                foreach ($methods as $method) {
                    echo "<li>{$method}</li>";
                }
                echo "</ul>";
                
                // Check if viewNotification method exists
                if (in_array('viewNotification', $methods)) {
                    echo "<p>✅ viewNotification() method exists</p>";
                } else {
                    echo "<p>❌ viewNotification() method not found</p>";
                }
                
                // Check if there's still a view method conflict
                if (in_array('view', $methods)) {
                    echo "<p>⚠️ view() method still exists - this might cause conflicts</p>";
                } else {
                    echo "<p>✅ No view() method conflict</p>";
                }
                
            } else {
                echo "<p>❌ NotificationCenterController class not found after loading</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Exception loading NotificationCenterController: " . $e->getMessage() . "</p>";
        } catch (Error $e) {
            echo "<p>❌ Fatal error loading NotificationCenterController: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ NotificationCenterController file not found</p>";
    }
    
    echo "<h2>3. Testing Method Compatibility</h2>";
    
    if (class_exists('Controller') && class_exists('NotificationCenterController')) {
        try {
            // Check if NotificationCenterController extends Controller
            $reflection = new ReflectionClass('NotificationCenterController');
            $parentClass = $reflection->getParentClass();
            
            if ($parentClass && $parentClass->getName() === 'Controller') {
                echo "<p>✅ NotificationCenterController properly extends Controller</p>";
            } else {
                echo "<p>❌ NotificationCenterController does not extend Controller properly</p>";
            }
            
            // Check method signatures
            $controllerReflection = new ReflectionClass('Controller');
            $notificationReflection = new ReflectionClass('NotificationCenterController');
            
            if ($controllerReflection->hasMethod('view')) {
                $controllerViewMethod = $controllerReflection->getMethod('view');
                $controllerParams = $controllerViewMethod->getParameters();
                
                echo "<p>Controller::view() parameters:</p>";
                echo "<ul>";
                foreach ($controllerParams as $param) {
                    $paramInfo = $param->getName();
                    if ($param->isOptional()) {
                        $paramInfo .= " (optional)";
                    }
                    echo "<li>{$paramInfo}</li>";
                }
                echo "</ul>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Error checking method compatibility: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>4. Testing URL Routing</h2>";
    
    $baseUrl = BASE_URL;
    echo "<p>Base URL: <strong>{$baseUrl}</strong></p>";
    
    $testUrls = [
        '/notification_center' => 'Main notification center',
        '/notification_center/viewNotification/1' => 'View specific notification'
    ];
    
    foreach ($testUrls as $url => $description) {
        $fullUrl = $baseUrl . $url;
        echo "<p>🔗 <a href='{$fullUrl}' target='_blank'>{$description}</a> - {$fullUrl}</p>";
    }
    
    echo "<h2>✅ Controller Fix Test Complete!</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>Ready to Test!</h3>";
        echo "<p>The controller method conflict has been resolved. You can now test:</p>";
        echo "<ul>";
        echo "<li><a href='{$baseUrl}/notification_center' target='_blank'>Notification Center</a></li>";
        echo "<li>Send a judge message to test notifications</li>";
        echo "<li>Click push notifications to verify they work</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>Login Required</h3>";
        echo "<p><a href='{$baseUrl}/auth/login' target='_blank'>Login first</a> to test the notification center.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
