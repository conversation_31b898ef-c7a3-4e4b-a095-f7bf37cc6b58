<?php
/**
 * Run migration to add email_message_id field to ticket_numbers table
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';

try {
    $db = new Database();
    
    echo "Adding email_message_id field to ticket_numbers table...\n";
    
    // Check if column already exists
    $db->query("SHOW COLUMNS FROM ticket_numbers LIKE 'email_message_id'");
    $exists = $db->single();
    
    if (!$exists) {
        // Add the column
        $db->query("ALTER TABLE `ticket_numbers` 
                   ADD COLUMN `email_message_id` VARCHAR(255) NULL 
                   COMMENT 'Temporary storage of email message_id during processing to prevent race conditions'
                   AFTER `message_id`");
        $db->execute();
        echo "✓ Added email_message_id column\n";
        
        // Add index
        $db->query("ALTER TABLE `ticket_numbers` 
                   ADD INDEX `idx_email_message_id` (`email_message_id`)");
        $db->execute();
        echo "✓ Added index for email_message_id\n";
        
        echo "Migration completed successfully!\n";
    } else {
        echo "Column email_message_id already exists. Skipping migration.\n";
    }
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}