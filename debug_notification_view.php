<?php
/**
 * Debug Notification View
 * 
 * This script helps debug why clicking "View" on notifications doesn't work
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'core/Auth.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Debug Notification View</h1>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to test notification viewing.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $notificationCenterModel = new NotificationCenterModel();
    
    echo "<p><strong>User ID:</strong> {$userId}</p>";
    echo "<p><strong>Base URL:</strong> " . BASE_URL . "</p>";
    
    echo "<h2>1. Available Notifications</h2>";
    
    // Get some notifications for this user
    $notifications = $notificationCenterModel->getUserNotifications($userId, 'all', 'all', 10, 0);
    
    if (empty($notifications)) {
        echo "<p>❌ No notifications found for this user</p>";
        echo "<p>Try sending a message through judge management first to create some notifications.</p>";
    } else {
        echo "<p>✅ Found " . count($notifications) . " notifications</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Title</th>";
        echo "<th style='padding: 8px;'>Action URL</th>";
        echo "<th style='padding: 8px;'>Test Link</th>";
        echo "</tr>";
        
        foreach ($notifications as $notification) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$notification->id}</td>";
            echo "<td style='padding: 8px;'>{$notification->notification_type}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($notification->title, 0, 50)) . "...</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($notification->action_url ?? 'None') . "</td>";
            
            $viewUrl = BASE_URL . "/notification_center/viewNotification/" . $notification->id;
            echo "<td style='padding: 8px;'><a href='{$viewUrl}' target='_blank'>Test View</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>2. Test Specific Notification</h2>";
    
    // Test with a specific notification ID
    $testId = $_GET['test_id'] ?? null;
    
    if ($testId) {
        echo "<h3>Testing Notification ID: {$testId}</h3>";
        
        try {
            $notification = $notificationCenterModel->getNotificationById($testId, $userId);
            
            if ($notification) {
                echo "<p>✅ Notification found successfully</p>";
                echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>Notification Details:</h4>";
                echo "<ul>";
                echo "<li><strong>ID:</strong> {$notification->id}</li>";
                echo "<li><strong>Type:</strong> {$notification->notification_type}</li>";
                echo "<li><strong>Title:</strong> " . htmlspecialchars($notification->title) . "</li>";
                echo "<li><strong>Message:</strong> " . htmlspecialchars(substr($notification->message, 0, 100)) . "...</li>";
                echo "<li><strong>Created:</strong> {$notification->created_at}</li>";
                echo "<li><strong>Source Table:</strong> {$notification->source_table}</li>";
                echo "<li><strong>Source ID:</strong> {$notification->source_id}</li>";
                echo "</ul>";
                echo "</div>";
                
                // Test the view URL
                $viewUrl = BASE_URL . "/notification_center/viewNotification/" . $notification->id;
                echo "<p><strong>View URL:</strong> <a href='{$viewUrl}' target='_blank'>{$viewUrl}</a></p>";
                
            } else {
                echo "<p>❌ Notification not found or doesn't belong to this user</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Error getting notification: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>Add <code>?test_id=X</code> to the URL to test a specific notification ID</p>";
        
        if (!empty($notifications)) {
            $firstNotification = $notifications[0];
            $testUrl = $_SERVER['REQUEST_URI'] . "?test_id=" . $firstNotification->id;
            echo "<p>Example: <a href='{$testUrl}'>Test notification ID {$firstNotification->id}</a></p>";
        }
    }
    
    echo "<h2>3. Controller Method Test</h2>";
    
    // Test if the controller method exists
    if (class_exists('NotificationCenterController')) {
        echo "<p>✅ NotificationCenterController class exists</p>";
        
        $reflection = new ReflectionClass('NotificationCenterController');
        $methods = $reflection->getMethods();
        $methodNames = array_map(function($method) { return $method->getName(); }, $methods);
        
        if (in_array('viewNotification', $methodNames)) {
            echo "<p>✅ viewNotification method exists</p>";
        } else {
            echo "<p>❌ viewNotification method not found</p>";
            echo "<p>Available methods: " . implode(', ', $methodNames) . "</p>";
        }
        
    } else {
        echo "<p>❌ NotificationCenterController class not found</p>";
    }
    
    echo "<h2>4. URL Routing Test</h2>";
    
    echo "<p>Test these URLs manually:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Notification Center Main</a></li>";
    
    if (!empty($notifications)) {
        $firstId = $notifications[0]->id;
        echo "<li><a href='" . BASE_URL . "/notification_center/viewNotification/{$firstId}' target='_blank'>View Notification {$firstId}</a></li>";
    }
    
    echo "</ul>";
    
    echo "<h2>5. Common Issues & Solutions</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Possible Issues:</h4>";
    echo "<ol>";
    echo "<li><strong>URL Rewriting:</strong> Make sure .htaccess is configured correctly</li>";
    echo "<li><strong>Method Name:</strong> Ensure the controller method is named 'viewNotification'</li>";
    echo "<li><strong>Permissions:</strong> Check if the notification belongs to the current user</li>";
    echo "<li><strong>Database:</strong> Verify the notification exists in the database</li>";
    echo "<li><strong>Routing:</strong> Check if the App.php handles the route correctly</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>6. Quick Fixes</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Try These:</h4>";
    echo "<ol>";
    echo "<li>Check browser console for JavaScript errors</li>";
    echo "<li>Check server error logs for PHP errors</li>";
    echo "<li>Try accessing the URL directly instead of clicking the button</li>";
    echo "<li>Clear browser cache and cookies</li>";
    echo "<li>Check if the notification center controller is properly loaded</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
