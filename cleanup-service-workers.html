<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Cleanup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Service Worker Cleanup</h1>
        <p>This page helps clean up old service workers that might be causing conflicts.</p>
        
        <button id="cleanup-sw" class="btn btn-danger">Cleanup All Service Workers</button>
        <button id="register-main" class="btn btn-primary">Register Main Service Worker</button>
        <button id="check-sw" class="btn btn-info">Check Current Service Workers</button>
        
        <div class="mt-3">
            <h3>Console Output:</h3>
            <div id="console-output" class="border p-3" style="height: 400px; overflow-y: auto; background: #f8f9fa;"></div>
        </div>
    </div>

    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };
        
        // Cleanup all service workers
        document.getElementById('cleanup-sw').addEventListener('click', async () => {
            console.log('🧹 Starting service worker cleanup...');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    console.log(`Found ${registrations.length} service worker registrations`);
                    
                    for (const registration of registrations) {
                        console.log(`Unregistering: ${registration.scope}`);
                        await registration.unregister();
                        console.log(`✅ Unregistered: ${registration.scope}`);
                    }
                    
                    console.log('🎉 All service workers cleaned up!');
                    console.log('💡 Now click "Register Main Service Worker" to register the correct one');
                } catch (error) {
                    console.error('❌ Error during cleanup:', error);
                }
            } else {
                console.log('❌ Service workers not supported');
            }
        });
        
        // Register main service worker
        document.getElementById('register-main').addEventListener('click', async () => {
            console.log('📝 Registering main service worker...');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    console.log('✅ Main service worker registered:', registration.scope);
                    
                    await navigator.serviceWorker.ready;
                    console.log('✅ Service worker is ready');
                    
                    console.log('🎉 Main service worker setup complete!');
                } catch (error) {
                    console.error('❌ Error registering main service worker:', error);
                }
            } else {
                console.log('❌ Service workers not supported');
            }
        });
        
        // Check current service workers
        document.getElementById('check-sw').addEventListener('click', async () => {
            console.log('🔍 Checking current service workers...');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    console.log(`Found ${registrations.length} service worker registrations:`);
                    
                    registrations.forEach((registration, index) => {
                        console.log(`${index + 1}. Scope: ${registration.scope}`);
                        console.log(`   Installing: ${registration.installing ? 'Yes' : 'No'}`);
                        console.log(`   Waiting: ${registration.waiting ? 'Yes' : 'No'}`);
                        console.log(`   Active: ${registration.active ? 'Yes' : 'No'}`);
                        if (registration.active) {
                            console.log(`   Script URL: ${registration.active.scriptURL}`);
                        }
                    });
                    
                    if (registrations.length === 0) {
                        console.log('No service workers registered');
                    }
                } catch (error) {
                    console.error('❌ Error checking service workers:', error);
                }
            } else {
                console.log('❌ Service workers not supported');
            }
        });
        
        // Auto-check on load
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('check-sw').click();
        });
    </script>
</body>
</html>