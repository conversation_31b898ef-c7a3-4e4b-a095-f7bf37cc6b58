<?php
/**
 * Test Pagination Fix
 * 
 * This script tests if the pagination type error is resolved
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'core/Controller.php';
require_once 'core/Auth.php';

// Load models
require_once 'models/NotificationCenterModel.php';

echo "<h1>Pagination Fix Test</h1>";

try {
    echo "<h2>1. Testing Data Types</h2>";
    
    // Test the model directly
    $db = new Database();
    $notificationCenterModel = new NotificationCenterModel();
    
    // Get a test user
    $db->query("SELECT id FROM users WHERE status = 'active' LIMIT 1");
    $testUser = $db->single();
    
    if (!$testUser) {
        echo "<p>❌ No active users found for testing</p>";
        exit;
    }
    
    $testUserId = $testUser->id;
    echo "<p>Using test user ID: {$testUserId}</p>";
    
    // Test getTotalNotificationCount
    $totalCount = $notificationCenterModel->getTotalNotificationCount($testUserId, 'all', 'all');
    echo "<p>Total count: {$totalCount} (type: " . gettype($totalCount) . ")</p>";
    
    if (is_int($totalCount)) {
        echo "<p>✅ getTotalNotificationCount returns integer</p>";
    } else {
        echo "<p>❌ getTotalNotificationCount returns " . gettype($totalCount) . "</p>";
    }
    
    // Test pagination calculation
    $limit = 20;
    $totalPages = ceil($totalCount / $limit);
    echo "<p>Total pages: {$totalPages} (type: " . gettype($totalPages) . ")</p>";
    
    if (is_float($totalPages) || is_int($totalPages)) {
        echo "<p>✅ Total pages calculation works</p>";
    } else {
        echo "<p>❌ Total pages calculation failed</p>";
    }
    
    // Test integer casting
    $totalPagesInt = (int)ceil($totalCount / $limit);
    echo "<p>Total pages (int cast): {$totalPagesInt} (type: " . gettype($totalPagesInt) . ")</p>";
    
    if (is_int($totalPagesInt)) {
        echo "<p>✅ Integer casting works</p>";
    } else {
        echo "<p>❌ Integer casting failed</p>";
    }
    
    echo "<h2>2. Testing Pagination Logic</h2>";
    
    // Test the pagination logic that was causing the error
    $current_page = 1;
    $total_pages = $totalPagesInt;
    
    echo "<p>Current page: {$current_page} (type: " . gettype($current_page) . ")</p>";
    echo "<p>Total pages: {$total_pages} (type: " . gettype($total_pages) . ")</p>";
    
    // Test the problematic calculation
    try {
        $start = max(1, $current_page - 2);
        $end = min($total_pages, $current_page + 2);
        echo "<p>✅ Pagination range: {$start} to {$end}</p>";
        
        // Test the loop
        $pageNumbers = [];
        for ($i = $start; $i <= $end; $i++) {
            $pageNumbers[] = $i;
        }
        echo "<p>✅ Page numbers: " . implode(', ', $pageNumbers) . "</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Pagination calculation error: " . $e->getMessage() . "</p>";
    } catch (TypeError $e) {
        echo "<p>❌ Type error in pagination: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. Testing with String Values (Simulating GET params)</h2>";
    
    // Simulate what happens when values come from $_GET
    $current_page_string = "1";
    $total_pages_string = (string)$totalPagesInt;
    
    echo "<p>Current page (string): '{$current_page_string}' (type: " . gettype($current_page_string) . ")</p>";
    echo "<p>Total pages (string): '{$total_pages_string}' (type: " . gettype($total_pages_string) . ")</p>";
    
    // Test with proper casting
    try {
        $start = max(1, (int)$current_page_string - 2);
        $end = min((int)$total_pages_string, (int)$current_page_string + 2);
        echo "<p>✅ Pagination range with casting: {$start} to {$end}</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Pagination calculation error with strings: " . $e->getMessage() . "</p>";
    } catch (TypeError $e) {
        echo "<p>❌ Type error with strings: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>4. Testing Controller Data</h2>";
    
    // Simulate controller logic
    $page = max(1, (int)($current_page_string ?? 1));
    $totalCount = (int)$notificationCenterModel->getTotalNotificationCount($testUserId, 'all', 'all');
    $totalPages = (int)ceil($totalCount / $limit);
    
    echo "<p>Controller page: {$page} (type: " . gettype($page) . ")</p>";
    echo "<p>Controller total count: {$totalCount} (type: " . gettype($totalCount) . ")</p>";
    echo "<p>Controller total pages: {$totalPages} (type: " . gettype($totalPages) . ")</p>";
    
    // Test the view data
    $data = [
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_count' => $totalCount
    ];
    
    echo "<p>✅ Controller data prepared successfully</p>";
    echo "<pre>" . print_r($data, true) . "</pre>";
    
    echo "<h2>5. Testing URL Access</h2>";
    
    $baseUrl = BASE_URL;
    $testUrls = [
        '/notification_center' => 'Main notification center (page 1)',
        '/notification_center?page=1' => 'Page 1 explicitly',
        '/notification_center?page=2' => 'Page 2 (if exists)'
    ];
    
    foreach ($testUrls as $url => $description) {
        $fullUrl = $baseUrl . $url;
        echo "<p>🔗 <a href='{$fullUrl}' target='_blank'>{$description}</a></p>";
    }
    
    echo "<h2>✅ Pagination Fix Test Complete!</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>Ready to Test!</h3>";
        echo "<p>The pagination type error has been resolved. You can now test:</p>";
        echo "<ul>";
        echo "<li><a href='{$baseUrl}/notification_center' target='_blank'>Notification Center</a></li>";
        echo "<li>Navigate through pages if you have many notifications</li>";
        echo "<li>Test different filter combinations</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>Login Required</h3>";
        echo "<p><a href='{$baseUrl}/auth/login' target='_blank'>Login first</a> to test the notification center pagination.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
