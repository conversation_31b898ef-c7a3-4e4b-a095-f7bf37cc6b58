<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Update Banner Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/pwa-features.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>PWA Update Banner Test</h1>
        <p>This page tests if the PWA update banner is visible when triggered.</p>
        
        <button id="test-banner" class="btn btn-primary">Test Update Banner</button>
        <button id="clear-storage" class="btn btn-warning">Clear Storage</button>
        
        <div class="mt-3">
            <h3>Debug Info:</h3>
            <div id="debug-info"></div>
        </div>
    </div>

    <script>
        // Mock PWA Features class for testing
        class PWAFeatures {
            showUpdateBanner() {
                // Check if update was recently dismissed
                const dismissed = sessionStorage.getItem('pwa_update_dismissed');
                const lastShown = localStorage.getItem('pwa_last_update_shown');
                const now = Date.now();
                
                // Don't show if dismissed in this session or shown recently (within 5 minutes)
                if (dismissed === 'true' || (lastShown && (now - parseInt(lastShown)) < 5 * 60 * 1000)) {
                    console.log('[PWA] Update banner suppressed (recently dismissed or shown)');
                    document.getElementById('debug-info').innerHTML += '<p class="text-warning">Banner suppressed (recently dismissed or shown)</p>';
                    return;
                }

                // Remove any existing update banners
                this.removeExistingUpdateBanners();

                console.log('[PWA] Showing update banner');
                document.getElementById('debug-info').innerHTML += '<p class="text-info">Showing update banner</p>';
                
                const banner = document.createElement('div');
                banner.className = 'update-banner alert alert-info alert-dismissible fade show position-fixed';
                banner.style.cssText = `
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    z-index: 9999;
                    max-width: 90%;
                    width: 400px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    border: none;
                    background: linear-gradient(135deg, #1338BE 0%, #0056b3 100%);
                    color: white;
                    border-radius: 8px;
                `;
                
                banner.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="fas fa-sync-alt me-2"></i>
                        <div class="flex-grow-1">
                            <strong>App Update Available</strong>
                            <div class="small">A new version is ready to install</div>
                        </div>
                        <div class="ms-2">
                            <button type="button" class="btn btn-light btn-sm me-2" id="pwa-update-now">
                                <i class="fas fa-download me-1"></i>Update
                            </button>
                            <button type="button" class="btn-close btn-close-white" aria-label="Close" id="pwa-update-dismiss"></button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(banner);
                document.getElementById('debug-info').innerHTML += '<p class="text-success">Banner added to DOM</p>';
                
                // Add event listeners
                document.getElementById('pwa-update-now').addEventListener('click', () => {
                    this.applyUpdate();
                });
                
                document.getElementById('pwa-update-dismiss').addEventListener('click', () => {
                    this.dismissUpdate();
                });
                
                // Auto-dismiss after 30 seconds if no action
                setTimeout(() => {
                    if (document.querySelector('.update-banner')) {
                        console.log('[PWA] Auto-dismissing update banner after 30 seconds');
                        document.getElementById('debug-info').innerHTML += '<p class="text-warning">Auto-dismissing update banner after 30 seconds</p>';
                        this.dismissUpdate();
                    }
                }, 30000);
                
                // Store when we showed this banner
                localStorage.setItem('pwa_last_update_shown', now.toString());
            }

            dismissUpdate() {
                console.log('[PWA] Dismiss update clicked');
                document.getElementById('debug-info').innerHTML += '<p class="text-info">Dismiss update clicked</p>';
                
                const banner = document.querySelector('.update-banner');
                if (banner) {
                    banner.remove();
                    console.log('[PWA] Update banner removed');
                    document.getElementById('debug-info').innerHTML += '<p class="text-success">Update banner removed</p>';
                }
                
                // Set flags to not show the banner again
                sessionStorage.setItem('pwa_update_dismissed', 'true');
                localStorage.setItem('pwa_last_update_shown', Date.now().toString());
                console.log('[PWA] Update dismissed for this session and next 5 minutes');
                document.getElementById('debug-info').innerHTML += '<p class="text-info">Update dismissed for this session and next 5 minutes</p>';
            }

            applyUpdate() {
                console.log('[PWA] Apply update clicked');
                document.getElementById('debug-info').innerHTML += '<p class="text-success">Apply update clicked (would reload page in real app)</p>';
                this.dismissUpdate();
            }

            removeExistingUpdateBanners() {
                const existingBanners = document.querySelectorAll('.update-banner');
                existingBanners.forEach(banner => {
                    banner.remove();
                });
                if (existingBanners.length > 0) {
                    document.getElementById('debug-info').innerHTML += '<p class="text-warning">Removed ' + existingBanners.length + ' existing banners</p>';
                }
            }
        }

        // Initialize
        const pwa = new PWAFeatures();

        // Test button
        document.getElementById('test-banner').addEventListener('click', () => {
            document.getElementById('debug-info').innerHTML = '<p class="text-primary">Testing update banner...</p>';
            pwa.showUpdateBanner();
        });

        // Clear storage button
        document.getElementById('clear-storage').addEventListener('click', () => {
            sessionStorage.removeItem('pwa_update_dismissed');
            localStorage.removeItem('pwa_last_update_shown');
            document.getElementById('debug-info').innerHTML = '<p class="text-success">Storage cleared</p>';
        });
    </script>
</body>
</html>