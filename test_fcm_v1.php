<?php
/**
 * Test FCM HTTP v1 API implementation
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Prevent direct access without security key
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'fcm_v1_test_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/helpers/fcm_v1_helper.php';

echo "=== FCM HTTP v1 API TEST ===\n\n";

try {
    echo "1. CHECKING SERVICE ACCOUNT FILE\n";
    
    // Check for service account file
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    
    if (!file_exists($serviceAccountPath)) {
        echo "   ❌ Service account file not found: $serviceAccountPath\n\n";
        echo "   SETUP REQUIRED:\n";
        echo "   1. Go to https://console.firebase.google.com/\n";
        echo "   2. Select your project\n";
        echo "   3. Go to Project Settings → Service Accounts\n";
        echo "   4. Click 'Generate new private key'\n";
        echo "   5. Download the JSON file\n";
        echo "   6. Upload it to: $serviceAccountPath\n";
        echo "   7. Make sure the file is not publicly accessible\n\n";
        
        echo "   ALTERNATIVE LOCATIONS:\n";
        echo "   You can also place the file at:\n";
        echo "   - " . APPROOT . "/firebase-service-account.json\n";
        echo "   - " . dirname(APPROOT) . "/firebase-service-account.json (outside public)\n";
        echo "   - Update the path in this script\n\n";
        
        // Check alternative locations
        $altPaths = [
            APPROOT . '/firebase-service-account.json',
            dirname(APPROOT) . '/firebase-service-account.json'
        ];
        
        foreach ($altPaths as $altPath) {
            if (file_exists($altPath)) {
                echo "   ✅ Found service account at: $altPath\n";
                $serviceAccountPath = $altPath;
                break;
            }
        }
        
        if (!file_exists($serviceAccountPath)) {
            exit("   Please upload the Firebase service account JSON file first.\n");
        }
    } else {
        echo "   ✅ Service account file found\n";
    }
    
    echo "\n2. INITIALIZING FCM v1 HELPER\n";
    
    $fcm = new FCMv1Helper($serviceAccountPath);
    echo "   ✅ FCM v1 Helper initialized\n";
    
    echo "\n3. GETTING USER SUBSCRIPTIONS\n";
    
    $userId = 3;
    $db = new Database();
    $db->query("SELECT * FROM push_subscriptions WHERE user_id = ? AND active = 1 ORDER BY created_at DESC LIMIT 3");
    $db->bind(1, $userId);
    $db->execute();
    $subscriptions = $db->resultSet();
    
    echo "   Found " . count($subscriptions) . " subscriptions\n";
    
    if (empty($subscriptions)) {
        echo "   No subscriptions to test with\n";
        exit;
    }
    
    echo "\n4. EXTRACTING FCM TOKENS\n";
    
    $fcmTokens = [];
    foreach ($subscriptions as $sub) {
        $token = FCMv1Helper::extractFCMToken($sub->endpoint);
        if ($token) {
            $fcmTokens[] = $token;
            echo "   Token: " . substr($token, 0, 20) . "...\n";
        }
    }
    
    if (empty($fcmTokens)) {
        echo "   No FCM tokens found in subscriptions\n";
        exit;
    }
    
    echo "\n5. SENDING TEST NOTIFICATION\n";
    
    $title = "🚀 FCM v1 API Test";
    $body = "This notification was sent using FCM HTTP v1 API at " . date('H:i:s');
    $data = [
        'url' => '/',
        'event_id' => '0',
        'event_type' => 'fcm_v1_test'
    ];
    
    echo "   Title: $title\n";
    echo "   Body: $body\n";
    echo "   Sending to " . count($fcmTokens) . " tokens...\n\n";
    
    $results = $fcm->sendToMultipleTokens($fcmTokens, $title, $body, $data);
    
    echo "6. RESULTS\n";
    
    $successCount = 0;
    $failCount = 0;
    
    foreach ($results as $i => $result) {
        echo "   Token " . ($i + 1) . " (" . $result['token'] . "): ";
        
        if ($result['success']) {
            echo "✅ SUCCESS\n";
            $successCount++;
        } else {
            echo "❌ FAILED - " . $result['error'] . "\n";
            $failCount++;
        }
    }
    
    echo "\n7. SUMMARY\n";
    echo "   Successful: $successCount\n";
    echo "   Failed: $failCount\n";
    
    if ($successCount > 0) {
        echo "\n🎉 FCM v1 API WORKING!\n";
        echo "   Check your browser for notifications\n";
        echo "   This solution bypasses all VAPID issues\n";
        echo "   Ready to integrate into NotificationService\n";
    } else {
        echo "\n❌ All notifications failed\n";
        echo "   Check the error messages above\n";
        echo "   Verify Firebase project setup\n";
    }
    
    echo "\n8. NEXT STEPS\n";
    if ($successCount > 0) {
        echo "   1. Integrate FCM v1 Helper into NotificationService.php\n";
        echo "   2. Replace VAPID implementation with FCM v1 API\n";
        echo "   3. Update push notification methods\n";
        echo "   4. Test with real notifications\n";
    } else {
        echo "   1. Verify Firebase service account JSON is correct\n";
        echo "   2. Check Firebase project has FCM enabled\n";
        echo "   3. Ensure tokens are valid\n";
        echo "   4. Check server can access googleapis.com\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== END TEST ===\n";
?>
