<?php
/**
 * Debug FCM Sending Process
 * 
 * Test the actual FCM notification sending with your subscriptions
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'helpers/csrf_helper.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$userId = $_SESSION['user_id'];

echo "<h2>FCM Sending Debug</h2>";
echo "<p><strong>User ID:</strong> $userId</p>";

try {
    // Get one active subscription to test
    $db = new Database();
    $db->query('SELECT * FROM push_subscriptions WHERE user_id = :user_id AND active = 1 LIMIT 1');
    $db->bind(':user_id', $userId);
    $subscription = $db->single();
    
    if (!$subscription) {
        echo "<p style='color: red;'>❌ No active subscriptions found</p>";
        exit;
    }
    
    echo "<h3>Testing with Subscription ID: {$subscription->id}</h3>";
    echo "<p><strong>Endpoint:</strong> " . substr($subscription->endpoint, 0, 50) . "...</p>";
    
    // Test FCM token extraction
    require_once 'helpers/fcm_v1_helper.php';
    $fcmToken = FCMv1Helper::extractFCMToken($subscription->endpoint);
    
    echo "<p><strong>Extracted FCM Token:</strong> " . ($fcmToken ? substr($fcmToken, 0, 30) . '...' : 'NULL') . "</p>";
    
    if (!$fcmToken) {
        echo "<p style='color: red;'>❌ Failed to extract FCM token from endpoint</p>";
        exit;
    }
    
    // Test service account file
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    echo "<p><strong>Service Account Path:</strong> $serviceAccountPath</p>";
    echo "<p><strong>Service Account Exists:</strong> " . (file_exists($serviceAccountPath) ? '✅ Yes' : '❌ No') . "</p>";
    
    if (!file_exists($serviceAccountPath)) {
        echo "<p style='color: red;'>❌ Service account file not found</p>";
        exit;
    }
    
    // Test FCM helper initialization
    try {
        $fcm = new FCMv1Helper($serviceAccountPath);
        echo "<p><strong>FCM Helper Initialized:</strong> ✅ Success</p>";
        
        // Test sending a notification
        echo "<h3>Sending Test Notification</h3>";
        
        $result = $fcm->sendNotification($fcmToken, [
            'title' => 'FCM Debug Test',
            'body' => 'This is a test notification sent at ' . date('H:i:s')
        ], [
            'test' => 'true',
            'timestamp' => time()
        ]);
        
        echo "<p><strong>Send Result:</strong> " . ($result ? '✅ Success' : '❌ Failed') . "</p>";
        
        if ($result) {
            echo "<p style='color: green;'><strong>🎉 FCM notification sent successfully!</strong></p>";
            echo "<p>Check your device for the notification.</p>";
        } else {
            echo "<p style='color: red;'><strong>❌ FCM notification failed to send</strong></p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>FCM Helper Error:</strong> " . $e->getMessage() . "</p>";
    }
    
    // Test the NotificationService method
    echo "<h3>Testing NotificationService Method</h3>";
    
    require_once 'models/NotificationService.php';
    $notificationService = new NotificationService();
    
    // Create a test payload
    $payload = json_encode([
        'title' => 'NotificationService Test',
        'body' => 'Testing the sendPushToSubscription method at ' . date('H:i:s'),
        'data' => [
            'test' => 'true',
            'method' => 'NotificationService'
        ]
    ]);
    
    // Use reflection to access the private method
    $reflection = new ReflectionClass($notificationService);
    $method = $reflection->getMethod('sendPushToSubscription');
    $method->setAccessible(true);
    
    $serviceResult = $method->invoke($notificationService, $subscription, $payload);
    
    echo "<p><strong>NotificationService Result:</strong> " . ($serviceResult ? '✅ Success' : '❌ Failed') . "</p>";
    
    if ($serviceResult) {
        echo "<p style='color: green;'><strong>🎉 NotificationService method works!</strong></p>";
    } else {
        echo "<p style='color: red;'><strong>❌ NotificationService method failed</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='/debug_fcm_subscriptions.php'>← Back to Subscriptions Debug</a></p>";
echo "<p><a href='/test_push_fix.php'>← Back to Push Test</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>FCM Sending Debug</title>
    <meta name="csrf-token" content="<?php echo generateCsrfToken(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
</body>
</html>