<?php
require_once dirname(__DIR__) . '/config/config.php';
require_once APPROOT . '/core/ContentCleaner.php';

$testContent = '------sinikael-?=_1-17528868592260.1560288133272248
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 7bit
test message  
 asking a question
------sinikael-?=_1-17528868592260.1560288133272248
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable
<html><head></head><body><div>
    <div dir="auto" id="compose-body-wrapper">test message </div><div dir="auto" id="compose-body-wrapper">asking a question</div><div dir="auto" id="compose-body-wrapper"><br/>
      <div dir="auto"><br/></div>
      <div dir="auto" id="tmjah_g_1299">
      Get <a href="https://bluemail.me/download/" target="_blank">BlueMail for Mobile</a>
      </div>
      <div dir="auto"><br/></div>
    </div>
  </div></body></html>
------sinikael-?=_1-17528868592260.1560288133272248--';

echo "Quick test of cleanMessageContent:\n";
echo "Original content preview:\n";
echo substr($testContent, 0, 300) . "...\n\n";

echo "Testing regex patterns manually:\n";

// Test the exact regex from ContentCleaner
if (preg_match('/Content-Type:\s*text\/plain[^;]*(?:;[^=]*=[^;]+)*.*?\nContent-Transfer-Encoding:[^\n]*\n(.+?)(?=\n------)/s', $testContent, $matches)) {
    echo "✓ Pattern 1 matched!\n";
    echo "Captured: '" . $matches[1] . "'\n\n";
} else {
    echo "✗ Pattern 1 did not match\n\n";
}

// Test simpler pattern
if (preg_match('/text\/plain.*?\n.*?\n(.+?)(?=\n------)/s', $testContent, $matches)) {
    echo "✓ Simple pattern matched!\n";
    echo "Captured: '" . $matches[1] . "'\n\n";
} else {
    echo "✗ Simple pattern did not match\n\n";
}

echo "Now testing ContentCleaner::cleanMessageContent:\n";
$result = ContentCleaner::cleanMessageContent($testContent);
echo "Result: '" . substr($result, 0, 200) . "...'\n";
echo "Length: " . strlen($result) . "\n";
echo "Contains 'test message': " . (strpos($result, 'test message') !== false ? 'YES' : 'NO') . "\n";
echo "Contains HTML: " . (strpos($result, '<html>') !== false ? 'YES' : 'NO') . "\n";
?>