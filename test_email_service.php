<?php
/**
 * Test Email Service
 * 
 * This file tests the EmailService functionality with and without PHPMailer.
 */

// Start session and include necessary files
session_start();

// Define constants
define('APPROOT', dirname(__FILE__));
define('BASE_URL', 'https://events.rowaneliterides.com');
define('DEBUG_MODE', true);

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/SettingsModel.php';
require_once APPROOT . '/models/EmailService.php';

echo "<h1>📧 Email Service Test</h1>";

try {
    echo "<h2>🔧 Testing Email Service Initialization</h2>";
    
    // Create EmailService instance
    $emailService = new EmailService();
    echo "<p style='color: green;'>✅ EmailService created successfully</p>";
    
    // Check if email is configured
    $isConfigured = $emailService->isConfigured();
    echo "<p><strong>Email Configuration Status:</strong> " . ($isConfigured ? '✅ Configured' : '❌ Not Configured') . "</p>";
    
    if (!$isConfigured) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ Email Not Configured</h3>";
        echo "<p>To test email functionality, you need to configure SMTP settings in the admin panel:</p>";
        echo "<ul>";
        echo "<li>Go to <strong>/admin/settings_email</strong></li>";
        echo "<li>Configure SMTP Host, Port, Username, Password</li>";
        echo "<li>Set From Address and From Name</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<h2>🧪 Testing Email Sending</h2>";
        
        // Test email content
        $testEmail = '<EMAIL>'; // Change this to a real email for testing
        $subject = 'Test Email from Events and Shows System';
        $body = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #2c3e50; margin-bottom: 20px; }
                .info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>Test Email</h1>
                <div class='info'>
                    <p>This is a test email from your Events and Shows application.</p>
                    <p>If you're receiving this email, your email configuration is working correctly!</p>
                </div>
                <p>Server time (UTC): " . gmdate('Y-m-d H:i:s') . "</p>
                <p>This email was sent to: " . htmlspecialchars($testEmail) . "</p>
                <p><strong>Email Service Status:</strong> Using fallback mail() function (PHPMailer not installed)</p>
            </div>
        </body>
        </html>
        ";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>Test Email Details:</h3>";
        echo "<p><strong>To:</strong> " . htmlspecialchars($testEmail) . "</p>";
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>";
        echo "<p><strong>Method:</strong> Fallback mail() function (PHPMailer not available)</p>";
        echo "</div>";
        
        // Attempt to send the email
        echo "<h3>Sending Test Email...</h3>";
        $result = $emailService->send($testEmail, $subject, $body);
        
        if ($result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
            echo "<h3>✅ Email Sent Successfully!</h3>";
            echo "<p>The email was sent using the fallback mail() function.</p>";
            echo "<p><strong>Note:</strong> Check the recipient's inbox (and spam folder) to confirm delivery.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
            echo "<h3>❌ Email Sending Failed</h3>";
            echo "<p>The email could not be sent. This could be due to:</p>";
            echo "<ul>";
            echo "<li>Server mail configuration issues</li>";
            echo "<li>SMTP settings not properly configured</li>";
            echo "<li>Mail server restrictions</li>";
            echo "</ul>";
            echo "</div>";
        }
    }
    
    echo "<h2>🔍 PHPMailer Status</h2>";
    
    // Check PHPMailer availability
    $phpmailerPath = APPROOT . '/vendor/autoload.php';
    echo "<p><strong>Vendor autoload path:</strong> " . $phpmailerPath . "</p>";
    echo "<p><strong>Vendor autoload exists:</strong> " . (file_exists($phpmailerPath) ? '✅ Yes' : '❌ No') . "</p>";
    
    if (file_exists($phpmailerPath)) {
        require_once $phpmailerPath;
        $phpmailerAvailable = class_exists('PHPMailer\\PHPMailer\\PHPMailer');
        echo "<p><strong>PHPMailer class available:</strong> " . ($phpmailerAvailable ? '✅ Yes' : '❌ No') . "</p>";
    } else {
        echo "<p><strong>PHPMailer class available:</strong> ❌ No (vendor/autoload.php not found)</p>";
    }
    
    echo "<h2>📋 Email Service Improvements Made</h2>";
    echo "<ol>";
    echo "<li><strong>Graceful PHPMailer handling:</strong> No longer logs error when PHPMailer is not available</li>";
    echo "<li><strong>Improved fallback:</strong> Better mail() function implementation with proper headers</li>";
    echo "<li><strong>Multiple recipient support:</strong> Fallback now handles array of recipients</li>";
    echo "<li><strong>Better debugging:</strong> More informative debug messages</li>";
    echo "<li><strong>Error handling:</strong> Improved error reporting for failed emails</li>";
    echo "</ol>";
    
    echo "<h2>🚀 Next Steps</h2>";
    echo "<ol>";
    echo "<li>Copy the updated <code>models/EmailService.php</code> to your server</li>";
    echo "<li>Test the email functionality at <code>/admin/settings_email</code></li>";
    echo "<li>The system will now use the fallback mail() function without errors</li>";
    echo "<li>Optionally install PHPMailer for better email features:
        <ul>
            <li>Run <code>composer require phpmailer/phpmailer</code> in your project root</li>
            <li>Or manually download and install PHPMailer</li>
        </ul>
    </li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// Self-delete after 5 minutes
if (file_exists(__FILE__) && (time() - filemtime(__FILE__)) > 300) {
    unlink(__FILE__);
}
?>
