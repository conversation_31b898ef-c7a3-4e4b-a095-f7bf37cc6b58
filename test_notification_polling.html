<!DOCTYPE html>
<html>
<head>
    <title>Test Notification Polling</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔍 Notification Polling Test</h1>
    
    <div id="status" class="status info">
        <strong>Status:</strong> Initializing...
    </div>
    
    <div class="status info">
        <h3>What This Test Does:</h3>
        <ul>
            <li>Checks if NotificationManager is loaded</li>
            <li>Tests the /notification/getUnread endpoint</li>
            <li>Shows polling activity in real-time</li>
            <li>Displays any errors that prevent notifications</li>
        </ul>
    </div>
    
    <h3>📊 Real-time Log:</h3>
    <div id="log" class="log"></div>
    
    <h3>🔧 Manual Tests:</h3>
    <button onclick="testGetUnread()">Test getUnread Endpoint</button>
    <button onclick="testNotificationManager()">Test NotificationManager</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <script>
        const BASE_URL = 'https://events.rowaneliterides.com';
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            
            if (type === 'error') {
                logEntry.style.color = 'red';
            } else if (type === 'success') {
                logEntry.style.color = 'green';
            } else if (type === 'warning') {
                logEntry.style.color = 'orange';
            }
            
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            statusElement.className = `status ${type}`;
            statusElement.innerHTML = `<strong>Status:</strong> ${message}`;
        }
        
        function clearLog() {
            logElement.innerHTML = '';
        }
        
        async function testGetUnread() {
            log('🔄 Testing /notification/getUnread endpoint...');
            
            try {
                const response = await fetch(`${BASE_URL}/notification/getUnread`);
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ getUnread SUCCESS: ${data.push?.length || 0} push, ${data.toast?.length || 0} toast notifications`, 'success');
                    
                    if (data.push?.length > 0) {
                        log(`📱 Push notifications: ${JSON.stringify(data.push.slice(0, 2))}`, 'info');
                    }
                    
                    if (data.toast?.length > 0) {
                        log(`🍞 Toast notifications: ${JSON.stringify(data.toast.slice(0, 2))}`, 'info');
                    }
                } else {
                    log(`❌ getUnread FAILED: HTTP ${response.status}`, 'error');
                    log(`Response: ${JSON.stringify(data)}`, 'error');
                }
            } catch (error) {
                log(`❌ getUnread ERROR: ${error.message}`, 'error');
            }
        }
        
        function testNotificationManager() {
            log('🔄 Testing NotificationManager...');
            
            if (typeof window.NotificationManager !== 'undefined') {
                log('✅ NotificationManager class is available', 'success');
            } else {
                log('❌ NotificationManager class NOT available', 'error');
            }
            
            if (window.notificationManager) {
                log('✅ window.notificationManager instance exists', 'success');
                
                // Test if the polling is working
                if (window.notificationManager.loadUnreadNotifications) {
                    log('✅ loadUnreadNotifications method exists', 'success');
                    
                    // Manually trigger it
                    log('🔄 Manually triggering loadUnreadNotifications...');
                    window.notificationManager.loadUnreadNotifications();
                } else {
                    log('❌ loadUnreadNotifications method NOT found', 'error');
                }
            } else {
                log('❌ window.notificationManager instance NOT found', 'error');
            }
        }
        
        // Monitor for NotificationManager initialization
        function checkNotificationManager() {
            if (window.notificationManager) {
                log('✅ NotificationManager initialized successfully', 'success');
                updateStatus('NotificationManager is running', 'success');
                
                // Hook into the loadUnreadNotifications method to monitor calls
                const originalMethod = window.notificationManager.loadUnreadNotifications;
                window.notificationManager.loadUnreadNotifications = function() {
                    log('🔄 NotificationManager polling for notifications...');
                    return originalMethod.call(this);
                };
                
                return true;
            }
            return false;
        }
        
        // Check every second for 10 seconds
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            checkCount++;
            
            if (checkNotificationManager()) {
                clearInterval(checkInterval);
            } else if (checkCount >= 10) {
                log('❌ NotificationManager failed to initialize after 10 seconds', 'error');
                updateStatus('NotificationManager failed to initialize', 'error');
                clearInterval(checkInterval);
            } else {
                log(`⏳ Waiting for NotificationManager... (${checkCount}/10)`);
            }
        }, 1000);
        
        // Initial status
        log('🚀 Starting notification polling test...');
        log(`📍 Base URL: ${BASE_URL}`);
        
        // Test basic connectivity
        fetch(`${BASE_URL}/`)
            .then(response => {
                if (response.ok) {
                    log('✅ Site connectivity OK', 'success');
                } else {
                    log(`⚠️ Site returned HTTP ${response.status}`, 'warning');
                }
            })
            .catch(error => {
                log(`❌ Site connectivity failed: ${error.message}`, 'error');
            });
        
        // Monitor console errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            log(`🚨 Console Error: ${args.join(' ')}`, 'error');
            originalConsoleError.apply(console, args);
        };
        
        // Auto-test after 5 seconds
        setTimeout(() => {
            log('🔄 Running automatic tests...');
            testGetUnread();
            testNotificationManager();
        }, 5000);
    </script>
</body>
</html>
