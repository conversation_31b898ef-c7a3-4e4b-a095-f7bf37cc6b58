<?php
/**
 * Web-based VAPID Key Generator
 * Generates public/private VAPID key pairs for push notifications
 */

// Prevent direct access without security key
if (!isset($_GET['generate_key']) || $_GET['generate_key'] !== 'vapid_keys_2025') {
    http_response_code(404);
    exit('Not Found');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>VAPID Key Generator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .key-box { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; word-break: break-all; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 VAPID Key Generator</h1>
        
        <?php
        // Function to generate VAPID keys
        function generateVAPIDKeys() {
            // Check OpenSSL support
            if (!function_exists('openssl_pkey_new')) {
                throw new Exception('OpenSSL extension is not available');
            }

            // Generate a private key
            $privateKey = openssl_pkey_new([
                'curve_name' => 'prime256v1',
                'private_key_type' => OPENSSL_KEYTYPE_EC,
            ]);

            if (!$privateKey) {
                $error = openssl_error_string();
                throw new Exception('Failed to generate private key: ' . ($error ?: 'Unknown OpenSSL error'));
            }

            // Export private key
            if (!openssl_pkey_export($privateKey, $privateKeyPEM)) {
                throw new Exception('Failed to export private key');
            }

            // Get public key details
            $publicKeyDetails = openssl_pkey_get_details($privateKey);

            if (!$publicKeyDetails) {
                throw new Exception('Failed to get public key details');
            }

            // Check if required keys exist
            if (!isset($publicKeyDetails['key'])) {
                throw new Exception('Public key not found in key details');
            }

            if (!isset($publicKeyDetails['ec']) || !isset($publicKeyDetails['ec']['d']) || !isset($publicKeyDetails['ec']['key'])) {
                throw new Exception('EC key components not found. Server may not support EC keys properly.');
            }

            $publicKeyPEM = $publicKeyDetails['key'];

            // Convert to base64url format for web push
            $privateKeyRaw = $publicKeyDetails['ec']['d'];
            $publicKeyRaw = substr($publicKeyDetails['ec']['key'], 1); // Remove first byte (0x04)

            $privateKeyBase64 = rtrim(strtr(base64_encode($privateKeyRaw), '+/', '-_'), '=');
            $publicKeyBase64 = rtrim(strtr(base64_encode($publicKeyRaw), '+/', '-_'), '=');

            return [
                'private_key' => $privateKeyBase64,
                'public_key' => $publicKeyBase64,
                'private_key_pem' => $privateKeyPEM,
                'public_key_pem' => $publicKeyPEM
            ];
        }

        if (isset($_POST['generate'])) {
            try {
                echo '<div class="success key-box">';
                echo '<h2>✅ VAPID Keys Generated Successfully!</h2>';
                
                $keys = generateVAPIDKeys();
                
                echo '<h3>📋 Copy these to your config.php file:</h3>';
                echo '<div class="key-box">';
                echo '<strong>// VAPID Keys for Push Notifications</strong><br>';
                echo "define('VAPID_PUBLIC_KEY', '{$keys['public_key']}');<br>";
                echo "define('VAPID_PRIVATE_KEY', '{$keys['private_key']}');<br>";
                echo "define('VAPID_SUBJECT', 'https://events.rowaneliterides.com');<br>";
                echo '</div>';
                
                echo '<h3>🔍 Key Details:</h3>';
                echo '<p><strong>Public Key Length:</strong> ' . strlen($keys['public_key']) . ' characters</p>';
                echo '<p><strong>Private Key Length:</strong> ' . strlen($keys['private_key']) . ' characters</p>';
                
                echo '<div class="step">';
                echo '<h3>📝 Next Steps:</h3>';
                echo '<ol>';
                echo '<li><strong>Copy the keys above</strong></li>';
                echo '<li><strong>Edit your config/config.php file</strong></li>';
                echo '<li><strong>Replace the existing VAPID_PUBLIC_KEY and VAPID_PRIVATE_KEY lines</strong></li>';
                echo '<li><strong>Save the file</strong></li>';
                echo '<li><strong>Test push notifications again</strong></li>';
                echo '</ol>';
                echo '</div>';
                
                echo '<div class="warning key-box">';
                echo '<h3>⚠️ Important Security Notes:</h3>';
                echo '<ul>';
                echo '<li><strong>Keep the private key secret</strong> - never share it publicly</li>';
                echo '<li><strong>The public key</strong> can be shared with browsers</li>';
                echo '<li><strong>These keys are unique</strong> - don\'t use them on other sites</li>';
                echo '<li><strong>Back up these keys</strong> - losing them means regenerating all subscriptions</li>';
                echo '</ul>';
                echo '</div>';
                
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<div class="error key-box">';
                echo '<h2>❌ Error Generating Keys</h2>';
                echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '<p>Your server may not support EC (Elliptic Curve) cryptography properly.</p>';

                echo '<h3>🔧 Alternative Solutions:</h3>';
                echo '<ol>';
                echo '<li><strong>Use online VAPID generator:</strong> <a href="https://vapidkeys.com/" target="_blank">https://vapidkeys.com/</a></li>';
                echo '<li><strong>Use Node.js web-push library</strong> if available on your server</li>';
                echo '<li><strong>Contact your hosting provider</strong> about OpenSSL EC support</li>';
                echo '</ol>';

                echo '<h3>📋 Manual Key Format:</h3>';
                echo '<p>If you generate keys elsewhere, they should look like:</p>';
                echo '<div class="key-box">';
                echo "define('VAPID_PUBLIC_KEY', 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLat3eAALZ-pOSXgNGmhGBgkgGLfCWQkaNwG7x2SkqTuTU4jNuBKPU');<br>";
                echo "define('VAPID_PRIVATE_KEY', 'your-43-character-private-key-here');<br>";
                echo "define('VAPID_SUBJECT', 'https://events.rowaneliterides.com');<br>";
                echo '</div>';
                echo '<p><strong>Public key:</strong> ~87 characters, starts with "B"</p>';
                echo '<p><strong>Private key:</strong> ~43 characters, random base64url</p>';

                echo '</div>';
            }
        } else {
            // Show current keys
            if (defined('VAPID_PUBLIC_KEY') && defined('VAPID_PRIVATE_KEY')) {
                echo '<div class="warning key-box">';
                echo '<h2>🔍 Current VAPID Keys:</h2>';
                echo '<p><strong>Public Key:</strong> ' . VAPID_PUBLIC_KEY . '</p>';
                echo '<p><strong>Private Key:</strong> ' . VAPID_PRIVATE_KEY . '</p>';
                echo '<p><strong>Subject:</strong> ' . (defined('VAPID_SUBJECT') ? VAPID_SUBJECT : 'Not set') . '</p>';
                
                // Analyze current keys
                echo '<h3>📊 Key Analysis:</h3>';
                $publicLen = strlen(VAPID_PUBLIC_KEY);
                $privateLen = strlen(VAPID_PRIVATE_KEY);
                
                echo '<p>Public Key Length: ' . $publicLen . ' characters ' . ($publicLen == 87 ? '✅' : '❌') . '</p>';
                echo '<p>Private Key Length: ' . $privateLen . ' characters ' . ($privateLen == 43 ? '✅' : '❌') . '</p>';
                
                // Check if private key looks like a pattern
                $privateKey = VAPID_PRIVATE_KEY;
                $isPattern = preg_match('/^[A-Z0-9]+$/', $privateKey) && 
                           (strpos($privateKey, '1234') !== false || 
                            strpos($privateKey, 'ABCD') !== false ||
                            preg_match('/(.)\1{2,}/', $privateKey));
                
                if ($isPattern) {
                    echo '<p><strong>⚠️ Private Key Issue:</strong> Current private key appears to be a placeholder/pattern, not a real cryptographic key!</p>';
                    echo '<p><strong>Recommendation:</strong> Generate new keys below.</p>';
                } else {
                    echo '<p><strong>✅ Private Key:</strong> Appears to be a valid cryptographic key.</p>';
                }
                
                echo '</div>';
            } else {
                echo '<div class="error key-box">';
                echo '<h2>❌ No VAPID Keys Found</h2>';
                echo '<p>VAPID keys are not configured in config.php</p>';
                echo '</div>';
            }
            
            echo '<div class="step">';
            echo '<h2>🚀 Generate New VAPID Keys</h2>';
            echo '<p>Click the button below to generate new cryptographically secure VAPID keys for push notifications.</p>';
            echo '<p><strong>Note:</strong> Generating new keys will invalidate all existing push subscriptions. Users will need to re-enable notifications.</p>';
            echo '<form method="post">';
            echo '<button type="submit" name="generate" class="btn btn-success">🔑 Generate New VAPID Keys</button>';
            echo '</form>';
            echo '</div>';
        }
        ?>
        
        <div class="step">
            <h2>📚 About VAPID Keys</h2>
            <p><strong>VAPID (Voluntary Application Server Identification)</strong> keys are used to authenticate your server with push notification services like Firebase Cloud Messaging.</p>
            <ul>
                <li><strong>Public Key:</strong> Shared with browsers to create push subscriptions</li>
                <li><strong>Private Key:</strong> Used by your server to sign push notification requests</li>
                <li><strong>Subject:</strong> Identifies your application (usually your website URL or email)</li>
            </ul>
        </div>
        
        <div class="step">
            <h2>🔧 Troubleshooting</h2>
            <p>If push notifications aren't working:</p>
            <ol>
                <li>Generate new VAPID keys using this tool</li>
                <li>Update your config.php file with the new keys</li>
                <li>Clear browser data and re-enable notifications</li>
                <li>Test with the push notification debug tools</li>
            </ol>
        </div>
    </div>
</body>
</html>
