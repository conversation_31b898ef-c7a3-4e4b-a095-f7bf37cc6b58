<?php
/**
 * Test Unified Messaging System Integration
 * 
 * This script tests that the unified messaging system properly integrates
 * with both global notification settings and user notification preferences.
 */

// Include the application bootstrap
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';
require_once 'models/NotificationModel.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Unified Messaging System Integration Test</h1>";

try {
    $unifiedModel = new UnifiedMessageModel();
    $notificationModel = new NotificationModel();
    
    // Test user ID (you can change this to test with a specific user)
    $testUserId = 1;
    
    echo "<h2>📋 Current Settings Status</h2>";
    
    // 1. Check global notification settings
    echo "<h3>🌐 Global Notification Settings (from notification_settings table)</h3>";
    $globalSettings = $notificationModel->getNotificationSettings();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    $settings = ['email_enabled', 'sms_enabled', 'push_enabled', 'toast_enabled'];
    foreach ($settings as $setting) {
        $value = $globalSettings[$setting] ?? 'NOT SET';
        $status = ($value === '1' || $value === 1 || $value === true) ? '✅ Enabled' : '❌ Disabled';
        echo "<tr><td>$setting</td><td>$value</td><td>$status</td></tr>";
    }
    echo "</table>";
    
    // 2. Check user notification preferences
    echo "<h3>👤 User Notification Preferences (User ID: $testUserId)</h3>";
    $userPrefs = $notificationModel->getUserPreferences($testUserId);
    
    if ($userPrefs) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Preference</th><th>Value</th><th>Status</th></tr>";
        
        $prefs = [
            'email_notifications' => $userPrefs->email_notifications ?? 0,
            'sms_notifications' => $userPrefs->sms_notifications ?? 0,
            'push_notifications' => $userPrefs->push_notifications ?? 0,
            'toast_notifications' => $userPrefs->toast_notifications ?? 0
        ];
        
        foreach ($prefs as $pref => $value) {
            $status = ($value == 1) ? '✅ Enabled' : '❌ Disabled';
            echo "<tr><td>$pref</td><td>$value</td><td>$status</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No user preferences found for User ID: $testUserId</p>";
    }
    
    // 3. Test the unified messaging system integration
    echo "<h2>🔗 Integration Test Results</h2>";
    
    // Test canUserSendNotifications method
    $canSend = $unifiedModel->canUserSendNotifications($testUserId);
    echo "<p><strong>Can User Send Notifications:</strong> " . ($canSend ? '✅ YES' : '❌ NO') . "</p>";
    
    // Show what notification types would be sent
    echo "<h3>📤 Notification Types That Would Be Sent</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Type</th><th>Global Setting</th><th>User Preference</th><th>Will Send?</th></tr>";
    
    $types = [
        'email' => ['global' => 'email_enabled', 'user' => 'email_notifications'],
        'sms' => ['global' => 'sms_enabled', 'user' => 'sms_notifications'],
        'push' => ['global' => 'push_enabled', 'user' => 'push_notifications'],
        'toast' => ['global' => 'toast_enabled', 'user' => 'toast_notifications']
    ];
    
    foreach ($types as $type => $keys) {
        $globalEnabled = ($globalSettings[$keys['global']] ?? false) == 1;
        $userEnabled = ($userPrefs->{$keys['user']} ?? false) == 1;
        $willSend = $globalEnabled && $userEnabled;
        
        $globalStatus = $globalEnabled ? '✅' : '❌';
        $userStatus = $userEnabled ? '✅' : '❌';
        $sendStatus = $willSend ? '✅ YES' : '❌ NO';
        
        echo "<tr><td>$type</td><td>$globalStatus</td><td>$userStatus</td><td>$sendStatus</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ Integration Test Complete</h2>";
    echo "<p><strong>Summary:</strong> The unified messaging system is now properly checking both global notification settings and user preferences before sending messages.</p>";
    
    // Test recommendations
    echo "<h3>💡 Test Recommendations</h3>";
    echo "<ul>";
    echo "<li>Try disabling a notification type globally and verify messages don't send via that method</li>";
    echo "<li>Try disabling a notification type for a specific user and verify it respects that preference</li>";
    echo "<li>Test with different user IDs to verify per-user preferences work correctly</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error During Testing</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
