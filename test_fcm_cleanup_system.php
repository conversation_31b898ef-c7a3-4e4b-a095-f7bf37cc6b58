<?php
/**
 * Test FCM Token Cleanup System
 * 
 * This script tests the automatic cleanup functionality
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationModel.php';

echo "<h1>🧪 Test FCM Token Cleanup System</h1>";

try {
    $notificationModel = new NotificationModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>📊 Initial State</h2>";
    
    // Get current token counts
    $db->query("SELECT 
                    COUNT(*) as total,
                    SUM(active) as active,
                    COUNT(*) - SUM(active) as inactive
                FROM fcm_tokens");
    $initialStats = $db->single();
    
    echo "<p><strong>Initial tokens:</strong> {$initialStats->total} total, {$initialStats->active} active, {$initialStats->inactive} inactive</p>";
    
    echo "<h2>🧪 Creating Test Inactive Tokens</h2>";
    
    // Create some old inactive tokens for testing
    $testTokens = [
        ['token' => 'test_old_token_1_' . time(), 'days_old' => 35],
        ['token' => 'test_old_token_2_' . time(), 'days_old' => 45],
        ['token' => 'test_old_token_3_' . time(), 'days_old' => 95],
    ];
    
    foreach ($testTokens as $testToken) {
        $db->query("INSERT INTO fcm_tokens 
                    (user_id, token, user_agent, active, created_at, updated_at, last_used) 
                    VALUES (:user_id, :token, 'Test User Agent', 0, 
                            DATE_SUB(NOW(), INTERVAL :days DAY),
                            DATE_SUB(NOW(), INTERVAL :days DAY),
                            DATE_SUB(NOW(), INTERVAL :days DAY))");
        $db->bind(':user_id', $userId);
        $db->bind(':token', $testToken['token']);
        $db->bind(':days', $testToken['days_old']);
        
        if ($db->execute()) {
            echo "<p>✅ Created test inactive token ({$testToken['days_old']} days old)</p>";
        }
    }
    
    echo "<h2>📊 After Creating Test Tokens</h2>";
    
    // Get updated counts
    $db->query("SELECT 
                    COUNT(*) as total,
                    SUM(active) as active,
                    COUNT(*) - SUM(active) as inactive
                FROM fcm_tokens");
    $afterCreateStats = $db->single();
    
    echo "<p><strong>After creating test tokens:</strong> {$afterCreateStats->total} total, {$afterCreateStats->active} active, {$afterCreateStats->inactive} inactive</p>";
    
    echo "<h2>🧹 Testing Manual Cleanup</h2>";
    
    // Test cleanup with different timeframes
    $cleanupTests = [
        ['days' => 30, 'description' => '30+ day old tokens'],
        ['days' => 90, 'description' => '90+ day old tokens'],
        ['days' => 7, 'description' => '7+ day old tokens']
    ];
    
    foreach ($cleanupTests as $test) {
        echo "<h3>🗑️ Cleaning {$test['description']}</h3>";
        
        $cleaned = $notificationModel->cleanupInactiveTokens($test['days']);
        echo "<p>Cleaned: {$cleaned} tokens</p>";
        
        // Show current state
        $db->query("SELECT 
                        COUNT(*) as total,
                        SUM(active) as active,
                        COUNT(*) - SUM(active) as inactive
                    FROM fcm_tokens");
        $currentStats = $db->single();
        
        echo "<p>Current: {$currentStats->total} total, {$currentStats->active} active, {$currentStats->inactive} inactive</p>";
    }
    
    echo "<h2>🤖 Testing Automatic Cleanup During Registration</h2>";
    
    // Test the automatic cleanup that happens during token registration
    echo "<p>Registering new tokens to trigger automatic cleanup (10% chance per registration)...</p>";
    
    $cleanupTriggered = false;
    $attempts = 0;
    
    // Try up to 20 times to trigger the automatic cleanup
    while (!$cleanupTriggered && $attempts < 20) {
        $attempts++;
        
        // Create a test token to trigger potential cleanup
        $testToken = 'auto_cleanup_test_' . time() . '_' . $attempts;
        $result = $notificationModel->saveFCMToken($userId, $testToken, 'Auto Cleanup Test Agent');
        
        if ($result) {
            echo "<p>Attempt {$attempts}: Registered token (ID: {$result})</p>";
            
            // Check if any cleanup happened by comparing token counts
            $db->query("SELECT COUNT(*) as total FROM fcm_tokens");
            $newCount = $db->single();
            
            if ($newCount->total < $afterCreateStats->total) {
                $cleanupTriggered = true;
                echo "<p style='color: green;'>✅ Automatic cleanup triggered! Token count decreased.</p>";
            }
        }
        
        // Small delay
        usleep(100000); // 0.1 second
    }
    
    if (!$cleanupTriggered) {
        echo "<p style='color: orange;'>⚠️ Automatic cleanup not triggered in {$attempts} attempts (this is normal - it's only a 10% chance)</p>";
    }
    
    echo "<h2>📊 Final Statistics</h2>";
    
    // Get final counts
    $db->query("SELECT 
                    COUNT(*) as total,
                    SUM(active) as active,
                    COUNT(*) - SUM(active) as inactive
                FROM fcm_tokens");
    $finalStats = $db->single();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Stage</th><th>Total</th><th>Active</th><th>Inactive</th></tr>";
    echo "<tr><td>Initial</td><td>{$initialStats->total}</td><td>{$initialStats->active}</td><td>{$initialStats->inactive}</td></tr>";
    echo "<tr><td>After Test Creation</td><td>{$afterCreateStats->total}</td><td>{$afterCreateStats->active}</td><td>{$afterCreateStats->inactive}</td></tr>";
    echo "<tr><td>Final</td><td>{$finalStats->total}</td><td>{$finalStats->active}</td><td>{$finalStats->inactive}</td></tr>";
    echo "</table>";
    
    $totalCleaned = ($afterCreateStats->total - $finalStats->total) + count($testTokens);
    echo "<p><strong>Total tokens cleaned during test:</strong> {$totalCleaned}</p>";
    
    echo "<h2>✅ Cleanup System Features</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🤖 Automatic Cleanup</h3>";
    echo "<ul>";
    echo "<li><strong>During registration:</strong> 10% chance on each new token registration</li>";
    echo "<li><strong>Removes tokens:</strong> Inactive for 30+ days by default</li>";
    echo "<li><strong>Safe operation:</strong> Only removes inactive tokens, never active ones</li>";
    echo "</ul>";
    
    echo "<h3>🛠️ Manual Cleanup Tools</h3>";
    echo "<ul>";
    echo "<li><strong>Admin tool:</strong> <code>admin_cleanup_fcm_tokens.php</code></li>";
    echo "<li><strong>Cron job:</strong> <code>cron/cleanup_fcm_tokens.php</code></li>";
    echo "<li><strong>API method:</strong> <code>NotificationModel::cleanupInactiveTokens()</code></li>";
    echo "</ul>";
    
    echo "<h3>🎯 Cleanup Benefits</h3>";
    echo "<ul>";
    echo "<li>🚀 Faster FCM token queries</li>";
    echo "<li>💾 Reduced database size</li>";
    echo "<li>🔧 Easier debugging and monitoring</li>";
    echo "<li>🛡️ Improved security (removes old tokens)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📅 Recommended Setup</h2>";
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>For production, set up a daily cron job:</strong></p>";
    echo "<code>0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_fcm_tokens.php</code>";
    echo "<p>This will run daily at 2 AM and clean up:</p>";
    echo "<ul>";
    echo "<li>Inactive tokens older than 7, 30, and 90 days</li>";
    echo "<li>Orphaned tokens (users no longer exist)</li>";
    echo "<li>Duplicate tokens</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
