-- Unified Message System Migration
-- This creates a clean, unified message system that eliminates duplication
-- and provides a single source of truth for all communications

-- Step 1: Create the unified messages table
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_user_id` int(10) UNSIGNED NOT NULL,
  `to_user_id` int(10) UNSIGNED NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `show_id` int(11) DEFAULT NULL COMMENT 'Related show if applicable',
  `parent_message_id` int(11) DEFAULT NULL COMMENT 'For reply threading',
  `message_type` enum('direct','system','judging','event','admin','notification') NOT NULL DEFAULT 'direct',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `requires_reply` tinyint(1) DEFAULT 0,
  `allows_reply` tinyint(1) DEFAULT 1,
  `reply_used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  `replied_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_to_user` (`to_user_id`),
  KEY `idx_from_user` (`from_user_id`),
  KEY `idx_show` (`show_id`),
  KEY `idx_parent` (`parent_message_id`),
  KEY `idx_unread` (`to_user_id`, `is_read`),
  KEY `idx_created` (`created_at` DESC),
  KEY `idx_type` (`message_type`),
  FOREIGN KEY (`from_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`to_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_message_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 2: Create delivery tracking table
CREATE TABLE IF NOT EXISTS `message_deliveries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `delivery_method` enum('email','sms','push','toast','in_app') NOT NULL,
  `status` enum('pending','sent','failed','cancelled') DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `attempts` int(11) DEFAULT 0,
  `last_attempt` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message` (`message_id`),
  KEY `idx_status` (`status`),
  KEY `idx_method` (`delivery_method`),
  KEY `idx_pending` (`status`, `delivery_method`, `created_at`),
  FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 3: Migrate existing data from user_messages if it exists
INSERT IGNORE INTO `messages` 
(from_user_id, to_user_id, subject, message, show_id, parent_message_id, message_type, is_read, requires_reply, allows_reply, reply_used, created_at, read_at)
SELECT 
    from_user_id, 
    to_user_id, 
    subject, 
    message, 
    show_id, 
    parent_message_id,
    'direct' as message_type,
    COALESCE(is_read, 0),
    COALESCE(requires_reply, 0),
    COALESCE(allows_reply, 1),
    COALESCE(reply_used, 0),
    created_at,
    read_at
FROM user_messages 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'user_messages');

-- Step 4: Migrate push notifications as system messages
INSERT IGNORE INTO `messages`
(from_user_id, to_user_id, subject, message, message_type, is_read, created_at)
SELECT 
    1 as from_user_id, -- System user
    user_id as to_user_id,
    title as subject,
    message,
    'notification' as message_type,
    COALESCE(is_read, 0),
    created_at
FROM user_push_notifications 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'user_push_notifications');

-- Step 5: Migrate toast notifications as system messages
INSERT IGNORE INTO `messages`
(from_user_id, to_user_id, subject, message, message_type, is_read, created_at)
SELECT 
    1 as from_user_id, -- System user
    user_id as to_user_id,
    title as subject,
    message,
    'notification' as message_type,
    COALESCE(is_read, 0),
    created_at
FROM user_toast_notifications 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'user_toast_notifications');

-- Step 6: Create system user if it doesn't exist
INSERT IGNORE INTO `users` (id, name, email, password, role, created_at)
VALUES (1, 'System', 'system@localhost', '', 'system', NOW());

-- Step 7: Ensure user_notification_preferences table has the columns we need (they should already exist)
-- This step is mostly for safety - the table should already be correct

-- Step 8: Insert default notification preferences for users who don't have them
INSERT IGNORE INTO `user_notification_preferences` (user_id, email_notifications, sms_notifications, push_notifications, toast_notifications)
SELECT id, 1, 0, 1, 1 FROM users 
WHERE id NOT IN (SELECT user_id FROM user_notification_preferences WHERE user_id IS NOT NULL);

-- Step 9: Create indexes for performance
CREATE INDEX IF NOT EXISTS `idx_messages_user_unread` ON `messages` (`to_user_id`, `is_read`, `created_at` DESC);
CREATE INDEX IF NOT EXISTS `idx_messages_conversation` ON `messages` (`from_user_id`, `to_user_id`, `created_at` DESC);
CREATE INDEX IF NOT EXISTS `idx_deliveries_pending` ON `message_deliveries` (`status`, `delivery_method`, `created_at`);

-- Step 10: Create view for backward compatibility
CREATE OR REPLACE VIEW `notification_center_view` AS
SELECT 
    m.id,
    m.to_user_id as user_id,
    CASE 
        WHEN m.message_type = 'direct' THEN 'message'
        WHEN m.message_type = 'notification' THEN 'system'
        ELSE m.message_type
    END as notification_type,
    'messages' as source_table,
    m.id as source_id,
    m.subject as title,
    m.message,
    CONCAT('/notification_center/viewMessage/', m.id) as action_url,
    CASE WHEN m.requires_reply = 1 THEN 'Reply' ELSE 'View' END as action_text,
    JSON_OBJECT('show_id', m.show_id, 'from_user_id', m.from_user_id) as metadata,
    m.is_read,
    0 as is_archived,
    m.created_at,
    m.read_at
FROM messages m
WHERE m.is_archived = 0;