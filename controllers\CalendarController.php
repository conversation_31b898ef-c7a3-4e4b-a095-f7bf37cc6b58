<?php
/**
 * Calendar Controller
 * 
 * This controller handles all calendar-related functionality.
 * 
 * Version 1.0.4 - Added map view pagination with pin navigation
 * - Added pagination support for map view with configurable page sizes
 * - Implemented toggle between 'all pins' and 'current page pins' modes
 * - Added pin click navigation to jump to correct page and highlight event
 * - Enhanced event list with pagination controls and smooth scrolling
 * - Improved user experience with event highlighting and page navigation
 * 
 * Version 1.0.3 - Fixed geocoding issues with consistent implementation
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 * - Fixed geocoding to use enhanced method with fallbacks
 * - Improved geocoding consistency between batch and individual operations
 */
class CalendarController extends Controller {
    private $calendarModel;
    private $showModel;
    private $userModel;
    private $settingsModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Get the current method being called
        $currentMethod = $this->getCurrentMethod();
        
        // Methods that don't require login (guest accessible)
        $guestMethods = ['index', 'event', 'getEvents', 'getUpcomingEvents', 'getStates', 'getCities', 'getClubs', 'getVenues', 'getCategories', 'getTags'];
        
        // Check if user is logged in (skip for guest methods)
        if (!in_array($currentMethod, $guestMethods) && !isLoggedIn()) {
            redirect('auth/login');
        }
        
        // Initialize models
        $this->calendarModel = $this->model('CalendarModel');
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        $this->settingsModel = $this->model('SettingsModel');
        
        // Check if calendar tables exist (only for logged-in users)
        if (isLoggedIn() && !$this->calendarModel->tablesExist()) {
            // Create tables
            if (!$this->calendarModel->createTables()) {
                die('Error creating calendar tables');
            }
            
            // Create default calendar
            $defaultCalendar = [
                'name' => 'Main Calendar',
                'description' => 'Default calendar for all events',
                'color' => '#3788d8',
                'is_visible' => 1,
                'is_public' => 1,
                'owner_id' => $_SESSION['user_id']
            ];
            
            $calendarId = $this->calendarModel->createCalendar($defaultCalendar);
            
            // Sync with existing shows
            if ($calendarId) {
                $this->calendarModel->syncEventsWithShows($calendarId);
            }
        }
    }
    
    /**
     * Get the current method being called
     * 
     * @return string
     */
    private function getCurrentMethod() {
        // Get the URL segments
        $url = isset($_GET['url']) ? $_GET['url'] : '';
        $segments = explode('/', trim($url, '/'));
        
        // Return the method (second segment after controller)
        $method = isset($segments[1]) ? $segments[1] : 'index';
        
        // Debug logging if enabled
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("CalendarController::getCurrentMethod - URL: $url, Method: $method");
        }
        
        return $method;
    }
    
    /**
     * Calendar index page
     * 
     * @return void
     */
    public function index() {
        // Get calendars - for guests, get public calendars only
        if (isLoggedIn()) {
            $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
        } else {
            $calendars = $this->calendarModel->getPublicCalendars();
        }
        
        // Get calendar settings
        $settings = $this->calendarModel->getCalendarDisplaySettings();
        
        // Get map settings for geocoding functionality
        $mapSettings = $this->calendarModel->getMapProviderSettings();
        
        $data = [
            'title' => 'Calendar',
            'calendars' => $calendars,
            'settings' => $settings,
            'mapSettings' => $mapSettings
        ];
        
        // Use fixed custom calendar view instead of the FullCalendar implementation
        $this->view('calendar/custom_index_fixed', $data);
    }
    
    /**
     * Get events as JSON for AJAX requests
     * 
     * @return void
     */
    public function getEvents() {
        // Set content type to JSON first to ensure proper response format
        header('Content-Type: application/json');
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('=== CalendarController::getEvents called at ' . gmdate('Y-m-d H:i:s') . ' ===');
            error_log('Raw GET parameters: ' . json_encode($_GET));
            error_log('User logged in: ' . (isLoggedIn() ? 'Yes (ID: ' . $_SESSION['user_id'] . ')' : 'No (Guest)'));
        }
        
        // Skip AJAX check for direct API calls from fetch()
        // Modern fetch() API doesn't always set X-Requested-With header
        
        // Get filter parameters
        $start = isset($_GET['start']) ? $_GET['start'] : null;
        $end = isset($_GET['end']) ? $_GET['end'] : null;
        $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : null;
        $state = isset($_GET['state']) ? $_GET['state'] : null;
        $city = isset($_GET['city']) ? $_GET['city'] : null;
        $venueId = isset($_GET['venue_id']) ? $_GET['venue_id'] : null;
        $clubId = isset($_GET['club_id']) ? $_GET['club_id'] : null;
        $keyword = isset($_GET['keyword']) ? $_GET['keyword'] : null;
        $radius = isset($_GET['radius']) ? (int)$_GET['radius'] : null;
        $lat = isset($_GET['lat']) ? (float)$_GET['lat'] : null;
        $lng = isset($_GET['lng']) ? (float)$_GET['lng'] : null;
        $showId = isset($_GET['show_id']) ? $_GET['show_id'] : null;
        $category = isset($_GET['category']) ? $_GET['category'] : null;
        $tag = isset($_GET['tag']) ? $_GET['tag'] : null;
        $price = isset($_GET['price']) ? $_GET['price'] : null;
        $priceRange = isset($_GET['price_range']) ? $_GET['price_range'] : null;
        
        $filters = [];
        
        if ($start) {
            $filters['start_date'] = $start;
        }
        
        if ($end) {
            $filters['end_date'] = $end;
        }
        
        // Handle calendar filter
        if ($calendarId !== null) {
            // Check if it's an empty string (no calendars selected)
            if ($calendarId === '') {
                // Only return empty array if no other filters are applied
                // This allows keyword search and other filters to work without calendar selection
                if (empty($keyword) && empty($state) && empty($city) && empty($venueId) && 
                    empty($clubId) && empty($category) && empty($tag) && empty($priceRange) &&
                    empty($lat) && empty($lng)) {
                    echo json_encode([]);
                    return;
                }
                // If other filters are present, continue without calendar filter
            } else {
                // Handle multiple calendar IDs (comma-separated)
                if (strpos($calendarId, ',') !== false) {
                    $calendarIds = explode(',', $calendarId);
                    // Filter out any empty values
                    $calendarIds = array_filter($calendarIds, function($id) {
                        return !empty($id);
                    });
                    
                    // If after filtering we have no calendar IDs, only return empty if no other filters
                    if (empty($calendarIds)) {
                        if (empty($keyword) && empty($state) && empty($city) && empty($venueId) && 
                            empty($clubId) && empty($category) && empty($tag) && empty($priceRange) &&
                            empty($lat) && empty($lng)) {
                            echo json_encode([]);
                            return;
                        }
                        // If other filters are present, continue without calendar filter
                    } else {
                        $filters['calendar_ids'] = $calendarIds;
                    }
                } else {
                    $filters['calendar_id'] = $calendarId;
                }
            }
        }
        
        // Add state filter
        if ($state) {
            $filters['state'] = $state;
        }
        
        // Add city filter
        if ($city) {
            $filters['city'] = $city;
        }
        
        // Add venue filter
        if ($venueId) {
            // Check if multiple venue IDs are provided
            if (strpos($venueId, ',') !== false) {
                $venueIds = array_map('trim', explode(',', $venueId));
                // Filter out any empty values
                $venueIds = array_filter($venueIds, function($id) {
                    return !empty($id);
                });
                $filters['venue_ids'] = array_values($venueIds);
            } else {
                // Single venue ID
                $filters['venue_id'] = $venueId;
            }
        }
        
        // Add club filter
        if ($clubId) {
            // Check if multiple club IDs are provided
            if (strpos($clubId, ',') !== false) {
                $clubIds = array_map('trim', explode(',', $clubId));
                // Filter out any empty values
                $clubIds = array_filter($clubIds, function($id) {
                    return !empty($id);
                });
                $filters['club_ids'] = array_values($clubIds);
            } else {
                // Single club ID
                $filters['club_id'] = $clubId;
            }
        }
        
        // Add keyword filter
        if ($keyword) {
            $filters['keyword'] = $keyword;
        }
        
        // Location-based filtering
        if ($radius && $lat && $lng) {
            $filters['radius'] = $radius;
            $filters['center_lat'] = $lat;
            $filters['center_lng'] = $lng;
        }
        
        // Add show filter
        if ($showId) {
            $filters['show_id'] = $showId;
        }
        
        // Add category filter
        if ($category) {
            // Handle multiple categories (comma-separated)
            if (strpos($category, ',') !== false) {
                $categories = explode(',', $category);
                $filters['categories'] = $categories;
            } else {
                $filters['category'] = $category;
            }
        }
        
        // Add tag filter
        if ($tag) {
            // Handle multiple tags (comma-separated)
            if (strpos($tag, ',') !== false) {
                $tags = explode(',', $tag);
                $filters['tags'] = $tags;
            } else {
                $filters['tag'] = $tag;
            }
        }
        
        // Add price filter
        if ($price) {
            $filters['price'] = $price;
        }
        
        // Add price range filter
        if ($priceRange) {
            // Format should be min-max, e.g., "0-100"
            $parts = explode('-', $priceRange);
            if (count($parts) === 2) {
                $filters['price_min'] = (float)$parts[0];
                $filters['price_max'] = (float)$parts[1];
            }
        }
        
        try {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('CalendarController::getEvents - Raw parameters received:');
                error_log('  calendar_id: ' . ($calendarId ?? 'null'));
                error_log('  keyword: ' . ($keyword ?? 'null'));
                error_log('  state: ' . ($state ?? 'null'));
                error_log('  city: ' . ($city ?? 'null'));
                error_log('CalendarController::getEvents - Applying filters: ' . json_encode($filters));
            }
            
            // For guests, validate calendar access before querying
            if (!isLoggedIn() && isset($filters['calendar_id'])) {
                $this->db->query('SELECT id FROM calendars WHERE id = :id AND is_public = 1');
                $this->db->bind(':id', $filters['calendar_id']);
                if (!$this->db->single()) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('CalendarController::getEvents - Guest trying to access non-public calendar: ' . $filters['calendar_id']);
                    }
                    echo json_encode([]);
                    return;
                }
            }
            
            // Get events - pass user ID if logged in, null for guests
            $userId = isLoggedIn() ? $_SESSION['user_id'] : null;

            // Debug user ID and session
            if (defined('DEBUG_MODE') && DEBUG_MODE && $userId) {
                error_log("CALENDAR DEBUG - Session User ID: {$userId}");
                error_log("CALENDAR DEBUG - isLoggedIn(): " . (isLoggedIn() ? 'true' : 'false'));
            }

            $events = $this->calendarModel->getEvents($filters, $userId);
            
            // Format events for custom calendar
            $formattedEvents = [];
            
            foreach ($events as $event) {
                // Database stores correct UTC event times, but database server time is unreliable
                // Convert stored UTC event times to user timezone without using server time functions
                if (isLoggedIn()) {
                    // For logged-in users, convert to their saved timezone preference
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CALENDAR DEBUG - About to call getUserTimezone with userId: {$userId}");
                    }
                    $userTimezone = getUserTimezone($userId);
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CALENDAR DEBUG - getUserTimezone returned: {$userTimezone}");
                    }

                    // Do timezone conversion directly to match event detail page logic
                    try {
                        $startDate = new DateTime($event->start_date, new DateTimeZone('UTC'));
                        $startDate->setTimezone(new DateTimeZone($userTimezone));
                        $startTime = $startDate->format('c');

                        $endDate = new DateTime($event->end_date, new DateTimeZone('UTC'));
                        $endDate->setTimezone(new DateTimeZone($userTimezone));
                        $endTime = $endDate->format('c');

                        // Debug the conversion
                        if (defined('DEBUG_MODE') && DEBUG_MODE && $event->id == 21) {
                            error_log("CALENDAR DEBUG - Event 21 conversion:");
                            error_log("CALENDAR DEBUG - Original UTC: {$event->start_date}");
                            error_log("CALENDAR DEBUG - User timezone: {$userTimezone}");
                            error_log("CALENDAR DEBUG - Converted time: {$startTime}");
                        }
                    } catch (Exception $e) {
                        // Fallback - just use the stored times as-is
                        $startTime = $event->start_date;
                        $endTime = $event->end_date;
                    }
                } else {
                    // For guests, convert to Eastern time using stored UTC times
                    try {
                        $startDate = new DateTime($event->start_date, new DateTimeZone('UTC'));
                        $startDate->setTimezone(new DateTimeZone('America/New_York'));
                        $startTime = $startDate->format('c');

                        $endDate = new DateTime($event->end_date, new DateTimeZone('UTC'));
                        $endDate->setTimezone(new DateTimeZone('America/New_York'));
                        $endTime = $endDate->format('c');

                        // Debug logging for guests
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("CALENDAR DEBUG - Guest User");
                            error_log("CALENDAR DEBUG - Original UTC: {$event->start_date}");
                            error_log("CALENDAR DEBUG - Converted Eastern: {$startTime}");
                        }
                    } catch (Exception $e) {
                        // Fallback - just use the stored times as-is
                        $startTime = $event->start_date;
                        $endTime = $event->end_date;
                    }
                }
                
                $formattedEvent = [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start' => $startTime,
                    'end' => $endTime,
                    'allDay' => (bool)$event->all_day,
                    'location' => $event->location,
                    'url' => URLROOT . '/calendar/event/' . $event->id,
                    'extendedProps' => [
                        'description' => $event->description,
                        'location' => $event->location,
                        'address1' => $event->address1,
                        'address2' => $event->address2,
                        'city' => $event->city,
                        'state' => $event->state,
                        'zipcode' => $event->zipcode,
                        'calendar_id' => $event->calendar_id,
                        'calendar_name' => $event->calendar_name,
                        'show_id' => $event->show_id,
                        'show_name' => $event->show_name,
                        'privacy' => $event->privacy,
                        'lat' => $event->lat,
                        'lng' => $event->lng,
                        'venue_id' => $event->venue_id,
                        'venue_name' => $event->venue_name,
                        'is_guest_view' => !isLoggedIn() // Flag to help frontend handle timezone
                    ]
                ];
                
                // Set event colors
                if (!empty($event->color)) {
                    $formattedEvent['backgroundColor'] = $event->color;
                    $formattedEvent['borderColor'] = $event->color;
                    $formattedEvent['color'] = $event->color;
                } else if (!empty($event->calendar_color)) {
                    $formattedEvent['backgroundColor'] = $event->calendar_color;
                    $formattedEvent['borderColor'] = $event->calendar_color;
                    $formattedEvent['color'] = $event->calendar_color;
                }
                
                $formattedEvents[] = $formattedEvent;
            }
            
            // Return JSON response
            echo json_encode($formattedEvents);
        } catch (Exception $e) {
            // Return error as JSON
            echo json_encode(['error' => 'Error loading events: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get upcoming events as JSON for AJAX requests
     * 
     * @return void
     */
    public function getUpcomingEvents() {
        // Set content type to JSON first to ensure proper response format
        header('Content-Type: application/json');
        
        // Skip AJAX check for direct API calls from fetch()
        // Modern fetch() API doesn't always set X-Requested-With header
        
        // Get filter parameters (handle timezone properly for guests vs logged-in users)
        if (isset($_GET['start'])) {
            $start = $_GET['start'];
        } else {
            // Default start date - use UTC for guests, user timezone for logged-in users
            if (isLoggedIn()) {
                $start = convertUTCToUserDateTime(gmdate('Y-m-d H:i:s'), $_SESSION['user_id'], 'Y-m-d H:i:s');
            } else {
                $start = gmdate('Y-m-d H:i:s'); // UTC for guests
            }
        }
        
        if (isset($_GET['end'])) {
            $end = $_GET['end'];
        } else {
            // Default end date - use UTC for guests, user timezone for logged-in users
            if (isLoggedIn()) {
                $end = convertUTCToUserDateTime(gmdate('Y-m-d H:i:s', strtotime('+30 days')), $_SESSION['user_id'], 'Y-m-d H:i:s');
            } else {
                $end = gmdate('Y-m-d H:i:s', strtotime('+30 days')); // UTC for guests
            }
        }
        $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : null;
        $state = isset($_GET['state']) ? $_GET['state'] : null;
        $city = isset($_GET['city']) ? $_GET['city'] : null;
        $venueId = isset($_GET['venue_id']) ? $_GET['venue_id'] : null;
        $clubId = isset($_GET['club_id']) ? $_GET['club_id'] : null;
        $keyword = isset($_GET['keyword']) ? $_GET['keyword'] : null;
        $radius = isset($_GET['radius']) ? (int)$_GET['radius'] : null;
        $lat = isset($_GET['lat']) ? (float)$_GET['lat'] : null;
        $lng = isset($_GET['lng']) ? (float)$_GET['lng'] : null;
        $showId = isset($_GET['show_id']) ? $_GET['show_id'] : null;
        $category = isset($_GET['category']) ? $_GET['category'] : null;
        $tag = isset($_GET['tag']) ? $_GET['tag'] : null;
        $price = isset($_GET['price']) ? $_GET['price'] : null;
        $priceRange = isset($_GET['price_range']) ? $_GET['price_range'] : null;
        
        $filters = [
            'start_date' => $start,
            'end_date' => $end,
            'limit' => 10,
            'order_by' => 'start_date ASC'
        ];
        
        // Handle calendar filter
        if ($calendarId !== null) {
            // Check if it's an empty string (no calendars selected)
            if ($calendarId === '') {
                // Only return empty array if no other filters are applied
                // This allows keyword search and other filters to work without calendar selection
                if (empty($keyword) && empty($state) && empty($city) && empty($venueId) && 
                    empty($clubId) && empty($category) && empty($tag) && empty($priceRange) &&
                    empty($lat) && empty($lng)) {
                    echo json_encode([]);
                    return;
                }
                // If other filters are present, continue without calendar filter
            } else {
                // Handle multiple calendar IDs (comma-separated)
                if (strpos($calendarId, ',') !== false) {
                    $calendarIds = explode(',', $calendarId);
                    // Filter out any empty values
                    $calendarIds = array_filter($calendarIds, function($id) {
                        return !empty($id);
                    });
                    
                    // If after filtering we have no calendar IDs, only return empty if no other filters
                    if (empty($calendarIds)) {
                        if (empty($keyword) && empty($state) && empty($city) && empty($venueId) && 
                            empty($clubId) && empty($category) && empty($tag) && empty($priceRange) &&
                            empty($lat) && empty($lng)) {
                            echo json_encode([]);
                            return;
                        }
                        // If other filters are present, continue without calendar filter
                    } else {
                        $filters['calendar_ids'] = $calendarIds;
                    }
                } else {
                    $filters['calendar_id'] = $calendarId;
                }
            }
        }
        
        // Add state filter
        if ($state) {
            $filters['state'] = $state;
        }
        
        // Add city filter
        if ($city) {
            $filters['city'] = $city;
        }
        
        // Add venue filter
        if ($venueId) {
            // Check if multiple venue IDs are provided
            if (strpos($venueId, ',') !== false) {
                $venueIds = array_map('trim', explode(',', $venueId));
                // Filter out any empty values
                $venueIds = array_filter($venueIds, function($id) {
                    return !empty($id);
                });
                $filters['venue_ids'] = array_values($venueIds);
            } else {
                // Single venue ID
                $filters['venue_id'] = $venueId;
            }
        }
        
        // Add club filter
        if ($clubId) {
            // Check if multiple club IDs are provided
            if (strpos($clubId, ',') !== false) {
                $clubIds = array_map('trim', explode(',', $clubId));
                // Filter out any empty values
                $clubIds = array_filter($clubIds, function($id) {
                    return !empty($id);
                });
                $filters['club_ids'] = array_values($clubIds);
            } else {
                // Single club ID
                $filters['club_id'] = $clubId;
            }
        }
        
        // Add keyword filter
        if ($keyword) {
            $filters['keyword'] = $keyword;
        }
        
        // Location-based filtering
        if ($radius && $lat && $lng) {
            $filters['radius'] = $radius;
            $filters['lat'] = $lat;
            $filters['lng'] = $lng;
        }
        
        // Add show filter
        if ($showId) {
            $filters['show_id'] = $showId;
        }
        
        // Add category filter
        if ($category) {
            // Handle multiple categories (comma-separated)
            if (strpos($category, ',') !== false) {
                $categories = explode(',', $category);
                $filters['categories'] = $categories;
            } else {
                $filters['category'] = $category;
            }
        }
        
        // Add tag filter
        if ($tag) {
            // Handle multiple tags (comma-separated)
            if (strpos($tag, ',') !== false) {
                $tags = explode(',', $tag);
                $filters['tags'] = $tags;
            } else {
                $filters['tag'] = $tag;
            }
        }
        
        // Add price filter
        if ($price) {
            $filters['price'] = $price;
        }
        
        // Add price range filter
        if ($priceRange) {
            // Format should be min-max, e.g., "0-100"
            $parts = explode('-', $priceRange);
            if (count($parts) === 2) {
                $filters['price_min'] = (float)$parts[0];
                $filters['price_max'] = (float)$parts[1];
            }
        }
        
        try {
            // Get events - pass user ID if logged in, null for guests
            $userId = isLoggedIn() ? $_SESSION['user_id'] : null;
            $events = $this->calendarModel->getEvents($filters, $userId);
            
            // Format events
            $formattedEvents = [];
            
            foreach ($events as $event) {
                // Database stores correct UTC event times, but database server time is unreliable
                // Convert stored UTC event times to user timezone without using server time functions
                if (isLoggedIn()) {
                    // For logged-in users, convert to their saved timezone preference
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("UPCOMING EVENTS DEBUG - About to call getUserTimezone with userId: {$userId}");
                    }
                    $userTimezone = getUserTimezone($userId);
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("UPCOMING EVENTS DEBUG - getUserTimezone returned: {$userTimezone}");
                    }

                    // Do timezone conversion directly to match event detail page logic
                    try {
                        $startDate = new DateTime($event->start_date, new DateTimeZone('UTC'));
                        $startDate->setTimezone(new DateTimeZone($userTimezone));
                        $startTime = $startDate->format('c');

                        $endDate = new DateTime($event->end_date, new DateTimeZone('UTC'));
                        $endDate->setTimezone(new DateTimeZone($userTimezone));
                        $endTime = $endDate->format('c');
                    } catch (Exception $e) {
                        // Fallback - just use the stored times as-is
                        $startTime = $event->start_date;
                        $endTime = $event->end_date;
                    }
                } else {
                    // For guests, convert to Eastern time using stored UTC times
                    try {
                        $startDate = new DateTime($event->start_date, new DateTimeZone('UTC'));
                        $startDate->setTimezone(new DateTimeZone('America/New_York'));
                        $startTime = $startDate->format('c');

                        $endDate = new DateTime($event->end_date, new DateTimeZone('UTC'));
                        $endDate->setTimezone(new DateTimeZone('America/New_York'));
                        $endTime = $endDate->format('c');
                    } catch (Exception $e) {
                        // Fallback - just use the stored times as-is
                        $startTime = $event->start_date;
                        $endTime = $event->end_date;
                    }
                }
                
                $formattedEvent = [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start' => $startTime,
                    'end' => $endTime,
                    'allDay' => (bool)$event->all_day,
                    'location' => $event->location,
                    'calendar_name' => $event->calendar_name,
                    'show_name' => $event->show_name,
                    'is_guest_view' => !isLoggedIn() // Flag to help frontend handle timezone
                ];
                
                $formattedEvents[] = $formattedEvent;
            }
            
            // Return JSON response
            echo json_encode($formattedEvents);
        } catch (Exception $e) {
            // Return error as JSON
            echo json_encode(['error' => 'Error loading upcoming events: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Update event dates via AJAX
     * 
     * @return void
     */
    public function updateEventDates() {
        // Check for AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        // Validate CSRF token
        if (!validateCsrfToken()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
            return;
        }
        
        // Get POST data
        $id = isset($_POST['id']) ? filter_var($_POST['id'], FILTER_SANITIZE_NUMBER_INT) : null;
        $startDate = isset($_POST['start_date']) ? $_POST['start_date'] : null;
        $endDate = isset($_POST['end_date']) ? $_POST['end_date'] : null;
        
        // Validate data
        if (!$id || !$startDate || !$endDate) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
            return;
        }
        
        // Get event
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Event not found']);
            return;
        }
        
        // Check if user has permission to edit this event
        // Admins can edit any event, otherwise check if user created it or has calendar permission
        if ($_SESSION['user_role'] !== 'admin' && 
            $event->created_by != $_SESSION['user_id'] && 
            !$this->calendarModel->userHasCalendarPermission($event->calendar_id, $_SESSION['user_id'], 'edit')) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'You do not have permission to edit this event']);
            return;
        }
        
        // Update event dates
        $data = [
            'id' => $id,
            'start_date' => convertUserDateTimeToUTC($startDate, $_SESSION['user_id']),
            'end_date' => convertUserDateTimeToUTC($endDate, $_SESSION['user_id'])
        ];
        
        if ($this->calendarModel->updateEventDates($data)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true]);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to update event dates']);
        }
    }
    
    /**
     * View event details
     * 
     * @param int $id Event ID
     * @return void
     */
    public function event($id) {
        // Get event details
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            // For guests, redirect to home instead of calendar
            if (!isLoggedIn()) {
                flash('message', 'Event not found', 'alert alert-danger');
                redirect('');
            } else {
                flash('calendar_message', 'Event not found', 'alert alert-danger');
                redirect('calendar');
            }
        }
        
        // Check if user has permission to view this event
        if ($event->privacy == 'draft') {
            // Draft events are only visible to the creator
            if (!isLoggedIn() || $event->created_by != $_SESSION['user_id']) {
                if (!isLoggedIn()) {
                    flash('message', 'Event not found', 'alert alert-danger');
                    redirect('');
                } else {
                    flash('calendar_message', 'Event not found', 'alert alert-danger');
                    redirect('calendar');
                }
            }
        }
        // Public events are accessible to everyone (no additional checks needed)
        
        // Get venue details if applicable
        $venue = null;
        if ($event->venue_id) {
            $venue = $this->calendarModel->getVenueById($event->venue_id);
        }
        
        // Convert event times to appropriate timezone - same logic as calendar AJAX endpoints
        // Database stores correct UTC event times, but database server time is unreliable
        if (isLoggedIn()) {
            // For logged-in users, convert to their saved timezone preference
            $userId = $_SESSION['user_id'];
            $userTimezone = getUserTimezone($userId);

            // Do timezone conversion directly to match calendar logic
            try {
                $startDate = new DateTime($event->start_date, new DateTimeZone('UTC'));
                $startDate->setTimezone(new DateTimeZone($userTimezone));
                $event->display_start_date = $startDate->format('c');

                $endDate = new DateTime($event->end_date, new DateTimeZone('UTC'));
                $endDate->setTimezone(new DateTimeZone($userTimezone));
                $event->display_end_date = $endDate->format('c');
            } catch (Exception $e) {
                // Fallback - just use the stored times as-is
                $event->display_start_date = $event->start_date;
                $event->display_end_date = $event->end_date;
            }

            $event->display_timezone = $userTimezone;
        } else {
            // For guests, convert to Eastern time using stored UTC times
            try {
                $startDate = new DateTime($event->start_date, new DateTimeZone('UTC'));
                $startDate->setTimezone(new DateTimeZone('America/New_York'));
                $event->display_start_date = $startDate->format('c');

                $endDate = new DateTime($event->end_date, new DateTimeZone('UTC'));
                $endDate->setTimezone(new DateTimeZone('America/New_York'));
                $event->display_end_date = $endDate->format('c');

                $event->display_timezone = 'America/New_York';

                // Debug logging for guests
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("EVENT DETAIL DEBUG - Guest User");
                    error_log("EVENT DETAIL DEBUG - Original UTC: {$event->start_date}");
                    error_log("EVENT DETAIL DEBUG - Converted Eastern: {$event->display_start_date}");
                }
            } catch (Exception $e) {
                // Fallback - just use the stored times as-is
                $event->display_start_date = $event->start_date;
                $event->display_end_date = $event->end_date;
                $event->display_timezone = 'UTC';
            }
        }

        // Get show details if applicable
        $show = null;
        if ($event->show_id) {
            $show = $this->showModel->getShowById($event->show_id);
        }
        
        $data = [
            'title' => 'Event Details',
            'event' => $event,
            'venue' => $venue,
            'show' => $show
        ];
        
        $this->view('calendar/event', $data);
    }
    
    /**
     * Create new event
     * 
     * @return void
     */
    public function createEvent() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            flash('calendar_message', 'You must be logged in to create events', 'alert alert-danger');
            redirect('users/login');
            return;
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - but preserve description field for WYSIWYG content
            $description = $_POST['description'] ?? '';
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            // Restore the original description to preserve HTML content and base64 images
            $_POST['description'] = $description;
            
            // Process form
            $data = [
                'title' => trim($_POST['title']),
                'description' => $description, // Use the unsanitized description
                'start_date' => convertUserDateTimeToUTC($_POST['start_date'], $_SESSION['user_id']),
                'end_date' => convertUserDateTimeToUTC($_POST['end_date'], $_SESSION['user_id']),
                'all_day' => isset($_POST['all_day']) ? 1 : 0,
                'location' => trim($_POST['location']),
                'address1' => trim($_POST['address1'] ?? ''),
                'address2' => trim($_POST['address2'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zipcode' => trim($_POST['zipcode'] ?? ''),
                'lat' => !empty($_POST['lat']) ? $_POST['lat'] : null,
                'lng' => !empty($_POST['lng']) ? $_POST['lng'] : null,
                'venue_id' => !empty($_POST['venue_id']) ? $_POST['venue_id'] : null,
                'url' => trim($_POST['url']),
                'color' => trim($_POST['color']),
                'is_recurring' => isset($_POST['is_recurring']) ? 1 : 0,
                'recurrence_pattern' => isset($_POST['recurrence_pattern']) ? $_POST['recurrence_pattern'] : null,
                'recurrence_end_date' => !empty($_POST['recurrence_end_date']) ? convertUserDateTimeToUTC($_POST['recurrence_end_date'], $_SESSION['user_id']) : null,
                'privacy' => $_POST['privacy'],
                'calendar_id' => $_POST['calendar_id'],
                'show_id' => null, // Shows are now automatically linked to events when created
                'created_by' => $_SESSION['user_id'],
                'clubs' => isset($_POST['clubs']) ? $_POST['clubs'] : [],
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // Validate title
            if (empty($data['title'])) {
                $data['title_err'] = 'Please enter a title';
            }
            
            // Validate start date
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            // Validate end date
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif (!empty($data['start_date']) && !empty($data['end_date'])) {
                try {
                    $startDateTime = new DateTime($data['start_date']);
                    $endDateTime = new DateTime($data['end_date']);
                    
                    if ($endDateTime <= $startDateTime) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['end_date'] <= $data['start_date']) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CalendarController::createEvent - DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            // Validate calendar ID
            if (empty($data['calendar_id'])) {
                $data['calendar_id_err'] = 'Please select a calendar';
            }
            
            // Automatically geocode address if needed
            if ((!$data['lat'] || !$data['lng']) && 
                ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
                
                // Load geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Create address array for geocoding - EXACTLY like the admin batch tool
                // Only include address fields, not lat/lng
                $eventData = [
                    'address1' => $data['address1'] ?? '',
                    'address2' => $data['address2'] ?? '',
                    'city' => $data['city'] ?? '',
                    'state' => $data['state'] ?? '',
                    'zipcode' => $data['zipcode'] ?? ''
                ];
                
                // Use the enhanced geocodeEvent function
                $eventData = geocodeEvent($eventData, $mapSettings, 'createEvent');
                
                // If geocoding was successful, update the original data
                if (isset($eventData['lat']) && $eventData['lat'] && isset($eventData['lng']) && $eventData['lng']) {
                    $data['lat'] = $eventData['lat'];
                    $data['lng'] = $eventData['lng'];
                }
                
                // Log the geocoding attempt
                error_log("Geocoding attempt for new event: " . 
                (isset($data['lat']) && $data['lat'] && isset($data['lng']) && $data['lng'] ? "SUCCESS" : "FAILED") . 
                " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
                
                // If geocoding failed, add a warning message
                if (!isset($data['lat']) || !$data['lat'] || !isset($data['lng']) || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
                }
            }
            
            // Make sure there are no errors
            if (empty($data['title_err']) && empty($data['start_date_err']) && empty($data['end_date_err']) && empty($data['calendar_id_err'])) {
                // Create event
                if ($this->calendarModel->createEvent($data)) {
                    flash('calendar_message', 'Event created successfully');
                    redirect('calendar');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/create_event', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/create_event', $data);
            }
        } else {
            // Get calendars
            $allCalendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
            
            // Filter out "Shows - {state}" calendars
            $calendars = array_filter($allCalendars, function($calendar) {
                return !preg_match('/^Shows - [A-Z]{2}$/', $calendar->name);
            });
            
            // Get venues
            $venues = $this->calendarModel->getVenues();
            
            // Get clubs
            $clubs = $this->calendarModel->getClubs();
            
            // Shows are now automatically linked to events when created
            
            // Init data
            $data = [
                'title' => '',
                'description' => '',
                'start_date' => convertUTCToUserDateTime(date('Y-m-d H:i:s'), $_SESSION['user_id'], 'Y-m-d\TH:i'),
                'end_date' => convertUTCToUserDateTime(date('Y-m-d H:i:s', strtotime('+1 hour')), $_SESSION['user_id'], 'Y-m-d\TH:i'),
                'all_day' => 0,
                'location' => '',
                'venue_id' => null,
                'venue_name' => '',
                'url' => '',
                'color' => '',
                'is_recurring' => 0,
                'recurrence_pattern' => null,
                'recurrence_end_date' => null,
                'privacy' => 'public',
                'calendar_id' => '',
                'show_id' => null,
                'clubs' => [],
                'calendars' => $calendars,
                'venues' => $venues,
                'clubs_list' => $clubs,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            $this->view('calendar/create_event', $data);
        }
    }
    
    /**
     * Edit event
     * 
     * @param int $id Event ID
     * @return void
     */
    public function editEvent($id) {
        // Get event details
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            flash('calendar_message', 'Event not found', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check if this event is linked to a show
        if (!empty($event->show_id)) {
            flash('calendar_message', 'This event is linked to a show. Please edit the show instead.', 'alert alert-warning');
            redirect('calendar/event/' . $id);
        }
        
        // Check if user has permission to edit this event
        // Admins can edit any event, otherwise check if user created it or has calendar permission
        if ($_SESSION['user_role'] !== 'admin' && 
            $event->created_by != $_SESSION['user_id'] && 
            !$this->calendarModel->userHasCalendarPermission($event->calendar_id, $_SESSION['user_id'], 'edit')) {
            flash('calendar_message', 'You do not have permission to edit this event', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - but preserve description field for WYSIWYG content
            $description = $_POST['description'] ?? '';
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            // Restore the original description to preserve HTML content and base64 images
            $_POST['description'] = $description;
            
            // Process form
            $data = [
                'id' => $id,
                'title' => trim($_POST['title']),
                'description' => $description, // Use the unsanitized description
                'start_date' => convertUserDateTimeToUTC($_POST['start_date'], $_SESSION['user_id']),
                'end_date' => convertUserDateTimeToUTC($_POST['end_date'], $_SESSION['user_id']),
                'all_day' => isset($_POST['all_day']) ? 1 : 0,
                'location' => trim($_POST['location']),
                'address1' => trim($_POST['address1'] ?? ''),
                'address2' => trim($_POST['address2'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zipcode' => trim($_POST['zipcode'] ?? ''),
                'lat' => !empty($_POST['lat']) ? $_POST['lat'] : null,
                'lng' => !empty($_POST['lng']) ? $_POST['lng'] : null,
                'venue_id' => !empty($_POST['venue_id']) ? $_POST['venue_id'] : null,
                'url' => trim($_POST['url']),
                'color' => trim($_POST['color']),
                'is_recurring' => isset($_POST['is_recurring']) ? 1 : 0,
                'recurrence_pattern' => isset($_POST['recurrence_pattern']) ? $_POST['recurrence_pattern'] : null,
                'recurrence_end_date' => !empty($_POST['recurrence_end_date']) ? convertUserDateTimeToUTC($_POST['recurrence_end_date'], $_SESSION['user_id']) : null,
                'privacy' => $_POST['privacy'],
                'calendar_id' => $_POST['calendar_id'],
                'show_id' => null, // Shows are now automatically linked to events when created
                'clubs' => isset($_POST['clubs']) ? $_POST['clubs'] : [],
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // Validate title
            if (empty($data['title'])) {
                $data['title_err'] = 'Please enter a title';
            }
            
            // Validate start date
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            // Validate end date
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif (!empty($data['start_date']) && !empty($data['end_date'])) {
                try {
                    $startDateTime = new DateTime($data['start_date']);
                    $endDateTime = new DateTime($data['end_date']);
                    
                    if ($endDateTime <= $startDateTime) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['end_date'] <= $data['start_date']) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CalendarController::editEvent - DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            // Validate calendar ID
            if (empty($data['calendar_id'])) {
                $data['calendar_id_err'] = 'Please select a calendar';
            }
            
            // Automatically geocode address if needed
            if ((!$data['lat'] || !$data['lng']) && 
                ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
                
                // Load geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Create address array for geocoding - EXACTLY like the admin batch tool
                // Only include address fields, not lat/lng
                $eventData = [
                    'address1' => $data['address1'] ?? '',
                    'address2' => $data['address2'] ?? '',
                    'city' => $data['city'] ?? '',
                    'state' => $data['state'] ?? '',
                    'zipcode' => $data['zipcode'] ?? ''
                ];
                
                // Use the enhanced geocodeEvent function
                $eventData = geocodeEvent($eventData, $mapSettings, 'editEvent', $id);
                
                // If geocoding was successful, update the original data
                if (isset($eventData['lat']) && $eventData['lat'] && isset($eventData['lng']) && $eventData['lng']) {
                    $data['lat'] = $eventData['lat'];
                    $data['lng'] = $eventData['lng'];
                }
                
                // Log the geocoding attempt
                error_log("Geocoding attempt for event ID {$id}: " . 
                (isset($data['lat']) && $data['lat'] && isset($data['lng']) && $data['lng'] ? "SUCCESS" : "FAILED") . 
                " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
                
                // If geocoding failed, add a warning message
                if (!isset($data['lat']) || !$data['lat'] || !isset($data['lng']) || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
                }
            }
            
            // Make sure there are no errors
            if (empty($data['title_err']) && empty($data['start_date_err']) && empty($data['end_date_err']) && empty($data['calendar_id_err'])) {
                // Update event
                if ($this->calendarModel->updateEvent($data)) {
                    flash('calendar_message', 'Event updated successfully');
                    redirect('calendar/event/' . $id);
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/edit_event', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/edit_event', $data);
            }
        } else {
            // Get calendars
            $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
            
            // Get venues
            $venues = $this->calendarModel->getVenues();
            
            // Get clubs
            $clubs = $this->calendarModel->getClubs();
            
            // Shows are now automatically linked to events when created
            
            // Get event clubs
            $eventClubs = $this->calendarModel->getEventClubs($id);
            $clubIds = [];
            foreach ($eventClubs as $club) {
                $clubIds[] = $club->id;
            }
            
            // Init data
            $data = [
                'id' => $event->id,
                'title' => $event->title,
                'description' => $event->description,
                'start_date' => convertUTCToUserDateTime($event->start_date, $_SESSION['user_id'], 'Y-m-d\TH:i'),
                'end_date' => convertUTCToUserDateTime($event->end_date, $_SESSION['user_id'], 'Y-m-d\TH:i'),
                'all_day' => $event->all_day,
                'location' => $event->location,
                'address1' => $event->address1 ?? '',
                'address2' => $event->address2 ?? '',
                'city' => $event->city ?? '',
                'state' => $event->state ?? '',
                'zipcode' => $event->zipcode ?? '',
                'lat' => $event->lat ?? '',
                'lng' => $event->lng ?? '',
                'venue_id' => $event->venue_id,
                'url' => $event->url,
                'color' => $event->color,
                'is_recurring' => $event->is_recurring,
                'recurrence_pattern' => $event->recurrence_pattern,
                'recurrence_end_date' => $event->recurrence_end_date ? convertUTCToUserDateTime($event->recurrence_end_date, $_SESSION['user_id'], 'Y-m-d\TH:i') : null,
                'privacy' => $event->privacy,
                'calendar_id' => $event->calendar_id,
                'show_id' => $event->show_id,
                'clubs' => $clubIds,
                'calendars' => $calendars,
                'venues' => $venues,
                'clubs_list' => $clubs,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            $this->view('calendar/edit_event', $data);
        }
    }
    
    /**
     * Delete event
     * 
     * @param int $id Event ID
     * @return void
     */
    public function deleteEvent($id) {
        // Get event details
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            flash('calendar_message', 'Event not found', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check if this event is linked to a show
        if (!empty($event->show_id)) {
            flash('calendar_message', 'This event is linked to a show. Please delete the show or update it instead.', 'alert alert-warning');
            redirect('calendar/event/' . $id);
        }
        
        // Check if user has permission to delete this event
        if ($event->created_by != $_SESSION['user_id'] && !$this->calendarModel->userHasCalendarPermission($event->calendar_id, $_SESSION['user_id'], 'edit')) {
            flash('calendar_message', 'You do not have permission to delete this event', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete event
            if ($this->calendarModel->deleteEvent($id)) {
                flash('calendar_message', 'Event deleted successfully');
                redirect('calendar');
            } else {
                flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                redirect('calendar/event/' . $id);
            }
        } else {
            redirect('calendar/event/' . $id);
        }
    }
    
    /**
     * Update event via AJAX
     * 
     * @return void
     */
    public function updateEventAjax() {
        // Check for AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Get JSON data
            $json = file_get_contents('php://input');
            $data = json_decode($json);
            
            if (!$data || !isset($data->id)) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid data']);
                return;
            }
            
            // Get event details
            $event = $this->calendarModel->getEventById($data->id);
            
            if (!$event) {
                http_response_code(404);
                echo json_encode(['error' => 'Event not found']);
                return;
            }
            
            // Check if user has permission to edit this event
            // Admins can edit any event, otherwise check if user created it or has calendar permission
            if ($_SESSION['user_role'] !== 'admin' && 
                $event->created_by != $_SESSION['user_id'] && 
                !$this->calendarModel->userHasCalendarPermission($event->calendar_id, $_SESSION['user_id'], 'edit')) {
                http_response_code(403);
                echo json_encode(['error' => 'Permission denied']);
                return;
            }
            
            // Update event data
            $updateData = [
                'id' => $data->id,
                'calendar_id' => $event->calendar_id,
                'title' => $event->title,
                'description' => $event->description,
                'start_date' => isset($data->start) ? convertUserDateTimeToUTC($data->start, $_SESSION['user_id']) : $event->start_date,
                'end_date' => isset($data->end) ? convertUserDateTimeToUTC($data->end, $_SESSION['user_id']) : $event->end_date,
                'all_day' => isset($data->allDay) ? ($data->allDay ? 1 : 0) : $event->all_day,
                'location' => $event->location,
                'venue_id' => $event->venue_id,
                'url' => $event->url,
                'color' => $event->color,
                'is_recurring' => $event->is_recurring,
                'recurrence_pattern' => $event->recurrence_pattern,
                'recurrence_end_date' => $event->recurrence_end_date,
                'privacy' => $event->privacy,
                'show_id' => $event->show_id
            ];
            
            // Update event
            if ($this->calendarModel->updateEvent($updateData)) {
                echo json_encode(['success' => true]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to update event']);
            }
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
    }
    
    /**
     * Manage calendars (optimized with lazy loading)
     *
     * @return void
     */
    public function manageCalendars() {
        // Check if user is admin
        if (!isAdmin()) {
            redirect('calendar');
            return;
        }

        // Get calendar counts for overview
        $calendarCounts = $this->calendarModel->getCalendarCounts($_SESSION['user_id']);

        $data = [
            'title' => 'Manage Calendars',
            'calendar_counts' => $calendarCounts
        ];

        $this->view('calendar/manage_calendars', $data);
    }

    /**
     * AJAX endpoint for loading paginated calendars
     *
     * @return void
     */
    public function loadCalendars() {
        // Check if user is admin and request is AJAX
        if (!isAdmin() || !isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $visibilityFilter = $_GET['visibility_filter'] ?? 'all';
        $publicFilter = $_GET['public_filter'] ?? 'all';
        $orderBy = $_GET['order_by'] ?? 'name';
        $orderDir = $_GET['order_dir'] ?? 'ASC';

        try {
            $result = $this->calendarModel->getPaginatedCalendars(
                $_SESSION['user_id'], $page, $perPage, $search, $visibilityFilter, $publicFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'calendars' => $result['calendars'],
                'pagination' => $result['pagination'],
                'filters' => $result['filters'],
                'performance' => $result['performance']
            ]);
        } catch (Exception $e) {
            error_log('Error in CalendarController::loadCalendars: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load calendars']);
        }
    }
    
    /**
     * Create new calendar
     * 
     * @return void
     */
    public function createCalendar() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Ensure $_POST is not null
            if ($_POST === null || $_POST === false) {
                $_POST = [];
            }
            
            // Process form with null coalescing to handle missing keys
            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'color' => trim($_POST['color'] ?? '#3788d8'),
                'is_visible' => isset($_POST['is_visible']) ? 1 : 0,
                'is_public' => isset($_POST['is_public']) ? 1 : 0,
                'owner_id' => $_SESSION['user_id'],
                'name_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Make sure there are no errors
            if (empty($data['name_err'])) {
                // Create calendar
                if ($this->calendarModel->createCalendar($data)) {
                    flash('calendar_message', 'Calendar created successfully');
                    redirect('calendar/manageCalendars');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/create_calendar', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/create_calendar', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'color' => '#3788d8',
                'is_visible' => 1,
                'is_public' => 1,
                'name_err' => ''
            ];
            
            $this->view('calendar/create_calendar', $data);
        }
    }
    
    /**
     * Edit calendar
     * 
     * @param int $id Calendar ID
     * @return void
     */
    public function editCalendar($id) {
        // Get calendar details
        $calendar = $this->calendarModel->getCalendarById($id);
        
        if (!$calendar) {
            flash('calendar_message', 'Calendar not found', 'alert alert-danger');
            redirect('calendar/manageCalendars');
        }
        
        // Check if user has permission to edit this calendar
        if ($calendar->owner_id != $_SESSION['user_id'] && !isAdmin()) {
            flash('calendar_message', 'You do not have permission to edit this calendar', 'alert alert-danger');
            redirect('calendar/manageCalendars');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Ensure $_POST is not null
            if ($_POST === null || $_POST === false) {
                $_POST = [];
            }
            
            // Process form with null coalescing to handle missing keys
            $data = [
                'id' => $id,
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'color' => trim($_POST['color'] ?? '#3788d8'),
                'is_visible' => isset($_POST['is_visible']) ? 1 : 0,
                'is_public' => isset($_POST['is_public']) ? 1 : 0,
                'name_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Make sure there are no errors
            if (empty($data['name_err'])) {
                // Update calendar
                if ($this->calendarModel->updateCalendar($data)) {
                    flash('calendar_message', 'Calendar updated successfully');
                    redirect('calendar/manageCalendars');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/edit_calendar', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/edit_calendar', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $calendar->id,
                'name' => $calendar->name,
                'description' => $calendar->description,
                'color' => $calendar->color,
                'is_visible' => $calendar->is_visible,
                'is_public' => $calendar->is_public,
                'name_err' => ''
            ];
            
            $this->view('calendar/edit_calendar', $data);
        }
    }
    
    /**
     * Delete calendar
     * 
     * @param int $id Calendar ID
     * @return void
     */
    public function deleteCalendar($id) {
        // Get calendar details
        $calendar = $this->calendarModel->getCalendarById($id);
        
        if (!$calendar) {
            flash('calendar_message', 'Calendar not found', 'alert alert-danger');
            redirect('calendar/manageCalendars');
        }
        
        // Check if user has permission to delete this calendar
        if ($calendar->owner_id != $_SESSION['user_id'] && !isAdmin()) {
            flash('calendar_message', 'You do not have permission to delete this calendar', 'alert alert-danger');
            redirect('calendar/manageCalendars');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete calendar
            if ($this->calendarModel->deleteCalendar($id)) {
                flash('calendar_message', 'Calendar deleted successfully');
                redirect('calendar/manageCalendars');
            } else {
                flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                redirect('calendar/manageCalendars');
            }
        } else {
            redirect('calendar/manageCalendars');
        }
    }
    
    /**
     * Manage venues (optimized with lazy loading)
     *
     * @return void
     */
    public function manageVenues() {
        // Check if user is admin
        if (!isAdmin()) {
            redirect('calendar');
            return;
        }

        // Get venue counts for overview
        $venueCounts = $this->calendarModel->getVenueCounts();

        $data = [
            'title' => 'Manage Venues',
            'venue_counts' => $venueCounts
        ];

        $this->view('calendar/manage_venues', $data);
    }

    /**
     * AJAX endpoint for loading paginated venues
     *
     * @return void
     */
    public function loadVenues() {
        // Check if user is admin and request is AJAX
        if (!isAdmin() || !isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $state = $_GET['state'] ?? '';
        $city = $_GET['city'] ?? '';
        $orderBy = $_GET['order_by'] ?? 'name';
        $orderDir = $_GET['order_dir'] ?? 'ASC';

        try {
            $result = $this->calendarModel->getPaginatedVenues(
                $page, $perPage, $search, $state, $city, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'venues' => $result['venues'],
                'pagination' => $result['pagination'],
                'filters' => $result['filters'],
                'performance' => $result['performance']
            ]);
        } catch (Exception $e) {
            error_log('Error in CalendarController::loadVenues: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load venues']);
        }
    }
    
    /**
     * Create new venue
     * 
     * @return void
     */
    public function createVenue() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Ensure $_POST is not null
            if ($_POST === null || $_POST === false) {
                $_POST = [];
            }
            
            // Process form with null coalescing to handle missing keys
            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'address' => trim($_POST['address'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zip' => trim($_POST['zip'] ?? ''),
                'country' => trim($_POST['country'] ?? ''),
                'latitude' => !empty($_POST['latitude']) ? $_POST['latitude'] : null,
                'longitude' => !empty($_POST['longitude']) ? $_POST['longitude'] : null,
                'website' => trim($_POST['website'] ?? ''),
                'phone' => trim($_POST['phone'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'capacity' => !empty($_POST['capacity']) ? $_POST['capacity'] : null,
                'notes' => trim($_POST['notes'] ?? ''),
                'name_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Make sure there are no errors
            if (empty($data['name_err'])) {
                // Create venue
                if ($this->calendarModel->createVenue($data)) {
                    flash('calendar_message', 'Venue created successfully');
                    redirect('calendar/manageVenues');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/create_venue', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/create_venue', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'address' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'latitude' => null,
                'longitude' => null,
                'website' => '',
                'phone' => '',
                'email' => '',
                'capacity' => null,
                'notes' => '',
                'name_err' => '',
                'mapSettings' => $this->calendarModel->getMapProviderSettings()
            ];
            
            $this->view('calendar/create_venue', $data);
        }
    }
    
    /**
     * API endpoint for creating venue via AJAX
     * Returns JSON response
     * 
     * @return void
     */
    public function createVenueApi() {
        // Set JSON header
        header('Content-Type: application/json');
        
        // Check for POST and AJAX request
        if ($_SERVER['REQUEST_METHOD'] != 'POST' || !isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid request']);
            return;
        }
        
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
            return;
        }
        
        // Sanitize POST data
        $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        
        // Ensure $_POST is not null
        if ($_POST === null || $_POST === false) {
            $_POST = [];
        }
        
        // Process form data
        $data = [
            'name' => trim($_POST['name'] ?? ''),
            'address' => trim($_POST['address'] ?? ''),
            'city' => trim($_POST['city'] ?? ''),
            'state' => trim($_POST['state'] ?? ''),
            'zip' => trim($_POST['zip'] ?? ''),
            'country' => trim($_POST['country'] ?? ''),
            'latitude' => !empty($_POST['latitude']) ? $_POST['latitude'] : null,
            'longitude' => !empty($_POST['longitude']) ? $_POST['longitude'] : null,
            'website' => trim($_POST['website'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'capacity' => !empty($_POST['capacity']) ? $_POST['capacity'] : null,
            'notes' => trim($_POST['notes'] ?? '')
        ];
        
        // Validation
        $errors = [];
        
        // Validate required fields
        if (empty($data['name'])) {
            $errors['name'] = 'Please enter a venue name';
        }
        
        // Validate email format if provided
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        }
        
        // Validate website URL if provided
        if (!empty($data['website']) && !filter_var($data['website'], FILTER_VALIDATE_URL)) {
            $errors['website'] = 'Please enter a valid website URL';
        }
        
        // Validate capacity if provided
        if (!empty($data['capacity']) && (!is_numeric($data['capacity']) || $data['capacity'] < 0)) {
            $errors['capacity'] = 'Capacity must be a positive number';
        }
        
        // Validate coordinates if provided
        if (!empty($data['latitude']) && (!is_numeric($data['latitude']) || $data['latitude'] < -90 || $data['latitude'] > 90)) {
            $errors['latitude'] = 'Latitude must be between -90 and 90';
        }
        
        if (!empty($data['longitude']) && (!is_numeric($data['longitude']) || $data['longitude'] < -180 || $data['longitude'] > 180)) {
            $errors['longitude'] = 'Longitude must be between -180 and 180';
        }
        
        // If there are validation errors, return them
        if (!empty($errors)) {
            http_response_code(422);
            echo json_encode([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $errors
            ]);
            return;
        }
        
        // Try to create the venue
        try {
            $venueId = $this->calendarModel->createVenue($data);
            
            if ($venueId) {
                // Get the created venue data
                $venue = $this->calendarModel->getVenueById($venueId);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Venue created successfully',
                    'venue' => [
                        'id' => $venue->id,
                        'name' => $venue->name,
                        'address' => $venue->address,
                        'city' => $venue->city,
                        'state' => $venue->state,
                        'zip' => $venue->zip,
                        'country' => $venue->country,
                        'phone' => $venue->phone,
                        'email' => $venue->email,
                        'website' => $venue->website,
                        'capacity' => $venue->capacity,
                        'latitude' => $venue->latitude,
                        'longitude' => $venue->longitude,
                        'notes' => $venue->notes
                    ]
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to create venue'
                ]);
            }
        } catch (Exception $e) {
            error_log('Venue creation error: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'An error occurred while creating the venue'
            ]);
        }
    }
    
    /**
     * Edit venue
     * 
     * @param int $id Venue ID
     * @return void
     */
    public function editVenue($id) {
        // Get venue details
        $venue = $this->calendarModel->getVenueById($id);
        
        if (!$venue) {
            flash('calendar_message', 'Venue not found', 'alert alert-danger');
            redirect('calendar/manageVenues');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Ensure $_POST is not null
            if ($_POST === null || $_POST === false) {
                $_POST = [];
            }
            
            // Process form with null coalescing to handle missing keys
            $data = [
                'id' => $id,
                'name' => trim($_POST['name'] ?? ''),
                'address' => trim($_POST['address'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zip' => trim($_POST['zip'] ?? ''),
                'country' => trim($_POST['country'] ?? ''),
                'latitude' => !empty($_POST['latitude']) ? $_POST['latitude'] : null,
                'longitude' => !empty($_POST['longitude']) ? $_POST['longitude'] : null,
                'website' => trim($_POST['website'] ?? ''),
                'phone' => trim($_POST['phone'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'capacity' => !empty($_POST['capacity']) ? $_POST['capacity'] : null,
                'notes' => trim($_POST['notes'] ?? ''),
                'name_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Make sure there are no errors
            if (empty($data['name_err'])) {
                // Update venue
                if ($this->calendarModel->updateVenue($data)) {
                    flash('calendar_message', 'Venue updated successfully');
                    redirect('calendar/manageVenues');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/edit_venue', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/edit_venue', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $venue->id,
                'name' => $venue->name,
                'address' => $venue->address,
                'city' => $venue->city,
                'state' => $venue->state,
                'zip' => $venue->zip,
                'country' => $venue->country,
                'latitude' => $venue->latitude,
                'longitude' => $venue->longitude,
                'website' => $venue->website,
                'phone' => $venue->phone,
                'email' => $venue->email,
                'capacity' => $venue->capacity,
                'notes' => $venue->notes,
                'name_err' => '',
                'mapSettings' => $this->calendarModel->getMapProviderSettings()
            ];
            
            $this->view('calendar/edit_venue', $data);
        }
    }
    
    /**
     * Search venues by name
     * 
     * @return void
     */
    public function searchVenues() {
        // Check for AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
        }
        
        // Set content type to JSON
        header('Content-Type: application/json');
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        // Validate CSRF token
        if (!validateCsrfToken()) {
            echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
            return;
        }
        
        // Get search term
        $search = isset($_POST['search']) ? trim($_POST['search']) : '';
        
        if (empty($search)) {
            echo json_encode(['success' => false, 'message' => 'No search term provided']);
            return;
        }
        
        // Check if this is a request for local venues only
        $localOnly = isset($_POST['local_only']) && $_POST['local_only'] === 'true';
        
        // First, search local venues in database
        try {
            $localVenues = $this->calendarModel->searchVenues($search);
            
            // Format local venues for response
            $formattedLocalVenues = [];
            foreach ($localVenues as $venue) {
                $formattedLocalVenues[] = [
                    'id' => $venue->id,
                    'name' => $venue->name,
                    'address' => $venue->address,
                    'city' => $venue->city,
                    'state' => $venue->state,
                    'zip' => $venue->zip,
                    'latitude' => $venue->latitude,
                    'longitude' => $venue->longitude,
                    'display_name' => $venue->name . ($venue->city ? ', ' . $venue->city : '') . ($venue->state ? ', ' . $venue->state : ''),
                    'source' => 'local'
                ];
            }
            
            // If we have local results and this is a local-only request, return them
            if ($localOnly || !empty($formattedLocalVenues)) {
                echo json_encode([
                    'success' => true,
                    'venues' => $formattedLocalVenues,
                    'count' => count($formattedLocalVenues),
                    'source' => 'local'
                ]);
                return;
            }
        } catch (Exception $e) {
            error_log('Error searching local venues: ' . $e->getMessage());
            // Continue to API search if local search fails
        }
        
        // If no local results or not local-only, search using external APIs
        try {
            // Get map settings to determine which provider to use
            $mapSettings = $this->calendarModel->getMapProviderSettings();
            $provider = $mapSettings['provider'] ?? 'openstreetmap';
            $apiKey = $mapSettings['api_key'] ?? '';
            $serverApiKey = $mapSettings['server_api_key'] ?? '';
            
            // Get current user's location for biasing search results
            $userLocation = null;
            if (isset($_SESSION['user_id'])) {
                $user = $this->userModel->getUserById($_SESSION['user_id']);
                if ($user && (!empty($user->city) || !empty($user->state) || !empty($user->zip))) {
                    $userLocation = [
                        'city' => $user->city ?? '',
                        'state' => $user->state ?? '',
                        'zip' => $user->zip ?? '',
                        'address' => $user->address ?? ''
                    ];
                }
            }
            
            // Search venues based on the configured provider
            try {
                switch ($provider) {
                    case 'google':
                        $venues = $this->searchVenuesGoogle($search, $serverApiKey, $userLocation);
                        break;
                    case 'mapbox':
                        $venues = $this->searchVenuesMapbox($search, $apiKey, $userLocation);
                        break;
                    case 'here':
                        $venues = $this->searchVenuesHere($search, $apiKey, $userLocation);
                        break;
                    case 'openstreetmap':
                    default:
                        $venues = $this->searchVenuesOpenStreetMap($search, $userLocation);
                        break;
                }
            } catch (Exception $providerError) {
                // If the primary provider fails, fall back to OpenStreetMap
                if ($provider !== 'openstreetmap') {
                    error_log("Primary provider ({$provider}) failed, falling back to OpenStreetMap: " . $providerError->getMessage());
                    try {
                        $venues = $this->searchVenuesOpenStreetMap($search, $userLocation);
                        $provider = 'openstreetmap (fallback)';
                    } catch (Exception $fallbackError) {
                        error_log("Fallback provider also failed: " . $fallbackError->getMessage());
                        throw new Exception('All venue search providers failed');
                    }
                } else {
                    throw $providerError;
                }
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Venue search - Found " . count($venues) . " venues using provider: " . $provider);
            }
            
            echo json_encode([
                'success' => true, 
                'venues' => $venues,
                'provider' => $provider
            ]);
        } catch (Exception $e) {
            error_log("Venue search error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'An error occurred while searching for venues: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Delete venue
     * 
     * @param int $id Venue ID
     * @return void
     */
    public function deleteVenue($id) {
        // Get venue details
        $venue = $this->calendarModel->getVenueById($id);
        
        if (!$venue) {
            flash('calendar_message', 'Venue not found', 'alert alert-danger');
            redirect('calendar/manageVenues');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete venue
            if ($this->calendarModel->deleteVenue($id)) {
                flash('calendar_message', 'Venue deleted successfully');
                redirect('calendar/manageVenues');
            } else {
                flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                redirect('calendar/manageVenues');
            }
        } else {
            redirect('calendar/manageVenues');
        }
    }
    
    /**
     * Manage clubs (optimized with lazy loading)
     *
     * @return void
     */
    public function manageClubs() {
        // Check if user is admin
        if (!isAdmin()) {
            redirect('calendar');
            return;
        }

        // Get club counts for overview
        $clubCounts = $this->calendarModel->getClubCounts();

        $data = [
            'title' => 'Manage Clubs',
            'club_counts' => $clubCounts
        ];

        $this->view('calendar/manage_clubs', $data);
    }

    /**
     * AJAX endpoint for loading paginated clubs
     *
     * @return void
     */
    public function loadClubs() {
        // Check if user is admin and request is AJAX
        if (!isAdmin() || !isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $ownerFilter = $_GET['owner_filter'] ?? 'all';
        $verificationFilter = $_GET['verification_filter'] ?? 'all';
        $orderBy = $_GET['order_by'] ?? 'name';
        $orderDir = $_GET['order_dir'] ?? 'ASC';

        try {
            $result = $this->calendarModel->getPaginatedClubs(
                $page, $perPage, $search, $ownerFilter, $verificationFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'clubs' => $result['clubs'],
                'pagination' => $result['pagination'],
                'filters' => $result['filters'],
                'performance' => $result['performance']
            ]);
        } catch (Exception $e) {
            error_log('Error in CalendarController::loadClubs: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load clubs']);
        }
    }
    
    /**
     * Create new club
     * 
     * @return void
     */
    public function createClub() {
        // Debug logging
        error_log("CalendarController::createClub - Method called (form-based)");
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Ensure $_POST is not null
            if ($_POST === null || $_POST === false) {
                $_POST = [];
            }
            
            // Process form with null coalescing to handle missing keys
            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'logo' => trim($_POST['logo'] ?? ''),
                'website' => trim($_POST['website'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'phone' => trim($_POST['phone'] ?? ''),
                'owner_id' => $_SESSION['user_id'],
                'name_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Make sure there are no errors
            if (empty($data['name_err'])) {
                // Create club
                if ($this->calendarModel->createClub($data)) {
                    flash('calendar_message', 'Club created successfully');
                    redirect('calendar/manageClubs');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/create_club', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/create_club', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'logo' => '',
                'website' => '',
                'email' => '',
                'phone' => '',
                'name_err' => ''
            ];
            
            $this->view('calendar/create_club', $data);
        }
    }
    
    /**
     * Edit club
     * 
     * @param int $id Club ID
     * @return void
     */
    public function editClub($id) {
        // Get club details
        $club = $this->calendarModel->getClubById($id);
        
        if (!$club) {
            flash('calendar_message', 'Club not found', 'alert alert-danger');
            redirect('calendar/manageClubs');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data - use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Ensure $_POST is not null
            if ($_POST === null || $_POST === false) {
                $_POST = [];
            }
            
            // Process form with null coalescing to handle missing keys
            $data = [
                'id' => $id,
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'logo' => trim($_POST['logo'] ?? ''),
                'website' => trim($_POST['website'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'phone' => trim($_POST['phone'] ?? ''),
                'name_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Make sure there are no errors
            if (empty($data['name_err'])) {
                // Update club
                if ($this->calendarModel->updateClub($data)) {
                    flash('calendar_message', 'Club updated successfully');
                    redirect('calendar/manageClubs');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/edit_club', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/edit_club', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $club->id,
                'name' => $club->name,
                'description' => $club->description,
                'logo' => $club->logo,
                'website' => $club->website,
                'email' => $club->email,
                'phone' => $club->phone,
                'name_err' => ''
            ];
            
            $this->view('calendar/edit_club', $data);
        }
    }
    
    /**
     * Delete club
     * 
     * @param int $id Club ID
     * @return void
     */
    public function deleteClub($id) {
        // Get club details
        $club = $this->calendarModel->getClubById($id);
        
        if (!$club) {
            flash('calendar_message', 'Club not found', 'alert alert-danger');
            redirect('calendar/manageClubs');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete club
            if ($this->calendarModel->deleteClub($id)) {
                flash('calendar_message', 'Club deleted successfully');
                redirect('calendar/manageClubs');
            } else {
                flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                redirect('calendar/manageClubs');
            }
        } else {
            redirect('calendar/manageClubs');
        }
    }
    
    /**
     * Manage club members (optimized with lazy loading)
     *
     * @param int $id Club ID
     * @return void
     */
    public function manageClubMembers($id) {
        // Get club details
        $club = $this->calendarModel->getClubById($id);

        if (!$club) {
            flash('calendar_message', 'Club not found', 'alert alert-danger');
            redirect('calendar/manageClubs');
        }

        // Get member counts for overview
        $memberCounts = $this->calendarModel->getClubMemberCounts($id);

        $data = [
            'title' => 'Manage Club Members',
            'club' => $club,
            'member_counts' => $memberCounts
        ];

        $this->view('calendar/manage_club_members', $data);
    }

    /**
     * AJAX endpoint for loading paginated club members
     *
     * @param int $clubId Club ID from URL path
     * @return void
     */
    public function loadClubMembers($clubId = null) {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get club ID from parameter or URL path
        $clubId = (int)($clubId ?? $_GET['club_id'] ?? 0);

        // Get other parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $roleFilter = $_GET['role_filter'] ?? 'all';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $orderBy = $_GET['order_by'] ?? 'name';
        $orderDir = $_GET['order_dir'] ?? 'ASC';

        if (!$clubId) {
            http_response_code(400);
            echo json_encode(['error' => 'Club ID required']);
            return;
        }

        try {
            $result = $this->calendarModel->getPaginatedClubMembers(
                $clubId, $page, $perPage, $search, $roleFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'members' => $result['members'],
                'pagination' => $result['pagination'],
                'filters' => $result['filters'],
                'performance' => $result['performance']
            ]);
        } catch (Exception $e) {
            error_log('Error in CalendarController::loadClubMembers: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load club members']);
        }
    }

    /**
     * AJAX endpoint for searching users to add to club
     *
     * @return void
     */
    public function searchUsersForClub() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Get parameters
        $clubId = (int)($_GET['club_id'] ?? 0);
        $search = $_GET['search'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $orderBy = $_GET['order_by'] ?? 'name';
        $orderDir = $_GET['order_dir'] ?? 'ASC';

        if (!$clubId) {
            http_response_code(400);
            echo json_encode(['error' => 'Club ID required']);
            return;
        }

        try {
            $result = $this->userModel->searchUsersForClub(
                $search, $clubId, $page, $perPage, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'users' => $result['users'],
                'pagination' => $result['pagination'],
                'filters' => $result['filters'],
                'performance' => $result['performance']
            ]);
        } catch (Exception $e) {
            error_log('Error in CalendarController::searchUsersForClub: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to search users']);
        }
    }
    
    /**
     * Add club member
     * 
     * @param int $id Club ID
     * @return void
     */
    public function addClubMember($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Process form
            $userId = $_POST['user_id'];
            $role = $_POST['role'];
            
            // Add member
            if ($this->calendarModel->addClubMember($id, $userId, $role)) {
                flash('calendar_message', 'Member added successfully');
                redirect('calendar/manageClubMembers/' . $id);
            } else {
                flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                redirect('calendar/manageClubMembers/' . $id);
            }
        } else {
            redirect('calendar/manageClubMembers/' . $id);
        }
    }
    
    /**
     * Remove club member
     * 
     * @param int $clubId Club ID
     * @param int $userId User ID
     * @return void
     */
    public function removeClubMember($clubId, $userId) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Remove member
            if ($this->calendarModel->removeClubMember($clubId, $userId)) {
                flash('calendar_message', 'Member removed successfully');
                redirect('calendar/manageClubMembers/' . $clubId);
            } else {
                flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                redirect('calendar/manageClubMembers/' . $clubId);
            }
        } else {
            redirect('calendar/manageClubMembers/' . $clubId);
        }
    }
    
    /**
     * Clear browser cache for calendar assets
     * 
     * @return void
     */
    public function clearCache() {
        // Only admins can clear cache
        if (!isAdmin()) {
            flash('calendar_message', 'You do not have permission to clear cache', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Force browser to reload the files by updating their modification time
        $jsFile = APPROOT . '/public/js/custom-calendar.js';
        $cssFile = APPROOT . '/public/css/custom-calendar.css';
        $debugFile = APPROOT . '/public/js/custom-calendar-debug.js';
        
        if (file_exists($jsFile)) {
            // Add a small delay to ensure the timestamp is different
            sleep(1);
            touch($jsFile);
        }
        
        if (file_exists($cssFile)) {
            // Add a small delay to ensure the timestamp is different
            sleep(1);
            touch($cssFile);
        }
        
        if (file_exists($debugFile)) {
            // Add a small delay to ensure the timestamp is different
            sleep(1);
            touch($debugFile);
        }
        
        // Set global cache buster in session
        $_SESSION['cache_buster'] = time();
        
        // Also set calendar-specific cache buster
        $_SESSION['calendar_cache_buster'] = time();
        
        flash('calendar_message', 'Calendar cache cleared successfully. Please refresh the page.', 'alert alert-success');
        redirect('calendar');
    }
    
    /**
     * Calendar settings
     * 
     * @return void
     */
    public function settings() {
        // Check if user is admin
        if (!isAdmin()) {
            flash('calendar_message', 'You do not have permission to access this page', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Process form
            $settings = [
                'default_view' => $_POST['default_view'],
                'business_hours_start' => $_POST['business_hours_start'],
                'business_hours_end' => $_POST['business_hours_end'],
                'week_starts_on' => $_POST['week_starts_on'],
                'time_format' => $_POST['time_format'],
                'date_format' => $_POST['date_format'],
                'default_event_duration' => $_POST['default_event_duration'],
                'enable_drag_drop' => isset($_POST['enable_drag_drop']) ? '1' : '0',
                'enable_resize' => isset($_POST['enable_resize']) ? '1' : '0',
                'show_weekends' => isset($_POST['show_weekends']) ? '1' : '0',
                'default_calendar_color' => $_POST['default_calendar_color'],
                'notification_default_time' => $_POST['notification_default_time'],
                // Event Chart Settings
                'event_show_weekends' => isset($_POST['event_show_weekends']) ? '1' : '0',
                'event_show_today_line' => isset($_POST['event_show_today_line']) ? '1' : '0',
                'event_enable_drag_drop' => isset($_POST['event_enable_drag_drop']) ? '1' : '0',
                'event_show_event_hover' => isset($_POST['event_show_event_hover']) ? '1' : '0'
            ];
            
            // Update settings
            $success = true;
            foreach ($settings as $key => $value) {
                if (!$this->calendarModel->updateCalendarSetting($key, $value)) {
                    $success = false;
                }
            }
            
            if ($success) {
                flash('calendar_message', 'Settings updated successfully');
                redirect('calendar/settings');
            } else {
                flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                redirect('calendar/settings');
            }
        } else {
            // Get settings
            $settings = $this->calendarModel->getCalendarSettings(null);
            
            $data = [
                'title' => 'Calendar Settings',
                'settings' => $settings
            ];
            
            $this->view('calendar/settings', $data);
        }
    }
    
    /**
     * Import events
     * 
     * @return void
     */
    public function import() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            flash('calendar_message', 'You must be logged in to import events', 'alert alert-danger');
            redirect('users/login');
            return;
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Process form
            $importType = $_POST['import_type'];
            $calendarId = $_POST['calendar_id'];
            
            if ($importType == 'ical') {
                $icalUrl = $_POST['ical_url'];
                
                if (empty($icalUrl)) {
                    flash('calendar_message', 'Please enter an iCal URL', 'alert alert-danger');
                    redirect('calendar/import');
                }
                
                if ($this->calendarModel->importFromIcal($calendarId, $icalUrl, $_SESSION['user_id'])) {
                    flash('calendar_message', 'Events imported successfully');
                    redirect('calendar');
                } else {
                    flash('calendar_message', 'Failed to import events', 'alert alert-danger');
                    redirect('calendar/import');
                }
            } elseif ($importType == 'facebook') {
                $facebookUrl = $_POST['facebook_url'];
                
                if (empty($facebookUrl)) {
                    flash('calendar_message', 'Please enter a Facebook event URL', 'alert alert-danger');
                    redirect('calendar/import');
                }
                
                if ($this->calendarModel->importFromFacebook($calendarId, $facebookUrl, $_SESSION['user_id'])) {
                    flash('calendar_message', 'Events imported successfully');
                    redirect('calendar');
                } else {
                    flash('calendar_message', 'Failed to import events', 'alert alert-danger');
                    redirect('calendar/import');
                }
            } elseif ($importType == 'shows') {
                if ($this->calendarModel->syncEventsWithShows($calendarId)) {
                    flash('calendar_message', 'Shows imported successfully');
                    redirect('calendar');
                } else {
                    flash('calendar_message', 'Failed to import shows', 'alert alert-danger');
                    redirect('calendar/import');
                }
            } else {
                flash('calendar_message', 'Invalid import type', 'alert alert-danger');
                redirect('calendar/import');
            }
        } else {
            // Get calendars
            $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
            
            $data = [
                'title' => 'Import Events',
                'calendars' => $calendars
            ];
            
            $this->view('calendar/import', $data);
        }
    }
    
    /**
     * Export calendar to iCal
     * 
     * @param int $id Calendar ID
     * @return void
     */
    public function export($id) {
        // Get calendar details
        $calendar = $this->calendarModel->getCalendarById($id);
        
        if (!$calendar) {
            flash('calendar_message', 'Calendar not found', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check if user has permission to view this calendar
        if (!$calendar->is_public && $calendar->owner_id != $_SESSION['user_id'] && !$this->calendarModel->userHasCalendarPermission($id, $_SESSION['user_id'], 'view')) {
            flash('calendar_message', 'You do not have permission to export this calendar', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Generate iCal content
        $ical = $this->calendarModel->exportToIcal($id);
        
        if ($ical) {
            // Set headers for file download
            header('Content-Type: text/calendar; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $calendar->name . '.ics"');
            
            // Output iCal content
            echo $ical;
            exit;
        } else {
            flash('calendar_message', 'Failed to export calendar', 'alert alert-danger');
            redirect('calendar');
        }
    }
    
    /**
     * Display the map view of events
     * Handles both regular map view and focused views (show/event)
     *
     * @param string $focusType Optional focus type ('show' or 'event')
     * @param int $focusId Optional ID to focus on
     * @return void
     */
    public function map($focusType = null, $focusId = null)
    {
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('CalendarController::map called with focusType: ' . ($focusType ?? 'null') . ', focusId: ' . ($focusId ?? 'null'));
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('users/login');
        }

        $focusShow = null;
        $focusEvent = null;
        $title = 'Calendar Map View';

        // Handle focus parameters
        if ($focusType && $focusId) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('CalendarController::map processing focus parameters: type=' . $focusType . ', id=' . $focusId);
            }

            if ($focusType === 'show' && is_numeric($focusId)) {
                // Get show details
                $focusShow = $this->showModel->getShowById($focusId);
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('CalendarController::map show lookup result: ' . ($focusShow ? 'found' : 'not found'));
                }

                if ($focusShow) {
                    // Find calendar event associated with this show
                    $focusEvent = $this->calendarModel->getEventByShowId($focusId);
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log('CalendarController::map event lookup result: ' . ($focusEvent ? 'found event ID ' . $focusEvent->id : 'not found'));
                        if ($focusEvent) {
                            error_log('CalendarController::map event details: lat=' . ($focusEvent->lat ?? 'null') . ', lng=' . ($focusEvent->lng ?? 'null'));
                        }
                    }
                    $title = 'Calendar Map View - ' . $focusShow->name;
                }
            } elseif ($focusType === 'event' && is_numeric($focusId)) {
                // Get event details directly
                $focusEvent = $this->calendarModel->getEventById($focusId);
                if ($focusEvent && $focusEvent->show_id) {
                    $focusShow = $this->showModel->getShowById($focusEvent->show_id);
                }
                if ($focusEvent) {
                    $title = 'Calendar Map View - ' . $focusEvent->title;
                }
            }
        }
        
        // Get all calendars
        $calendars = $this->calendarModel->getCalendars();
        
        // Get map provider settings
        $mapSettings = $this->calendarModel->getMapProviderSettings();
        
        // Get marker customization settings
        $markerSettings = $this->calendarModel->getMarkerCustomizationSettings();
        
        // Merge marker settings into map settings
        $mapSettings = array_merge($mapSettings, $markerSettings);
        
        $data = [
            'title' => $title,
            'calendars' => $calendars,
            'mapSettings' => $mapSettings,
            'focusShow' => $focusShow,
            'focusEvent' => $focusEvent
        ];
        
        $this->view('calendar/map', $data);
    }
    
    /**
     * Batch geocode venues and events
     * 
     * @return void
     */
    public function batchGeocode()
    {
        // Check if user is logged in and is admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Get venues without coordinates
        $venuesWithoutCoordinates = $this->calendarModel->getVenuesWithoutCoordinates();
        $venuesWithoutCoordinatesCount = count($venuesWithoutCoordinates);
        
        // Get events without coordinates
        $eventsWithoutCoordinates = $this->calendarModel->getEventsWithoutCoordinates();
        $eventsWithoutCoordinatesCount = count($eventsWithoutCoordinates);
        
        $data = [
            'title' => 'Batch Geocoding',
            'venues_without_coordinates_count' => $venuesWithoutCoordinatesCount,
            'events_without_coordinates_count' => $eventsWithoutCoordinatesCount,
            'mapSettings' => $this->calendarModel->getMapProviderSettings()
        ];
        
        $this->view('calendar/batch_geocode', $data);
    }
    
    /**
     * Get venues without coordinates for AJAX requests
     * 
     * @return void
     */
    public function getVenuesWithoutCoordinates()
    {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        // Get venues without coordinates
        $venues = $this->calendarModel->getVenuesWithoutCoordinates();
        
        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode($venues);
        exit;
    }
    
    /**
     * Get events without coordinates for AJAX requests
     * 
     * @return void
     */
    public function getEventsWithoutCoordinates()
    {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        // Get events without coordinates
        $events = $this->calendarModel->getEventsWithoutCoordinates();
        
        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode($events);
        exit;
    }
    
    /**
     * Update venue coordinates for AJAX requests
     * 
     * @return void
     */
    public function updateVenueCoordinates()
    {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        // Get JSON data
        $json = file_get_contents('php://input');
        $data = json_decode($json);
        
        // Update venue coordinates
        $success = $this->calendarModel->updateVenueCoordinates($data->id, $data->latitude, $data->longitude);
        
        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode(['success' => $success]);
        exit;
    }
    
    /**
     * Update event coordinates for AJAX requests
     * 
     * @return void
     */
    public function updateEventCoordinates()
    {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        // Get JSON data
        $json = file_get_contents('php://input');
        $data = json_decode($json);
        
        // Update event coordinates
        $success = $this->calendarModel->updateEventCoordinates($data->id, $data->lat, $data->lng);
        
        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode(['success' => $success]);
        exit;
    }
    
    /**
     * Get venue details for AJAX requests
     * 
     * @param int $id Venue ID
     * @return void
     */
    public function getVenueDetails($id = 0)
    {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        // Get venue details
        $venue = $this->calendarModel->getVenueById($id);
        
        // Return JSON response
        header('Content-Type: application/json');
        
        if ($venue) {
            // Map venue fields to expected format
            $response = [
                'id' => $venue->id,
                'name' => $venue->name,
                'address' => $venue->address,
                'city' => $venue->city,
                'state' => $venue->state,
                'zipcode' => $venue->zip,
                'country' => $venue->country,
                'lat' => $venue->latitude,
                'lng' => $venue->longitude,
                'phone' => $venue->phone,
                'email' => $venue->email,
                'website' => $venue->website,
                'notes' => $venue->notes,
                'capacity' => $venue->capacity
            ];
            
            echo json_encode($response);
        } else {
            echo json_encode(['error' => 'Venue not found']);
        }
        exit;
    }
    
    /**
     * Manage map settings
     *
     * @return void
     */
    public function mapSettings()
    {
        // Check if user is logged in and is admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Get current map settings
        $mapSettings = $this->calendarModel->getMapProviderSettings();
        
        // Get marker customization settings
        $markerSettings = $this->calendarModel->getMarkerCustomizationSettings();
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token if the function exists
            if (function_exists('validateCsrfToken')) {
                if (!validateCsrfToken()) {
                    flash('calendar_message', 'Security validation failed. Please try again.', 'alert alert-danger');
                    redirect('calendar/mapSettings');
                    return;
                }
            }
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Process form
            $data = [
                'provider' => trim($_POST['provider']),
                'api_key' => trim($_POST['api_key']),
                'server_api_key' => isset($_POST['server_api_key']) ? trim($_POST['server_api_key']) : '',
                'default_zoom' => (int)$_POST['default_zoom'],
                'default_lat' => (float)$_POST['default_lat'],
                'default_lng' => (float)$_POST['default_lng'],
                'filter_radius' => (int)$_POST['filter_radius'],
                'tile_url' => trim($_POST['tile_url']),
                'attribution' => trim($_POST['attribution']),
                // Marker customization settings
                'marker_type' => trim($_POST['marker_type']),
                'marker_size' => (int)$_POST['marker_size'],
                'marker_border' => $_POST['marker_border'],
                'marker_border_color' => trim($_POST['marker_border_color']),
                'marker_border_width' => (int)$_POST['marker_border_width'],
                'custom_marker_url' => isset($_POST['custom_marker_url']) ? trim($_POST['custom_marker_url']) : '',
                'use_calendar_colors' => $_POST['use_calendar_colors'],
                'default_marker_color' => trim($_POST['default_marker_color']),
                'errors' => []
            ];
            
            // Validate provider
            if (empty($data['provider'])) {
                $data['errors']['provider'] = 'Please select a map provider';
            }
            
            // Validate API key for providers that require it
            if (in_array($data['provider'], ['google', 'mapbox', 'here']) && empty($data['api_key'])) {
                $data['errors']['api_key'] = 'API key is required for this provider';
            }
            
            // Validate server API key for Google (required for Places API and Geocoding)
            if ($data['provider'] == 'google' && empty($data['server_api_key'])) {
                $data['errors']['server_api_key'] = 'Server-side API key is required for Google Places and Geocoding APIs';
            }
            
            // Validate tile URL for OpenStreetMap
            if ($data['provider'] == 'openstreetmap' && empty($data['tile_url'])) {
                $data['errors']['tile_url'] = 'Tile URL is required for OpenStreetMap';
            }
            
            // Validate custom marker URL if custom marker type is selected
            if ($data['marker_type'] == 'custom' && empty($data['custom_marker_url'])) {
                $data['errors']['custom_marker_url'] = 'Custom marker URL is required when using custom marker type';
            }
            
            // If no errors, update settings
            if (empty($data['errors'])) {
                $mapResult = $this->calendarModel->updateMapProviderSettings($data);
                $markerResult = $this->calendarModel->updateMarkerCustomizationSettings($data);
                
                if ($mapResult && $markerResult) {
                    flash('calendar_message', 'Map settings updated successfully', 'alert alert-success');
                    redirect('calendar/mapSettings');
                } else {
                    flash('calendar_message', 'Failed to update map settings', 'alert alert-danger');
                }
            }
        } else {
            $data = array_merge($mapSettings, $markerSettings);
            $data['errors'] = [];
        }
        
        $data['title'] = 'Map Settings';
        
        $this->view('calendar/map_settings', $data);
    }
    
    /**
     * Get events with location data for the map view
     *
     * @return void
     */
    public function mapEvents()
    {
        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        // Get filter parameters
        $start = isset($_GET['start']) ? $_GET['start'] : null;
        $end = isset($_GET['end']) ? $_GET['end'] : null;
        $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : null;
        $state = isset($_GET['state']) ? $_GET['state'] : null;
        $city = isset($_GET['city']) ? $_GET['city'] : null;
        $venueId = isset($_GET['venue_id']) ? $_GET['venue_id'] : null;
        $clubId = isset($_GET['club_id']) ? $_GET['club_id'] : null;
        $keyword = isset($_GET['keyword']) ? $_GET['keyword'] : null;
        $radius = isset($_GET['radius']) ? (int)$_GET['radius'] : null;
        $lat = isset($_GET['lat']) ? (float)$_GET['lat'] : null;
        $lng = isset($_GET['lng']) ? (float)$_GET['lng'] : null;
        $showId = isset($_GET['show_id']) ? $_GET['show_id'] : null;
        $category = isset($_GET['category']) ? $_GET['category'] : null;
        $tag = isset($_GET['tag']) ? $_GET['tag'] : null;
        $price = isset($_GET['price']) ? $_GET['price'] : null;
        $priceRange = isset($_GET['price_range']) ? $_GET['price_range'] : null;
        
        // Get pagination parameters
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $pageSize = isset($_GET['page_size']) ? max(10, min(100, (int)$_GET['page_size'])) : 25;
        $paginationMode = isset($_GET['pagination_mode']) ? $_GET['pagination_mode'] : 'current_page'; // 'all_pins' or 'current_page'
        
        $filters = [
            'map_view' => true // Flag to indicate this is for map view
        ];
        
        if ($start) {
            $filters['start'] = $start;
        }
        
        if ($end) {
            $filters['end'] = $end;
        }
        
        // Handle calendar IDs
        $calendarIds = [];
        
        if ($calendarId) {
            // Check if multiple calendar IDs are provided
            if (strpos($calendarId, ',') !== false) {
                $calendarIds = array_map('trim', explode(',', $calendarId));
                // Filter out any empty values
                $calendarIds = array_filter($calendarIds, function($id) {
                    return !empty($id);
                });
            } else {
                // Single calendar ID
                $filters['calendar_id'] = $calendarId;
            }
        } else {
            // If no calendar ID is provided, get all visible calendars
            $calendars = $this->calendarModel->getCalendars();
            if (!empty($calendars)) {
                $calendarIds = array_map(function($calendar) {
                    return $calendar->id;
                }, $calendars);
            }
        }
        
        // Add calendar IDs to filters if we have any
        if (!empty($calendarIds)) {
            $filters['calendar_ids'] = array_values($calendarIds); // Reset array keys
        }
        
        // Add state filter
        if ($state) {
            $filters['state'] = $state;
        }
        
        // Add city filter
        if ($city) {
            $filters['city'] = $city;
        }
        
        // Add venue filter
        if ($venueId) {
            // Check if multiple venue IDs are provided
            if (strpos($venueId, ',') !== false) {
                $venueIds = array_map('trim', explode(',', $venueId));
                // Filter out any empty values
                $venueIds = array_filter($venueIds, function($id) {
                    return !empty($id);
                });
                $filters['venue_ids'] = array_values($venueIds);
            } else {
                // Single venue ID
                $filters['venue_id'] = $venueId;
            }
        }
        
        // Add club filter
        if ($clubId) {
            // Check if multiple club IDs are provided
            if (strpos($clubId, ',') !== false) {
                $clubIds = array_map('trim', explode(',', $clubId));
                // Filter out any empty values
                $clubIds = array_filter($clubIds, function($id) {
                    return !empty($id);
                });
                $filters['club_ids'] = array_values($clubIds);
            } else {
                // Single club ID
                $filters['club_id'] = $clubId;
            }
        }
        
        // Add keyword filter
        if ($keyword) {
            $filters['keyword'] = $keyword;
        }
        
        // Location-based filtering
        if ($radius && $lat && $lng) {
            $filters['radius'] = $radius;
            $filters['lat'] = $lat;
            $filters['lng'] = $lng;
        }
        
        // Add show filter
        if ($showId) {
            $filters['show_id'] = $showId;
        }
        
        // Add category filter
        if ($category) {
            // Handle multiple categories (comma-separated)
            if (strpos($category, ',') !== false) {
                $categories = explode(',', $category);
                $filters['categories'] = $categories;
            } else {
                $filters['category'] = $category;
            }
        }
        
        // Add tag filter
        if ($tag) {
            // Handle multiple tags (comma-separated)
            if (strpos($tag, ',') !== false) {
                $tags = explode(',', $tag);
                $filters['tags'] = $tags;
            } else {
                $filters['tag'] = $tag;
            }
        }
        
        // Add price filter
        if ($price) {
            $filters['price'] = $price;
        }
        
        // Add price range filter
        if ($priceRange) {
            // Format should be min-max, e.g., "0-100"
            $parts = explode('-', $priceRange);
            if (count($parts) === 2) {
                $filters['price_min'] = (float)$parts[0];
                $filters['price_max'] = (float)$parts[1];
            }
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("CalendarController::mapEvents - Filters: " . json_encode($filters));
        }
        
        try {
            // Get user ID for timezone conversion
            $userId = isLoggedIn() ? $_SESSION['user_id'] : null;

            // Get events with location data
            $events = $this->calendarModel->getEventsWithLocation($filters);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::mapEvents - Found " . count($events) . " events");
            }
            
            $formattedEvents = [];
            
            foreach ($events as $event) {
                // Skip events without location data (either address OR coordinates)
                $hasAddressData = !empty($event->address1) || !empty($event->city) || !empty($event->state);
                $hasCoordinates = !empty($event->lat) && !empty($event->lng);

                if (!$hasAddressData && !$hasCoordinates) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CalendarController::mapEvents - Skipping event ID {$event->id} - no location data or coordinates");
                    }
                    continue;
                }

                if (defined('DEBUG_MODE') && DEBUG_MODE && $event->show_id == 5) {
                    error_log("CalendarController::mapEvents - Processing event for show ID 5: event ID {$event->id}, lat={$event->lat}, lng={$event->lng}");
                }
                
                // Format the location for display
                $locationParts = [];
                if (!empty($event->address1)) $locationParts[] = $event->address1;
                if (!empty($event->address2)) $locationParts[] = $event->address2;
                if (!empty($event->city)) $locationParts[] = $event->city;
                if (!empty($event->state)) $locationParts[] = $event->state;
                if (!empty($event->zipcode)) $locationParts[] = $event->zipcode;
                
                $formattedLocation = implode(', ', $locationParts);
                
                // Convert timezone directly like other endpoints
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("MAP EVENTS DEBUG - About to call getUserTimezone with userId: {$userId}");
                }
                $userTimezone = getUserTimezone($userId);
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("MAP EVENTS DEBUG - getUserTimezone returned: {$userTimezone}");
                }
                try {
                    $startDate = new DateTime($event->start_date, new DateTimeZone('UTC'));
                    $startDate->setTimezone(new DateTimeZone($userTimezone));
                    $startTime = $startDate->format('c');

                    $endDate = new DateTime($event->end_date, new DateTimeZone('UTC'));
                    $endDate->setTimezone(new DateTimeZone($userTimezone));
                    $endTime = $endDate->format('c');
                } catch (Exception $e) {
                    $startTime = $event->start_date;
                    $endTime = $event->end_date;
                }

                $formattedEvents[] = [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start' => $startTime,
                    'end' => $endTime,
                    'allDay' => (bool)$event->all_day,
                    'color' => $event->color,
                    'url' => URLROOT . '/calendar/event/' . $event->id,
                    'extendedProps' => [
                        'description' => $event->description,
                        'location' => $formattedLocation,
                        'address1' => $event->address1,
                        'address2' => $event->address2,
                        'city' => $event->city,
                        'state' => $event->state,
                        'zipcode' => $event->zipcode,
                        'calendar_id' => $event->calendar_id,
                        'calendar_name' => $event->calendar_name,
                        'show_id' => $event->show_id,
                        'show_name' => $event->show_name,
                        'privacy' => $event->privacy,
                        'lat' => $event->lat,
                        'lng' => $event->lng
                    ]
                ];
            }
            
            // Calculate pagination
            $totalEvents = count($formattedEvents);
            $totalPages = ceil($totalEvents / $pageSize);
            $offset = ($page - 1) * $pageSize;
            
            // Prepare response data
            $response = [
                'pagination' => [
                    'current_page' => $page,
                    'page_size' => $pageSize,
                    'total_events' => $totalEvents,
                    'total_pages' => $totalPages,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages,
                    'pagination_mode' => $paginationMode
                ]
            ];
            
            // Handle different pagination modes
            if ($paginationMode === 'all_pins') {
                // Return all events for map pins, but paginated list
                $response['all_events'] = $formattedEvents; // All events for map pins
                $response['list_events'] = array_slice($formattedEvents, $offset, $pageSize); // Paginated events for list
                
                // Add page information to each event for list navigation
                foreach ($response['all_events'] as $index => &$event) {
                    $event['list_page'] = floor($index / $pageSize) + 1;
                    $event['list_position'] = ($index % $pageSize) + 1;
                }
            } else {
                // Current page mode - return only current page events
                $response['all_events'] = array_slice($formattedEvents, $offset, $pageSize);
                $response['list_events'] = $response['all_events'];
                
                // Add page information
                foreach ($response['all_events'] as $index => &$event) {
                    $event['list_page'] = $page;
                    $event['list_position'] = $index + 1;
                }
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::mapEvents - Pagination: Page $page of $totalPages, Mode: $paginationMode");
            }
            
            echo json_encode($response);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get events: ' . $e->getMessage()]);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::mapEvents - Error: " . $e->getMessage());
                error_log("CalendarController::mapEvents - Stack trace: " . $e->getTraceAsString());
            }
        }
    }
    
    /**
     * Get event categories for the filter
     *
     * @return void
     */
    public function getCategories()
    {
        // Set content type to JSON
        header('Content-Type: application/json');
        
        try {
            // Get categories with event counts
            $categories = $this->calendarModel->getCategories();
            
            echo json_encode($categories);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get categories: ' . $e->getMessage()]);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::getCategories - Error: " . $e->getMessage());
                error_log("CalendarController::getCategories - Stack trace: " . $e->getTraceAsString());
            }
        }
    }
    
    /**
     * Get event tags for the filter
     *
     * @return void
     */
    public function getTags()
    {
        // Set content type to JSON
        header('Content-Type: application/json');
        
        try {
            // Get tags with event counts
            $tags = $this->calendarModel->getTags();
            
            echo json_encode($tags);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get tags: ' . $e->getMessage()]);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::getTags - Error: " . $e->getMessage());
                error_log("CalendarController::getTags - Stack trace: " . $e->getTraceAsString());
            }
        }
    }
    
    /**
     * Get US states for the map filter
     *
     * @return void
     */
    public function getStates()
    {
        try {
            // Get states with event counts
            $states = $this->calendarModel->getStatesWithEventCounts();
            echo json_encode($states);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get states: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get cities for the filter
     *
     * @return void
     */
    public function getCities()
    {
        // Get state parameter
        $state = isset($_GET['state']) ? $_GET['state'] : null;
        
        try {
            // Get cities with event counts
            $cities = $this->calendarModel->getCitiesWithEventCounts($state);
            echo json_encode($cities);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get cities: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get venues for the filter
     *
     * @return void
     */
    public function getVenues()
    {
        // Get state and city parameters
        $state = isset($_GET['state']) ? $_GET['state'] : null;
        $city = isset($_GET['city']) ? $_GET['city'] : null;
        
        try {
            // Get venues with event counts
            $venues = $this->calendarModel->getVenuesWithEventCounts($state, $city);
            echo json_encode($venues);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get venues: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get clubs for the filter
     *
     * @return void
     */
    public function getClubs()
    {
        try {
            // Get clubs with event counts
            $clubs = $this->calendarModel->getClubsWithEventCounts();
            echo json_encode($clubs);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get clubs: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get club by ID for AJAX requests
     * 
     * @param int $id Club ID
     * @return void
     */
    public function getClubById($id) {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        try {
            $club = $this->calendarModel->getClubById($id);
            
            if ($club) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'club' => [
                        'id' => $club->id,
                        'name' => $club->name,
                        'description' => $club->description ?? ''
                    ]
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'error' => 'Club not found']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to get club: ' . $e->getMessage()]);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::getClubById - Error: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Search clubs for AJAX requests
     * 
     * @return void
     */
    public function searchClubs() {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        $query = isset($_GET['q']) ? trim($_GET['q']) : '';
        
        if (strlen($query) < 2) {
            echo json_encode(['clubs' => []]);
            return;
        }
        
        try {
            // Search clubs by name
            $clubs = $this->calendarModel->searchClubsByName($query);
            
            // Format response
            $response = [
                'clubs' => array_map(function($club) {
                    return [
                        'id' => $club->id,
                        'name' => $club->name,
                        'description' => $club->description ?? ''
                    ];
                }, $clubs)
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Search failed: ' . $e->getMessage()]);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::searchClubs - Error: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Debug endpoint to check and create tables
     * 
     * @return void
     */
    public function debugTables() {
        header('Content-Type: application/json');
        
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        try {
            $tablesExist = $this->calendarModel->tablesExist();
            
            if (!$tablesExist) {
                $created = $this->calendarModel->createTables();
                echo json_encode([
                    'tables_existed' => false,
                    'tables_created' => $created,
                    'message' => $created ? 'Tables created successfully' : 'Failed to create tables'
                ]);
            } else {
                echo json_encode([
                    'tables_existed' => true,
                    'message' => 'All tables already exist'
                ]);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Debug failed: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Create a new club via AJAX
     * 
     * @return void
     */
    public function createClubAjax() {
        // Debug logging
        error_log("CalendarController::createClubAjax - Method called");
        
        // Set content type first to ensure JSON response
        header('Content-Type: application/json');
        
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request type']);
            return;
        }
        
        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        try {
            // Ensure tables exist
            if (!$this->calendarModel->tablesExist()) {
                if (!$this->calendarModel->createTables()) {
                    http_response_code(500);
                    echo json_encode(['error' => 'Database tables not available']);
                    return;
                }
            }
            
            // Get JSON data
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);
            
            if (!$data || empty($data['name'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Club name is required']);
                return;
            }
            
            // Debug logging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::createClubAjax - Received data: " . json_encode($data));
            }
            
            // Validate CSRF token if available
            if (function_exists('validateCsrfToken') && isset($data['csrf_token'])) {
                if (!validateCsrfToken($data['csrf_token'])) {
                    http_response_code(403);
                    echo json_encode(['error' => 'Invalid CSRF token']);
                    return;
                }
            }
            
            // Create club data
            $clubData = [
                'name' => trim($data['name']),
                'description' => isset($data['description']) ? trim($data['description']) : '',
                'logo' => null,
                'website' => null,
                'email' => null,
                'phone' => null,
                'owner_id' => $_SESSION['user_id']
            ];
            
            // Debug logging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::createClubAjax - Club data: " . json_encode($clubData));
            }
            
            // Create the club
            $clubId = $this->calendarModel->createClub($clubData);
            
            if ($clubId) {
                // Get the created club
                $club = $this->calendarModel->getClubById($clubId);
                
                if ($club) {
                    $response = [
                        'success' => true,
                        'message' => 'Club created successfully',
                        'club' => [
                            'id' => $club->id,
                            'name' => $club->name,
                            'description' => $club->description ?? ''
                        ]
                    ];
                    
                    echo json_encode($response);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => 'Club created but could not retrieve details']);
                }
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to create club - database operation failed']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create club: ' . $e->getMessage()]);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::createClubAjax - Error: " . $e->getMessage());
                error_log("CalendarController::createClubAjax - Stack trace: " . $e->getTraceAsString());
            }
        }
    }
    
    /**
     * Get event image settings for JavaScript
     * 
     * @return void
     */
    public function getEventImageSettings() {
        // Check if this is an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
            return;
        }
        
        try {
            $settings = $this->calendarModel->getEventImageSettings();
            
            header('Content-Type: application/json');
            echo json_encode($settings);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to get settings: ' . $e->getMessage()]);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarController::getEventImageSettings - Error: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Upload event image (Base64)
     * 
     * @return void
     */
    public function uploadEventImage() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }
        
        // Set JSON header
        header('Content-Type: application/json');
        
        // Check if request is POST and AJAX
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }
        
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            http_response_code(403);
            echo json_encode(['error' => 'Invalid CSRF token']);
            return;
        }
        
        try {
            // Get POST data
            $eventId = isset($_POST['event_id']) ? intval($_POST['event_id']) : 0;
            $imageData = isset($_POST['image_data']) ? $_POST['image_data'] : '';
            $fileName = isset($_POST['file_name']) ? trim($_POST['file_name']) : '';
            $fileType = isset($_POST['file_type']) ? trim($_POST['file_type']) : '';
            $fileSize = isset($_POST['file_size']) ? intval($_POST['file_size']) : 0;
            $width = isset($_POST['width']) ? intval($_POST['width']) : null;
            $height = isset($_POST['height']) ? intval($_POST['height']) : null;
            $altText = isset($_POST['alt_text']) ? trim($_POST['alt_text']) : '';
            $caption = isset($_POST['caption']) ? trim($_POST['caption']) : '';
            
            // Validate required fields
            if (empty($imageData) || empty($fileName) || empty($fileType)) {
                http_response_code(400);
                echo json_encode(['error' => 'Missing required fields']);
                return;
            }
            
            // Get image settings
            $settings = $this->calendarModel->getEventImageSettings();
            
            // Check file type
            if (!in_array($fileType, $settings['allowedTypes'])) {
                http_response_code(400);
                echo json_encode(['error' => 'File type not allowed']);
                return;
            }
            
            // Check file size (convert MB to bytes)
            $maxSizeBytes = $settings['maxImageSize'] * 1024 * 1024;
            if ($fileSize > $maxSizeBytes) {
                http_response_code(400);
                echo json_encode(['error' => 'File size exceeds limit']);
                return;
            }
            
            // Check image count limit if event ID is provided
            if ($eventId > 0) {
                $currentCount = $this->calendarModel->countEventImages($eventId);
                if ($currentCount >= $settings['maxImages']) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Maximum number of images reached']);
                    return;
                }
            }
            
            // Validate Base64 data
            if (!preg_match('/^data:image\/[a-zA-Z]+;base64,/', $imageData)) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid image data format']);
                return;
            }
            
            // Save image to database
            $userId = $_SESSION['user_id'];
            $displayOrder = $eventId > 0 ? $this->calendarModel->countEventImages($eventId) + 1 : 1;
            
            $imageId = $this->calendarModel->saveEventImage(
                $eventId,
                $userId,
                $imageData,
                $fileName,
                $fileType,
                $fileSize,
                $width,
                $height,
                $altText,
                $caption,
                $displayOrder,
                false // Not featured by default
            );
            
            if ($imageId) {
                echo json_encode([
                    'success' => true,
                    'image_id' => $imageId,
                    'message' => 'Image uploaded successfully'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to save image']);
            }
            
        } catch (Exception $e) {
            error_log('Error uploading event image: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error occurred']);
        }
    }
    
    /**
     * Delete event image
     * 
     * @return void
     */
    public function deleteEventImage() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }
        
        // Set JSON header
        header('Content-Type: application/json');
        
        // Check if request is POST and AJAX
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }
        
        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            http_response_code(403);
            echo json_encode(['error' => 'Invalid CSRF token']);
            return;
        }
        
        try {
            $imageId = isset($_POST['image_id']) ? intval($_POST['image_id']) : 0;
            
            if ($imageId <= 0) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid image ID']);
                return;
            }
            
            // Get image to check permissions
            $image = $this->calendarModel->getEventImageById($imageId);
            if (!$image) {
                http_response_code(404);
                echo json_encode(['error' => 'Image not found']);
                return;
            }
            
            // Check permissions (user must own the image or be admin/coordinator)
            $userId = $_SESSION['user_id'];
            $canDelete = ($image->user_id == $userId) || isAdmin() || isCoordinator();
            
            if (!$canDelete) {
                http_response_code(403);
                echo json_encode(['error' => 'Permission denied']);
                return;
            }
            
            // Delete image
            if ($this->calendarModel->deleteEventImage($imageId)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Image deleted successfully'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to delete image']);
            }
            
        } catch (Exception $e) {
            error_log('Error deleting event image: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error occurred']);
        }
    }
    
    /**
     * Get event images
     * 
     * @param int $eventId Event ID
     * @return void
     */
    public function getEventImages($eventId) {
        // Set JSON header
        header('Content-Type: application/json');
        
        try {
            $eventId = intval($eventId);
            if ($eventId <= 0) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid event ID']);
                return;
            }
            
            // Get event to check if it exists and user has permission
            $event = $this->calendarModel->getEventById($eventId);
            if (!$event) {
                http_response_code(404);
                echo json_encode(['error' => 'Event not found']);
                return;
            }
            
            // Check if user can view this event
            if ($event->privacy === 'private' && !isLoggedIn()) {
                http_response_code(403);
                echo json_encode(['error' => 'Permission denied']);
                return;
            }
            
            if ($event->privacy === 'members' && !isLoggedIn()) {
                http_response_code(403);
                echo json_encode(['error' => 'Login required']);
                return;
            }
            
            // Get images (exclude Base64 data for performance)
            $images = $this->calendarModel->getEventImages($eventId);
            
            // Remove Base64 data from response for performance
            $responseImages = [];
            foreach ($images as $image) {
                $responseImages[] = [
                    'id' => $image->id,
                    'file_name' => $image->file_name,
                    'file_type' => $image->file_type,
                    'file_size' => $image->file_size,
                    'width' => $image->width,
                    'height' => $image->height,
                    'alt_text' => $image->alt_text,
                    'caption' => $image->caption,
                    'display_order' => $image->display_order,
                    'is_featured' => $image->is_featured,
                    'created_at' => $image->created_at
                ];
            }
            
            echo json_encode([
                'success' => true,
                'images' => $responseImages
            ]);
            
        } catch (Exception $e) {
            error_log('Error getting event images: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error occurred']);
        }
    }
    
    /**
     * Request club ownership verification
     * 
     * @param int $clubId Club ID (optional)
     * @return void
     */
    public function requestClubOwnership($clubId = null) {
        // Check if user is logged in
        if (!isLoggedIn()) {
            flash('calendar_message', 'You must be logged in to request club ownership', 'alert alert-danger');
            redirect('users/login');
        }
        
        // Get user info
        $user = $this->userModel->getUserById($_SESSION['user_id']);
        
        // If club ID provided, get club info
        $club = null;
        if ($clubId) {
            $club = $this->calendarModel->getClubById($clubId);
            if (!$club) {
                flash('calendar_message', 'Club not found', 'alert alert-danger');
                redirect('calendar/manageClubs');
            }
            
            // Check if user already has a pending request for this club
            $existingRequest = $this->calendarModel->getClubOwnershipRequest($clubId, $_SESSION['user_id']);
            if ($existingRequest && $existingRequest->status == 'pending') {
                flash('calendar_message', 'You already have a pending ownership request for this club', 'alert alert-warning');
                redirect('calendar/manageClubs');
            }
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                flash('calendar_message', 'Invalid request', 'alert alert-danger');
                redirect('calendar/manageClubs');
            }
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Initialize data
            $data = [
                'club' => $club,
                'clubs' => $club ? null : $this->calendarModel->getClubs(),
                'title' => 'Request Club Ownership',
                'first_name' => trim($_POST['first_name']),
                'last_name' => trim($_POST['last_name']),
                'email' => trim($_POST['email']),
                'phone' => trim($_POST['phone']),
                'club_id' => $clubId ? $clubId : (int)$_POST['club_id'],
                'club_information' => trim($_POST['club_information']),
                'ownership_proof' => trim($_POST['ownership_proof']),
                'additional_info' => trim($_POST['additional_info']),
                'agree_terms' => isset($_POST['agree_terms']) ? 1 : 0,
                'first_name_err' => '',
                'last_name_err' => '',
                'email_err' => '',
                'phone_err' => '',
                'club_id_err' => '',
                'club_information_err' => '',
                'ownership_proof_err' => '',
                'agree_terms_err' => ''
            ];
            
            // Validate first name
            if (empty($data['first_name'])) {
                $data['first_name_err'] = 'Please enter your first name';
            }
            
            // Validate last name
            if (empty($data['last_name'])) {
                $data['last_name_err'] = 'Please enter your last name';
            }
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter your email';
            } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $data['email_err'] = 'Please enter a valid email address';
            }
            
            // Validate phone
            if (empty($data['phone'])) {
                $data['phone_err'] = 'Please enter your phone number';
            }
            
            // Validate club selection
            if (!$clubId && empty($data['club_id'])) {
                $data['club_id_err'] = 'Please select a club';
            }
            
            // Validate club information
            if (empty($data['club_information'])) {
                $data['club_information_err'] = 'Please provide club information';
            } elseif (strlen($data['club_information']) < 100) {
                $data['club_information_err'] = 'Club information must be at least 100 characters';
            }
            
            // Validate ownership proof
            if (empty($data['ownership_proof'])) {
                $data['ownership_proof_err'] = 'Please provide ownership proof';
            } elseif (strlen($data['ownership_proof']) < 50) {
                $data['ownership_proof_err'] = 'Ownership proof must be at least 50 characters';
            }
            
            // Validate terms agreement
            if (!$data['agree_terms']) {
                $data['agree_terms_err'] = 'You must agree to the terms';
            }
            
            // Handle file uploads
            $attachments = [];
            if (isset($_FILES['documents']) && !empty($_FILES['documents']['name'][0])) {
                $uploadResult = $this->handleVerificationDocuments($_FILES['documents']);
                if ($uploadResult['success']) {
                    $attachments = $uploadResult['files'];
                } else {
                    flash('calendar_message', $uploadResult['error'], 'alert alert-danger');
                    $this->view('calendar/request_club_ownership', $data);
                    return;
                }
            }
            
            // Check for errors
            if (empty($data['first_name_err']) && empty($data['last_name_err']) && 
                empty($data['email_err']) && empty($data['phone_err']) && 
                empty($data['club_id_err']) && empty($data['club_information_err']) && 
                empty($data['ownership_proof_err']) && empty($data['agree_terms_err'])) {
                
                // Submit verification request
                $requestData = [
                    'club_id' => $data['club_id'],
                    'user_id' => $_SESSION['user_id'],
                    'user_name' => $data['first_name'] . ' ' . $data['last_name'],
                    'user_email' => $data['email'],
                    'user_phone' => $data['phone'],
                    'club_information' => $data['club_information'],
                    'ownership_proof' => $data['ownership_proof'],
                    'additional_info' => $data['additional_info'],
                    'attachments' => !empty($attachments) ? json_encode($attachments) : null
                ];
                
                if ($this->calendarModel->submitClubOwnershipVerification($requestData)) {
                    // Send notification to admins
                    $this->sendOwnershipRequestNotification($requestData);
                    
                    flash('calendar_message', 'Your club ownership verification request has been submitted successfully. You will be notified once it has been reviewed.', 'alert alert-success');
                    redirect('calendar/manageClubs');
                } else {
                    flash('calendar_message', 'Something went wrong. Please try again.', 'alert alert-danger');
                }
            }
            
            // Load view with errors
            $this->view('calendar/request_club_ownership', $data);
        } else {
            // Initialize data
            $data = [
                'club' => $club,
                'clubs' => $club ? null : $this->calendarModel->getClubs(),
                'title' => 'Request Club Ownership',
                'first_name' => $user->name ? explode(' ', $user->name)[0] : '',
                'last_name' => $user->name && strpos($user->name, ' ') !== false ? substr($user->name, strpos($user->name, ' ') + 1) : '',
                'email' => $user->email,
                'phone' => '',
                'club_id' => $clubId ? $clubId : '',
                'club_information' => '',
                'ownership_proof' => '',
                'additional_info' => '',
                'agree_terms' => 0,
                'first_name_err' => '',
                'last_name_err' => '',
                'email_err' => '',
                'phone_err' => '',
                'club_id_err' => '',
                'club_information_err' => '',
                'ownership_proof_err' => '',
                'agree_terms_err' => ''
            ];
            
            $this->view('calendar/request_club_ownership', $data);
        }
    }
    
    /**
     * Handle verification document uploads
     * 
     * @param array $files Files array from $_FILES
     * @return array Result with success status and files or error
     */
    private function handleVerificationDocuments($files) {
        $uploadDir = APPROOT . '/../uploads/club_verifications/';
        $allowedTypes = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'];
        $maxFileSize = 5 * 1024 * 1024; // 5MB
        $maxFiles = 5;
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                return ['success' => false, 'error' => 'Failed to create upload directory'];
            }
        }
        
        $uploadedFiles = [];
        $fileCount = count($files['name']);
        
        if ($fileCount > $maxFiles) {
            return ['success' => false, 'error' => "You can only upload up to {$maxFiles} files"];
        }
        
        for ($i = 0; $i < $fileCount; $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                continue; // Skip files with errors
            }
            
            $fileName = $files['name'][$i];
            $fileSize = $files['size'][$i];
            $fileTmp = $files['tmp_name'][$i];
            
            // Check file size
            if ($fileSize > $maxFileSize) {
                return ['success' => false, 'error' => "File '{$fileName}' is too large. Maximum size is 5MB"];
            }
            
            // Check file extension
            $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            if (!in_array($fileExt, $allowedTypes)) {
                return ['success' => false, 'error' => "File '{$fileName}' has an invalid file type"];
            }
            
            // Generate unique filename
            $newFileName = uniqid() . '_' . time() . '.' . $fileExt;
            $targetPath = $uploadDir . $newFileName;
            
            // Move uploaded file
            if (move_uploaded_file($fileTmp, $targetPath)) {
                $uploadedFiles[] = $newFileName;
            } else {
                return ['success' => false, 'error' => "Failed to upload file '{$fileName}'"];
            }
        }
        
        return ['success' => true, 'files' => $uploadedFiles];
    }
    
    /**
     * Send ownership request notification to admins
     * 
     * @param array $requestData Request data
     * @return void
     */
    private function sendOwnershipRequestNotification($requestData) {
        // Get admin users
        $admins = $this->userModel->getUsersByRole('admin');
        
        if (empty($admins)) {
            return;
        }
        
        // Get club info
        $club = $this->calendarModel->getClubById($requestData['club_id']);
        
        $subject = 'New Club Ownership Verification Request';
        $message = "A new club ownership verification request has been submitted.\n\n";
        $message .= "Club: " . $club->name . "\n";
        $message .= "Requester: " . $requestData['user_name'] . "\n";
        $message .= "Email: " . $requestData['user_email'] . "\n";
        $message .= "Phone: " . $requestData['user_phone'] . "\n\n";
        $message .= "Please review this request in the admin panel:\n";
        $message .= URLROOT . "/admin/clubOwnershipVerifications\n\n";
        $message .= "This is an automated message from " . SITENAME;
        
        foreach ($admins as $admin) {
            mail($admin->email, $subject, $message);
        }
    }
    
    /**
     * Search online venues using the configured map provider
     * 
     * @return void
     */
    public function searchOnlineVenues() {
        // Set JSON header
        header('Content-Type: application/json');
        
        // Check for POST and AJAX request
        if ($_SERVER['REQUEST_METHOD'] != 'POST' || !isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid request']);
            return;
        }
        
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
            return;
        }
        
        // Get venue name from POST data
        $venueName = isset($_POST['venue_name']) ? trim($_POST['venue_name']) : '';
        
        if (empty($venueName)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Venue name is required']);
            return;
        }
        
        if (strlen($venueName) < 3) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Venue name must be at least 3 characters']);
            return;
        }
        
        try {
            // Get map provider settings
            $mapSettings = $this->calendarModel->getMapProviderSettings();
            $provider = $mapSettings['provider'] ?? 'openstreetmap';
            $apiKey = $mapSettings['api_key'] ?? '';
            $serverApiKey = $mapSettings['server_api_key'] ?? '';
            
            // Get current user's location for biasing search results
            $userLocation = null;
            if (isset($_SESSION['user_id'])) {
                $user = $this->userModel->getUserById($_SESSION['user_id']);
                if ($user && (!empty($user->city) || !empty($user->state) || !empty($user->zip))) {
                    $userLocation = [
                        'city' => $user->city ?? '',
                        'state' => $user->state ?? '',
                        'zip' => $user->zip ?? '',
                        'address' => $user->address ?? ''
                    ];
                    
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("User location for venue search: " . json_encode($userLocation));
                    }
                }
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Venue search - Provider: {$provider}, API Key: " . (empty($apiKey) ? 'NOT SET' : 'SET'));
            }
            
            $venues = [];
            
            // Search venues based on the configured provider
            try {
                switch ($provider) {
                    case 'google':
                        $venues = $this->searchVenuesGoogle($venueName, $serverApiKey, $userLocation);
                        break;

                    case 'mapbox':
                        $venues = $this->searchVenuesMapbox($venueName, $apiKey, $userLocation);
                        break;
                    case 'here':
                        $venues = $this->searchVenuesHere($venueName, $apiKey, $userLocation);
                        break;
                    case 'openstreetmap':
                    default:
                        $venues = $this->searchVenuesOpenStreetMap($venueName, $userLocation);
                        break;
                }
            } catch (Exception $providerError) {
                // If the primary provider fails, fallback to OpenStreetMap
                if ($provider !== 'openstreetmap') {
                    error_log("Primary provider ({$provider}) failed, falling back to OpenStreetMap: " . $providerError->getMessage());
                    try {
                        $venues = $this->searchVenuesOpenStreetMap($venueName, $userLocation);
                        $provider = 'openstreetmap (fallback)';
                    } catch (Exception $fallbackError) {
                        error_log("Fallback provider also failed: " . $fallbackError->getMessage());
                        throw $providerError; // Throw original error
                    }
                } else {
                    throw $providerError;
                }
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Venue search - Found " . count($venues) . " venues");
            }
            
            $response = [
                'success' => true,
                'venues' => $venues,
                'provider' => $provider
            ];
            
            // Add debug information if debug mode is enabled
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $response['debug'] = [
                    'venue_count' => count($venues),
                    'first_venue_sample' => !empty($venues) ? [
                        'name' => $venues[0]['name'] ?? 'N/A',
                        'address' => $venues[0]['address'] ?? 'N/A',
                        'city' => $venues[0]['city'] ?? 'N/A',
                        'state' => $venues[0]['state'] ?? 'N/A',
                        'zip' => $venues[0]['zip'] ?? 'N/A',
                        'phone' => $venues[0]['phone'] ?? 'N/A',
                        'website' => $venues[0]['website'] ?? 'N/A',
                        'formatted_address' => $venues[0]['formatted_address'] ?? 'N/A'
                    ] : null,
                    'provider_used' => $provider,
                    'api_key_configured' => !empty($serverApiKey) ? 'YES' : 'NO'
                ];
            }
            
            echo json_encode($response);
            
        } catch (Exception $e) {
            error_log('Venue search error (' . ($provider ?? 'unknown') . '): ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'An error occurred while searching for venues: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Search venues using Google Places API
     * 
     * @param string $venueName Venue name to search
     * @param string $apiKey Google API key
     * @param array|null $userLocation User's location for biasing results
     * @return array Array of venue data
     */
    private function searchVenuesGoogle($venueName, $apiKey, $userLocation = null) {
        if (empty($apiKey)) {
            throw new Exception('Google API key not configured');
        }
        
        // Build query parameters
        $queryParams = [
            'query' => $venueName,
            'type' => 'establishment',
            'key' => $apiKey
        ];
        
        // Add location biasing if user location is available
        if ($userLocation) {
            // Try to create a location string for biasing
            $locationParts = [];
            if (!empty($userLocation['city'])) {
                $locationParts[] = $userLocation['city'];
            }
            if (!empty($userLocation['state'])) {
                $locationParts[] = $userLocation['state'];
            }
            if (!empty($userLocation['zip'])) {
                $locationParts[] = $userLocation['zip'];
            }
            
            if (!empty($locationParts)) {
                // Add location bias to restrict results to USA and user's area
                $locationString = implode(', ', $locationParts) . ', USA';
                $queryParams['query'] = $venueName . ' in ' . $locationString;
                
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("Google Places search query with location: " . $queryParams['query']);
                }
            }
        } else {
            // If no user location, at least restrict to USA
            $queryParams['query'] = $venueName . ' in USA';
            $queryParams['region'] = 'us'; // Bias results to US
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Google Places search query (no user location): " . $queryParams['query']);
            }
        }
        
        $url = 'https://maps.googleapis.com/maps/api/place/textsearch/json?' . http_build_query($queryParams);
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Google Places API URL: " . $url);
        }
        
        // Use cURL for better error handling
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Events and Shows Calendar/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("Google Places API cURL error: " . $error);
            throw new Exception('Network error connecting to Google Places API: ' . $error);
        }
        
        if ($httpCode !== 200) {
            error_log("Google Places API HTTP error: " . $httpCode . " - Response: " . $response);
            throw new Exception('Google Places API returned error: ' . $httpCode);
        }
        
        $data = json_decode($response, true);
        
        if (!$data) {
            error_log("Google Places API invalid JSON response: " . $response);
            throw new Exception('Invalid response from Google Places API');
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Google Places API response status: " . ($data['status'] ?? 'unknown'));
            error_log("Google Places API results count: " . (isset($data['results']) ? count($data['results']) : 0));
            if (isset($data['status']) && $data['status'] !== 'OK') {
                error_log("Google Places API full response: " . json_encode($data));
            }
        }
        
        // Check for API errors
        if (isset($data['status']) && $data['status'] !== 'OK') {
            $errorMessage = 'Google Places API error: ' . $data['status'];
            if (isset($data['error_message'])) {
                $errorMessage .= ' - ' . $data['error_message'];
            }
            error_log($errorMessage);
            throw new Exception($errorMessage);
        }
        
        $venues = [];
        if ($data && $data['status'] === 'OK' && !empty($data['results'])) {
            foreach (array_slice($data['results'], 0, 10) as $place) {
                $venue = [
                    'name' => $place['name'],
                    'address' => '',
                    'city' => '',
                    'state' => '',
                    'zip' => '',
                    'country' => '',
                    'latitude' => $place['geometry']['location']['lat'] ?? null,
                    'longitude' => $place['geometry']['location']['lng'] ?? null,
                    'phone' => '',
                    'website' => '',
                    'email' => '',
                    'formatted_address' => $place['formatted_address'] ?? '',
                    'place_id' => $place['place_id'] ?? ''
                ];
                
                // Parse address components properly
                if (!empty($place['address_components'])) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("Text Search address_components found: " . json_encode($place['address_components']));
                    }
                    foreach ($place['address_components'] as $component) {
                        $types = $component['types'];
                        if (in_array('street_number', $types)) {
                            $venue['address'] = $component['long_name'];
                        } elseif (in_array('route', $types)) {
                            $venue['address'] = trim(($venue['address'] ?? '') . ' ' . $component['long_name']);
                        } elseif (in_array('locality', $types)) {
                            $venue['city'] = $component['long_name'];
                        } elseif (in_array('administrative_area_level_1', $types)) {
                            $venue['state'] = $component['short_name'];
                        } elseif (in_array('postal_code', $types)) {
                            $venue['zip'] = $component['long_name'];
                        } elseif (in_array('country', $types)) {
                            $venue['country'] = $component['long_name'];
                        }
                    }
                } else {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("No address_components in Text Search result for: " . $place['name']);
                    }
                }
                
                // Get additional details from Place Details API if place_id is available
                if (!empty($venue['place_id'])) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("Calling Place Details API for place_id: " . $venue['place_id']);
                    }
                    $details = $this->getGooglePlaceDetails($venue['place_id'], $apiKey);
                    if ($details) {
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("Place Details API returned data: " . json_encode($details));
                        }
                        // Add contact information
                        if (!empty($details['phone'])) $venue['phone'] = $details['phone'];
                        if (!empty($details['website'])) $venue['website'] = $details['website'];
                        
                        // Parse address components from Place Details (Text Search doesn't include them)
                        if (!empty($details['address_components'])) {
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("Place Details address_components found: " . json_encode($details['address_components']));
                                error_log("Current venue data before Place Details parsing: " . json_encode([
                                    'address' => $venue['address'],
                                    'city' => $venue['city'],
                                    'state' => $venue['state'],
                                    'zip' => $venue['zip']
                                ]));
                            }
                            
                            // Reset address to build it properly from components
                            $streetNumber = '';
                            $route = '';
                            
                            foreach ($details['address_components'] as $component) {
                                $types = $component['types'];
                                if (in_array('street_number', $types)) {
                                    $streetNumber = $component['long_name'];
                                } elseif (in_array('route', $types)) {
                                    $route = $component['long_name'];
                                } elseif (in_array('locality', $types)) {
                                    $venue['city'] = $component['long_name'];
                                } elseif (in_array('administrative_area_level_1', $types)) {
                                    $venue['state'] = $component['short_name'];
                                } elseif (in_array('postal_code', $types)) {
                                    $venue['zip'] = $component['long_name'];
                                } elseif (in_array('country', $types)) {
                                    $venue['country'] = $component['long_name'];
                                }
                            }
                            
                            // Build proper street address
                            if ($streetNumber && $route) {
                                $venue['address'] = trim($streetNumber . ' ' . $route);
                            } elseif ($route) {
                                $venue['address'] = $route;
                            }
                            
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("Venue data after Place Details parsing: " . json_encode([
                                    'address' => $venue['address'],
                                    'city' => $venue['city'],
                                    'state' => $venue['state'],
                                    'zip' => $venue['zip']
                                ]));
                            }
                        } else {
                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("No address_components in Place Details result");
                            }
                        }
                    } else {
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("Place Details API returned no data for place_id: " . $venue['place_id']);
                        }
                    }
                } else {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("No place_id available for venue: " . $venue['name']);
                    }
                }
                
                // If we still couldn't parse the street address properly, try to extract it from formatted_address
                if (empty($venue['address']) && !empty($venue['formatted_address'])) {
                    $venue['address'] = $this->extractStreetAddressFromFormatted($venue['formatted_address'], $venue['city'], $venue['state'], $venue['zip']);
                }
                
                $venues[] = $venue;
            }
        }
        
        return $venues;
    }
    
    /**
     * Extract street address from formatted address
     * 
     * @param string $formattedAddress Full formatted address
     * @param string $city City name
     * @param string $state State name
     * @param string $zip ZIP code
     * @return string Street address
     */
    private function extractStreetAddressFromFormatted($formattedAddress, $city = '', $state = '', $zip = '') {
        if (empty($formattedAddress)) {
            return '';
        }
        
        // Split the formatted address by commas
        $parts = array_map('trim', explode(',', $formattedAddress));
        
        if (empty($parts)) {
            return '';
        }
        
        // The first part is usually the street address
        $streetAddress = $parts[0];
        
        // Remove city, state, zip, and country from the street address if they somehow got included
        $toRemove = array_filter([$city, $state, $zip, 'USA', 'United States']);
        foreach ($toRemove as $remove) {
            if (!empty($remove)) {
                $streetAddress = str_ireplace($remove, '', $streetAddress);
            }
        }
        
        return trim($streetAddress);
    }
    
    /**
     * Get additional place details from Google Place Details API
     * 
     * @param string $placeId Google Place ID
     * @param string $apiKey Google API key
     * @return array|null Place details or null if failed
     */
    private function getGooglePlaceDetails($placeId, $apiKey) {
        if (empty($placeId) || empty($apiKey)) {
            return null;
        }
        
        $queryParams = [
            'place_id' => $placeId,
            'fields' => 'formatted_phone_number,website,international_phone_number,address_components',
            'key' => $apiKey
        ];
        
        $url = 'https://maps.googleapis.com/maps/api/place/details/json?' . http_build_query($queryParams);
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Google Place Details API URL: " . $url);
        }
        
        // Use cURL for better error handling
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Shorter timeout for details
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Events and Shows Calendar/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error || $httpCode !== 200) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Google Place Details API error: HTTP {$httpCode}, cURL error: {$error}");
            }
            return null; // Don't throw exception, just return null for optional data
        }
        
        $data = json_decode($response, true);
        
        if (!$data || !isset($data['status']) || $data['status'] !== 'OK') {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Google Place Details API invalid response: " . json_encode($data));
            }
            return null;
        }
        
        $result = $data['result'] ?? [];
        $details = [];
        
        // Extract phone number
        if (!empty($result['formatted_phone_number'])) {
            $details['phone'] = $result['formatted_phone_number'];
        } elseif (!empty($result['international_phone_number'])) {
            $details['phone'] = $result['international_phone_number'];
        }
        
        // Extract website
        if (!empty($result['website'])) {
            $details['website'] = $result['website'];
        }
        
        // Extract address components if available
        if (!empty($result['address_components'])) {
            $details['address_components'] = $result['address_components'];
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Google Place Details extracted: " . json_encode($details));
        }
        
        return $details;
    }
    
    /**
     * Search venues using Mapbox Geocoding API
     * 
     * @param string $venueName Venue name to search
     * @param string $apiKey Mapbox API key
     * @param array|null $userLocation User's location for biasing results
     * @return array Array of venue data
     */
    private function searchVenuesMapbox($venueName, $apiKey, $userLocation = null) {
        if (empty($apiKey)) {
            throw new Exception('Mapbox API key not configured');
        }
        
        // Build search query with location biasing
        $searchQuery = $venueName;
        $queryParams = [
            'access_token' => $apiKey,
            'types' => 'poi',
            'limit' => 10,
            'country' => 'us' // Restrict to USA
        ];
        
        // Add location biasing if user location is available
        if ($userLocation && (!empty($userLocation['city']) || !empty($userLocation['state']))) {
            $locationParts = [];
            if (!empty($userLocation['city'])) {
                $locationParts[] = $userLocation['city'];
            }
            if (!empty($userLocation['state'])) {
                $locationParts[] = $userLocation['state'];
            }
            
            if (!empty($locationParts)) {
                $searchQuery = $venueName . ' ' . implode(' ', $locationParts);
            }
        }
        
        $url = "https://api.mapbox.com/geocoding/v5/mapbox.places/" . urlencode($searchQuery) . ".json?" . http_build_query($queryParams);
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        $venues = [];
        if ($data && !empty($data['features'])) {
            foreach ($data['features'] as $feature) {
                $venue = [
                    'name' => $feature['text'] ?? '',
                    'address' => '',
                    'city' => '',
                    'state' => '',
                    'zip' => '',
                    'country' => '',
                    'latitude' => $feature['center'][1] ?? null,
                    'longitude' => $feature['center'][0] ?? null,
                    'phone' => '',
                    'website' => '',
                    'email' => '',
                    'formatted_address' => $feature['place_name'] ?? ''
                ];
                
                // Parse context for address components
                if (!empty($feature['context'])) {
                    foreach ($feature['context'] as $context) {
                        if (strpos($context['id'], 'place') === 0) {
                            $venue['city'] = $context['text'];
                        } elseif (strpos($context['id'], 'region') === 0) {
                            $venue['state'] = $context['short_code'] ?? $context['text'];
                        } elseif (strpos($context['id'], 'postcode') === 0) {
                            $venue['zip'] = $context['text'];
                        } elseif (strpos($context['id'], 'country') === 0) {
                            $venue['country'] = $context['text'];
                        }
                    }
                }
                
                // Extract street address from place_name
                if (!empty($venue['formatted_address'])) {
                    $venue['address'] = $this->extractStreetAddressFromFormatted(
                        $venue['formatted_address'], 
                        $venue['city'], 
                        $venue['state'], 
                        $venue['zip']
                    );
                }
                
                $venues[] = $venue;
            }
        }
        
        return $venues;
    }
    
    /**
     * Search venues using HERE Geocoding API
     * 
     * @param string $venueName Venue name to search
     * @param string $apiKey HERE API key
     * @param array|null $userLocation User's location for biasing results
     * @return array Array of venue data
     */
    private function searchVenuesHere($venueName, $apiKey, $userLocation = null) {
        if (empty($apiKey)) {
            throw new Exception('HERE API key not configured');
        }
        
        // Build search query with location biasing
        $searchQuery = $venueName;
        $queryParams = [
            'apikey' => $apiKey,
            'limit' => 10,
            'in' => 'countryCode:USA' // Restrict to USA
        ];
        
        // Add location biasing if user location is available
        if ($userLocation && (!empty($userLocation['city']) || !empty($userLocation['state']))) {
            $locationParts = [];
            if (!empty($userLocation['city'])) {
                $locationParts[] = $userLocation['city'];
            }
            if (!empty($userLocation['state'])) {
                $locationParts[] = $userLocation['state'];
            }
            
            if (!empty($locationParts)) {
                $searchQuery = $venueName . ', ' . implode(', ', $locationParts) . ', USA';
            }
        } else {
            $searchQuery = $venueName . ', USA';
        }
        
        $queryParams['q'] = $searchQuery;
        $url = 'https://geocode.search.hereapi.com/v1/geocode?' . http_build_query($queryParams);
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        $venues = [];
        if ($data && !empty($data['items'])) {
            foreach ($data['items'] as $item) {
                $venue = [
                    'name' => $item['title'] ?? '',
                    'address' => '',
                    'city' => $item['address']['city'] ?? '',
                    'state' => $item['address']['state'] ?? '',
                    'zip' => $item['address']['postalCode'] ?? '',
                    'country' => $item['address']['countryName'] ?? '',
                    'latitude' => $item['position']['lat'] ?? null,
                    'longitude' => $item['position']['lng'] ?? null,
                    'phone' => '',
                    'website' => '',
                    'email' => '',
                    'formatted_address' => $item['address']['label'] ?? ''
                ];
                
                // Build proper street address from components
                $addressParts = [];
                if (!empty($item['address']['houseNumber'])) {
                    $addressParts[] = $item['address']['houseNumber'];
                }
                if (!empty($item['address']['street'])) {
                    $addressParts[] = $item['address']['street'];
                }
                $venue['address'] = implode(' ', $addressParts);
                
                // If we couldn't build address from components, try to extract from label
                if (empty($venue['address']) && !empty($venue['formatted_address'])) {
                    $venue['address'] = $this->extractStreetAddressFromFormatted(
                        $venue['formatted_address'], 
                        $venue['city'], 
                        $venue['state'], 
                        $venue['zip']
                    );
                }
                
                $venues[] = $venue;
            }
        }
        
        return $venues;
    }
    
    /**
     * Search venues using OpenStreetMap Nominatim
     * 
     * @param string $venueName Venue name to search
     * @param array|null $userLocation User's location for biasing results
     * @return array Array of venue data
     */
    private function searchVenuesOpenStreetMap($venueName, $userLocation = null) {
        // Build search query with location biasing
        $searchQuery = $venueName;
        $queryParams = [
            'format' => 'json',
            'addressdetails' => 1,
            'limit' => 10,
            'extratags' => 1,
            'countrycodes' => 'us' // Restrict to USA
        ];
        
        // Add location biasing if user location is available
        if ($userLocation && (!empty($userLocation['city']) || !empty($userLocation['state']))) {
            $locationParts = [];
            if (!empty($userLocation['city'])) {
                $locationParts[] = $userLocation['city'];
            }
            if (!empty($userLocation['state'])) {
                $locationParts[] = $userLocation['state'];
            }
            
            if (!empty($locationParts)) {
                $searchQuery = $venueName . ', ' . implode(', ', $locationParts) . ', USA';
            }
        } else {
            $searchQuery = $venueName . ', USA';
        }
        
        $queryParams['q'] = $searchQuery;
        $url = 'https://nominatim.openstreetmap.org/search?' . http_build_query($queryParams);
        
        $context = stream_context_create([
            'http' => [
                'header' => "User-Agent: EventsAndShows/1.0\r\n"
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        $data = json_decode($response, true);
        
        $venues = [];
        if ($data && is_array($data)) {
            foreach ($data as $item) {
                $venue = [
                    'name' => $item['display_name'] ?? '',
                    'address' => '',
                    'city' => $item['address']['city'] ?? $item['address']['town'] ?? $item['address']['village'] ?? '',
                    'state' => $item['address']['state'] ?? '',
                    'zip' => $item['address']['postcode'] ?? '',
                    'country' => $item['address']['country'] ?? '',
                    'latitude' => $item['lat'] ?? null,
                    'longitude' => $item['lon'] ?? null,
                    'phone' => $item['extratags']['phone'] ?? '',
                    'website' => $item['extratags']['website'] ?? '',
                    'email' => $item['extratags']['email'] ?? '',
                    'formatted_address' => $item['display_name'] ?? ''
                ];
                
                // Build address from components
                $addressParts = [];
                if (!empty($item['address']['house_number'])) $addressParts[] = $item['address']['house_number'];
                if (!empty($item['address']['road'])) $addressParts[] = $item['address']['road'];
                $venue['address'] = implode(' ', $addressParts);
                
                // Use name from extratags if available, otherwise use display_name
                if (!empty($item['extratags']['name'])) {
                    $venue['name'] = $item['extratags']['name'];
                } elseif (!empty($item['name'])) {
                    $venue['name'] = $item['name'];
                }
                
                $venues[] = $venue;
            }
        }
        
        return $venues;
    }
}