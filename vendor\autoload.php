<?php
/**
 * Autoloader for vendor dependencies
 * 
 * This file provides autoloading for PHPMailer and other vendor dependencies.
 */

// Autoloader for PHPMailer
spl_autoload_register(function ($class) {
    // PHPMailer namespace
    $prefix = 'PHPMailer\\PHPMailer\\';
    $base_dir = __DIR__ . '/phpmailer/src/';
    
    // Check if the class uses the PHPMailer namespace prefix
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) === 0) {
        // Get the relative class name
        $relative_class = substr($class, $len);
        
        // Replace namespace separators with directory separators
        // and add .php extension
        $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
        
        // If the file exists, require it
        if (file_exists($file)) {
            require_once $file;
            return true;
        }
    }
    
    return false;
});

// Note: Facebook SDK autoloaders removed since this project uses Facebook API directly

// Note: Only PHPMailer autoloader included to avoid conflicts with existing Facebook API setup

// Debug logging for autoloader (only if DEBUG_MODE is defined and true)
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_log('Vendor autoloader loaded successfully');
    
    // Log available PHPMailer classes
    $phpmailerSrcDir = __DIR__ . '/phpmailer/src/';
    if (is_dir($phpmailerSrcDir)) {
        $phpmailerFiles = scandir($phpmailerSrcDir);
        $phpmailerClasses = array_filter($phpmailerFiles, function($file) {
            return pathinfo($file, PATHINFO_EXTENSION) === 'php';
        });
        error_log('PHPMailer classes available: ' . implode(', ', $phpmailerClasses));
    }
}
?>
