<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Service Workers</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        #console-logs { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔍 Debug Service Workers</h2>
        
        <div class="debug-section">
            <h3>1. Service Worker Support</h3>
            <div id="sw-support">Checking...</div>
        </div>
        
        <div class="debug-section">
            <h3>2. Active Service Workers</h3>
            <div id="active-sw">Checking...</div>
        </div>
        
        <div class="debug-section">
            <h3>3. Service Worker Registration Status</h3>
            <div id="registration-status">Checking...</div>
        </div>
        
        <div class="debug-section">
            <h3>4. FCM Service Worker Test</h3>
            <div id="fcm-sw-status">Testing...</div>
            <button id="test-fcm-sw" class="btn btn-primary mt-2">Test FCM Service Worker</button>
        </div>
        
        <div class="debug-section">
            <h3>5. Fix Service Worker Issues</h3>
            <div id="fix-status">Ready to fix...</div>
            <button id="unregister-all" class="btn btn-warning mt-2">Unregister All Service Workers</button>
            <button id="register-fcm" class="btn btn-success mt-2">Register FCM Service Worker</button>
        </div>
        
        <div class="debug-section">
            <h3>Console Logs</h3>
            <div id="console-logs"></div>
        </div>
    </div>

    <script>
        // Console logging
        const logs = [];
        const originalLog = console.log;
        const originalError = console.error;
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateConsoleLogs();
            
            if (type === 'error') {
                originalError(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = function(...args) { addLog('log', args.join(' ')); };
        console.error = function(...args) { addLog('error', args.join(' ')); };
        
        function updateConsoleLogs() {
            document.getElementById('console-logs').innerHTML = logs.slice(-20).join('<br>');
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="${type}">${message}</span>`;
        }
        
        // 1. Check service worker support
        function checkServiceWorkerSupport() {
            const supported = 'serviceWorker' in navigator;
            updateStatus('sw-support', 
                supported ? '✅ Service Worker API supported' : '❌ Service Worker API not supported',
                supported ? 'success' : 'error'
            );
            console.log('Service Worker support:', supported);
        }
        
        // 2. Check active service workers
        async function checkActiveServiceWorkers() {
            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                console.log('Found', registrations.length, 'service worker registrations');
                
                let statusHtml = `<strong>Found ${registrations.length} registrations:</strong><br>`;
                
                for (let i = 0; i < registrations.length; i++) {
                    const reg = registrations[i];
                    const scope = reg.scope;
                    const scriptURL = reg.active?.scriptURL || reg.installing?.scriptURL || reg.waiting?.scriptURL || 'Unknown';
                    const state = reg.active?.state || reg.installing?.state || reg.waiting?.state || 'Unknown';
                    
                    statusHtml += `<br><strong>Registration ${i + 1}:</strong><br>`;
                    statusHtml += `&nbsp;&nbsp;Scope: ${scope}<br>`;
                    statusHtml += `&nbsp;&nbsp;Script: ${scriptURL}<br>`;
                    statusHtml += `&nbsp;&nbsp;State: ${state}<br>`;
                    
                    console.log(`Registration ${i + 1}:`, {scope, scriptURL, state});
                }
                
                updateStatus('active-sw', statusHtml, registrations.length > 0 ? 'info' : 'warning');
                
            } catch (error) {
                console.error('Error checking service workers:', error);
                updateStatus('active-sw', '❌ Error checking service workers: ' + error.message, 'error');
            }
        }
        
        // 3. Check registration status
        async function checkRegistrationStatus() {
            try {
                // Check if we have a controller
                const controller = navigator.serviceWorker.controller;
                let statusHtml = '';
                
                if (controller) {
                    statusHtml += `✅ <strong>Active Controller:</strong> ${controller.scriptURL}<br>`;
                    statusHtml += `&nbsp;&nbsp;State: ${controller.state}<br>`;
                } else {
                    statusHtml += '❌ <strong>No active controller</strong><br>';
                }
                
                // Check ready state
                const registration = await navigator.serviceWorker.ready;
                statusHtml += `✅ <strong>Ready Registration:</strong> ${registration.scope}<br>`;
                
                if (registration.active) {
                    statusHtml += `&nbsp;&nbsp;Active: ${registration.active.scriptURL}<br>`;
                }
                
                updateStatus('registration-status', statusHtml, controller ? 'success' : 'warning');
                
            } catch (error) {
                console.error('Error checking registration status:', error);
                updateStatus('registration-status', '❌ Error: ' + error.message, 'error');
            }
        }
        
        // 4. Test FCM service worker
        document.getElementById('test-fcm-sw').addEventListener('click', async () => {
            try {
                updateStatus('fcm-sw-status', '🔄 Testing FCM service worker...', 'warning');
                
                // Try to register FCM service worker
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                console.log('FCM SW registration successful:', registration);
                
                await registration.update();
                console.log('FCM SW updated');
                
                // Wait for it to be ready
                await navigator.serviceWorker.ready;
                
                updateStatus('fcm-sw-status', '✅ FCM service worker registered and ready', 'success');
                
                // Refresh the active service workers list
                await checkActiveServiceWorkers();
                await checkRegistrationStatus();
                
            } catch (error) {
                console.error('FCM SW registration failed:', error);
                updateStatus('fcm-sw-status', '❌ FCM SW registration failed: ' + error.message, 'error');
            }
        });
        
        // 5. Unregister all service workers
        document.getElementById('unregister-all').addEventListener('click', async () => {
            try {
                updateStatus('fix-status', '🔄 Unregistering all service workers...', 'warning');
                
                const registrations = await navigator.serviceWorker.getRegistrations();
                console.log('Unregistering', registrations.length, 'service workers');
                
                for (const registration of registrations) {
                    await registration.unregister();
                    console.log('Unregistered:', registration.scope);
                }
                
                updateStatus('fix-status', '✅ All service workers unregistered', 'success');
                
                // Refresh the lists
                setTimeout(async () => {
                    await checkActiveServiceWorkers();
                    await checkRegistrationStatus();
                }, 1000);
                
            } catch (error) {
                console.error('Error unregistering service workers:', error);
                updateStatus('fix-status', '❌ Error: ' + error.message, 'error');
            }
        });
        
        // 6. Register FCM service worker
        document.getElementById('register-fcm').addEventListener('click', async () => {
            try {
                updateStatus('fix-status', '🔄 Registering FCM service worker...', 'warning');
                
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                console.log('FCM SW registered:', registration);
                
                await navigator.serviceWorker.ready;
                console.log('FCM SW ready');
                
                updateStatus('fix-status', '✅ FCM service worker registered successfully', 'success');
                
                // Refresh the lists
                setTimeout(async () => {
                    await checkActiveServiceWorkers();
                    await checkRegistrationStatus();
                }, 1000);
                
            } catch (error) {
                console.error('Error registering FCM service worker:', error);
                updateStatus('fix-status', '❌ Error: ' + error.message, 'error');
            }
        });
        
        // Initialize on load
        window.addEventListener('load', async () => {
            console.log('Service worker debug tool loaded');
            checkServiceWorkerSupport();
            await checkActiveServiceWorkers();
            await checkRegistrationStatus();
        });
    </script>
</body>
</html>