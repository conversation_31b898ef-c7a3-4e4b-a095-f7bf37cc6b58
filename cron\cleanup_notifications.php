<?php
/**
 * Notification Cleanup Cron Job
 * 
 * This script cleans up old archived notifications automatically.
 * Should be run daily via cron job.
 * 
 * Usage: php cleanup_notifications.php
 * Cron: 0 2 * * * /usr/bin/php /path/to/cleanup_notifications.php
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__, 2));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load core classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/UnifiedMessageModel.php';

// Set up logging
$logFile = APPROOT . '/logs/notification_cleanup.log';
$logDir = dirname($logFile);

// Create logs directory if it doesn't exist
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * Log message with timestamp
 */
function logMessage($message, $level = 'INFO') {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running from command line
    if (php_sapi_name() === 'cli') {
        echo $logEntry;
    }
}

try {
    logMessage("Starting notification cleanup process");
    
    // Initialize the model
    $unifiedMessageModel = new UnifiedMessageModel();
    
    // Configuration - can be moved to settings later
    $daysAfterShowEnd = 14; // Delete archived notifications 14 days after show ends
    
    logMessage("Cleaning up archived notifications older than {$daysAfterShowEnd} days after show end");
    
    // Run the cleanup
    $result = $unifiedMessageModel->cleanupOldArchivedNotifications($daysAfterShowEnd);
    
    if ($result === false) {
        throw new Exception("Cleanup operation failed");
    }
    
    // Log results
    logMessage("Cleanup completed successfully:");
    logMessage("- Show-related notifications deleted: " . $result['show_related_deleted']);
    logMessage("- General cleanup (90+ days old): " . $result['general_cleanup_deleted']);
    logMessage("- Total notifications deleted: " . $result['total_deleted']);
    
    // Additional cleanup: Remove very old notification queue entries
    $db = new Database();
    
    // Clean up processed notification queue entries older than 30 days
    $db->query("DELETE FROM notification_queue 
                WHERE status = 'sent' 
                AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $db->execute();
    $queueCleanupCount = $db->rowCount();
    
    if ($queueCleanupCount > 0) {
        logMessage("Cleaned up {$queueCleanupCount} old notification queue entries");
    }
    
    // Clean up old push subscriptions that haven't been used in 90 days
    $db->query("DELETE FROM push_subscriptions 
                WHERE active = 0 
                OR updated_at < DATE_SUB(NOW(), INTERVAL 90 DAY)");
    $db->execute();
    $subscriptionCleanupCount = $db->rowCount();
    
    if ($subscriptionCleanupCount > 0) {
        logMessage("Cleaned up {$subscriptionCleanupCount} old push subscriptions");
    }
    
    logMessage("Notification cleanup process completed successfully");
    
    // If running via web (for testing), output results
    if (php_sapi_name() !== 'cli') {
        echo "<h1>Notification Cleanup Results</h1>";
        echo "<p><strong>Show-related notifications deleted:</strong> " . $result['show_related_deleted'] . "</p>";
        echo "<p><strong>General cleanup (90+ days old):</strong> " . $result['general_cleanup_deleted'] . "</p>";
        echo "<p><strong>Total notifications deleted:</strong> " . $result['total_deleted'] . "</p>";
        echo "<p><strong>Queue entries cleaned:</strong> " . $queueCleanupCount . "</p>";
        echo "<p><strong>Push subscriptions cleaned:</strong> " . $subscriptionCleanupCount . "</p>";
        echo "<p><strong>Log file:</strong> " . $logFile . "</p>";
    }
    
} catch (Exception $e) {
    $errorMessage = "Notification cleanup failed: " . $e->getMessage();
    logMessage($errorMessage, 'ERROR');
    logMessage("Stack trace: " . $e->getTraceAsString(), 'ERROR');
    
    // If running via web, show error
    if (php_sapi_name() !== 'cli') {
        echo "<h1>Notification Cleanup Error</h1>";
        echo "<p style='color: red;'>" . htmlspecialchars($errorMessage) . "</p>";
    }
    
    exit(1);
}

// Success exit
exit(0);
