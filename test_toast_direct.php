<?php
/**
 * Direct Toast Notification Test
 * 
 * This script directly inserts a toast notification into the database
 * to test if the JavaScript polling and display system is working.
 */

require_once 'config/config.php';
require_once 'core/Database.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$db = new Database();
$userId = $_SESSION['user_id'];

echo "<h2>Direct Toast Notification Test</h2>";
echo "<p><strong>User ID:</strong> $userId</p>";
echo "<p><strong>Debug Mode:</strong> " . (DEBUG_MODE ? 'Enabled' : 'Disabled') . "</p>";

// Create a test toast notification directly
if (isset($_POST['create_toast'])) {
    try {
        $db->query('INSERT INTO user_toast_notifications (user_id, title, message, event_type, is_read, created_at) 
                   VALUES (:user_id, :title, :message, :event_type, 0, NOW())');
        $db->bind(':user_id', $userId);
        $db->bind(':title', 'Direct Test Toast');
        $db->bind(':message', 'This is a direct database test toast notification created at ' . date('H:i:s'));
        $db->bind(':event_type', 'test');
        
        if ($db->execute()) {
            echo "<p style='color: green;'>✅ Toast notification created directly in database!</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create toast notification.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

// Check current toast notifications
try {
    $db->query('SELECT * FROM user_toast_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 10');
    $db->bind(':user_id', $userId);
    $toasts = $db->resultSet();
    
    echo "<h3>Current Toast Notifications in Database</h3>";
    echo "<p><strong>Total:</strong> " . count($toasts) . "</p>";
    
    if (!empty($toasts)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Read</th><th>Created</th></tr>";
        foreach ($toasts as $toast) {
            $readStatus = $toast->is_read ? 'Yes' : 'No';
            echo "<tr>";
            echo "<td>{$toast->id}</td>";
            echo "<td>" . htmlspecialchars($toast->title) . "</td>";
            echo "<td>" . htmlspecialchars(substr($toast->message, 0, 50)) . "...</td>";
            echo "<td>{$readStatus}</td>";
            echo "<td>{$toast->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check unread count
    $db->query('SELECT COUNT(*) as unread_count FROM user_toast_notifications WHERE user_id = :user_id AND is_read = 0');
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult->unread_count ?? 0;
    
    echo "<p><strong>Unread Count:</strong> $unreadCount</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}

// Test the API endpoint
echo "<h3>Test API Endpoint</h3>";
echo "<p>Testing <code>/notification/getUnread</code> endpoint:</p>";

try {
    // Simulate the API call
    require_once 'models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    $unreadNotifications = $notificationModel->getUnreadNotifications($userId);
    
    echo "<p><strong>API Response:</strong></p>";
    echo "<pre>";
    echo "Push notifications: " . count($unreadNotifications['push']) . "\n";
    echo "Toast notifications: " . count($unreadNotifications['toast']) . "\n";
    echo "\nToast notifications data:\n";
    print_r($unreadNotifications['toast']);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ API Error: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Direct Toast Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        
        console.log('[DirectTest] Page loaded, BASE_URL:', BASE_URL);
        
        // Test the API endpoint directly
        async function testAPI() {
            try {
                console.log('[DirectTest] Testing /notification/getUnread endpoint...');
                const response = await fetch(`${BASE_URL}/notification/getUnread`);
                const data = await response.json();
                console.log('[DirectTest] API Response:', data);
                
                if (data.toast && data.toast.length > 0) {
                    console.log('[DirectTest] ✅ Found', data.toast.length, 'toast notifications');
                    console.log('[DirectTest] Toast data:', data.toast);
                } else {
                    console.log('[DirectTest] ❌ No toast notifications found');
                }
            } catch (error) {
                console.error('[DirectTest] API Error:', error);
            }
        }
        
        // Check if notification manager loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[DirectTest] DOM ready');
            
            // Test API immediately
            testAPI();
            
            // Check for notification manager
            setTimeout(() => {
                if (window.notificationManager) {
                    console.log('[DirectTest] ✅ NotificationManager found');
                    console.log('[DirectTest] Forcing notification reload...');
                    window.notificationManager.loadUnreadNotifications();
                } else {
                    console.log('[DirectTest] ❌ NotificationManager not found');
                }
            }, 1000);
        });
    </script>
</head>
<body data-debug-mode="true">
    
    <div style="margin: 20px;">
        <form method="post">
            <button type="submit" name="create_toast" class="btn btn-primary">Create Test Toast Notification</button>
        </form>
        
        <hr>
        
        <h3>Instructions</h3>
        <ol>
            <li>Click "Create Test Toast Notification" to add a toast to the database</li>
            <li>Check the browser console (F12 → Console) for debug messages</li>
            <li>Look for toast notifications appearing in the top-right corner</li>
            <li>Check if the notification manager is loading and polling correctly</li>
        </ol>
        
        <p><a href="/admin/dashboard">← Back to Admin Dashboard</a></p>
    </div>
    
    <!-- Include the notification system -->
    <script src="<?php echo BASE_URL; ?>/public/js/notifications.js"></script>
</body>
</html>