<?php
/**
 * Cleanup Duplicate Delivery Records
 * 
 * This script removes duplicate delivery tracking records that were created
 * by the old system that tracked both 'pending' and final status.
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🧹 Cleanup Duplicate Delivery Records</h1>";

try {
    $db = new Database();
    
    // Check if message_deliveries table exists
    $db->query("SHOW TABLES LIKE 'message_deliveries'");
    $tableExists = $db->single();
    
    if (!$tableExists) {
        echo "<p style='color: orange;'>⚠️ message_deliveries table does not exist. No cleanup needed.</p>";
        exit;
    }
    
    echo "<h2>📊 Current Status</h2>";
    
    // Show current duplicate records
    $db->query("
        SELECT message_id, delivery_method, COUNT(*) as count 
        FROM message_deliveries 
        GROUP BY message_id, delivery_method 
        HAVING COUNT(*) > 1
        ORDER BY message_id, delivery_method
    ");
    $duplicates = $db->resultSet();
    
    if (empty($duplicates)) {
        echo "<p style='color: green;'>✅ No duplicate delivery records found!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Found " . count($duplicates) . " sets of duplicate records:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Message ID</th><th>Delivery Method</th><th>Duplicate Count</th></tr>";
        foreach ($duplicates as $dup) {
            echo "<tr><td>{$dup->message_id}</td><td>{$dup->delivery_method}</td><td>{$dup->count}</td></tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>🔧 Cleanup Process</h2>";
    
    if (!empty($duplicates)) {
        echo "<p>Cleaning up duplicates by keeping only the most recent record for each message/method combination...</p>";
        
        $cleanedCount = 0;
        
        foreach ($duplicates as $dup) {
            // Keep only the most recent record (highest ID) for each message_id + delivery_method
            $db->query("
                DELETE FROM message_deliveries 
                WHERE message_id = :message_id 
                AND delivery_method = :delivery_method 
                AND id NOT IN (
                    SELECT * FROM (
                        SELECT MAX(id) 
                        FROM message_deliveries 
                        WHERE message_id = :message_id2 
                        AND delivery_method = :delivery_method2
                    ) as temp
                )
            ");
            
            $db->bind(':message_id', $dup->message_id);
            $db->bind(':delivery_method', $dup->delivery_method);
            $db->bind(':message_id2', $dup->message_id);
            $db->bind(':delivery_method2', $dup->delivery_method);
            
            if ($db->execute()) {
                $deletedRows = $db->rowCount();
                $cleanedCount += $deletedRows;
                echo "<p style='color: green;'>✅ Cleaned {$deletedRows} duplicate records for Message {$dup->message_id} - {$dup->delivery_method}</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to clean duplicates for Message {$dup->message_id} - {$dup->delivery_method}</p>";
            }
        }
        
        echo "<p style='color: green;'><strong>✅ Cleanup complete! Removed {$cleanedCount} duplicate records.</strong></p>";
    }
    
    echo "<h2>📊 Final Status</h2>";
    
    // Show final counts
    $db->query("SELECT COUNT(*) as total FROM message_deliveries");
    $total = $db->single();
    
    $db->query("
        SELECT delivery_method, status, COUNT(*) as count 
        FROM message_deliveries 
        GROUP BY delivery_method, status 
        ORDER BY delivery_method, status
    ");
    $summary = $db->resultSet();
    
    echo "<p><strong>Total delivery records:</strong> {$total->total}</p>";
    
    if (!empty($summary)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Delivery Method</th><th>Status</th><th>Count</th></tr>";
        foreach ($summary as $row) {
            $statusColor = '';
            switch ($row->status) {
                case 'sent':
                    $statusColor = 'green';
                    break;
                case 'failed':
                    $statusColor = 'red';
                    break;
                case 'queued':
                    $statusColor = 'orange';
                    break;
            }
            echo "<tr><td>{$row->delivery_method}</td><td style='color: {$statusColor};'>{$row->status}</td><td>{$row->count}</td></tr>";
        }
        echo "</table>";
    }
    
    // Verify no more duplicates
    $db->query("
        SELECT COUNT(*) as duplicate_count
        FROM (
            SELECT message_id, delivery_method, COUNT(*) as count 
            FROM message_deliveries 
            GROUP BY message_id, delivery_method 
            HAVING COUNT(*) > 1
        ) as duplicates
    ");
    $remainingDuplicates = $db->single();
    
    if ($remainingDuplicates->duplicate_count == 0) {
        echo "<p style='color: green;'>✅ Verification: No duplicate records remain!</p>";
    } else {
        echo "<p style='color: red;'>❌ Warning: {$remainingDuplicates->duplicate_count} duplicate sets still exist</p>";
    }
    
    echo "<h2>✅ Summary</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>What was fixed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Removed duplicate delivery tracking records</li>";
    echo "<li>✅ Each message now has only one record per delivery method</li>";
    echo "<li>✅ Kept the most recent/final status for each delivery</li>";
    echo "<li>✅ Future messages will only create one record per method</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Cleanup completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
