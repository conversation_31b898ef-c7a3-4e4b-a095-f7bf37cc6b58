<?php
/**
 * Cleanup Script: Remove notification_preferences Table Dependencies
 * 
 * This script safely removes:
 * 1. Files that reference the unused notification_preferences table
 * 2. Unused PWA notification methods from NotificationModel
 * 3. Migration files that create the redundant table
 * 4. Temporary/cleanup files related to notification preferences
 * 
 * IMPORTANT: Run this script from the root directory of your application
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧹 Notification Preferences System Cleanup</h1>";
echo "<p><strong>Starting cleanup process...</strong></p>";

// Define the root directory (adjust if needed)
$rootDir = __DIR__;

// Track cleanup results
$results = [
    'files_deleted' => [],
    'files_not_found' => [],
    'methods_removed' => [],
    'database_cleaned' => false,
    'errors' => []
];

/**
 * Helper function to safely delete a file
 */
function safeDelete($filePath, &$results) {
    $relativePath = str_replace(__DIR__ . DIRECTORY_SEPARATOR, '', $filePath);
    
    if (file_exists($filePath)) {
        if (unlink($filePath)) {
            $results['files_deleted'][] = $relativePath;
            echo "<p style='color: green;'>✅ Deleted: {$relativePath}</p>";
            return true;
        } else {
            $results['errors'][] = "Failed to delete: {$relativePath}";
            echo "<p style='color: red;'>❌ Failed to delete: {$relativePath}</p>";
            return false;
        }
    } else {
        $results['files_not_found'][] = $relativePath;
        echo "<p style='color: orange;'>⚠️ Not found: {$relativePath}</p>";
        return false;
    }
}

echo "<h2>📁 Step 1: Removing Migration Files</h2>";
echo "<p>Removing PWA migration files that create the notification_preferences table...</p>";

$migrationFiles = [
    'database/migrations/pwa_tables_only.sql',
    'database/migrations/pwa_compatible_migration.sql', 
    'database/migrations/pwa_safe_migration.sql',
    'database/migrations/add_pwa_features.sql',
    'database/migrations/add_pwa_features_mariadb.sql',
    'database/migrations/pwa_fixed_migration.sql',
    'database/migrations/pwa_minimal_migration.sql'
];

foreach ($migrationFiles as $file) {
    $fullPath = $rootDir . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $file);
    safeDelete($fullPath, $results);
}

echo "<h2>🗑️ Step 2: Removing Cleanup/Temporary Files</h2>";
echo "<p>Removing temporary files and cleanup scripts...</p>";

$tempFiles = [
    'fix_notification_preferences.php',
    'finalize_notification_system.php', 
    'copy_notification_file.php',
    'notification_preferences_temp.php',
    'views/user/notification_preferences_new.php',
    'views/user/notification_preferences_fixed.php'
];

foreach ($tempFiles as $file) {
    $fullPath = $rootDir . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $file);
    safeDelete($fullPath, $results);
}

echo "<h2>🔧 Step 3: Removing Unused Methods from NotificationModel</h2>";
echo "<p>Removing PWA notification methods that use notification_preferences table...</p>";

$notificationModelPath = $rootDir . DIRECTORY_SEPARATOR . 'models' . DIRECTORY_SEPARATOR . 'NotificationModel.php';

if (file_exists($notificationModelPath)) {
    $content = file_get_contents($notificationModelPath);
    $originalContent = $content;

    // More precise method removal using line-based approach
    $lines = explode("\n", $content);
    $newLines = [];
    $skipLines = false;
    $methodsFound = [];

    for ($i = 0; $i < count($lines); $i++) {
        $line = $lines[$i];

        // Check for start of PWA methods
        if (strpos($line, 'Get notification preferences for PWA') !== false) {
            $skipLines = true;
            $methodsFound[] = 'getPWANotificationPreferences()';
            continue;
        }

        if (strpos($line, 'Update PWA notification preferences') !== false) {
            $skipLines = true;
            $methodsFound[] = 'updatePWANotificationPreferences()';
            continue;
        }

        if (strpos($line, 'Create default PWA notification preferences') !== false) {
            $skipLines = true;
            $methodsFound[] = 'createDefaultPWAPreferences()';
            continue;
        }

        // Check for end of method (closing brace at start of line)
        if ($skipLines && preg_match('/^\s*\}\s*$/', $line)) {
            $skipLines = false;
            continue;
        }

        // Add line if we're not skipping
        if (!$skipLines) {
            $newLines[] = $line;
        }
    }

    if (!empty($methodsFound)) {
        $newContent = implode("\n", $newLines);
        if (file_put_contents($notificationModelPath, $newContent)) {
            $results['methods_removed'] = array_merge($results['methods_removed'], $methodsFound);
            echo "<p style='color: green;'>✅ Removed " . count($methodsFound) . " unused PWA methods from NotificationModel.php</p>";
            foreach ($methodsFound as $method) {
                echo "<p style='color: green; margin-left: 20px;'>• {$method}</p>";
            }
        } else {
            $results['errors'][] = 'Failed to update NotificationModel.php';
            echo "<p style='color: red;'>❌ Failed to update NotificationModel.php</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ No PWA methods found to remove in NotificationModel.php</p>";
    }
} else {
    $results['errors'][] = 'NotificationModel.php not found';
    echo "<p style='color: red;'>❌ NotificationModel.php not found</p>";
}

echo "<h2>🗄️ Step 4: Database Cleanup</h2>";
echo "<p>Dropping the notification_preferences table...</p>";

try {
    // Include database configuration
    if (file_exists($rootDir . '/config/config.php')) {
        require_once $rootDir . '/config/config.php';
        require_once $rootDir . '/core/Database.php';
        
        $db = new Database();
        
        // Check if table exists
        $db->query("SHOW TABLES LIKE 'notification_preferences'");
        $tableExists = $db->single();
        
        if ($tableExists) {
            // Drop the table
            $db->query("DROP TABLE notification_preferences");
            if ($db->execute()) {
                $results['database_cleaned'] = true;
                echo "<p style='color: green;'>✅ Successfully dropped notification_preferences table</p>";
            } else {
                $results['errors'][] = 'Failed to drop notification_preferences table';
                echo "<p style='color: red;'>❌ Failed to drop notification_preferences table</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ notification_preferences table not found (may already be removed)</p>";
        }
        
    } else {
        $results['errors'][] = 'Database configuration not found';
        echo "<p style='color: red;'>❌ Database configuration not found</p>";
    }
} catch (Exception $e) {
    $results['errors'][] = 'Database error: ' . $e->getMessage();
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>📊 Cleanup Summary</h2>";

echo "<h3>✅ Files Successfully Deleted (" . count($results['files_deleted']) . ")</h3>";
if (!empty($results['files_deleted'])) {
    echo "<ul>";
    foreach ($results['files_deleted'] as $file) {
        echo "<li>{$file}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No files were deleted.</p>";
}

echo "<h3>🔧 Methods Removed (" . count($results['methods_removed']) . ")</h3>";
if (!empty($results['methods_removed'])) {
    echo "<ul>";
    foreach ($results['methods_removed'] as $method) {
        echo "<li>{$method}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No methods were removed.</p>";
}

echo "<h3>⚠️ Files Not Found (" . count($results['files_not_found']) . ")</h3>";
if (!empty($results['files_not_found'])) {
    echo "<details><summary>Show files not found</summary><ul>";
    foreach ($results['files_not_found'] as $file) {
        echo "<li>{$file}</li>";
    }
    echo "</ul></details>";
}

echo "<h3>🗄️ Database Cleanup</h3>";
echo "<p>notification_preferences table: " . ($results['database_cleaned'] ? '✅ Removed' : '⚠️ Not removed') . "</p>";

if (!empty($results['errors'])) {
    echo "<h3>❌ Errors (" . count($results['errors']) . ")</h3>";
    echo "<ul>";
    foreach ($results['errors'] as $error) {
        echo "<li style='color: red;'>{$error}</li>";
    }
    echo "</ul>";
}

echo "<h2>🎉 Cleanup Complete!</h2>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>Files deleted: " . count($results['files_deleted']) . "</li>";
echo "<li>Methods removed: " . count($results['methods_removed']) . "</li>";
echo "<li>Database cleaned: " . ($results['database_cleaned'] ? 'Yes' : 'No') . "</li>";
echo "<li>Errors: " . count($results['errors']) . "</li>";
echo "</ul>";

if (empty($results['errors'])) {
    echo "<p style='color: green; font-weight: bold;'>✅ All cleanup operations completed successfully!</p>";
    echo "<p>Your system now uses only the unified messaging system with:</p>";
    echo "<ul>";
    echo "<li>✅ <strong>notification_settings</strong> table for global settings</li>";
    echo "<li>✅ <strong>user_notification_preferences</strong> table for user preferences</li>";
    echo "<li>✅ <strong>messages</strong> table for unified messaging</li>";
    echo "</ul>";
} else {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ Cleanup completed with some errors. Please review the errors above.</p>";
}

echo "<hr>";
echo "<p><em>Cleanup completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
