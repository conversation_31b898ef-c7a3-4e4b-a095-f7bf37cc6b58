<?php
/**
 * Test Notification Bell Icon Fixes
 * 
 * This script tests the fixes for notification bell icon issues
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🔔 Test Notification Bell Icon Fixes</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Issues Being Fixed</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Issue 1: Desktop Bell Count Flashes and Disappears</h3>";
    echo "<p><strong>Problem:</strong> P<PERSON> generates initial count, but JavaScript immediately overwrites it</p>";
    echo "<p><strong>Fix:</strong> Added delay when initial count exists to prevent flash</p>";
    
    echo "<h3>Issue 2: Mobile Bell Never Shows Count + Should Turn Yellow</h3>";
    echo "<p><strong>Problem:</strong> Mobile bell uses different ID and lacks styling</p>";
    echo "<p><strong>Fix:</strong> Updated JavaScript to handle mobile badge + added yellow glow effect</p>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td>Unread Messages for User {$userId}</td><td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td></tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>No unread messages found. Creating a test message to demonstrate the bell icon fixes...</p>";
        
        $subject = "🔔 Bell Icon Test";
        $message = "This message tests the notification bell icon fixes for both desktop and mobile.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🔧 JavaScript Fixes Applied</h2>";
    
    // Check if the JavaScript file has been updated
    $jsFile = 'public/js/notification-center.js';
    if (file_exists($jsFile)) {
        $jsContent = file_get_contents($jsFile);
        
        $fixes = [
            'Delay for initial count' => strpos($jsContent, 'setTimeout(() => {') !== false,
            'Mobile badge update' => strpos($jsContent, 'mobile-notification-badge') !== false,
            'Yellow bell icon' => strpos($jsContent, '#FFD700') !== false,
            'Pulse animation' => strpos($jsContent, 'badge-pulse') !== false
        ];
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr style='background: #f0f0f0;'><th>Fix</th><th>Status</th></tr>";
        foreach ($fixes as $fix => $applied) {
            $status = $applied ? '✅ Applied' : '❌ Missing';
            echo "<tr><td>{$fix}</td><td>{$status}</td></tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>❌ notification-center.js file not found</p>";
    }
    
    echo "<h2>🎨 CSS Fixes Applied</h2>";
    
    // Check CSS files for the new styles
    $cssFiles = [
        'public/css/notifications.css' => ['badge-pulse', 'notification-badge-pulse'],
        'public/css/pwa-features.css' => ['bell-glow', 'has-unread', 'badge-appear']
    ];
    
    foreach ($cssFiles as $cssFile => $expectedStyles) {
        echo "<h3>" . basename($cssFile) . "</h3>";
        
        if (file_exists($cssFile)) {
            $cssContent = file_get_contents($cssFile);
            
            echo "<table border='1' cellpadding='5'>";
            echo "<tr style='background: #f0f0f0;'><th>Style</th><th>Status</th></tr>";
            
            foreach ($expectedStyles as $style) {
                $found = strpos($cssContent, $style) !== false;
                $status = $found ? '✅ Found' : '❌ Missing';
                echo "<tr><td>{$style}</td><td>{$status}</td></tr>";
            }
            echo "</table>";
            
        } else {
            echo "<p style='color: red;'>❌ {$cssFile} not found</p>";
        }
    }
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Desktop Testing:</h3>";
    echo "<ol>";
    echo "<li>🖥️ <strong>Open the site on desktop</strong></li>";
    echo "<li>🔄 <strong>Refresh the page</strong> and watch the bell icon</li>";
    echo "<li>👀 <strong>Verify:</strong> Bell count should appear and stay visible (not flash and disappear)</li>";
    echo "<li>✨ <strong>Look for:</strong> Subtle pulse animation when count updates</li>";
    echo "</ol>";
    
    echo "<h3>Mobile PWA Testing:</h3>";
    echo "<ol>";
    echo "<li>📱 <strong>Open the PWA on mobile</strong></li>";
    echo "<li>👀 <strong>Look at bottom navigation</strong> - Messages tab with bell icon</li>";
    echo "<li>🔔 <strong>Verify:</strong> Bell icon should be bright yellow with glow effect</li>";
    echo "<li>🔴 <strong>Check:</strong> Red badge with count should appear on bell icon</li>";
    echo "<li>✨ <strong>Look for:</strong> Subtle glow animation on yellow bell</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Platform</th><th>Before Fix</th><th>After Fix</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Desktop</strong></td>";
    echo "<td style='color: red;'>❌ Count flashes and disappears</td>";
    echo "<td style='color: green;'>✅ Count stays visible with pulse animation</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Mobile PWA</strong></td>";
    echo "<td style='color: red;'>❌ No count, normal bell color</td>";
    echo "<td style='color: green;'>✅ Red badge + bright yellow bell with glow</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Details</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Desktop Fix:</h3>";
    echo "<ul>";
    echo "<li><strong>Problem:</strong> JavaScript <code>updateNotificationCount()</code> called immediately</li>";
    echo "<li><strong>Solution:</strong> Check for existing PHP count, delay update if present</li>";
    echo "<li><strong>Animation:</strong> Added pulse effect for visual feedback</li>";
    echo "</ul>";
    
    echo "<h3>Mobile Fix:</h3>";
    echo "<ul>";
    echo "<li><strong>Problem:</strong> <code>mobile-notification-badge</code> not updated</li>";
    echo "<li><strong>Solution:</strong> Added mobile badge handling in <code>updateBadge()</code></li>";
    echo "<li><strong>Styling:</strong> Yellow bell (#FFD700) with glow effect when unread</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Testing</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s), which means:</p>";
        echo "<ul>";
        echo "<li>🖥️ Desktop bell should show count and stay visible</li>";
        echo "<li>📱 Mobile bell should be bright yellow with red badge</li>";
        echo "<li>✨ Both should have subtle animations</li>";
        echo "</ul>";
        echo "<p><strong>Test both platforms now to see the fixes in action!</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
