<?php
/**
 * Notification Center Controller
 * 
 * This controller handles the notification center functionality including
 * viewing notifications, messages, and handling replies.
 */
class NotificationCenterController extends Controller {
    private $auth;
    private $db;
    private $notificationCenterModel;
    private $userModel;
    private $showModel;
    private $emailFolderModel;
    private $emailTemplateModel;
    private $emailReminderModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Call parent constructor first
        parent::__construct();

        // Initialize core dependencies
        $this->auth = new Auth();
        $this->db = new Database();
        
        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Initialize models - use unified message system
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        require_once APPROOT . '/models/EmailService.php';
        require_once APPROOT . '/models/SettingsModel.php';
        $this->notificationCenterModel = new UnifiedMessageModel();
        $this->userModel = $this->model('UserModel');

        // Initialize email management models
        require_once APPROOT . '/models/EmailFolderModel.php';
        require_once APPROOT . '/models/EmailTemplateModel.php';
        require_once APPROOT . '/models/EmailReminderModel.php';

        $this->emailFolderModel = new EmailFolderModel();
        $this->emailTemplateModel = new EmailTemplateModel();
        $this->emailReminderModel = new EmailReminderModel();
        $this->showModel = $this->model('ShowModel');
    }
    
    /**
     * Default index method - shows notification center
     */
    public function index() {
        $this->center();
    }
    
    /**
     * Main notification center page - unified messages view
     */
    public function center() {
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();

        // Check if user can use notification system
        $canSendNotifications = $this->notificationCenterModel->canUserSendNotifications($userId);

        // Get filter parameters with email support for admin/coordinator
        $status = $_GET['status'] ?? 'all';
        $validStatuses = ['all', 'unread', 'archived'];

        // Add email status for admin/coordinator roles
        if (in_array($userRole, ['admin', 'coordinator'])) {
            $validStatuses[] = 'email';
        }

        // Add email management status for admin only
        if ($userRole === 'admin') {
            $validStatuses[] = 'manage';
        }

        if (!in_array($status, $validStatuses)) {
            $status = 'all';
        }

        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // Get messages based on status and role
        if ($status === 'email' && in_array($userRole, ['admin', 'coordinator'])) {
            $messages = $this->getEmailMessages($userId, $userRole, $limit, $offset);
        } elseif ($status === 'manage' && $userRole === 'admin') {
            // For email management, we don't need to load messages initially
            $messages = [];
        } else {
            $messages = $this->notificationCenterModel->getUserMessages(
                $userId,
                $status,
                $limit,
                $offset
            );
        }

        // Get counts for badges - use the reliable database counts
        $counts = $this->notificationCenterModel->getMessageCounts($userId);

        // Add email counts for admin/coordinator
        if (in_array($userRole, ['admin', 'coordinator'])) {
            $counts['email_count'] = $this->getEmailMessageCount($userId, $userRole);
        }

        // Get conversation counts for tabs
        $conversationCounts = $this->notificationCenterModel->getConversationCounts($userId);

        // Calculate pagination
        $totalCount = $counts['total_count'];
        if ($status === 'unread') {
            $totalCount = $counts['total_unread'];
        } elseif ($status === 'archived') {
            $totalCount = $counts['archived_count'];
        } elseif ($status === 'email') {
            $totalCount = $counts['email_count'] ?? 0;
        } elseif ($status === 'manage') {
            $totalCount = 0; // No pagination for management dashboard
        }

        $totalPages = (int)ceil($totalCount / $limit);

        $data = [
            'title' => 'Messages',
            'messages' => $messages,
            'counts' => $counts,
            'conversationCounts' => $conversationCounts,
            'current_status' => $status,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_count' => $totalCount,
            'can_send_notifications' => $canSendNotifications,
            'user_id' => $userId,
            'user_role' => $userRole,
            'has_email_access' => in_array($userRole, ['admin', 'coordinator'])
        ];

        // Add email management data if on manage tab (for admin and coordinator)
        if ($status === 'manage' && in_array($userRole, ['admin', 'coordinator'])) {
            // Get email statistics for dashboard
            $this->db->query("SELECT COUNT(*) as total_emails FROM messages WHERE message_type = 'email'");
            $totalEmails = $this->db->single();

            $this->db->query("SELECT COUNT(*) as follow_up FROM messages WHERE message_type = 'email' AND folder_id = 3");
            $followUpEmails = $this->db->single();

            $this->db->query("SELECT COUNT(*) as resolved FROM messages WHERE message_type = 'email' AND folder_id = 4");
            $resolvedEmails = $this->db->single();

            // Get recent emails for dashboard
            if ($userRole === 'admin') {
                // Admin can see all emails
                $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color
                                 FROM messages m
                                 LEFT JOIN email_folders f ON m.folder_id = f.id
                                 WHERE m.message_type = 'email'
                                 ORDER BY m.created_at DESC
                                 LIMIT 10");
                $recentEmails = $this->db->resultSet();
            } else {
                // Coordinator can see all emails for now (until show_roles table is implemented)
                $this->db->query("SELECT m.*, f.name as folder_name, f.color as folder_color
                                 FROM messages m
                                 LEFT JOIN email_folders f ON m.folder_id = f.id
                                 WHERE m.message_type = 'email'
                                 ORDER BY m.created_at DESC
                                 LIMIT 10");
                $recentEmails = $this->db->resultSet();
            }

            $data['email_stats'] = [
                'total_emails' => $totalEmails ? $totalEmails->total_emails : 0,
                'follow_up' => $followUpEmails ? $followUpEmails->follow_up : 0,
                'resolved' => $resolvedEmails ? $resolvedEmails->resolved : 0
            ];
            $data['recent_emails'] = $recentEmails;
            $data['email_folders'] = $this->emailFolderModel->getAllFolders();
            $data['email_templates'] = $this->emailTemplateModel->getAllTemplates(true);
            $data['email_reminders'] = $this->emailReminderModel->getUserReminders($userId);
        }

        parent::view('notification_center/index', $data);
    }
    
    // ... rest of the file content would continue here
}