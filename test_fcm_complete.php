<?php
/**
 * Complete FCM Test Script
 * 
 * Tests the complete FCM implementation including token storage and sending
 * 
 * Usage: https://events.rowaneliterides.com/test_fcm_complete.php?test_key=fcm_complete_2025
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Security check
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'fcm_complete_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';

echo "=== COMPLETE FCM TEST ===\n\n";

try {
    $db = new Database();
    
    echo "1. CHECKING FCM TOKENS TABLE\n";
    
    // Check if fcm_tokens table exists
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    if (!$db->single()) {
        echo "   ❌ FCM tokens table not found\n";
        echo "   Run migration script first: /migrate_to_fcm.php?migrate_key=fcm_migration_2025\n";
        exit;
    }
    
    // Get FCM token statistics
    $db->query("SELECT COUNT(*) as total FROM fcm_tokens");
    $db->execute();
    $total = $db->single()->total;
    
    $db->query("SELECT COUNT(*) as active FROM fcm_tokens WHERE active = 1");
    $db->execute();
    $active = $db->single()->active;
    
    echo "   Total FCM tokens: $total\n";
    echo "   Active FCM tokens: $active\n";
    
    if ($active == 0) {
        echo "   ⚠️  No active FCM tokens found\n";
        echo "   Users need to enable notifications in their browsers\n\n";
    }
    
    echo "\n2. TESTING FIREBASE SERVICE ACCOUNT\n";
    
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    if (!file_exists($serviceAccountPath)) {
        echo "   ❌ Firebase service account file not found\n";
        echo "   Create: /config/firebase-service-account.json\n";
        exit;
    }
    
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    if (!$serviceAccount || !isset($serviceAccount['project_id'])) {
        echo "   ❌ Invalid Firebase service account file\n";
        exit;
    }
    
    echo "   ✅ Service account file found\n";
    echo "   Project ID: " . $serviceAccount['project_id'] . "\n";
    echo "   Client Email: " . $serviceAccount['client_email'] . "\n";
    
    echo "\n3. TESTING FCM HELPER\n";
    
    require_once APPROOT . '/helpers/fcm_v1_helper.php';
    
    try {
        $fcm = new FCMv1Helper($serviceAccountPath);
        echo "   ✅ FCM helper initialized\n";
        
        // Test OAuth token generation
        $reflection = new ReflectionClass($fcm);
        $method = $reflection->getMethod('getAccessToken');
        $method->setAccessible(true);
        
        $accessToken = $method->invoke($fcm);
        echo "   ✅ OAuth2 access token obtained\n";
        echo "   Token length: " . strlen($accessToken) . " characters\n";
        
    } catch (Exception $e) {
        echo "   ❌ FCM helper failed: " . $e->getMessage() . "\n";
        exit;
    }
    
    echo "\n4. TESTING WITH SAMPLE TOKEN\n";
    
    // Test with a known invalid token to verify API connectivity
    $testToken = "INVALID_TEST_TOKEN_123";
    $result = $fcm->sendNotification($testToken, "Test Title", "Test message");
    
    if (!$result['success']) {
        if (strpos($result['error'], 'INVALID_ARGUMENT') !== false) {
            echo "   ✅ FCM API is responding correctly (invalid token rejected)\n";
        } else {
            echo "   ❌ Unexpected FCM API response: " . $result['error'] . "\n";
        }
    } else {
        echo "   ⚠️  Unexpected success with invalid token\n";
    }
    
    echo "\n5. TESTING WITH REAL TOKENS (if available)\n";
    
    if ($active > 0) {
        // Get a few recent tokens for testing
        $db->query("SELECT token, user_id FROM fcm_tokens WHERE active = 1 ORDER BY last_used DESC LIMIT 3");
        $db->execute();
        $tokens = $db->resultSet();
        
        $successCount = 0;
        $testCount = 0;
        
        foreach ($tokens as $tokenData) {
            $testCount++;
            echo "   Testing token " . substr($tokenData->token, -10) . "... ";
            
            $result = $fcm->sendNotification(
                $tokenData->token, 
                "FCM Test Notification", 
                "This is a test notification sent at " . date('H:i:s'),
                ['test' => 'true', 'timestamp' => time()]
            );
            
            if ($result['success']) {
                echo "✅ SUCCESS\n";
                $successCount++;
            } else {
                echo "❌ FAILED: " . $result['error'] . "\n";
                
                // Check if token should be removed
                if (strpos($result['error'], 'UNREGISTERED') !== false || 
                    strpos($result['error'], 'INVALID_ARGUMENT') !== false) {
                    echo "      (Token marked for removal)\n";
                }
            }
        }
        
        echo "\n   Test Results: $successCount/$testCount notifications sent successfully\n";
        
    } else {
        echo "   No active tokens to test with\n";
        echo "   Users need to enable notifications first\n";
    }
    
    echo "\n6. TESTING NOTIFICATION MODEL INTEGRATION\n";
    
    try {
        require_once APPROOT . '/models/NotificationModel.php';
        $notificationModel = new NotificationModel();
        
        echo "   ✅ NotificationModel loaded\n";
        
        // Test the new FCM methods
        if (method_exists($notificationModel, 'getUserFCMTokens')) {
            echo "   ✅ FCM methods available\n";
            
            // Test with a user that has tokens
            if ($active > 0) {
                $db->query("SELECT user_id FROM fcm_tokens WHERE active = 1 LIMIT 1");
                $db->execute();
                $testUser = $db->single();
                
                if ($testUser) {
                    $userTokens = $notificationModel->getUserFCMTokens($testUser->user_id);
                    echo "   User {$testUser->user_id} has " . count($userTokens) . " FCM tokens\n";
                }
            }
        } else {
            echo "   ❌ FCM methods not found in NotificationModel\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ NotificationModel test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n7. FRONTEND INTEGRATION CHECK\n";
    
    $frontendFiles = [
        '/public/js/fcm-notifications.js' => 'FCM JavaScript',
        '/firebase-messaging-sw.js' => 'Firebase Service Worker'
    ];
    
    foreach ($frontendFiles as $file => $description) {
        if (file_exists(APPROOT . $file)) {
            echo "   ✅ $description found\n";
        } else {
            echo "   ❌ $description missing: $file\n";
        }
    }
    
    echo "\n8. CONFIGURATION CHECKLIST\n";
    
    $configChecks = [
        'Firebase service account configured' => file_exists($serviceAccountPath),
        'FCM helper available' => file_exists(APPROOT . '/helpers/fcm_v1_helper.php'),
        'Frontend JavaScript ready' => file_exists(APPROOT . '/public/js/fcm-notifications.js'),
        'Service worker ready' => file_exists(APPROOT . '/firebase-messaging-sw.js'),
        'FCM tokens table exists' => true, // Already checked above
        'Active FCM tokens available' => $active > 0
    ];
    
    foreach ($configChecks as $check => $status) {
        echo "   " . ($status ? "✅" : "❌") . " $check\n";
    }
    
    echo "\n9. NEXT STEPS\n";
    
    if ($active == 0) {
        echo "   🔧 IMMEDIATE: Get users to enable notifications\n";
        echo "      1. Users visit the site\n";
        echo "      2. Click notification permission prompt\n";
        echo "      3. Allow notifications in browser\n";
        echo "      4. FCM tokens will be automatically stored\n\n";
    }
    
    echo "   🔧 CONFIGURATION: Update Firebase web config\n";
    echo "      Update firebaseConfig in these files:\n";
    echo "      - /public/js/fcm-notifications.js\n";
    echo "      - /firebase-messaging-sw.js\n";
    echo "      Get config from Firebase Console > Project Settings\n\n";
    
    echo "   🔧 TESTING: Send real notifications\n";
    echo "      1. Use admin panel to send notifications\n";
    echo "      2. Test different notification types\n";
    echo "      3. Verify foreground and background delivery\n\n";
    
    echo "   🔧 MONITORING: Set up cleanup and monitoring\n";
    echo "      1. Monitor FCM token growth\n";
    echo "      2. Clean up invalid tokens regularly\n";
    echo "      3. Track notification delivery rates\n\n";
    
    echo "=== FCM TEST COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>