<!DOCTYPE html>
<html>
<head>
    <title>FCM Loading Test</title>
</head>
<body>
    <h1>FCM Loading Test</h1>
    <div id="status"></div>
    
    <script>
        // Set up PWA_CONFIG for testing
        window.PWA_CONFIG = {
            userId: 1,
            userRole: 'admin',
            debugMode: true
        };
        
        console.log('Test page loaded');
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML += '<div>' + message + '</div>';
        }
        
        updateStatus('Page loaded');
        
        // Check if FCM script loads
        setTimeout(() => {
            if (typeof FCMNotificationManager !== 'undefined') {
                updateStatus('✅ FCMNotificationManager class found');
                console.log('FCMNotificationManager available');
            } else {
                updateStatus('❌ FCMNotificationManager class NOT found');
                console.error('FCMNotificationManager NOT available');
            }
            
            if (window.fcmManager) {
                updateStatus('✅ FCM manager instance found');
            } else {
                updateStatus('❌ FCM manager instance NOT found');
            }
        }, 3000);
    </script>
    
    <!-- Include FCM script -->
    <script src="/public/js/fcm-notifications.js"></script>
</body>
</html>