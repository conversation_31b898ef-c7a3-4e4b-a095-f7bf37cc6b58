-- Fix Custom Email Folders
-- This script ensures that custom folders (non-system folders) have is_system = 0
-- Run this if custom folders are not showing edit/delete buttons

-- First, let's see what folders exist and their is_system values
SELECT id, name, is_system, created_by, created_at 
FROM email_folders 
ORDER BY is_system, name;

-- Update any custom folders that might have been created without is_system = 0
-- System folders typically have IDs 1-5 and no created_by user
-- Custom folders should have created_by set and is_system = 0
UPDATE email_folders 
SET is_system = 0 
WHERE created_by IS NOT NULL 
  AND created_by > 0 
  AND is_system != 0;

-- Verify the changes
SELECT id, name, is_system, created_by, created_at 
FROM email_folders 
WHERE created_by IS NOT NULL 
ORDER BY created_at DESC;
