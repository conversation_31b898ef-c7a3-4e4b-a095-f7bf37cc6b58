<?php
/**
 * Verification Script: Confirm notification_preferences Cleanup
 * 
 * This script verifies that the cleanup was successful by checking:
 * 1. Database table is removed
 * 2. Files are deleted
 * 3. Methods are removed from NotificationModel
 * 4. Unified messaging system is working
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🔍 Cleanup Verification Report</h1>";

$allGood = true;
$issues = [];

echo "<h2>🗄️ Database Verification</h2>";

try {
    $db = new Database();
    
    // Check if notification_preferences table exists
    $db->query("SHOW TABLES LIKE 'notification_preferences'");
    $tableExists = $db->single();
    
    if ($tableExists) {
        $allGood = false;
        $issues[] = "notification_preferences table still exists";
        echo "<p style='color: red;'>❌ notification_preferences table still exists</p>";
    } else {
        echo "<p style='color: green;'>✅ notification_preferences table successfully removed</p>";
    }
    
    // Check that correct tables exist
    $requiredTables = [
        'notification_settings' => 'Global notification settings',
        'user_notification_preferences' => 'User notification preferences', 
        'messages' => 'Unified messaging system'
    ];
    
    foreach ($requiredTables as $table => $description) {
        $db->query("SHOW TABLES LIKE '$table'");
        $exists = $db->single();
        
        if ($exists) {
            echo "<p style='color: green;'>✅ {$table} table exists ({$description})</p>";
        } else {
            $allGood = false;
            $issues[] = "{$table} table missing";
            echo "<p style='color: red;'>❌ {$table} table missing</p>";
        }
    }
    
} catch (Exception $e) {
    $allGood = false;
    $issues[] = "Database error: " . $e->getMessage();
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>📁 File Verification</h2>";

// Check that migration files are removed
$migrationFiles = [
    'database/migrations/pwa_tables_only.sql',
    'database/migrations/pwa_compatible_migration.sql',
    'database/migrations/pwa_safe_migration.sql',
    'database/migrations/add_pwa_features.sql'
];

$filesRemoved = 0;
foreach ($migrationFiles as $file) {
    if (!file_exists($file)) {
        $filesRemoved++;
        echo "<p style='color: green;'>✅ {$file} removed</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ {$file} still exists</p>";
    }
}

echo "<p><strong>Migration files removed: {$filesRemoved}/" . count($migrationFiles) . "</strong></p>";

// Check that temp files are removed
$tempFiles = [
    'fix_notification_preferences.php',
    'finalize_notification_system.php',
    'copy_notification_file.php'
];

$tempFilesRemoved = 0;
foreach ($tempFiles as $file) {
    if (!file_exists($file)) {
        $tempFilesRemoved++;
        echo "<p style='color: green;'>✅ {$file} removed</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ {$file} still exists</p>";
    }
}

echo "<p><strong>Temp files removed: {$tempFilesRemoved}/" . count($tempFiles) . "</strong></p>";

echo "<h2>🔧 Code Verification</h2>";

// Check NotificationModel for removed methods
$notificationModelPath = 'models/NotificationModel.php';
if (file_exists($notificationModelPath)) {
    $content = file_get_contents($notificationModelPath);
    
    $methodsToCheck = [
        'getPWANotificationPreferences' => 'Get PWA notification preferences',
        'updatePWANotificationPreferences' => 'Update PWA notification preferences',
        'createDefaultPWAPreferences' => 'Create default PWA preferences'
    ];
    
    foreach ($methodsToCheck as $method => $description) {
        if (strpos($content, $method) === false) {
            echo "<p style='color: green;'>✅ {$method}() method removed</p>";
        } else {
            $allGood = false;
            $issues[] = "{$method}() method still exists";
            echo "<p style='color: red;'>❌ {$method}() method still exists</p>";
        }
    }
    
    // Check for notification_preferences table references
    if (strpos($content, 'notification_preferences') === false) {
        echo "<p style='color: green;'>✅ No references to notification_preferences table found</p>";
    } else {
        $allGood = false;
        $issues[] = "notification_preferences table still referenced in NotificationModel";
        echo "<p style='color: red;'>❌ notification_preferences table still referenced in NotificationModel</p>";
    }
    
} else {
    $allGood = false;
    $issues[] = "NotificationModel.php not found";
    echo "<p style='color: red;'>❌ NotificationModel.php not found</p>";
}

echo "<h2>🚀 System Verification</h2>";

// Test unified messaging system
try {
    require_once 'models/UnifiedMessageModel.php';
    $messageModel = new UnifiedMessageModel();
    echo "<p style='color: green;'>✅ UnifiedMessageModel loads successfully</p>";
    
    // Test that it can access global settings
    if (method_exists($messageModel, 'canUserSendNotifications')) {
        echo "<p style='color: green;'>✅ UnifiedMessageModel has required methods</p>";
    } else {
        $allGood = false;
        $issues[] = "UnifiedMessageModel missing required methods";
        echo "<p style='color: red;'>❌ UnifiedMessageModel missing required methods</p>";
    }
    
} catch (Exception $e) {
    $allGood = false;
    $issues[] = "UnifiedMessageModel error: " . $e->getMessage();
    echo "<p style='color: red;'>❌ UnifiedMessageModel error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>📊 Final Report</h2>";

if ($allGood) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<h3>🎉 Cleanup Verification: SUCCESS</h3>";
    echo "<p><strong>All cleanup operations verified successfully!</strong></p>";
    echo "<ul>";
    echo "<li>✅ notification_preferences table removed</li>";
    echo "<li>✅ Migration files cleaned up</li>";
    echo "<li>✅ Unused methods removed</li>";
    echo "<li>✅ Unified messaging system working</li>";
    echo "</ul>";
    echo "<p><strong>Your system is now clean and using only the unified notification system.</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>⚠️ Cleanup Verification: ISSUES FOUND</h3>";
    echo "<p><strong>The following issues were detected:</strong></p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>{$issue}</li>";
    }
    echo "</ul>";
    echo "<p><strong>Please review and address these issues.</strong></p>";
    echo "</div>";
}

echo "<h3>📋 System Status</h3>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Component</th><th>Status</th><th>Description</th></tr>";
echo "<tr><td>Global Settings</td><td style='color: green;'>✅ Active</td><td>notification_settings table</td></tr>";
echo "<tr><td>User Preferences</td><td style='color: green;'>✅ Active</td><td>user_notification_preferences table</td></tr>";
echo "<tr><td>Unified Messaging</td><td style='color: green;'>✅ Active</td><td>messages table + UnifiedMessageModel</td></tr>";
echo "<tr><td>Legacy PWA Table</td><td style='color: " . ($allGood ? "green'>✅ Removed" : "red'>❌ Still Present") . "</td><td>notification_preferences table</td></tr>";
echo "</table>";

echo "<hr>";
echo "<p><em>Verification completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
