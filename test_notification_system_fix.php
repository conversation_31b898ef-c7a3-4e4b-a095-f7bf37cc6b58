<?php
/**
 * Test Notification System Fix
 * 
 * This script tests both FCM push notifications and toast notifications
 * to verify the system is working after the fix.
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationModel.php';
require_once 'models/NotificationService.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

echo "<h2>Notification System Fix Test</h2>";
echo "<p><strong>Debug Mode:</strong> " . (DEBUG_MODE ? 'Enabled' : 'Disabled') . "</p>";

$db = new Database();
$notificationModel = new NotificationModel();
$notificationService = new NotificationService();

try {
    $userId = $_SESSION['user_id'];
    
    echo "<h3>1. Testing Toast Notification Creation</h3>";
    
    // Add a test toast notification
    if (isset($_POST['test_toast'])) {
        $result = $notificationService->sendTestToastNotification(
            $userId, 
            "System Fix Test", 
            "This is a test toast notification to verify the fix is working - " . date('H:i:s')
        );
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test toast notification created successfully!</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create test toast notification.</p>";
        }
    }
    
    echo "<h3>2. Testing FCM Push Notification</h3>";
    
    // Test FCM push notification
    if (isset($_POST['test_push'])) {
        // Check if user has FCM subscriptions
        $subscriptions = $notificationModel->getUserPushSubscriptions($userId);
        
        if (empty($subscriptions)) {
            echo "<p style='color: orange;'>⚠️ No FCM subscriptions found for your user. Please enable push notifications in your browser first.</p>";
        } else {
            echo "<p>Found " . count($subscriptions) . " FCM subscription(s)</p>";
            
            // Create a test push notification manually
            $testNotification = (object) [
                'id' => 0,
                'user_id' => $userId,
                'notification_type' => 'push',
                'subject' => "FCM Fix Test",
                'message' => "This is a test FCM push notification to verify the fix is working - " . date('H:i:s'),
                'event_id' => 0,
                'event_type' => 'test'
            ];
            
            $result = $notificationService->sendNotification($testNotification);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Test FCM push notification sent successfully!</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to send test FCM push notification.</p>";
            }
        }
    }
    
    echo "<h3>3. Current Unread Notifications</h3>";
    
    // Get current unread notifications
    $unreadNotifications = $notificationModel->getUnreadNotifications($userId);
    
    echo "<p><strong>Toast Notifications:</strong> " . count($unreadNotifications['toast']) . "</p>";
    echo "<p><strong>Push Notifications:</strong> " . count($unreadNotifications['push']) . "</p>";
    
    if (!empty($unreadNotifications['toast'])) {
        echo "<h4>Recent Toast Notifications:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Created</th></tr>";
        foreach (array_slice($unreadNotifications['toast'], 0, 5) as $toast) {
            echo "<tr>";
            echo "<td>{$toast->id}</td>";
            echo "<td>" . htmlspecialchars($toast->title) . "</td>";
            echo "<td>" . htmlspecialchars(substr($toast->message, 0, 50)) . "...</td>";
            echo "<td>{$toast->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>4. System Status</h3>";
    
    // Check FCM Helper
    $fcmHelperPath = APPROOT . '/helpers/fcm_v1_helper.php';
    echo "<p><strong>FCM Helper:</strong> " . (file_exists($fcmHelperPath) ? '✅ Found' : '❌ Missing') . "</p>";
    
    // Check Firebase service account
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    echo "<p><strong>Firebase Service Account:</strong> " . (file_exists($serviceAccountPath) ? '✅ Found' : '❌ Missing') . "</p>";
    
    if (file_exists($serviceAccountPath)) {
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
        $hasRealKeys = $serviceAccount && 
                      !empty($serviceAccount['project_id']) && 
                      $serviceAccount['project_id'] !== 'YOUR_PROJECT_ID' &&
                      !empty($serviceAccount['private_key']) && 
                      $serviceAccount['private_key'] !== 'YOUR_PRIVATE_KEY_HERE';
        
        echo "<p><strong>Firebase Keys:</strong> " . ($hasRealKeys ? '✅ Configured' : '❌ Placeholder values') . "</p>";
    }
    
    echo "<h3>5. Test Actions</h3>";
    echo "<div style='margin: 20px 0;'>";
    
    echo "<form method='post' style='display: inline-block; margin-right: 10px;'>";
    echo "<button type='submit' name='test_toast' class='btn btn-primary'>Test Toast Notification</button>";
    echo "</form>";
    
    echo "<form method='post' style='display: inline-block; margin-right: 10px;'>";
    echo "<button type='submit' name='test_push' class='btn btn-success'>Test FCM Push Notification</button>";
    echo "</form>";
    
    echo "</div>";
    
    echo "<h3>6. JavaScript Test</h3>";
    echo "<p>The page should automatically load and display any unread toast notifications.</p>";
    echo "<p><strong>What to look for:</strong></p>";
    echo "<ul>";
    echo "<li>Toast notifications should appear in the top-right corner</li>";
    echo "<li>Check browser console for debug messages (F12 → Console)</li>";
    echo "<li>Look for '[NotificationManager]' messages in console</li>";
    echo "<li>If you created a test toast above, it should appear automatically</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='/admin/dashboard'>← Back to Admin Dashboard</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Notification System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        
        // Debug logging
        console.log('[Test] Page loaded, BASE_URL:', BASE_URL);
        
        // Check if notification manager loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[Test] DOM ready');
            
            setTimeout(() => {
                if (window.notificationManager) {
                    console.log('[Test] ✅ NotificationManager found');
                    console.log('[Test] Forcing notification reload...');
                    window.notificationManager.loadUnreadNotifications();
                } else {
                    console.log('[Test] ❌ NotificationManager not found');
                }
            }, 1000);
        });
    </script>
</head>
<body data-debug-mode="true">
    <!-- Include the notification system -->
    <script src="<?php echo BASE_URL; ?>/public/js/notifications.js"></script>
</body>
</html>