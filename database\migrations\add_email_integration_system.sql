-- Email Integration System Migration
-- This adds email server settings, processing logs, and ticket number system

-- Step 1: Add email server settings to system_settings table
INSERT IGNORE INTO `system_settings` (`setting_key`, `setting_value`, `setting_group`) VALUES
('email_pop3_enabled', '0', 'email'),
('email_imap_enabled', '0', 'email'),
('email_server_host', '', 'email'),
('email_server_port', '993', 'email'),
('email_server_username', '', 'email'),
('email_server_password', '', 'email'),
('email_server_encryption', 'ssl', 'email'),
('email_server_protocol', 'imap', 'email'),
('email_delete_after_processing', '1', 'email'),
('email_processing_enabled', '1', 'email'),
('email_spam_filtering', '1', 'email'),
('email_max_size_mb', '10', 'email'),
('email_attachment_enabled', '1', 'email'),
('email_attachment_max_size_mb', '5', 'email'),
('email_auto_reply_enabled', '1', 'email'),
('ticket_number_prefix', 'RER', 'email'),
('ticket_number_year', '2025', 'email'),
('ticket_number_counter', '0', 'email');

-- Step 2: Create email processing log table
CREATE TABLE IF NOT EXISTS `email_processing_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email_message_id` varchar(255) NOT NULL COMMENT 'Email Message-ID header',
  `sender_email` varchar(255) NOT NULL COMMENT 'Email sender address',
  `subject` varchar(500) NOT NULL COMMENT 'Email subject',
  `ticket_number` varchar(50) DEFAULT NULL COMMENT 'Generated or extracted ticket number',
  `message_id` int(11) DEFAULT NULL COMMENT 'Created message ID',
  `processing_status` enum('pending','processed','failed','ignored') DEFAULT 'pending',
  `error_message` text DEFAULT NULL COMMENT 'Error details if processing failed',
  `email_size` int(11) DEFAULT NULL COMMENT 'Email size in bytes',
  `attachment_count` int(11) DEFAULT 0 COMMENT 'Number of attachments',
  `spam_score` decimal(3,2) DEFAULT NULL COMMENT 'Spam score (0.00-1.00)',
  `is_auto_reply` tinyint(1) DEFAULT 0 COMMENT 'Detected as auto-reply',
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email_message_id` (`email_message_id`),
  KEY `idx_sender` (`sender_email`),
  KEY `idx_ticket` (`ticket_number`),
  KEY `idx_status` (`processing_status`),
  KEY `idx_created` (`created_at` DESC),
  UNIQUE KEY `unique_email_message` (`email_message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 3: Create ticket number sequence table
CREATE TABLE IF NOT EXISTS `ticket_numbers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_number` varchar(50) NOT NULL COMMENT 'Full ticket number (e.g., RER-2025-001)',
  `prefix` varchar(10) NOT NULL COMMENT 'Ticket prefix (e.g., RER)',
  `year` int(4) NOT NULL COMMENT 'Year component',
  `sequence` int(11) NOT NULL COMMENT 'Sequence number',
  `message_id` int(11) DEFAULT NULL COMMENT 'Associated message ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_ticket_number` (`ticket_number`),
  KEY `idx_year_sequence` (`year`, `sequence`),
  KEY `idx_message` (`message_id`),
  UNIQUE KEY `unique_ticket` (`ticket_number`),
  UNIQUE KEY `unique_year_sequence` (`prefix`, `year`, `sequence`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 4: Insert default email templates
INSERT IGNORE INTO `email_templates` (`name`, `subject`, `body`, `created_by`) VALUES
('Auto Reply Confirmation', 'Re: {{subject}} [{{ticket_number}}]', 
'Hello,

Thank you for contacting us. We have received your message and assigned it ticket number {{ticket_number}}.

Your message:
Subject: {{subject}}
Received: {{date}}

We will review your message and respond as soon as possible. Please reference the ticket number {{ticket_number}} in any future correspondence regarding this matter.

Best regards,
{{site_name}} Support Team

---
This is an automated response. Please do not reply to this email.', 1),

('Welcome Message', 'Welcome to {{site_name}}', 
'Hello {{name}},

Welcome to {{site_name}}! We''re excited to have you join our community.

If you have any questions or need assistance, please don''t hesitate to contact us.

Best regards,
{{site_name}} Team', 1),

('General Response', 'Re: {{subject}} [{{ticket_number}}]', 
'Hello,

Thank you for your message regarding {{subject}}.

{{response_content}}

If you have any additional questions, please feel free to contact us and reference ticket number {{ticket_number}}.

Best regards,
{{admin_name}}
{{site_name}} Team', 1);

-- Step 5: Insert default admin folders for existing admin users
INSERT IGNORE INTO `admin_email_folders` (`admin_user_id`, `name`, `color`, `sort_order`, `is_system`)
SELECT u.id, 'Inbox', '#007bff', 1, 1 FROM users u WHERE u.role = 'admin' AND u.status = 'active';

INSERT IGNORE INTO `admin_email_folders` (`admin_user_id`, `name`, `color`, `sort_order`, `is_system`)
SELECT u.id, 'Sent', '#28a745', 2, 1 FROM users u WHERE u.role = 'admin' AND u.status = 'active';

INSERT IGNORE INTO `admin_email_folders` (`admin_user_id`, `name`, `color`, `sort_order`, `is_system`)
SELECT u.id, 'Archive', '#6c757d', 3, 1 FROM users u WHERE u.role = 'admin' AND u.status = 'active';

INSERT IGNORE INTO `admin_email_folders` (`admin_user_id`, `name`, `color`, `sort_order`, `is_system`)
SELECT u.id, 'Trash', '#dc3545', 4, 1 FROM users u WHERE u.role = 'admin' AND u.status = 'active';

-- Step 6: Add indexes for new message fields
CREATE INDEX IF NOT EXISTS `idx_messages_ticket` ON `messages` (`ticket_number`);
CREATE INDEX IF NOT EXISTS `idx_messages_email_id` ON `messages` (`email_message_id`);
CREATE INDEX IF NOT EXISTS `idx_messages_folder` ON `messages` (`folder_id`);
CREATE INDEX IF NOT EXISTS `idx_messages_owner` ON `messages` (`owned_by_admin_id`);
CREATE INDEX IF NOT EXISTS `idx_messages_sender_email` ON `messages` (`original_sender_email`);

-- Step 7: Add foreign key constraints
ALTER TABLE `admin_email_folders` 
ADD CONSTRAINT `fk_folders_admin` FOREIGN KEY (`admin_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `message_reminders` 
ADD CONSTRAINT `fk_reminders_message` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_reminders_admin` FOREIGN KEY (`admin_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `email_templates` 
ADD CONSTRAINT `fk_templates_admin` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `message_attachments` 
ADD CONSTRAINT `fk_attachments_message` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE;

ALTER TABLE `ticket_numbers` 
ADD CONSTRAINT `fk_tickets_message` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE SET NULL;
