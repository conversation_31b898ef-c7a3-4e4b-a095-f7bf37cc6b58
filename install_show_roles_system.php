<?php
/**
 * Install Show Roles System
 * 
 * This script creates the necessary database tables for the show roles system
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running by admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

echo "<h1>Install Show Roles System</h1>";

try {
    $db = new Database();
    
    echo "<h2>Creating Database Tables...</h2>";
    
    // Create show_role_requests table
    echo "<h3>1. Creating show_role_requests table...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS `show_role_requests` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `show_id` int(11) NOT NULL,
        `user_id` int(11) NOT NULL,
        `requested_role` enum('coordinator','judge','staff') NOT NULL,
        `requested_by` int(11) NOT NULL COMMENT 'User ID of coordinator/admin who made the request',
        `status` enum('pending','approved','declined','expired') NOT NULL DEFAULT 'pending',
        `request_message` text DEFAULT NULL COMMENT 'Optional message from requester',
        `response_message` text DEFAULT NULL COMMENT 'Optional message from user when responding',
        `requested_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `responded_at` datetime DEFAULT NULL,
        `expires_at` datetime NOT NULL COMMENT 'When this request expires if not responded to',
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `show_id` (`show_id`),
        KEY `user_id` (`user_id`),
        KEY `requested_by` (`requested_by`),
        KEY `status` (`status`),
        KEY `expires_at` (`expires_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stores role assignment requests with approval workflow'";
    
    if ($db->query($sql)) {
        echo "✅ show_role_requests table created successfully<br>";
    } else {
        echo "❌ Failed to create show_role_requests table<br>";
    }
    
    // Create show_role_assignments table
    echo "<h3>2. Creating show_role_assignments table...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS `show_role_assignments` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `show_id` int(11) NOT NULL,
        `user_id` int(11) NOT NULL,
        `assigned_role` enum('coordinator','judge','staff') NOT NULL,
        `assigned_by` int(11) NOT NULL COMMENT 'User ID of coordinator/admin who made the assignment',
        `request_id` int(11) DEFAULT NULL COMMENT 'Reference to the original request',
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `assigned_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `expires_at` datetime NOT NULL COMMENT 'Show end date + 1 week for automatic cleanup',
        `auto_cleanup_date` datetime NOT NULL COMMENT 'Date when this assignment will be automatically removed',
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_active_assignment` (`show_id`, `user_id`, `assigned_role`),
        KEY `show_id` (`show_id`),
        KEY `user_id` (`user_id`),
        KEY `assigned_by` (`assigned_by`),
        KEY `request_id` (`request_id`),
        KEY `is_active` (`is_active`),
        KEY `auto_cleanup_date` (`auto_cleanup_date`),
        KEY `expires_at` (`expires_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stores active show-specific role assignments'";
    
    if ($db->query($sql)) {
        echo "✅ show_role_assignments table created successfully<br>";
    } else {
        echo "❌ Failed to create show_role_assignments table<br>";
    }
    
    // Create user_show_roles view
    echo "<h3>3. Creating user_show_roles view...</h3>";
    $sql = "CREATE OR REPLACE VIEW `user_show_roles` AS
    SELECT 
        sra.user_id,
        sra.show_id,
        s.name as show_name,
        sra.assigned_role,
        sra.is_active,
        sra.assigned_at,
        sra.expires_at,
        u.name as user_name,
        u.email as user_email,
        assigner.name as assigned_by_name
    FROM show_role_assignments sra
    JOIN shows s ON sra.show_id = s.id
    JOIN users u ON sra.user_id = u.id
    JOIN users assigner ON sra.assigned_by = assigner.id
    WHERE sra.is_active = 1";
    
    if ($db->query($sql)) {
        echo "✅ user_show_roles view created successfully<br>";
    } else {
        echo "❌ Failed to create user_show_roles view<br>";
    }
    
    // Check notification_queue table and suggest enum update
    echo "<h3>4. Checking notification_queue table...</h3>";
    $db->query("SHOW COLUMNS FROM notification_queue LIKE 'notification_category'");
    $column = $db->single();
    
    if ($column) {
        echo "✅ notification_queue table exists<br>";
        echo "Current notification_category enum: " . $column->Type . "<br>";
        
        if (strpos($column->Type, 'role_assignment') === false) {
            echo "<div style='padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>⚠️ Optional Enhancement:</strong><br>";
            echo "To enable role assignment notifications, you can manually run this SQL command:<br>";
            echo "<code style='background-color: #f8f9fa; padding: 5px; border-radius: 3px;'>";
            echo "ALTER TABLE notification_queue MODIFY COLUMN notification_category ENUM('event_reminder','registration_deadline','test','role_assignment') NOT NULL DEFAULT 'event_reminder';";
            echo "</code><br>";
            echo "For now, the system will use the 'test' category for role assignment notifications.";
            echo "</div>";
        } else {
            echo "✅ notification_category enum already includes 'role_assignment'<br>";
        }
    } else {
        echo "❌ notification_queue table not found<br>";
    }
    
    // Test the installation
    echo "<h2>Testing Installation...</h2>";
    
    // Test inserting a sample request (will be cleaned up)
    $testUserId = $_SESSION['user_id'];
    $db->query("SELECT id FROM shows LIMIT 1");
    $testShow = $db->single();
    
    if ($testShow) {
        echo "<h3>5. Testing database operations...</h3>";
        
        // Test insert
        $testSql = "INSERT INTO show_role_requests 
                    (show_id, user_id, requested_role, requested_by, expires_at) 
                    VALUES (?, ?, 'staff', ?, DATE_ADD(NOW(), INTERVAL 7 DAY))";
        
        $db->query($testSql);
        $db->bind(1, $testShow->id);
        $db->bind(2, $testUserId);
        $db->bind(3, $testUserId);
        
        if ($db->execute()) {
            $testRequestId = $db->lastInsertId();
            echo "✅ Test request inserted successfully (ID: {$testRequestId})<br>";
            
            // Clean up test data
            $db->query("DELETE FROM show_role_requests WHERE id = ?");
            $db->bind(1, $testRequestId);
            if ($db->execute()) {
                echo "✅ Test data cleaned up successfully<br>";
            }
        } else {
            echo "❌ Failed to insert test request<br>";
        }
    }
    
    echo "<h2>Installation Complete!</h2>";
    echo "<div style='padding: 20px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<strong>✅ Show Roles System Installation Complete!</strong><br><br>";
    echo "<strong>Next Steps:</strong><br>";
    echo "1. The system is now ready to use<br>";
    echo "2. You can access it via: <code>/show_roles/manage/{show_id}</code><br>";
    echo "3. Admin overview available at: <code>/show_roles/admin_overview</code><br>";
    echo "4. Users can view their requests at: <code>/show_roles/my_requests</code><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Installation Error</h2>";
    echo "<div style='padding: 20px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Line:</strong> " . $e->getLine();
    echo "</div>";
}

echo "<br><br><a href='/admin/dashboard'>← Back to Admin Dashboard</a>";
?>
