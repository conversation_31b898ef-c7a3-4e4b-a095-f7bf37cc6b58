<?php
/**
 * Process Reminders Cron Job
 * 
 * Processes due reminders and sends notifications to admins
 * Runs every 2 minutes via cron job
 */

// Define APPROOT (go up one level from /cron/ to root)
define('APPROOT', dirname(__FILE__, 2));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting for debugging
ini_set('display_errors', 0); // Don't display errors in cron
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/SettingsModel.php';
require_once APPROOT . '/models/ReminderService.php';

// Create instances
$db = new Database();
$settingsModel = new SettingsModel();
$reminderService = new ReminderService();

// Log start of processing
$startTime = microtime(true);
error_log("process_reminders.php - Starting reminder processing at " . date('Y-m-d H:i:s'));

try {
    // Process due reminders
    $results = $reminderService->processDueReminders();
    
    // Log results
    $endTime = microtime(true);
    $processingTime = round($endTime - $startTime, 2);

    $logMessage = "process_reminders.php - Processing completed in {$processingTime}s: ";
    $logMessage .= "Processed: {$results['processed']}, ";
    $logMessage .= "Failed: {$results['failed']}, ";
    $logMessage .= "Total: {$results['total']}";

    error_log($logMessage);
    
    // Update heartbeat file
    updateReminderHeartbeat($results, $processingTime);
    
    // Cleanup old reminders if needed (run once per day)
    $lastCleanup = $settingsModel->getSetting('reminder_last_cleanup', '');
    $today = date('Y-m-d');
    
    if ($lastCleanup !== $today) {
        try {
            $cleanupDays = (int)$settingsModel->getSetting('reminder_cleanup_days', '90');
            $reminderService->cleanupOldReminders($cleanupDays);
            $settingsModel->setSetting('reminder_last_cleanup', $today);
            error_log("process_reminders.php - Cleaned up old reminders older than {$cleanupDays} days");
        } catch (Exception $e) {
            error_log("process_reminders.php - Cleanup error: " . $e->getMessage());
        }
    }
    
    exit(0);
    
} catch (Exception $e) {
    $endTime = microtime(true);
    $processingTime = round($endTime - $startTime, 2);

    error_log("process_reminders.php - Fatal error after {$processingTime}s: " . $e->getMessage());
    error_log("process_reminders.php - Stack trace: " . $e->getTraceAsString());
    
    // Update heartbeat with error
    updateReminderHeartbeat(['error' => $e->getMessage()], $processingTime);
    
    exit(1);
}

/**
 * Update heartbeat file to track cron job health
 */
function updateReminderHeartbeat($results, $processingTime) {
    try {
        $heartbeatFile = APPROOT . '/logs/reminder_processing_heartbeat.txt';
        $heartbeatDir = dirname($heartbeatFile);
        
        // Create logs directory if it doesn't exist
        if (!is_dir($heartbeatDir)) {
            mkdir($heartbeatDir, 0755, true);
        }
        
        $heartbeatData = [
            'last_run' => gmdate('Y-m-d H:i:s'),
            'processing_time' => $processingTime,
            'status' => isset($results['error']) ? 'error' : 'success',
            'processed' => $results['processed'] ?? 0,
            'failed' => $results['failed'] ?? 0,
            'total' => $results['total'] ?? 0,
            'error' => $results['error'] ?? null
        ];
        
        file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
        
    } catch (Exception $e) {
        error_log("process_reminders.php - Failed to update heartbeat: " . $e->getMessage());
    }
}
