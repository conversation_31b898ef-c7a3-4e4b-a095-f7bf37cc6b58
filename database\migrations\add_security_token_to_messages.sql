-- Add security token field for enhanced ticket security
-- This prevents ticket number guessing attacks by requiring both ticket number and random token

-- Check and add security_token column to messages table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'messages'
     AND column_name = 'security_token'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE messages ADD COLUMN security_token VARCHAR(10) NULL COMMENT "Random security token for ticket validation"',
    'SELECT "security_token column already exists in messages table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add security_token column to ticket_numbers table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'ticket_numbers'
     AND column_name = 'security_token'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE ticket_numbers ADD COLUMN security_token VARCHAR(10) NULL COMMENT "Random security token for ticket validation"',
    'SELECT "security_token column already exists in ticket_numbers table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add index for messages table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'messages'
     AND index_name = 'idx_ticket_security'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE messages ADD INDEX idx_ticket_security (ticket_number, security_token)',
    'SELECT "idx_ticket_security index already exists on messages table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add index for ticket_numbers table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'ticket_numbers'
     AND index_name = 'idx_ticket_security'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE ticket_numbers ADD INDEX idx_ticket_security (ticket_number, security_token)',
    'SELECT "idx_ticket_security index already exists on ticket_numbers table" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
