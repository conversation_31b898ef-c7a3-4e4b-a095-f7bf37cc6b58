<?php
/**
 * Debug Archived Query
 * Test the archived messages query directly
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Debug Archived Query</h1>";

$db = new Database();
$userId = 3; // Your user ID

echo "<h2>1. All Messages for User $userId</h2>";
$sql = "SELECT id, subject, is_read, is_archived, created_at FROM messages WHERE to_user_id = :user_id ORDER BY created_at DESC";
$db->query($sql);
$db->bind(':user_id', $userId);
$allMessages = $db->resultSet();

echo "<table border='1'>";
echo "<tr><th>ID</th><th>Subject</th><th>is_read</th><th>is_archived</th><th>Created</th></tr>";
foreach ($allMessages as $msg) {
    echo "<tr>";
    echo "<td>{$msg->id}</td>";
    echo "<td>" . htmlspecialchars($msg->subject) . "</td>";
    echo "<td>{$msg->is_read}</td>";
    echo "<td>{$msg->is_archived}</td>";
    echo "<td>{$msg->created_at}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>2. Archived Messages Query (should be empty)</h2>";
$sql = "SELECT id, subject, is_read, is_archived, created_at FROM messages WHERE to_user_id = :user_id AND is_archived = 1 ORDER BY created_at DESC";
$db->query($sql);
$db->bind(':user_id', $userId);
$archivedMessages = $db->resultSet();

echo "<p><strong>Count:</strong> " . count($archivedMessages) . "</p>";
if (count($archivedMessages) > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Subject</th><th>is_read</th><th>is_archived</th><th>Created</th></tr>";
    foreach ($archivedMessages as $msg) {
        echo "<tr>";
        echo "<td>{$msg->id}</td>";
        echo "<td>" . htmlspecialchars($msg->subject) . "</td>";
        echo "<td>{$msg->is_read}</td>";
        echo "<td>{$msg->is_archived}</td>";
        echo "<td>{$msg->created_at}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No archived messages found (this is expected).</p>";
}

echo "<h2>3. Test the UnifiedMessageModel Method</h2>";
require_once 'models/UnifiedMessageModel.php';
$model = new UnifiedMessageModel();

$archivedFromModel = $model->getUserMessages($userId, 'archived', 20, 0);
echo "<p><strong>Count from Model:</strong> " . count($archivedFromModel) . "</p>";

if (count($archivedFromModel) > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Subject</th><th>is_read</th><th>is_archived</th><th>Created</th></tr>";
    foreach ($archivedFromModel as $msg) {
        echo "<tr>";
        echo "<td>{$msg->id}</td>";
        echo "<td>" . htmlspecialchars($msg->subject) . "</td>";
        echo "<td>{$msg->is_read}</td>";
        echo "<td>{$msg->is_archived}</td>";
        echo "<td>{$msg->created_at}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No archived messages from model (this is expected).</p>";
}

echo "<h2>4. Database Schema Check</h2>";
$sql = "DESCRIBE messages";
$db->query($sql);
$schema = $db->resultSet();

echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
foreach ($schema as $field) {
    echo "<tr>";
    echo "<td>{$field->Field}</td>";
    echo "<td>{$field->Type}</td>";
    echo "<td>{$field->Null}</td>";
    echo "<td>{$field->Key}</td>";
    echo "<td>{$field->Default}</td>";
    echo "<td>{$field->Extra}</td>";
    echo "</tr>";
}
echo "</table>";
?>