<?php
/**
 * Test Corrected Mobile Button Sizes and Layout
 * 
 * Tests the corrected button sizes and truly horizontal layout
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🔘 Test Corrected Mobile Button Sizes & Layout</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Corrected Button Improvements</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Size Corrections Applied:</h3>";
    echo "<ul>";
    echo "<li><strong>Main Bulk Actions:</strong> Reduced to 5px 9px (perfect size)</li>";
    echo "<li><strong>Message Actions:</strong> Reduced to 3px 7px (perfect size)</li>";
    echo "<li><strong>Horizontal Layout:</strong> Added flex-direction: row !important</li>";
    echo "<li><strong>No Wrapping:</strong> Added flex-wrap: nowrap for bulk actions</li>";
    echo "<li><strong>Force Display:</strong> Added display: flex !important</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the corrected button layout...</p>";
        
        $subject = "🔘 Corrected Button Test";
        $message = "This tests the corrected mobile button sizes: Main Bulk (5px 9px), Message Actions (3px 7px), and truly horizontal layout.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Mobile Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile Button Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile device</strong></li>";
    echo "<li><strong>Navigate to Messages</strong> (notification center)</li>";
    echo "<li><strong>Select multiple messages:</strong> Check the checkboxes</li>";
    echo "<li><strong>Check bulk actions:</strong> Should be horizontal row (not stacked)</li>";
    echo "<li><strong>Test button size:</strong> Should be comfortable but not oversized</li>";
    echo "<li><strong>Check message actions:</strong> Should be horizontal and appropriately sized</li>";
    echo "<li><strong>Verify no stacking:</strong> All buttons should be in rows, not columns</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Mobile Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Element</th><th>Previous Issue</th><th>Corrected Result</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Main Bulk Actions</strong></td>";
    echo "<td style='color: red;'>❌ Too big (8px 12px) + stacking vertically</td>";
    echo "<td style='color: green;'>✅ Perfect size (5px 9px) + horizontal row</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message Actions</strong></td>";
    echo "<td style='color: red;'>❌ Too big (6px 8px) + sometimes stacking</td>";
    echo "<td style='color: green;'>✅ Perfect size (3px 7px) + horizontal row</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Layout Direction</strong></td>";
    echo "<td style='color: red;'>❌ Still stacking vertically despite changes</td>";
    echo "<td style='color: green;'>✅ Forced horizontal with flex-direction: row !important</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Button Wrapping</strong></td>";
    echo "<td style='color: red;'>❌ Bulk actions wrapping to new lines</td>";
    echo "<td style='color: green;'>✅ No wrapping with flex-wrap: nowrap</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Corrections Made</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔘 Main Bulk Actions HTML:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- CORRECTED: Proper horizontal layout -->
<div class=\"btn-group\" role=\"group\" style=\"display: flex; flex-wrap: nowrap;\">
    <button class=\"btn btn-outline-success btn-sm\" style=\"padding: 5px 9px;\">✅</button>
    <button class=\"btn btn-outline-warning btn-sm\" style=\"padding: 5px 9px;\">📦</button>
    <button class=\"btn btn-outline-danger btn-sm\" style=\"padding: 5px 9px;\">🗑️</button>
    <button class=\"btn btn-outline-secondary btn-sm\" style=\"padding: 5px 9px;\">❌</button>
</div>";
    echo "</pre>";
    
    echo "<h3>🔘 Message Action Buttons HTML:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- CORRECTED: Smaller size, horizontal layout -->
<div class=\"btn-group btn-group-sm\" style=\"display: flex; flex-wrap: wrap;\">
    <button style=\"padding: 3px 7px; font-size: 11px; margin-bottom: 2px;\">👁️</button>
    <button style=\"padding: 3px 7px; font-size: 11px; margin-bottom: 2px;\">✅</button>
    <button style=\"padding: 3px 7px; font-size: 11px; margin-bottom: 2px;\">📦</button>
</div>";
    echo "</pre>";
    
    echo "<h3>🎨 Corrected CSS:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "/* Force horizontal layout for bulk actions */
.d-md-none .btn-group {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
}

/* Main bulk actions - perfect size */
.d-md-none .btn-group .btn {
    padding: 5px 9px !important;
    font-size: 13px !important;
    flex-shrink: 0 !important;
}

/* Message actions - smaller size */
.d-md-none .message-item .btn-group .btn {
    padding: 3px 7px !important;
    font-size: 11px !important;
    flex-shrink: 0 !important;
}";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>📏 Button Size Comparison</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📐 Size Evolution:</h3>";
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Button Type</th><th>Original</th><th>Too Big</th><th>Corrected</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Main Bulk Actions</strong></td>";
    echo "<td>4px 8px</td>";
    echo "<td style='color: red;'>8px 12px (too big)</td>";
    echo "<td style='color: green;'><strong>5px 9px (perfect)</strong></td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message Actions</strong></td>";
    echo "<td>2px 6px</td>";
    echo "<td style='color: red;'>6px 8px (too big)</td>";
    echo "<td style='color: green;'><strong>3px 7px (perfect)</strong></td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    echo "<h2>🔧 Layout Fixes Applied</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 Horizontal Layout Enforcement:</h3>";
    echo "<ul>";
    echo "<li><strong>display: flex !important</strong> - Forces flexbox layout</li>";
    echo "<li><strong>flex-direction: row !important</strong> - Forces horizontal direction</li>";
    echo "<li><strong>flex-wrap: nowrap !important</strong> - Prevents wrapping for bulk actions</li>";
    echo "<li><strong>flex-shrink: 0 !important</strong> - Prevents buttons from shrinking</li>";
    echo "<li><strong>Inline styles</strong> - Added to HTML for immediate effect</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Corrected Button Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>What you should see on mobile:</strong></p>";
        echo "<ul>";
        echo "<li>🔘 <strong>Horizontal bulk actions:</strong> ✅ 📦 🗑️ ❌ in a perfect row</li>";
        echo "<li>📏 <strong>Perfect size:</strong> 5px 9px - comfortable but not oversized</li>";
        echo "<li>🔘 <strong>Horizontal message actions:</strong> 👁️ ✅ 📦 in rows per message</li>";
        echo "<li>📐 <strong>Smaller message buttons:</strong> 3px 7px - appropriately sized</li>";
        echo "<li>🚫 <strong>No stacking:</strong> All buttons truly horizontal</li>";
        echo "</ul>";
        echo "<p><strong>The buttons should now be perfectly sized and truly horizontal!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated file:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/notification_center/index.php</code> - Corrected button sizes and layout</li>";
    echo "</ul>";
    echo "<p><strong>Corrections made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Reduced main bulk actions: 8px 12px → 5px 9px</li>";
    echo "<li>✅ Reduced message actions: 6px 8px → 3px 7px</li>";
    echo "<li>✅ Added flex-direction: row !important</li>";
    echo "<li>✅ Added flex-wrap: nowrap for bulk actions</li>";
    echo "<li>✅ Added display: flex !important</li>";
    echo "<li>✅ Added flex-shrink: 0 to prevent button shrinking</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Corrected mobile button test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
