<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-envelope-open"></i> View Message
                </h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-warning btn-sm" onclick="showReminderModal(<?= $data['message']->id ?>)">
                        <i class="fas fa-bell"></i> Set Reminder
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="showMoveToFolderModal(<?= $data['message']->id ?>)">
                        <i class="fas fa-folder-open"></i> Move to Folder
                    </button>
                    <a href="<?= URLROOT ?>/admin/email_dashboard" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Message Content -->
        <div class="col-lg-8">
            <!-- Message Header -->
            <div class="card mb-3">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h5 class="mb-1"><?= htmlspecialchars($data['message']->subject) ?></h5>
                            <div class="d-flex gap-3 text-muted small">
                                <span><i class="fas fa-user"></i> From: <?= htmlspecialchars($data['message']->sender_name ?: $data['message']->original_sender_email) ?></span>
                                <span><i class="fas fa-clock"></i> <?= date('M j, Y g:i A', strtotime($data['message']->created_at)) ?></span>
                                <?php if ($data['message']->ticket_number): ?>
                                <span><i class="fas fa-ticket-alt"></i> <?= htmlspecialchars($data['message']->ticket_number) ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <?php
                            $priorityColors = [
                                'low' => 'success',
                                'normal' => 'secondary',
                                'high' => 'warning',
                                'urgent' => 'danger'
                            ];
                            $priorityColor = $priorityColors[$data['message']->priority] ?? 'secondary';
                            ?>
                            <span class="badge bg-<?= $priorityColor ?>"><?= ucfirst($data['message']->priority) ?></span>
                            <?php if ($data['message']->owned_by_admin_id): ?>
                            <span class="badge bg-info">
                                <i class="fas fa-user-lock"></i> Owned by <?= htmlspecialchars($data['message']->owner_name) ?>
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="message-content" style="white-space: pre-wrap; line-height: 1.6;">
                        <?= htmlspecialchars($data['message']->message) ?>
                    </div>
                </div>
            </div>

            <!-- Quick Reply -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-reply"></i> Quick Reply
                    </h6>
                </div>
                <div class="card-body">
                    <form id="quickReplyForm">
                        <div class="mb-3">
                            <label for="reply_template" class="form-label">Use Template (Optional)</label>
                            <select class="form-select" id="reply_template" onchange="loadTemplate()">
                                <option value="">Select a template...</option>
                                <?php foreach ($data['templates'] as $template): ?>
                                <option value="<?= $template->id ?>" 
                                        data-subject="<?= htmlspecialchars($template->subject) ?>"
                                        data-body="<?= htmlspecialchars($template->body) ?>">
                                    <?= htmlspecialchars($template->name) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reply_subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="reply_subject" 
                                   value="Re: <?= htmlspecialchars($data['message']->subject) ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reply_message" class="form-label">Message</label>
                            <textarea class="form-control" id="reply_message" rows="6" required></textarea>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Send Reply
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearReply()">
                                <i class="fas fa-times"></i> Clear
                            </button>
                        </div>
                        
                        <input type="hidden" id="message_id" value="<?= $data['message']->id ?>">
                        <input type="hidden" id="csrf_token" value="<?= $data['csrf_token'] ?>">
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Message Actions -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs"></i> Message Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-warning btn-sm" onclick="showReminderModal(<?= $data['message']->id ?>)">
                            <i class="fas fa-bell"></i> Set Reminder
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="showMoveToFolderModal(<?= $data['message']->id ?>)">
                            <i class="fas fa-folder-open"></i> Move to Folder
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="showTransferModal(<?= $data['message']->id ?>)">
                            <i class="fas fa-user-friends"></i> Transfer Ownership
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="markAsRead(<?= $data['message']->id ?>)">
                            <i class="fas fa-envelope-open"></i> Mark as Read
                        </button>
                    </div>
                </div>
            </div>

            <!-- Message Details -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Message Details
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <th>Type:</th>
                                <td><span class="badge bg-secondary"><?= ucfirst($data['message']->message_type) ?></span></td>
                            </tr>
                            <tr>
                                <th>Priority:</th>
                                <td><span class="badge bg-<?= $priorityColor ?>"><?= ucfirst($data['message']->priority) ?></span></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <?php if ($data['message']->is_read): ?>
                                    <span class="badge bg-success">Read</span>
                                    <?php else: ?>
                                    <span class="badge bg-warning">Unread</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php if ($data['message']->ticket_number): ?>
                            <tr>
                                <th>Ticket:</th>
                                <td><code><?= htmlspecialchars($data['message']->ticket_number) ?></code></td>
                            </tr>
                            <?php endif; ?>
                            <?php if ($data['message']->original_sender_email): ?>
                            <tr>
                                <th>Original Email:</th>
                                <td><small><?= htmlspecialchars($data['message']->original_sender_email) ?></small></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>Received:</th>
                                <td><small><?= date('M j, Y g:i A', strtotime($data['message']->created_at)) ?></small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Set Reminder Modal -->
<div class="modal fade" id="reminderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Set Reminder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="reminder_time" class="form-label">Reminder Time</label>
                    <input type="datetime-local" class="form-control" id="reminder_time" required>
                </div>
                <div class="mb-3">
                    <label for="reminder_note" class="form-label">Note (Optional)</label>
                    <textarea class="form-control" id="reminder_note" rows="3" placeholder="Add a note for this reminder..."></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Quick Options</label>
                    <div class="d-flex gap-2 flex-wrap">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(15)">15 min</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(60)">1 hour</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(1440)">1 day</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(10080)">1 week</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="confirmSetReminder()">Set Reminder</button>
            </div>
        </div>
    </div>
</div>

<script>
// Email Message JavaScript Functions
console.log('Email message script loading...');

// Global variables
var currentMessageId = <?= $data['message']->id ?>;

// Function 1: Show Reminder Modal
function showReminderModal(messageId) {
    console.log('showReminderModal called with ID:', messageId);
    currentMessageId = messageId;
    var modal = new bootstrap.Modal(document.getElementById('reminderModal'));
    modal.show();
}

// Function 2: Show Move to Folder Modal
function showMoveToFolderModal(messageId) {
    console.log('showMoveToFolderModal called with ID:', messageId);
    alert('Move to folder functionality - needs folder selection modal');
}

// Function 3: Show Transfer Modal
function showTransferModal(messageId) {
    console.log('showTransferModal called with ID:', messageId);
    alert('Transfer ownership functionality - needs admin selection modal');
}

// Function 4: Mark as Read
function markAsRead(messageId) {
    console.log('markAsRead called with ID:', messageId);
    var formData = new FormData();
    formData.append('message_ids', [messageId]);
    
    fetch('<?= URLROOT ?>/admin/bulkMarkAsRead', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Message marked as read');
            window.location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error marking message as read');
    });
}

// Function 5: Load Template
function loadTemplate() {
    console.log('loadTemplate called');
    var select = document.getElementById('reply_template');
    var selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        var subject = selectedOption.getAttribute('data-subject') || '';
        var body = selectedOption.getAttribute('data-body') || '';
        
        document.getElementById('reply_subject').value = subject;
        document.getElementById('reply_message').value = body;
        
        alert('Template loaded: ' + selectedOption.text);
    } else {
        document.getElementById('reply_subject').value = 'Re: <?= htmlspecialchars($data['message']->subject) ?>';
        document.getElementById('reply_message').value = '';
    }
}

// Function 6: Clear Reply
function clearReply() {
    document.getElementById('reply_template').value = '';
    document.getElementById('reply_subject').value = 'Re: <?= htmlspecialchars($data['message']->subject) ?>';
    document.getElementById('reply_message').value = '';
}

// Function 7: Set Quick Reminder
function setQuickReminder(minutes) {
    console.log('setQuickReminder called with minutes:', minutes);
    var now = new Date();
    now.setMinutes(now.getMinutes() + minutes);
    
    var year = now.getFullYear();
    var month = String(now.getMonth() + 1).padStart(2, '0');
    var day = String(now.getDate()).padStart(2, '0');
    var hours = String(now.getHours()).padStart(2, '0');
    var mins = String(now.getMinutes()).padStart(2, '0');
    
    document.getElementById('reminder_time').value = year + '-' + month + '-' + day + 'T' + hours + ':' + mins;
}

// Function 8: Confirm Set Reminder
function confirmSetReminder() {
    console.log('confirmSetReminder called');
    var reminderTime = document.getElementById('reminder_time').value;
    var note = document.getElementById('reminder_note').value;
    
    if (!reminderTime) {
        alert('Please select a reminder time');
        return;
    }
    
    var formData = new FormData();
    formData.append('message_id', currentMessageId);
    formData.append('reminder_time', reminderTime);
    formData.append('note', note);
    
    fetch('<?= URLROOT ?>/admin/setEmailReminder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Reminder set successfully');
            document.getElementById('reminder_time').value = '';
            document.getElementById('reminder_note').value = '';
            var modal = bootstrap.Modal.getInstance(document.getElementById('reminderModal'));
            if (modal) modal.hide();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error setting reminder');
    });
}

console.log('All email message functions loaded successfully');
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
