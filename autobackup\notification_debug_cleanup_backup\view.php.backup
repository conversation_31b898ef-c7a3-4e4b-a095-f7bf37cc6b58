<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="<?php echo BASE_URL; ?>/notification_center" class="btn btn-outline-secondary btn-sm me-3">
                        <i class="fas fa-arrow-left me-1"></i>Back to Messages
                    </a>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Conversation Thread -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-comments text-primary me-2"></i>
                                Conversation: <?php echo htmlspecialchars($message->subject); ?>
                            </h5>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-info"><?php echo count($messageThread); ?> message<?php echo count($messageThread) !== 1 ? 's' : ''; ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    <!-- Thread Messages -->
                    <?php if (!empty($messageThread)): ?>
                        <?php foreach ($messageThread as $index => $threadMessage): ?>
                            <?php 
                            $isCurrentUser = ($threadMessage->from_user_id == $currentUserId);
                            $isOriginal = ($index === 0);
                            $isReply = !$isOriginal;
                            ?>
                            
                            <div class="message-item <?php echo $isReply ? 'reply-message' : 'original-message'; ?> <?php echo $isCurrentUser ? 'from-current-user' : 'from-other-user'; ?>" 
                                 style="<?php echo $isReply ? 'margin-left: 2rem; border-left: 3px solid #dee2e6;' : ''; ?>">
                                
                                <div class="p-4 <?php echo $index < count($messageThread) - 1 ? 'border-bottom' : ''; ?>">
                                    <!-- Message Header -->
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                <?php echo strtoupper(substr($threadMessage->from_user_name ?? 'U', 0, 1)); ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">
                                                    <?php echo htmlspecialchars($threadMessage->from_user_name ?? 'Unknown User'); ?>
                                                    <?php if ($isCurrentUser): ?>
                                                        <span class="badge bg-primary ms-2">You</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y \a\t g:i A', strtotime($threadMessage->created_at)); ?>
                                                    <?php if ($isReply): ?>
                                                        <span class="badge bg-secondary ms-2">Reply</span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <!-- Message Actions -->
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php if (!$isCurrentUser): ?>
                                                    <li><a class="dropdown-item" href="#" onclick="showReplyForm(<?php echo $threadMessage->id; ?>)">
                                                        <i class="fas fa-reply me-2"></i>Reply
                                                    </a></li>
                                                <?php endif; ?>
                                                <li><a class="dropdown-item" href="#" onclick="copyMessageText(<?php echo $threadMessage->id; ?>)">
                                                    <i class="fas fa-copy me-2"></i>Copy Text
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <!-- Message Subject (only for original message) -->
                                    <?php if ($isOriginal): ?>
                                        <div class="mb-3">
                                            <h6 class="text-primary mb-2">
                                                <i class="fas fa-envelope me-2"></i>
                                                <?php echo htmlspecialchars($threadMessage->subject); ?>
                                            </h6>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Message Content -->
                                    <div class="message-content">
                                        <div class="message-text" id="message-text-<?php echo $threadMessage->id; ?>">
                                            <?php echo nl2br(htmlspecialchars($threadMessage->message)); ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Show/Event Info -->
                                    <?php if ($threadMessage->show_id && $threadMessage->show_title): ?>
                                        <div class="mt-3 p-3 bg-light rounded">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt me-2"></i>
                                                Related to: <strong><?php echo htmlspecialchars($threadMessage->show_title); ?></strong>
                                                <?php if ($threadMessage->show_location): ?>
                                                    <br><i class="fas fa-map-marker-alt me-2"></i>
                                                    <?php echo htmlspecialchars($threadMessage->show_location); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Reply Form (hidden by default) -->
                                    <div id="reply-form-<?php echo $threadMessage->id; ?>" class="reply-form mt-4" style="display: none;">
                                        <form method="POST" action="<?php echo BASE_URL; ?>/notification_center/reply">
                                            <input type="hidden" name="message_id" value="<?php echo $threadMessage->id; ?>">
                                            <div class="mb-3">
                                                <label for="reply-message-<?php echo $threadMessage->id; ?>" class="form-label">Your Reply:</label>
                                                <textarea class="form-control" id="reply-message-<?php echo $threadMessage->id; ?>" name="reply_message" rows="4" required placeholder="Type your reply here..."></textarea>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-paper-plane me-2"></i>Send Reply
                                                </button>
                                                <button type="button" class="btn btn-secondary" onclick="hideReplyForm(<?php echo $threadMessage->id; ?>)">
                                                    Cancel
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="p-4 text-center text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No messages found in this conversation.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.message-item {
    transition: background-color 0.2s ease;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.reply-message {
    background-color: #f8f9fa;
}

.message-content {
    line-height: 1.6;
}

.reply-form {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
    margin-top: 1rem;
}

.from-current-user .message-content {
    background-color: #e3f2fd;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #2196f3;
}

.from-other-user .message-content {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #9e9e9e;
}
</style>

<script>
function showReplyForm(messageId) {
    // Hide all other reply forms
    document.querySelectorAll('.reply-form').forEach(form => {
        form.style.display = 'none';
    });
    
    // Show the specific reply form
    const replyForm = document.getElementById('reply-form-' + messageId);
    if (replyForm) {
        replyForm.style.display = 'block';
        // Focus on the textarea
        const textarea = replyForm.querySelector('textarea');
        if (textarea) {
            textarea.focus();
        }
    }
}

function hideReplyForm(messageId) {
    const replyForm = document.getElementById('reply-form-' + messageId);
    if (replyForm) {
        replyForm.style.display = 'none';
        // Clear the textarea
        const textarea = replyForm.querySelector('textarea');
        if (textarea) {
            textarea.value = '';
        }
    }
}

function copyMessageText(messageId) {
    const messageText = document.getElementById('message-text-' + messageId);
    if (messageText) {
        const text = messageText.textContent || messageText.innerText;
        navigator.clipboard.writeText(text).then(() => {
            // Show a temporary success message
            const button = event.target.closest('.dropdown-item');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            button.classList.add('text-success');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('text-success');
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy text: ', err);
            alert('Failed to copy text to clipboard');
        });
    }
}

// Auto-hide reply forms when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.reply-form') && !event.target.closest('[onclick*="showReplyForm"]')) {
        document.querySelectorAll('.reply-form').forEach(form => {
            form.style.display = 'none';
        });
    }
});
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>