<?php
/**
 * Quick FCM Endpoint Test
 * 
 * Tests that the FCM controller endpoint is accessible
 * 
 * Usage: https://events.rowaneliterides.com/test_fcm_endpoint.php?test_key=fcm_endpoint_2025
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Security check
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'fcm_endpoint_2025') {
    http_response_code(404);
    exit('Not Found');
}

echo "=== FCM CONTROLLER ENDPOINT TEST ===\n\n";

try {
    // Test the FCM subscription endpoint
    $testUrl = 'https://events.rowaneliterides.com/api/pwa/fcm-subscribe';
    
    echo "1. TESTING FCM SUBSCRIPTION ENDPOINT\n";
    echo "   URL: $testUrl\n";
    
    // Test with invalid method (should return 405)
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Content-Type: application/json',
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($testUrl, false, $context);
    $httpCode = 0;
    
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (strpos($header, 'HTTP/') === 0) {
                $httpCode = (int)substr($header, 9, 3);
                break;
            }
        }
    }
    
    echo "   HTTP Response Code: $httpCode\n";
    
    if ($httpCode === 405) {
        echo "   ✅ Endpoint is accessible (correctly rejected GET request)\n";
        
        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['error'])) {
                echo "   Response: " . $data['error'] . "\n";
            }
        }
    } else if ($httpCode === 404) {
        echo "   ❌ Endpoint not found - routing may not be working\n";
    } else if ($httpCode === 500) {
        echo "   ❌ Server error - check error logs\n";
    } else {
        echo "   ⚠️  Unexpected response code\n";
    }
    
    echo "\n2. TESTING VAPID KEY ENDPOINT\n";
    
    $vapidUrl = 'https://events.rowaneliterides.com/api/pwa/vapid-key';
    echo "   URL: $vapidUrl\n";
    
    $vapidResponse = @file_get_contents($vapidUrl);
    $vapidHttpCode = 0;
    
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (strpos($header, 'HTTP/') === 0) {
                $vapidHttpCode = (int)substr($header, 9, 3);
                break;
            }
        }
    }
    
    echo "   HTTP Response Code: $vapidHttpCode\n";
    
    if ($vapidHttpCode === 200) {
        echo "   ✅ VAPID endpoint is working\n";
        
        if ($vapidResponse) {
            $vapidData = json_decode($vapidResponse, true);
            if ($vapidData && isset($vapidData['success'])) {
                echo "   VAPID Key Available: " . ($vapidData['success'] ? 'Yes' : 'No') . "\n";
            }
        }
    } else {
        echo "   ❌ VAPID endpoint issue\n";
    }
    
    echo "\n3. CONTROLLER ROUTING CHECK\n";
    
    // Check if PwaController file exists
    $controllerPath = dirname(__FILE__) . '/controllers/PwaController.php';
    if (file_exists($controllerPath)) {
        echo "   ✅ PwaController.php exists\n";
        
        // Check if fcmSubscribe method exists
        $controllerContent = file_get_contents($controllerPath);
        if (strpos($controllerContent, 'function fcmSubscribe') !== false) {
            echo "   ✅ fcmSubscribe method exists in controller\n";
        } else {
            echo "   ❌ fcmSubscribe method not found in controller\n";
        }
    } else {
        echo "   ❌ PwaController.php not found\n";
    }
    
    // Check if App.php has the routing
    $appPath = dirname(__FILE__) . '/core/App.php';
    if (file_exists($appPath)) {
        echo "   ✅ App.php exists\n";
        
        $appContent = file_get_contents($appPath);
        if (strpos($appContent, 'fcm-subscribe') !== false) {
            echo "   ✅ fcm-subscribe route exists in App.php\n";
        } else {
            echo "   ❌ fcm-subscribe route not found in App.php\n";
        }
    } else {
        echo "   ❌ App.php not found\n";
    }
    
    echo "\n4. RECOMMENDATIONS\n";
    
    if ($httpCode === 405) {
        echo "   ✅ FCM endpoint is properly configured\n";
        echo "   ✅ Ready for Firebase configuration\n";
        echo "   🔧 Next: Complete Firebase setup (see FCM_SETUP_GUIDE.md)\n";
    } else {
        echo "   🔧 Fix endpoint accessibility first\n";
        echo "   🔧 Check server error logs\n";
        echo "   🔧 Verify routing configuration\n";
    }
    
    echo "\n=== TEST COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
?>