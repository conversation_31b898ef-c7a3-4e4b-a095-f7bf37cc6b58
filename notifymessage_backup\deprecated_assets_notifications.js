# DEPRECATED FILE - BACKED UP BEFORE DELETION

This file was located at `/assets/js/notifications.js` and was not being used anywhere.
It contained broken/incomplete code and was marked as deprecated.

The actual working file is at `/public/js/notifications.js`.

## Original Content:

/**
 * DEPRECATED FILE - DO NOT USE
 * 
 * This file has been moved to the correct location: /public/js/notifications.js
 * 
 * JavaScript and CSS files MUST be placed in /public/ directory:
 * - JavaScript: /public/js/
 * - CSS: /public/css/
 * - Images: /public/images/
 * 
 * This file should be deleted.
 */

console.error('DEPRECATED: This JavaScript file has been moved to /public/js/notifications.js');
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        this.init();
    }
    
    /**
     * Initialize the notification manager
     */
    init() {
        this.setupEventListeners();
        this.loadUnreadNotifications();
        this.requestPushPermission();
        
        // Check for unread notifications every 30 seconds
        setInterval(() => {
            this.loadUnreadNotifications();
        }, 30000);
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for notification clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('.notification-item')) {
                this.markAsRead(e.target.dataset.notificationId);
            }
        });
    }
    
    /**
     * Request push notification permission
     */
    async requestPushPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            const permission = await Notification.requestPermission();
            console.log('Push notification permission:', permission);
        }
    }
    
    /**
     * Load unread notifications
     */
    async loadUnreadNotifications() {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/getUnreadCount`);
            const data = await response.json();
            
            // Update notification badge with unified count
            if (data.success) {
                this.updateNotificationBadge(data.total_unread);
            }
            
        } catch (error) {
            console.error('Error loading unread notifications:', error);
        }
    }
    
    /**
     * Update notification badge
     */
    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'inline' : 'none';
        }
    }
    
    /**
     * Mark notification as read
     */
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`${this.baseUrl}/notification/markAsRead`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.csrfToken
                },
                body: JSON.stringify({ id: notificationId })
            });
            
            if (response.ok) {
                this.loadUnreadNotifications(); // Refresh count
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
    
    /**
     * Show toast notifications
     */
    showToastNotifications(notifications) {
        notifications.forEach(notification => {
            this.showToast(notification.title, notification.message);
        });
    }
    
    /**
     * Show a toast notification
     */
    showToast(title, message) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.innerHTML = `
            <div class="toast-header">
                <strong>${title}</strong>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="toast-body">${message}</div>
        `;
        
        // Add to page
        document.body.appendChild(toast);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new NotificationManager();
});