<?php
/**
 * Test Unified PWA + FCM System
 * Tests the restored PWA update functionality and unified FCM integration
 */

require_once 'config/config.php';
require_once 'config/database.php';

// Force debug mode for testing
define('DEBUG_MODE', true);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unified PWA + FCM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-cogs"></i> Unified PWA + FCM System Test</h1>
        <p class="text-muted">Testing restored PWA update functionality and unified FCM integration</p>

        <!-- Service Worker Status -->
        <div class="test-section">
            <h3><i class="fas fa-cog"></i> Service Worker Status</h3>
            <div id="sw-status">
                <div><span class="status-indicator status-warning"></span>Checking service workers...</div>
            </div>
            <button class="btn btn-primary btn-sm mt-2" onclick="checkServiceWorkers()">
                <i class="fas fa-sync"></i> Refresh Status
            </button>
        </div>

        <!-- PWA Update Test -->
        <div class="test-section">
            <h3><i class="fas fa-sync-alt"></i> PWA Update System</h3>
            <div id="pwa-status">
                <div><span class="status-indicator status-warning"></span>Checking PWA update system...</div>
            </div>
            <button class="btn btn-success btn-sm mt-2" onclick="testPWAUpdate()">
                <i class="fas fa-download"></i> Test Update Banner
            </button>
            <button class="btn btn-warning btn-sm mt-2" onclick="forceServiceWorkerUpdate()">
                <i class="fas fa-redo"></i> Force SW Update
            </button>
        </div>

        <!-- FCM Test -->
        <div class="test-section">
            <h3><i class="fas fa-bell"></i> FCM Integration</h3>
            <div id="fcm-status">
                <div><span class="status-indicator status-warning"></span>Checking FCM integration...</div>
            </div>
            <button class="btn btn-info btn-sm mt-2" onclick="testFCM()">
                <i class="fas fa-paper-plane"></i> Test FCM
            </button>
            <button class="btn btn-secondary btn-sm mt-2" onclick="requestNotificationPermission()">
                <i class="fas fa-bell"></i> Request Permission
            </button>
        </div>

        <!-- Proactive Notification System Test -->
        <div class="test-section">
            <h3><i class="fas fa-magic"></i> Proactive Notification System</h3>
            <div id="proactive-status">
                <div><span class="status-indicator status-warning"></span>Checking proactive notification system...</div>
            </div>
            <button class="btn btn-success btn-sm mt-2" onclick="testProactivePrompt()">
                <i class="fas fa-bell-slash"></i> Test Proactive Prompt
            </button>
            <button class="btn btn-warning btn-sm mt-2" onclick="simulateEventRegistration()">
                <i class="fas fa-calendar-plus"></i> Simulate Event Registration
            </button>
            <button class="btn btn-info btn-sm mt-2" onclick="simulateLogin()">
                <i class="fas fa-sign-in-alt"></i> Simulate Login
            </button>
            <button class="btn btn-primary btn-sm mt-2" onclick="resetPromptCooldown()">
                <i class="fas fa-redo"></i> Reset Cooldown
            </button>
        </div>

        <!-- Console Log -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> Console Log</h3>
            <div id="console-log" class="log-output">Initializing tests...\n</div>
            <button class="btn btn-outline-secondary btn-sm mt-2" onclick="clearLog()">
                <i class="fas fa-trash"></i> Clear Log
            </button>
        </div>
    </div>

    <!-- Include required scripts -->
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        
        // Console logging override
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : type === 'warn' ? '[WARN]' : '[LOG]';
            logDiv.textContent += `${timestamp} ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };
        
        function clearLog() {
            document.getElementById('console-log').textContent = '';
        }
        
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const statusClass = status === 'success' ? 'status-success' : 
                              status === 'warning' ? 'status-warning' : 'status-error';
            element.innerHTML = `<div><span class="status-indicator ${statusClass}"></span>${message}</div>`;
        }
        
        // Service Worker Status Check
        async function checkServiceWorkers() {
            console.log('Checking service worker status...');
            
            if (!('serviceWorker' in navigator)) {
                updateStatus('sw-status', 'error', 'Service Workers not supported');
                return;
            }
            
            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                console.log('Found', registrations.length, 'service worker registrations');
                
                let statusHtml = '';
                let hasMainSW = false;
                let hasDeprecatedFCM = false;
                
                for (const reg of registrations) {
                    const scope = reg.scope;
                    const scriptURL = reg.active?.scriptURL || 'No active worker';
                    
                    if (scriptURL.includes('/sw.js')) {
                        hasMainSW = true;
                        statusHtml += `<div><span class="status-indicator status-success"></span>Main SW: ${scope}</div>`;
                    } else if (scriptURL.includes('firebase-messaging-sw')) {
                        hasDeprecatedFCM = true;
                        statusHtml += `<div><span class="status-indicator status-warning"></span>Deprecated FCM SW: ${scope}</div>`;
                    } else {
                        statusHtml += `<div><span class="status-indicator status-warning"></span>Other SW: ${scope}</div>`;
                    }
                }
                
                if (hasMainSW && !hasDeprecatedFCM) {
                    statusHtml += '<div><span class="status-indicator status-success"></span><strong>✓ Unified system working correctly</strong></div>';
                } else if (hasDeprecatedFCM) {
                    statusHtml += '<div><span class="status-indicator status-warning"></span><strong>⚠ Deprecated FCM SW still registered</strong></div>';
                } else if (!hasMainSW) {
                    statusHtml += '<div><span class="status-indicator status-error"></span><strong>✗ Main service worker not found</strong></div>';
                }
                
                document.getElementById('sw-status').innerHTML = statusHtml;
                
            } catch (error) {
                console.error('Error checking service workers:', error);
                updateStatus('sw-status', 'error', 'Error checking service workers: ' + error.message);
            }
        }
        
        // PWA Update Test
        async function testPWAUpdate() {
            console.log('Testing PWA update system...');
            
            if (typeof window.pwaFeatures !== 'undefined' && window.pwaFeatures.showUpdateBanner) {
                console.log('Showing update banner manually...');
                window.pwaFeatures.showUpdateBanner();
                updateStatus('pwa-status', 'success', 'Update banner shown successfully');
            } else {
                console.error('PWA features not available or showUpdateBanner method missing');
                updateStatus('pwa-status', 'error', 'PWA update system not available');
            }
        }
        
        // Force Service Worker Update
        async function forceServiceWorkerUpdate() {
            console.log('Forcing service worker update...');
            
            try {
                const registration = await navigator.serviceWorker.getRegistration('/');
                if (registration) {
                    await registration.update();
                    console.log('Service worker update check completed');
                    updateStatus('pwa-status', 'success', 'Service worker update check completed');
                } else {
                    console.error('No service worker registration found');
                    updateStatus('pwa-status', 'error', 'No service worker registration found');
                }
            } catch (error) {
                console.error('Error updating service worker:', error);
                updateStatus('pwa-status', 'error', 'Error updating service worker: ' + error.message);
            }
        }
        
        // FCM Test
        async function testFCM() {
            console.log('Testing FCM integration...');
            
            if (typeof window.fcmManager !== 'undefined') {
                console.log('FCM Manager found, testing...');
                
                if (window.fcmManager.isSupported) {
                    updateStatus('fcm-status', 'success', 'FCM supported and manager available');
                    
                    // Test token generation
                    try {
                        await window.fcmManager.init();
                        console.log('FCM initialization completed');
                    } catch (error) {
                        console.error('FCM initialization failed:', error);
                        updateStatus('fcm-status', 'warning', 'FCM supported but initialization failed');
                    }
                } else {
                    updateStatus('fcm-status', 'warning', 'FCM not supported on this device/browser');
                }
            } else {
                console.error('FCM Manager not available');
                updateStatus('fcm-status', 'error', 'FCM Manager not available');
            }
        }
        
        // Request Notification Permission
        async function requestNotificationPermission() {
            console.log('Requesting notification permission...');
            
            if (!('Notification' in window)) {
                console.error('Notifications not supported');
                return;
            }
            
            const permission = await Notification.requestPermission();
            console.log('Notification permission:', permission);
            
            if (permission === 'granted') {
                updateStatus('fcm-status', 'success', 'Notification permission granted');
            } else {
                updateStatus('fcm-status', 'warning', 'Notification permission denied');
            }
        }
        
        // Test Proactive Notification System
        function testProactivePrompt() {
            console.log('Testing proactive notification prompt...');
            
            if (window.notificationPermissionManager) {
                window.notificationPermissionManager.triggerPrompt('test-manual');
                updateStatus('proactive-status', 'success', 'Proactive prompt triggered');
            } else {
                console.error('Notification Permission Manager not available');
                updateStatus('proactive-status', 'error', 'Notification Permission Manager not available');
            }
        }
        
        // Simulate Event Registration Success
        function simulateEventRegistration() {
            console.log('Simulating event registration success...');
            
            // Dispatch the event that triggers notification prompt
            document.dispatchEvent(new CustomEvent('eventRegistrationSuccess'));
            updateStatus('proactive-status', 'success', 'Event registration success event dispatched');
        }
        
        // Simulate Login
        function simulateLogin() {
            console.log('Simulating login...');
            
            // Set the session flag
            sessionStorage.setItem('just_logged_in', 'true');
            
            // Reload the page to trigger login detection
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
        
        // Reset Prompt Cooldown
        function resetPromptCooldown() {
            console.log('Resetting prompt cooldown...');
            
            // Clear all cooldown and session flags
            localStorage.removeItem('fcm_last_prompt_time');
            sessionStorage.removeItem('fcm_prompt_shown');
            sessionStorage.removeItem('notification_banner_dismissed');
            
            updateStatus('proactive-status', 'success', 'Prompt cooldown reset - you can now test prompts again');
        }
        
        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded, initializing...');
            
            // Wait a bit for scripts to load
            setTimeout(() => {
                checkServiceWorkers();
                
                // Check if PWA features are available
                if (typeof window.pwaFeatures !== 'undefined') {
                    updateStatus('pwa-status', 'success', 'PWA features loaded and available');
                } else {
                    updateStatus('pwa-status', 'warning', 'PWA features not yet loaded');
                }
                
                // Check if FCM is available
                if (typeof window.fcmManager !== 'undefined') {
                    updateStatus('fcm-status', 'success', 'FCM manager loaded and available');
                } else {
                    updateStatus('fcm-status', 'warning', 'FCM manager not yet loaded');
                }
                
                // Check if Proactive Notification System is available
                if (typeof window.notificationPermissionManager !== 'undefined') {
                    updateStatus('proactive-status', 'success', 'Proactive notification system loaded and available');
                } else {
                    updateStatus('proactive-status', 'warning', 'Proactive notification system not yet loaded');
                }
            }, 2000);
        });
    </script>

    <!-- Load Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>
    
    <!-- Load FCM Notifications -->
    <script src="<?php echo BASE_URL; ?>/public/js/fcm-notifications.js"></script>
    
    <!-- Load PWA Features -->
    <script src="<?php echo BASE_URL; ?>/public/js/pwa-features.js"></script>
    
    <!-- Initialize -->
    <script>
        // Initialize FCM Manager
        if (typeof FCMNotificationManager !== 'undefined') {
            window.fcmManager = new FCMNotificationManager();
            console.log('FCM Manager initialized');
        }
        
        // Initialize PWA Features
        if (typeof PWAFeatures !== 'undefined') {
            window.pwaFeatures = new PWAFeatures();
            console.log('PWA Features initialized');
        }
    </script>
</body>
</html>