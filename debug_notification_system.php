<?php
/**
 * Debug script to check notification system status
 */

require_once 'config/config.php';
require_once 'models/NotificationModel.php';
require_once 'models/NotificationService.php';

echo "<h1>Notification System Debug</h1>";

try {
    $notificationModel = new NotificationModel();
    $notificationService = new NotificationService();
    
    echo "<h2>1. Global Notification Settings</h2>";
    $settings = $notificationModel->getNotificationSettings();
    echo "<pre>";
    print_r($settings);
    echo "</pre>";
    
    echo "<h2>2. Pending Notifications in Queue</h2>";
    $pendingNotifications = $notificationModel->getPendingNotifications(10);
    echo "Found " . count($pendingNotifications) . " pending notifications:<br>";
    echo "<pre>";
    print_r($pendingNotifications);
    echo "</pre>";
    
    echo "<h2>3. User Notification Preferences (User ID 3)</h2>";
    $userPrefs = $notificationModel->getUserPreferences(3);
    echo "<pre>";
    print_r($userPrefs);
    echo "</pre>";
    
    echo "<h2>4. Test Toast Notification Storage</h2>";
    // Check if toast notifications are being stored
    $db = new Database();
    $db->query("SELECT * FROM user_toast_notifications WHERE user_id = 3 ORDER BY created_at DESC LIMIT 5");
    $toastNotifications = $db->resultSet();
    echo "Recent toast notifications for user 3:<br>";
    echo "<pre>";
    print_r($toastNotifications);
    echo "</pre>";
    
    echo "<h2>5. Test Push Notification Storage</h2>";
    // Check if push notifications are being stored
    $db->query("SELECT * FROM user_push_notifications WHERE user_id = 3 ORDER BY created_at DESC LIMIT 5");
    $pushNotifications = $db->resultSet();
    echo "Recent push notifications for user 3:<br>";
    echo "<pre>";
    print_r($pushNotifications);
    echo "</pre>";
    
    echo "<h2>6. Test Sending Toast Notification</h2>";
    $result = $notificationService->sendTestToastNotification(3, "Debug Test Toast", "This is a debug test toast notification");
    echo "Toast notification result: " . ($result ? "SUCCESS" : "FAILED") . "<br>";
    
    echo "<h2>7. Test Sending Push Notification</h2>";
    $result = $notificationService->sendTestPushNotification(3, "Debug Test Push", "This is a debug test push notification");
    echo "Push notification result: " . ($result ? "SUCCESS" : "FAILED") . "<br>";
    
    echo "<h2>8. Process Pending Notifications</h2>";
    $results = $notificationService->processPendingNotifications(10);
    echo "<pre>";
    print_r($results);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h2>ERROR</h2>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "Trace: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>
