<?php
/**
 * Debug Scheduled For Issue
 * 
 * Investigates why scheduled_for is being set to future times
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🕐 Debug Scheduled For Issue</h1>";

try {
    $db = new Database();
    
    echo "<h2>🔍 Scheduled For Analysis</h2>";
    
    // Get all pending notifications with their scheduled times
    $db->query("SELECT id, user_id, notification_type, subject, scheduled_for, created_at, updated_at FROM notification_queue WHERE status = 'pending' ORDER BY created_at DESC");
    $db->execute();
    $notifications = $db->resultSet();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Pending Notifications Time Analysis:</h3>";
    
    if (empty($notifications)) {
        echo "<p style='color: green;'>✅ No pending notifications found</p>";
    } else {
        $currentTime = date('Y-m-d H:i:s');
        echo "<p><strong>Current server time:</strong> {$currentTime}</p>";
        
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Type</th><th>Subject</th><th>Created At</th><th>Scheduled For</th><th>Time Diff</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($notifications as $notification) {
            $scheduledTime = $notification->scheduled_for;
            $createdTime = $notification->created_at;
            
            // Calculate time difference
            $scheduledTimestamp = strtotime($scheduledTime);
            $currentTimestamp = time();
            $timeDiff = $scheduledTimestamp - $currentTimestamp;
            
            $timeDiffText = '';
            $statusColor = 'green';
            $statusText = '✅ Ready';
            
            if ($scheduledTime === '0000-00-00 00:00:00') {
                $timeDiffText = 'Invalid date';
                $statusColor = 'orange';
                $statusText = '⚠️ Invalid';
            } elseif ($timeDiff > 0) {
                $minutes = round($timeDiff / 60);
                $timeDiffText = "+{$minutes} minutes";
                $statusColor = 'red';
                $statusText = '❌ Future';
            } elseif ($timeDiff < 0) {
                $minutes = round(abs($timeDiff) / 60);
                $timeDiffText = "-{$minutes} minutes";
                $statusColor = 'green';
                $statusText = '✅ Ready';
            } else {
                $timeDiffText = 'Now';
                $statusColor = 'green';
                $statusText = '✅ Ready';
            }
            
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->notification_type}</td>";
            echo "<td>" . htmlspecialchars(substr($notification->subject, 0, 30)) . "...</td>";
            echo "<td>{$createdTime}</td>";
            echo "<td>{$scheduledTime}</td>";
            echo "<td>{$timeDiffText}</td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    echo "<h2>🔧 Investigating Root Cause</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 Where is scheduled_for being set?</h3>";
    
    // Check the UnifiedMessageModel to see how it sets scheduled_for
    echo "<h4>📝 Code Analysis:</h4>";
    
    echo "<p><strong>Let's check how notifications are being queued...</strong></p>";
    
    // Look at the most recent notification
    if (!empty($notifications)) {
        $recentNotification = $notifications[0];
        echo "<p><strong>Most recent notification (ID {$recentNotification->id}):</strong></p>";
        echo "<ul>";
        echo "<li><strong>Created:</strong> {$recentNotification->created_at}</li>";
        echo "<li><strong>Scheduled:</strong> {$recentNotification->scheduled_for}</li>";
        echo "<li><strong>Subject:</strong> " . htmlspecialchars($recentNotification->subject) . "</li>";
        echo "</ul>";
        
        // Check if this is a contact form message
        if (strpos($recentNotification->subject, 'Contact Form:') !== false) {
            echo "<p style='color: blue;'>ℹ️ This appears to be a contact form notification</p>";
        }
    }
    
    echo "<h4>🔍 Possible Causes:</h4>";
    echo "<ol>";
    echo "<li><strong>UnifiedMessageModel::addToNotificationQueue()</strong> - Check if it's setting scheduled_for incorrectly</li>";
    echo "<li><strong>Database default value</strong> - Check notification_queue table schema</li>";
    echo "<li><strong>Timezone issues</strong> - Server timezone vs database timezone mismatch</li>";
    echo "<li><strong>NOW() function</strong> - Database NOW() vs PHP time() difference</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🧪 Database Schema Check</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 notification_queue Table Structure:</h3>";
    
    $db->query("DESCRIBE notification_queue");
    $db->execute();
    $columns = $db->resultSet();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        $defaultValue = $column->Default ?? 'NULL';
        if ($column->Field === 'scheduled_for') {
            echo "<tr style='background: #ffffcc;'>";
        } else {
            echo "<tr>";
        }
        echo "<td><strong>{$column->Field}</strong></td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$defaultValue}</td>";
        echo "<td>{$column->Extra}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check what the default value for scheduled_for is
    foreach ($columns as $column) {
        if ($column->Field === 'scheduled_for') {
            echo "<p><strong>scheduled_for default:</strong> {$column->Default}</p>";
            if ($column->Default === 'CURRENT_TIMESTAMP') {
                echo "<p style='color: orange;'>⚠️ Default is CURRENT_TIMESTAMP - this might be the issue!</p>";
            }
        }
    }
    echo "</div>";
    
    echo "<h2>🕐 Time Comparison</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⏰ Server vs Database Time:</h3>";
    
    // Get database time
    $db->query("SELECT NOW() as db_time, UNIX_TIMESTAMP() as db_timestamp");
    $db->execute();
    $dbTime = $db->single();
    
    $phpTime = date('Y-m-d H:i:s');
    $phpTimestamp = time();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Source</th><th>Time</th><th>Timestamp</th></tr>";
    echo "<tr><td><strong>PHP time()</strong></td><td>{$phpTime}</td><td>{$phpTimestamp}</td></tr>";
    echo "<tr><td><strong>Database NOW()</strong></td><td>{$dbTime->db_time}</td><td>{$dbTime->db_timestamp}</td></tr>";
    echo "</table>";
    
    $timeDifference = $dbTime->db_timestamp - $phpTimestamp;
    echo "<p><strong>Time difference:</strong> {$timeDifference} seconds</p>";
    
    if (abs($timeDifference) > 60) {
        echo "<p style='color: red;'>❌ <strong>ISSUE FOUND:</strong> Significant time difference between PHP and database!</p>";
    } else {
        echo "<p style='color: green;'>✅ PHP and database times are synchronized</p>";
    }
    echo "</div>";
    
    echo "<h2>🔧 Quick Fix</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚡ Fix the Future Scheduled Time:</h3>";
    
    if (!empty($notifications)) {
        $futureNotifications = array_filter($notifications, function($n) {
            return strtotime($n->scheduled_for) > time();
        });
        
        if (!empty($futureNotifications)) {
            echo "<p><strong>Found " . count($futureNotifications) . " notification(s) scheduled in the future</strong></p>";
            
            echo "<form method='post'>";
            echo "<button type='submit' name='fix_scheduled_times' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Fix Scheduled Times (Set to NOW)</button>";
            echo "</form>";
            
            if (isset($_POST['fix_scheduled_times'])) {
                echo "<h4>🔄 Fixing Results:</h4>";
                
                $fixed = 0;
                foreach ($futureNotifications as $notification) {
                    $db->query("UPDATE notification_queue SET scheduled_for = NOW() WHERE id = ?");
                    $db->bind(1, $notification->id);
                    if ($db->execute()) {
                        $fixed++;
                        echo "<p>✅ Fixed notification ID {$notification->id}</p>";
                    } else {
                        echo "<p>❌ Failed to fix notification ID {$notification->id}</p>";
                    }
                }
                
                echo "<p><strong>Fixed {$fixed} notification(s)</strong></p>";
                echo "<p><a href='debug_scheduled_for_issue.php'>Refresh page to see results</a></p>";
            }
        } else {
            echo "<p style='color: green;'>✅ No notifications scheduled in the future</p>";
        }
    }
    echo "</div>";
    
    echo "<h2>🎯 Recommended Actions</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 To Prevent Future Issues:</h3>";
    echo "<ol>";
    echo "<li><strong>Check UnifiedMessageModel::addToNotificationQueue()</strong> - Ensure it sets scheduled_for to NOW() or current time</li>";
    echo "<li><strong>Fix database schema</strong> - If scheduled_for has CURRENT_TIMESTAMP default, consider changing it</li>";
    echo "<li><strong>Synchronize server time</strong> - Ensure PHP and database use same timezone</li>";
    echo "<li><strong>Update notification creation</strong> - Always explicitly set scheduled_for to NOW() when creating notifications</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Debug Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Scheduled for debug completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
