<?php
/**
 * Quick push notification debug
 */

// Prevent direct access via web browser
if (isset($_SERVER['HTTP_HOST'])) {
    // Allow only if accessed with specific parameter
    if (!isset($_GET['debug_key']) || $_GET['debug_key'] !== 'push_debug_2025') {
        http_response_code(404);
        exit('Not Found');
    }
}

// Initialize the application
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';

// Initialize core classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Controller.php';

// Load models and helpers
require_once APPROOT . '/models/NotificationModel.php';
require_once APPROOT . '/models/NotificationService.php';
require_once APPROOT . '/helpers/notification_helper.php';

// Initialize notification helper
initializeNotificationHelper();

$userId = 3;

echo "=== PUSH NOTIFICATION DEBUG ===\n\n";

try {
    require_once APPROOT . '/models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    $db = new Database();
    
    // 1. Check user preferences
    echo "1. USER PREFERENCES:\n";
    $userPrefs = $notificationModel->getUserPreferences($userId);
    if ($userPrefs) {
        echo "   Push enabled: " . ($userPrefs->push_notifications ? "YES" : "NO") . "\n";
        echo "   Toast enabled: " . ($userPrefs->toast_notifications ? "YES" : "NO") . "\n";
    } else {
        echo "   ERROR: No preferences found\n";
    }
    
    // 2. Check push subscriptions
    echo "\n2. PUSH SUBSCRIPTIONS:\n";
    $subscriptions = $notificationModel->getUserPushSubscriptions($userId);
    echo "   Count: " . count($subscriptions) . "\n";
    if (!empty($subscriptions)) {
        foreach ($subscriptions as $sub) {
            echo "   - ID: {$sub->id}, Endpoint: " . substr($sub->endpoint, 0, 50) . "...\n";
        }
    }
    
    // 3. Check global settings
    echo "\n3. GLOBAL SETTINGS:\n";
    $globalSettings = $notificationModel->getNotificationSettings();
    echo "   Push enabled: " . ($globalSettings['push_enabled'] ? "YES" : "NO") . "\n";
    echo "   Toast enabled: " . ($globalSettings['toast_enabled'] ? "YES" : "NO") . "\n";
    
    // 4. Test immediate push
    echo "\n4. TESTING IMMEDIATE PUSH:\n";
    $result = sendPushNotification($userId, "Debug Push Test", "Testing push at " . date('H:i:s'));
    echo "   Result: " . ($result ? "SUCCESS" : "FAILED") . "\n";
    
    // 5. Check if notification was stored
    echo "\n5. CHECKING STORAGE:\n";
    $db->query("SELECT COUNT(*) as count FROM user_push_notifications WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
    $db->bind(1, $userId);
    $db->execute();
    $recentPush = $db->single()->count;
    echo "   Recent push notifications: $recentPush\n";
    
    // 6. Check pending queue
    echo "\n6. NOTIFICATION QUEUE:\n";
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE status = 'pending'");
    $db->execute();
    $pending = $db->single()->count;
    echo "   Pending notifications: $pending\n";
    
    // 7. VAPID keys
    echo "\n7. VAPID CONFIGURATION:\n";
    echo "   Public key: " . (defined('VAPID_PUBLIC_KEY') ? "CONFIGURED" : "MISSING") . "\n";
    echo "   Private key: " . (defined('VAPID_PRIVATE_KEY') ? "CONFIGURED" : "MISSING") . "\n";
    
    // 8. Test direct service
    echo "\n8. TESTING NOTIFICATION SERVICE:\n";
    require_once APPROOT . '/models/NotificationService.php';
    $service = new NotificationService();
    $directResult = $service->sendTestPushNotification($userId, "Direct Service Test", "Direct test at " . date('H:i:s'));
    echo "   Direct service result: " . ($directResult ? "SUCCESS" : "FAILED") . "\n";
    
    echo "\n=== DIAGNOSIS ===\n";
    
    if (!$userPrefs || !$userPrefs->push_notifications) {
        echo "❌ ISSUE: User has not enabled push notifications in preferences\n";
        echo "   SOLUTION: Go to notification preferences and enable push notifications\n";
    }
    
    if (empty($subscriptions)) {
        echo "❌ ISSUE: No push subscriptions found\n";
        echo "   SOLUTION: Browser needs to subscribe to push notifications\n";
        echo "   - Visit main site\n";
        echo "   - Allow notifications when prompted\n";
        echo "   - Check browser notification settings\n";
    }
    
    if (!$globalSettings['push_enabled']) {
        echo "❌ ISSUE: Push notifications disabled globally\n";
        echo "   SOLUTION: Admin needs to enable push notifications\n";
    }
    
    if (!defined('VAPID_PUBLIC_KEY') || !defined('VAPID_PRIVATE_KEY')) {
        echo "❌ ISSUE: VAPID keys not configured\n";
        echo "   SOLUTION: Configure VAPID keys in config.php\n";
    }
    
    if ($pending > 0) {
        echo "⚠️  WARNING: $pending notifications pending in queue\n";
        echo "   SOLUTION: Run cron job to process queue\n";
    }
    
    if ($result && !empty($subscriptions) && $userPrefs && $userPrefs->push_notifications) {
        echo "✅ SYSTEM LOOKS GOOD: Check browser notification settings\n";
        echo "   - Check if notifications are enabled for this site\n";
        echo "   - Check if Do Not Disturb is enabled\n";
        echo "   - Try different browser\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== END DEBUG ===\n";
?>
