<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Judging for <?php echo $show->name; ?></h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i> Back to Shows
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/exportResults/<?php echo $show->id; ?>" class="btn btn-success">
                <i class="fas fa-file-excel me-2"></i> Export Results
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="list-group">
                <a href="<?php echo BASE_URL; ?>/admin/editShow/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                    <i class="fas fa-cog me-2"></i> Show Details
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/editShow/<?php echo $show->id; ?>#categories" class="list-group-item list-group-item-action">
                    <i class="fas fa-tags me-2"></i> Categories
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/judgingMetrics/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                    <i class="fas fa-balance-scale me-2"></i> Judging Metrics
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/ageWeights/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                    <i class="fas fa-calendar-alt me-2"></i> Age Weights
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/registrations/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                    <i class="fas fa-clipboard-list me-2"></i> Registrations
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/judging/<?php echo $show->id; ?>" class="list-group-item list-group-item-action active">
                    <i class="fas fa-trophy me-2"></i> Judging
                </a>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">Show Details</h5>
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> <?php echo $show->name; ?></p>
                    <p><strong>Date:</strong> <?php echo isset($show->start_date) && $show->start_date ? formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y') : 'Not set'; ?><?php if(isset($show->end_date) && $show->end_date && $show->end_date != $show->start_date): ?> - <?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?><?php endif; ?></p>
                    <p><strong>Location:</strong> <?php echo $show->location; ?></p>
                    <p><strong>Status:</strong> 
                        <?php if ($show->status === 'upcoming') : ?>
                            <span class="badge bg-primary">Upcoming</span>
                        <?php elseif ($show->status === 'active') : ?>
                            <span class="badge bg-success">Active</span>
                        <?php elseif ($show->status === 'completed') : ?>
                            <span class="badge bg-secondary">Completed</span>
                        <?php else : ?>
                            <span class="badge bg-warning"><?php echo ucfirst($show->status); ?></span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Judging Stats</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Total Vehicles:</span>
                        <span class="badge bg-primary rounded-pill"><?php echo $total_vehicles; ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Judged Vehicles:</span>
                        <span class="badge bg-success rounded-pill"><?php echo $judged_vehicles; ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Pending Vehicles:</span>
                        <span class="badge bg-warning rounded-pill"><?php echo $total_vehicles - $judged_vehicles; ?></span>
                    </div>
                    <div class="progress mt-3">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo ($judged_vehicles / max(1, $total_vehicles)) * 100; ?>%" 
                             aria-valuenow="<?php echo $judged_vehicles; ?>" aria-valuemin="0" aria-valuemax="<?php echo $total_vehicles; ?>">
                            <?php echo round(($judged_vehicles / max(1, $total_vehicles)) * 100); ?>%
                        </div>
                    </div>
                    <div class="text-center mt-2 small text-muted">
                        Judging Progress
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Judges</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($judges)) : ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No judges assigned to this show.
                        </div>
                        <a href="<?php echo BASE_URL; ?>/judge_management/index/<?php echo $show->id; ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-users-cog me-1"></i> Judge Management
                        </a>
                    <?php else : ?>
                        <ul class="list-group">
                            <?php foreach ($judges as $judge) : ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <?php echo $judge->name; ?>
                                    <span class="badge bg-primary rounded-pill"><?php echo $judge->assigned_categories; ?> categories</span>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                        <div class="d-grid gap-2 mt-3">
                            <a href="<?php echo BASE_URL; ?>/judge_management/index/<?php echo $show->id; ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-users-cog me-1"></i> Judge Management
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <ul class="nav nav-tabs" id="judgingTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab" aria-controls="categories" aria-selected="true">
                        Categories
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results" type="button" role="tab" aria-controls="results" aria-selected="false">
                        Results
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="awards-tab" data-bs-toggle="tab" data-bs-target="#awards" type="button" role="tab" aria-controls="awards" aria-selected="false">
                        Awards
                    </button>
                </li>
            </ul>
            <div class="tab-content" id="judgingTabsContent">
                <div class="tab-pane fade show active" id="categories" role="tabpanel" aria-labelledby="categories-tab">
                    <div class="card border-top-0 rounded-top-0">
                        <div class="card-body">
                            <?php if (empty($categories)) : ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No categories found for this show.
                                </div>
                                <a href="<?php echo BASE_URL; ?>/admin/editShow/<?php echo $show->id; ?>#categories" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> Add Categories
                                </a>
                            <?php else : ?>
                                <div class="row">
                                    <?php foreach ($categories as $category) : ?>
                                        <div class="col-md-6 mb-4">
                                            <div class="card h-100">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0"><?php echo $category->name; ?></h5>
                                                    <span class="badge bg-primary"><?php echo $category->vehicle_count; ?> vehicles</span>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <strong>Judging Progress:</strong>
                                                        <div class="progress mt-2">
                                                            <div class="progress-bar bg-success" role="progressbar" 
                                                                 style="width: <?php echo ($category->judged_count / max(1, $category->vehicle_count)) * 100; ?>%" 
                                                                 aria-valuenow="<?php echo $category->judged_count; ?>" 
                                                                 aria-valuemin="0" 
                                                                 aria-valuemax="<?php echo $category->vehicle_count; ?>">
                                                                <?php echo $category->judged_count; ?> / <?php echo $category->vehicle_count; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <?php if (!empty($category->assigned_judges)) : ?>
                                                        <div class="mb-3">
                                                            <strong>Assigned Judges:</strong>
                                                            <ul class="list-unstyled">
                                                                <?php foreach ($category->assigned_judges as $judge) : ?>
                                                                    <li><i class="fas fa-user me-2"></i> <?php echo $judge->name; ?></li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    <?php else : ?>
                                                        <div class="alert alert-warning">
                                                            <i class="fas fa-exclamation-triangle me-2"></i> No judges assigned to this category.
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="card-footer">
                                                    <a href="<?php echo BASE_URL; ?>/admin/categoryJudging/<?php echo $category->id; ?>" class="btn btn-primary">
                                                        <i class="fas fa-clipboard-check me-1"></i> Judge Vehicles
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="results" role="tabpanel" aria-labelledby="results-tab">
                    <div class="card border-top-0 rounded-top-0">
                        <div class="card-body">
                            <?php if (empty($categories) || $judged_vehicles == 0) : ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No judging results available yet.
                                </div>
                            <?php else : ?>
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5>Results by Category</h5>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="showAllVehicles" checked>
                                            <label class="form-check-label" for="showAllVehicles">Show all vehicles</label>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion" id="resultsAccordion">
                                        <?php foreach ($categories as $category) : ?>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="heading<?php echo $category->id; ?>">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $category->id; ?>" aria-expanded="false" aria-controls="collapse<?php echo $category->id; ?>">
                                                        <?php echo $category->name; ?> 
                                                        <span class="badge bg-primary ms-2"><?php echo $category->vehicle_count; ?> vehicles</span>
                                                        <span class="badge bg-success ms-2"><?php echo $category->judged_count; ?> judged</span>
                                                    </button>
                                                </h2>
                                                <div id="collapse<?php echo $category->id; ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo $category->id; ?>" data-bs-parent="#resultsAccordion">
                                                    <div class="accordion-body">
                                                        <?php if (empty($category->results)) : ?>
                                                            <div class="alert alert-info">
                                                                <i class="fas fa-info-circle me-2"></i> No judging results available for this category.
                                                            </div>
                                                        <?php else : ?>
                                                            <div class="table-responsive">
                                                                <table class="table table-striped table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>Rank</th>
                                                                            <th>Vehicle</th>
                                                                            <th>Owner</th>
                                                                            <th>Raw Score</th>
                                                                            <th>Age Multiplier</th>
                                                                            <th>Final Score</th>
                                                                            <th>Actions</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <?php foreach ($category->results as $index => $result) : ?>
                                                                            <tr class="<?php echo $result->is_judged ? '' : 'table-warning not-judged'; ?>">
                                                                                <td>
                                                                                    <?php if ($result->is_judged) : ?>
                                                                                        <?php echo $index + 1; ?>
                                                                                        <?php if ($index < 3) : ?>
                                                                                            <?php if ($index == 0) : ?>
                                                                                                <i class="fas fa-trophy text-warning"></i>
                                                                                            <?php elseif ($index == 1) : ?>
                                                                                                <i class="fas fa-trophy text-secondary"></i>
                                                                                            <?php elseif ($index == 2) : ?>
                                                                                                <i class="fas fa-trophy" style="color: #cd7f32;"></i>
                                                                                            <?php endif; ?>
                                                                                        <?php endif; ?>
                                                                                    <?php else : ?>
                                                                                        <span class="badge bg-warning">Not Judged</span>
                                                                                    <?php endif; ?>
                                                                                </td>
                                                                                <td>
                                                                                    <div class="d-flex align-items-center">
                                                                                        <?php if (!empty($result->vehicle_image)) : ?>
                                                                                            <?php 
                                                                                            // Check if thumbnail exists
                                                                                            $imagePath = 'uploads/vehicles/' . $result->vehicle_image;
                                                                                            $thumbnailPath = 'uploads/vehicles/thumbnails/' . $result->vehicle_image;
                                                                                            $fullThumbnailPath = APPROOT . '/' . $thumbnailPath;
                                                                                            $useThumbnail = file_exists($fullThumbnailPath);
                                                                                            ?>
                                                                                            <img src="<?php echo BASE_URL; ?>/<?php echo $useThumbnail ? $thumbnailPath : $imagePath; ?>" 
                                                                                                 class="me-2" alt="Vehicle" style="width: 50px; height: 40px; object-fit: cover;">
                                                                                        <?php else : ?>
                                                                                            <div class="me-2 bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 40px;">
                                                                                                <i class="fas fa-car"></i>
                                                                                            </div>
                                                                                        <?php endif; ?>
                                                                                        <div>
                                                                                            <?php echo $result->year; ?> <?php echo $result->make; ?> <?php echo $result->model; ?>
                                                                                        </div>
                                                                                    </div>
                                                                                </td>
                                                                                <td><?php echo $result->owner_name; ?></td>
                                                                                <td><?php echo $result->is_judged ? $result->raw_score : '-'; ?></td>
                                                                                <td><?php echo $result->is_judged ? $result->age_multiplier . 'x' : '-'; ?></td>
                                                                                <td class="fw-bold"><?php echo $result->is_judged ? $result->final_score : '-'; ?></td>
                                                                                <td>
                                                                                    <a href="<?php echo BASE_URL; ?>/admin/viewScores/<?php echo $result->registration_id; ?>" class="btn btn-sm btn-primary">
                                                                                        <i class="fas fa-eye"></i>
                                                                                    </a>
                                                                                    <?php if ($result->is_judged) : ?>
                                                                                        <a href="<?php echo BASE_URL; ?>/admin/editScores/<?php echo $result->registration_id; ?>" class="btn btn-sm btn-warning">
                                                                                            <i class="fas fa-edit"></i>
                                                                                        </a>
                                                                                    <?php else : ?>
                                                                                        <a href="<?php echo BASE_URL; ?>/admin/judgeVehicle/<?php echo $result->registration_id; ?>" class="btn btn-sm btn-success">
                                                                                            <i class="fas fa-clipboard-check"></i>
                                                                                        </a>
                                                                                    <?php endif; ?>
                                                                                </td>
                                                                            </tr>
                                                                        <?php endforeach; ?>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <h5>Top Vehicles Overall</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Rank</th>
                                                    <th>Vehicle</th>
                                                    <th>Category</th>
                                                    <th>Owner</th>
                                                    <th>Final Score</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($top_vehicles as $index => $vehicle) : ?>
                                                    <tr>
                                                        <td>
                                                            <?php echo $index + 1; ?>
                                                            <?php if ($index < 3) : ?>
                                                                <?php if ($index == 0) : ?>
                                                                    <i class="fas fa-trophy text-warning"></i>
                                                                <?php elseif ($index == 1) : ?>
                                                                    <i class="fas fa-trophy text-secondary"></i>
                                                                <?php elseif ($index == 2) : ?>
                                                                    <i class="fas fa-trophy" style="color: #cd7f32;"></i>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <?php if (!empty($vehicle->vehicle_image)) : ?>
                                                                    <?php 
                                                                    // Check if thumbnail exists
                                                                    $imagePath = 'uploads/vehicles/' . $vehicle->vehicle_image;
                                                                    $thumbnailPath = 'uploads/vehicles/thumbnails/' . $vehicle->vehicle_image;
                                                                    $fullThumbnailPath = APPROOT . '/' . $thumbnailPath;
                                                                    $useThumbnail = file_exists($fullThumbnailPath);
                                                                    ?>
                                                                    <img src="<?php echo BASE_URL; ?>/<?php echo $useThumbnail ? $thumbnailPath : $imagePath; ?>" 
                                                                         class="me-2" alt="Vehicle" style="width: 50px; height: 40px; object-fit: cover;">
                                                                <?php else : ?>
                                                                    <div class="me-2 bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 40px;">
                                                                        <i class="fas fa-car"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <div>
                                                                    <?php echo $vehicle->year; ?> <?php echo $vehicle->make; ?> <?php echo $vehicle->model; ?>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td><?php echo $vehicle->category_name; ?></td>
                                                        <td><?php echo $vehicle->owner_name; ?></td>
                                                        <td class="fw-bold"><?php echo $vehicle->final_score; ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="awards" role="tabpanel" aria-labelledby="awards-tab">
                    <div class="card border-top-0 rounded-top-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5>Award Management</h5>
                                <a href="<?php echo BASE_URL; ?>/admin/addAward/<?php echo $show->id; ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> Add Award
                                </a>
                            </div>
                            
                            <?php if (empty($awards)) : ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No awards have been created for this show.
                                </div>
                            <?php else : ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Award Name</th>
                                                <th>Type</th>
                                                <th>Category</th>
                                                <th>Recipient</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($awards as $award) : ?>
                                                <tr>
                                                    <td><?php echo $award->name; ?></td>
                                                    <td>
                                                        <?php if ($award->type === 'category') : ?>
                                                            <span class="badge bg-primary">Category Award</span>
                                                        <?php elseif ($award->type === 'special') : ?>
                                                            <span class="badge bg-info">Special Award</span>
                                                        <?php elseif ($award->type === 'peoples_choice') : ?>
                                                            <span class="badge bg-success">People's Choice</span>
                                                        <?php else : ?>
                                                            <span class="badge bg-secondary"><?php echo ucfirst($award->type); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($award->type === 'category') : ?>
                                                            <?php echo $award->category_name; ?>
                                                        <?php else : ?>
                                                            <span class="text-muted">N/A</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($award->recipient_id)) : ?>
                                                            <?php echo $award->recipient_year; ?> <?php echo $award->recipient_make; ?> <?php echo $award->recipient_model; ?>
                                                            <div class="small text-muted"><?php echo $award->recipient_owner; ?></div>
                                                        <?php else : ?>
                                                            <a href="<?php echo BASE_URL; ?>/admin/assignAward/<?php echo $award->id; ?>" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-user-plus me-1"></i> Assign Recipient
                                                            </a>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="<?php echo BASE_URL; ?>/admin/editAward/<?php echo $award->id; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAwardModal<?php echo $award->id; ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        
                                                        <!-- Delete Award Modal -->
                                                        <div class="modal fade" id="deleteAwardModal<?php echo $award->id; ?>" tabindex="-1" aria-labelledby="deleteAwardModalLabel<?php echo $award->id; ?>" aria-hidden="true">
                                                            <div class="modal-dialog">
                                                                <div class="modal-content">
                                                                    <div class="modal-header bg-danger text-white">
                                                                        <h5 class="modal-title" id="deleteAwardModalLabel<?php echo $award->id; ?>">Confirm Delete</h5>
                                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <div class="modal-body">
                                                                        <p>Are you sure you want to delete the award <strong><?php echo $award->name; ?></strong>?</p>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                        <form action="<?php echo BASE_URL; ?>/admin/deleteAward/<?php echo $award->id; ?>" method="post">
                                                                            <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                                                                            <button type="submit" class="btn btn-danger">Delete Award</button>
                                                                        </form>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="mt-4">
                                    <h5>Print Options</h5>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h5 class="card-title">Award Certificates</h5>
                                                    <p class="card-text">Print certificates for all award recipients.</p>
                                                    <a href="<?php echo BASE_URL; ?>/admin/printAwardCertificates/<?php echo $show->id; ?>" class="btn btn-primary" target="_blank">
                                                        <i class="fas fa-print me-1"></i> Print Certificates
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h5 class="card-title">Award List</h5>
                                                    <p class="card-text">Print a list of all awards and recipients.</p>
                                                    <a href="<?php echo BASE_URL; ?>/admin/printAwardList/<?php echo $show->id; ?>" class="btn btn-primary" target="_blank">
                                                        <i class="fas fa-print me-1"></i> Print Award List
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h5 class="card-title">Results Announcement</h5>
                                                    <p class="card-text">Print a script for announcing results.</p>
                                                    <a href="<?php echo BASE_URL; ?>/admin/printAnnouncementScript/<?php echo $show->id; ?>" class="btn btn-primary" target="_blank">
                                                        <i class="fas fa-print me-1"></i> Print Script
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle not judged vehicles
        const showAllVehiclesCheckbox = document.getElementById('showAllVehicles');
        if (showAllVehiclesCheckbox) {
            showAllVehiclesCheckbox.addEventListener('change', function() {
                const notJudgedRows = document.querySelectorAll('.not-judged');
                notJudgedRows.forEach(row => {
                    row.style.display = this.checked ? '' : 'none';
                });
            });
        }
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>