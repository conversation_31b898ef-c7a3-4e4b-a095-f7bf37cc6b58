<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-server"></i> Email Server Settings
                </h2>
                <a href="<?= URLROOT ?>/admin/settings" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Settings
                </a>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-<?= $_SESSION['flash_message']['type'] === 'error' ? 'danger' : $_SESSION['flash_message']['type'] ?> alert-dismissible fade show">
        <?= htmlspecialchars($_SESSION['flash_message']['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['flash_message']); endif; ?>

    <div class="row">
        <!-- Email Server Configuration -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog"></i> Server Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= URLROOT ?>/admin/settings_email_server/save" method="POST">
                        <input type="hidden" name="csrf_token" value="<?= $data['csrf_token'] ?>">
                        
                        <!-- Enable Email Processing -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="email_processing_enabled" 
                                           name="email_processing_enabled" value="1" 
                                           <?= $data['settings']['email_processing_enabled'] === '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="email_processing_enabled">
                                        <strong>Enable Email Processing</strong>
                                    </label>
                                </div>
                                <small class="text-muted">Enable automatic email retrieval and processing</small>
                            </div>
                        </div>

                        <hr>

                        <!-- Server Type -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email_server_protocol" class="form-label">Protocol</label>
                                <select class="form-select" id="email_server_protocol" name="email_server_protocol" required>
                                    <option value="imap" <?= $data['settings']['email_server_protocol'] === 'imap' ? 'selected' : '' ?>>IMAP</option>
                                    <option value="pop3" <?= $data['settings']['email_server_protocol'] === 'pop3' ? 'selected' : '' ?>>POP3</option>
                                </select>
                                <small class="text-muted">IMAP is recommended for better message management</small>
                            </div>
                            <div class="col-md-6">
                                <label for="email_server_encryption" class="form-label">Encryption</label>
                                <select class="form-select" id="email_server_encryption" name="email_server_encryption" required>
                                    <option value="ssl" <?= $data['settings']['email_server_encryption'] === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                    <option value="tls" <?= $data['settings']['email_server_encryption'] === 'tls' ? 'selected' : '' ?>>TLS</option>
                                    <option value="none" <?= $data['settings']['email_server_encryption'] === 'none' ? 'selected' : '' ?>>None (Not Recommended)</option>
                                </select>
                            </div>
                        </div>

                        <!-- Server Details -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="email_server_host" class="form-label">Server Host</label>
                                <input type="text" class="form-control" id="email_server_host" 
                                       name="email_server_host" value="<?= htmlspecialchars($data['settings']['email_server_host']) ?>" 
                                       placeholder="mail.example.com" required>
                            </div>
                            <div class="col-md-4">
                                <label for="email_server_port" class="form-label">Port</label>
                                <input type="number" class="form-control" id="email_server_port" 
                                       name="email_server_port" value="<?= htmlspecialchars($data['settings']['email_server_port']) ?>" 
                                       placeholder="993" required>
                                <small class="text-muted">993 (IMAP SSL), 995 (POP3 SSL)</small>
                            </div>
                        </div>

                        <!-- Authentication -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email_server_username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="email_server_username" 
                                       name="email_server_username" value="<?= htmlspecialchars($data['settings']['email_server_username']) ?>" 
                                       placeholder="<EMAIL>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email_server_password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="email_server_password" 
                                       name="email_server_password" value="<?= htmlspecialchars($data['settings']['email_server_password']) ?>" 
                                       placeholder="Enter password" required>
                            </div>
                        </div>

                        <hr>

                        <!-- Processing Options -->
                        <h6 class="mb-3">Processing Options</h6>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="email_delete_after_processing" 
                                           name="email_delete_after_processing" value="1" 
                                           <?= $data['settings']['email_delete_after_processing'] === '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="email_delete_after_processing">
                                        Delete emails after processing
                                    </label>
                                </div>
                                <small class="text-muted">Remove emails from server after successful processing</small>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="email_spam_filtering" 
                                           name="email_spam_filtering" value="1" 
                                           <?= $data['settings']['email_spam_filtering'] === '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="email_spam_filtering">
                                        Enable spam filtering
                                    </label>
                                </div>
                                <small class="text-muted">Basic spam detection and filtering</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email_max_size_mb" class="form-label">Max Email Size (MB)</label>
                                <input type="number" class="form-control" id="email_max_size_mb" 
                                       name="email_max_size_mb" value="<?= htmlspecialchars($data['settings']['email_max_size_mb']) ?>" 
                                       min="1" max="100" required>
                                <small class="text-muted">Maximum email size to process</small>
                            </div>
                            <div class="col-md-6">
                                <label for="email_fetch_limit" class="form-label">Fetch Limit</label>
                                <input type="number" class="form-control" id="email_fetch_limit" 
                                       name="email_fetch_limit" value="<?= htmlspecialchars($data['settings']['email_fetch_limit'] ?? '50') ?>" 
                                       min="1" max="200" required>
                                <small class="text-muted">Maximum emails to fetch per run</small>
                            </div>
                        </div>

                        <hr>

                        <!-- Attachment Settings -->
                        <h6 class="mb-3">Attachment Settings</h6>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="email_attachment_enabled" 
                                           name="email_attachment_enabled" value="1" 
                                           <?= $data['settings']['email_attachment_enabled'] === '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="email_attachment_enabled">
                                        Allow email attachments
                                    </label>
                                </div>
                                <small class="text-muted">Process and store email attachments</small>
                            </div>
                            <div class="col-md-6">
                                <label for="email_attachment_max_size_mb" class="form-label">Max Attachment Size (MB)</label>
                                <input type="number" class="form-control" id="email_attachment_max_size_mb" 
                                       name="email_attachment_max_size_mb" value="<?= htmlspecialchars($data['settings']['email_attachment_max_size_mb']) ?>" 
                                       min="1" max="50" required>
                            </div>
                        </div>

                        <hr>

                        <!-- Auto-Reply Settings -->
                        <h6 class="mb-3">Auto-Reply Settings</h6>
                        
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="email_auto_reply_enabled" 
                                           name="email_auto_reply_enabled" value="1" 
                                           <?= $data['settings']['email_auto_reply_enabled'] === '1' ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="email_auto_reply_enabled">
                                        Send auto-reply confirmation emails
                                    </label>
                                </div>
                                <small class="text-muted">Automatically send confirmation emails with ticket numbers</small>
                            </div>
                        </div>

                        <hr>

                        <!-- Ticket Number Settings -->
                        <h6 class="mb-3">Ticket Number Settings</h6>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="ticket_number_prefix" class="form-label">Ticket Prefix</label>
                                <input type="text" class="form-control" id="ticket_number_prefix" 
                                       name="ticket_number_prefix" value="<?= htmlspecialchars($data['settings']['ticket_number_prefix']) ?>" 
                                       pattern="[A-Z]{2,5}" maxlength="5" required>
                                <small class="text-muted">2-5 uppercase letters (e.g., RER)</small>
                            </div>
                            <div class="col-md-6">
                                <label for="email_log_retention_days" class="form-label">Log Retention (Days)</label>
                                <input type="number" class="form-control" id="email_log_retention_days" 
                                       name="email_log_retention_days" value="<?= htmlspecialchars($data['settings']['email_log_retention_days'] ?? '30') ?>" 
                                       min="7" max="365" required>
                                <small class="text-muted">How long to keep email processing logs</small>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="testConnection()">
                                <i class="fas fa-plug"></i> Test Connection
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Status and Information -->
        <div class="col-lg-4">
            <!-- Connection Status -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-signal"></i> Connection Status
                    </h6>
                </div>
                <div class="card-body">
                    <div id="connectionStatus">
                        <div class="text-center">
                            <i class="fas fa-question-circle fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Click "Test Connection" to check server status</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Statistics -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar"></i> Processing Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success"><?= $data['stats']['processed_today'] ?? 0 ?></h4>
                            <small class="text-muted">Processed Today</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger"><?= $data['stats']['failed_today'] ?? 0 ?></h4>
                            <small class="text-muted">Failed Today</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-primary"><?= $data['stats']['total_processed'] ?? 0 ?></h5>
                            <small class="text-muted">Total Processed</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning"><?= $data['stats']['pending_reminders'] ?? 0 ?></h5>
                            <small class="text-muted">Pending Reminders</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Last Processing Run -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock"></i> Last Processing Run
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (isset($data['last_run'])): ?>
                    <p><strong>Time:</strong> <?= date('M j, Y g:i A', strtotime($data['last_run']['last_run'])) ?></p>
                    <p><strong>Status:</strong> 
                        <span class="badge bg-<?= $data['last_run']['status'] === 'success' ? 'success' : 'danger' ?>">
                            <?= ucfirst($data['last_run']['status']) ?>
                        </span>
                    </p>
                    <p><strong>Processing Time:</strong> <?= $data['last_run']['processing_time'] ?>s</p>
                    <?php if ($data['last_run']['error']): ?>
                    <p><strong>Error:</strong> <small class="text-danger"><?= htmlspecialchars($data['last_run']['error']) ?></small></p>
                    <?php endif; ?>
                    <?php else: ?>
                    <p class="text-muted">No processing runs recorded yet.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test email server connection
function testConnection() {
    const button = event.target;
    const originalText = button.innerHTML;

    // Disable button and show loading
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';

    // Get form data
    const formData = new FormData();
    formData.append('protocol', document.getElementById('email_server_protocol').value);
    formData.append('host', document.getElementById('email_server_host').value);
    formData.append('port', document.getElementById('email_server_port').value);
    formData.append('username', document.getElementById('email_server_username').value);
    formData.append('password', document.getElementById('email_server_password').value);
    formData.append('encryption', document.getElementById('email_server_encryption').value);

    fetch('<?= URLROOT ?>/admin/testConnection', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        updateConnectionStatus(data);
    })
    .catch(error => {
        updateConnectionStatus({
            success: false,
            message: 'Connection test failed: ' + error.message
        });
    })
    .finally(() => {
        // Re-enable button
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Update connection status display
function updateConnectionStatus(result) {
    const statusDiv = document.getElementById('connectionStatus');

    if (result.success) {
        statusDiv.innerHTML = `
            <div class="text-center">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h6 class="text-success">Connection Successful</h6>
                <p class="text-muted small">${result.message}</p>
            </div>
        `;
    } else {
        statusDiv.innerHTML = `
            <div class="text-center">
                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                <h6 class="text-danger">Connection Failed</h6>
                <p class="text-muted small">${result.message}</p>
            </div>
        `;
    }
}

// Auto-update port based on protocol and encryption
document.getElementById('email_server_protocol').addEventListener('change', updateDefaultPort);
document.getElementById('email_server_encryption').addEventListener('change', updateDefaultPort);

function updateDefaultPort() {
    const protocol = document.getElementById('email_server_protocol').value;
    const encryption = document.getElementById('email_server_encryption').value;
    const portField = document.getElementById('email_server_port');

    let defaultPort = '';

    if (protocol === 'imap') {
        if (encryption === 'ssl') {
            defaultPort = '993';
        } else if (encryption === 'tls') {
            defaultPort = '143';
        } else {
            defaultPort = '143';
        }
    } else if (protocol === 'pop3') {
        if (encryption === 'ssl') {
            defaultPort = '995';
        } else if (encryption === 'tls') {
            defaultPort = '110';
        } else {
            defaultPort = '110';
        }
    }

    // Only update if field is empty or contains a default port
    const currentPort = portField.value;
    if (!currentPort || ['143', '993', '110', '995'].includes(currentPort)) {
        portField.value = defaultPort;
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const prefix = document.getElementById('ticket_number_prefix').value;

    if (!/^[A-Z]{2,5}$/.test(prefix)) {
        e.preventDefault();
        alert('Ticket prefix must be 2-5 uppercase letters only');
        document.getElementById('ticket_number_prefix').focus();
        return false;
    }

    return true;
});

// Initialize default port on page load
document.addEventListener('DOMContentLoaded', function() {
    // Only set default port if field is empty
    if (!document.getElementById('email_server_port').value) {
        updateDefaultPort();
    }
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
