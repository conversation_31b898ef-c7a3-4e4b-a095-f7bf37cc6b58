# FCM (Firebase Cloud Messaging) Setup Guide

This guide will help you complete the migration from Web Push API with VAPID to Firebase Cloud Messaging (FCM) HTTP v1 API.

## Overview

Your system has been updated to use FCM instead of the old Web Push API. This provides better reliability, delivery rates, and integration with Google's infrastructure.

## What's Been Done

✅ **Backend Implementation**
- Created FCM helper class (`/helpers/fcm_v1_helper.php`)
- Updated NotificationModel to use FCM tokens instead of Web Push subscriptions
- Created FCM subscription controller endpoint (`/api/pwa/fcm-subscribe`)
- Added FCM token management methods

✅ **Frontend Implementation**
- Created FCM JavaScript manager (`/public/js/fcm-notifications.js`)
- Created Firebase service worker (`/firebase-messaging-sw.js`)
- Updated PWA features to use FCM
- Added Firebase SDK to header

✅ **Database Schema**
- FCM tokens table will be created automatically
- Migration script available

## What You Need to Do

### 1. Set Up Firebase Project Configuration

You need to get your Firebase web configuration and update two files:

#### Get Firebase Config:
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **rowaneliterides**
3. Go to **Project Settings** (gear icon) > **General** tab
4. Scroll down to "Your apps" section
5. If you don't have a web app, click "Add app" and select web (</>) icon
6. Copy the `firebaseConfig` object

#### Update Files:
Replace the `firebaseConfig` object in these files:

**File 1: `/public/js/fcm-notifications.js`** (around line 35)
```javascript
const firebaseConfig = {
    apiKey: "YOUR_ACTUAL_API_KEY",
    authDomain: "rowaneliterides.firebaseapp.com",
    projectId: "rowaneliterides",
    storageBucket: "rowaneliterides.appspot.com",
    messagingSenderId: "YOUR_ACTUAL_SENDER_ID",
    appId: "YOUR_ACTUAL_APP_ID"
};
```

**File 2: `/firebase-messaging-sw.js`** (around line 12)
```javascript
const firebaseConfig = {
    apiKey: "YOUR_ACTUAL_API_KEY",
    authDomain: "rowaneliterides.firebaseapp.com",
    projectId: "rowaneliterides",
    storageBucket: "rowaneliterides.appspot.com",
    messagingSenderId: "YOUR_ACTUAL_SENDER_ID",
    appId: "YOUR_ACTUAL_APP_ID"
};
```

### 2. Set Up Firebase Service Account

#### Download Service Account Key:
1. In Firebase Console, go to **Project Settings** > **Service Accounts** tab
2. Click **"Generate new private key"**
3. Download the JSON file
4. Rename it to `firebase-service-account.json`
5. Upload it to `/config/firebase-service-account.json` on your server

**Important:** Keep this file secure and never commit it to version control!

### 3. Update VAPID Key (Optional)

If you want to use a custom VAPID key:
1. In Firebase Console, go to **Project Settings** > **Cloud Messaging** tab
2. Under "Web configuration", find your "Web Push certificates"
3. Copy the "Key pair" value
4. Update the `getVAPIDKey()` method in `/public/js/fcm-notifications.js`

### 4. Test the Implementation

#### Run Migration Script:
```
https://events.rowaneliterides.com/migrate_to_fcm.php?migrate_key=fcm_migration_2025
```

#### Run Complete Test:
```
https://events.rowaneliterides.com/test_fcm_complete.php?test_key=fcm_complete_2025
```

#### Test in Browser:
1. Open your site in Chrome/Firefox
2. Open browser console (F12)
3. Look for FCM initialization messages
4. Allow notifications when prompted
5. Check if FCM token is generated

### 5. Verify Everything Works

#### Test Notification Flow:
1. User enables notifications in browser
2. FCM token is stored in database
3. Admin sends notification through admin panel
4. User receives notification

#### Check Database:
```sql
-- Check FCM tokens
SELECT COUNT(*) FROM fcm_tokens WHERE active = 1;

-- Check recent tokens
SELECT user_id, created_at, LEFT(token, 20) as token_preview 
FROM fcm_tokens 
WHERE active = 1 
ORDER BY created_at DESC 
LIMIT 5;
```

## Troubleshooting

### Common Issues:

**1. "Firebase is not defined" Error**
- Check that Firebase SDK scripts are loading before your FCM script
- Verify CDN URLs are accessible

**2. "Invalid registration token" Error**
- This should be fixed now - you were mixing Web Push endpoints with FCM tokens
- If still occurring, check that FCM tokens are being stored correctly

**3. "Service worker registration failed"**
- Ensure `/firebase-messaging-sw.js` is in the root directory
- Check browser console for specific errors

**4. "No FCM tokens found"**
- Users need to enable notifications in their browsers
- Check that the subscription flow is working

### Debug Mode:

Add `?debug=1` to any page URL to enable debug logging in browser console.

## Migration from Old System

### What Happens to Old Data:
- Old `push_subscriptions` table remains untouched
- New `fcm_tokens` table is created
- Users will need to re-enable notifications (FCM tokens are different from Web Push subscriptions)

### Cleanup (After FCM is Working):
```sql
-- Deactivate old subscriptions
UPDATE push_subscriptions SET active = 0;

-- Eventually, you can drop the old table
-- DROP TABLE push_subscriptions;
```

## Security Notes

1. **Service Account Key**: Keep `/config/firebase-service-account.json` secure
2. **VAPID Key**: Can be public (it's meant to be)
3. **FCM Tokens**: Store securely, clean up invalid ones regularly

## Performance Notes

1. **Token Cleanup**: Run cleanup regularly to remove invalid tokens
2. **Batch Sending**: FCM helper supports sending to multiple tokens
3. **Error Handling**: Invalid tokens are automatically marked for removal

## Support

If you encounter issues:
1. Check browser console for errors
2. Run the test scripts
3. Verify Firebase configuration
4. Check server error logs

The new FCM system should provide better reliability and delivery rates compared to the old Web Push API system.