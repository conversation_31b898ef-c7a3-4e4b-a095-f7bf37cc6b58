<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Management Test - Events & Shows</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-vial me-2"></i>Cache Management Test Page</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Test Instructions</h5>
                            <ol>
                                <li>Click "Create Test Data" to populate browser storage</li>
                                <li>Verify test data exists by clicking "Check Test Data"</li>
                                <li>Use the cache clearing function from <code>/admin/settings_developer</code></li>
                                <li>Return here and click "Check Test Data" to verify clearing worked</li>
                            </ol>
                        </div>

                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-success w-100" onclick="createTestData()">
                                    <i class="fas fa-plus me-2"></i>Create Test Data
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-info w-100" onclick="checkTestData()">
                                    <i class="fas fa-search me-2"></i>Check Test Data
                                </button>
                            </div>
                        </div>

                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <a href="/admin/settings_developer" class="btn btn-warning w-100">
                                    <i class="fas fa-tools me-2"></i>Go to Developer Settings
                                </a>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-danger w-100" onclick="clearTestData()">
                                    <i class="fas fa-trash me-2"></i>Clear Test Data Only
                                </button>
                            </div>
                        </div>

                        <div id="testResults" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        /**
         * Cache Management Test Functions
         */

        function createTestData() {
            try {
                // Create localStorage data
                localStorage.setItem('test_cache_management', JSON.stringify({
                    created: new Date().toISOString(),
                    data: 'This is test data for cache management testing'
                }));

                // Create sessionStorage data
                sessionStorage.setItem('test_session_data', 'Session test data');

                // Create cookies
                document.cookie = 'test_cookie=test_value; path=/; max-age=3600';
                document.cookie = 'cache_test=cache_value; path=/; max-age=3600';

                // Create IndexedDB data
                createIndexedDBData();

                showToast('Test data created successfully!', 'success');
                
                // Auto-check data after creation
                setTimeout(checkTestData, 500);

            } catch (error) {
                console.error('Error creating test data:', error);
                showToast('Error creating test data: ' + error.message, 'danger');
            }
        }

        function createIndexedDBData() {
            const request = indexedDB.open('TestCacheDB', 1);
            
            request.onupgradeneeded = function(event) {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('testStore')) {
                    const store = db.createObjectStore('testStore', { keyPath: 'id' });
                    store.add({
                        id: 1,
                        data: 'Test IndexedDB data for cache management',
                        created: new Date().toISOString()
                    });
                }
            };

            request.onsuccess = function(event) {
                const db = event.target.result;
                const transaction = db.transaction(['testStore'], 'readwrite');
                const store = transaction.objectStore('testStore');
                store.put({
                    id: 1,
                    data: 'Test IndexedDB data for cache management',
                    created: new Date().toISOString()
                });
                db.close();
            };
        }

        function checkTestData() {
            const results = [];
            
            // Check localStorage
            const localData = localStorage.getItem('test_cache_management');
            results.push({
                type: 'Local Storage',
                status: localData ? 'EXISTS' : 'NOT FOUND',
                data: localData ? JSON.parse(localData) : null,
                class: localData ? 'success' : 'danger'
            });

            // Check sessionStorage
            const sessionData = sessionStorage.getItem('test_session_data');
            results.push({
                type: 'Session Storage',
                status: sessionData ? 'EXISTS' : 'NOT FOUND',
                data: sessionData,
                class: sessionData ? 'success' : 'danger'
            });

            // Check cookies
            const cookies = document.cookie.split(';').map(c => c.trim());
            const testCookie = cookies.find(c => c.startsWith('test_cookie='));
            const cacheCookie = cookies.find(c => c.startsWith('cache_test='));
            
            results.push({
                type: 'Cookies',
                status: (testCookie || cacheCookie) ? 'EXISTS' : 'NOT FOUND',
                data: { testCookie, cacheCookie },
                class: (testCookie || cacheCookie) ? 'success' : 'danger'
            });

            // Check IndexedDB
            checkIndexedDBData(results);
        }

        function checkIndexedDBData(results) {
            const request = indexedDB.open('TestCacheDB', 1);
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                
                if (db.objectStoreNames.contains('testStore')) {
                    const transaction = db.transaction(['testStore'], 'readonly');
                    const store = transaction.objectStore('testStore');
                    const getRequest = store.get(1);
                    
                    getRequest.onsuccess = function() {
                        results.push({
                            type: 'IndexedDB',
                            status: getRequest.result ? 'EXISTS' : 'NOT FOUND',
                            data: getRequest.result,
                            class: getRequest.result ? 'success' : 'danger'
                        });
                        
                        displayResults(results);
                        db.close();
                    };
                } else {
                    results.push({
                        type: 'IndexedDB',
                        status: 'NOT FOUND',
                        data: null,
                        class: 'danger'
                    });
                    displayResults(results);
                    db.close();
                }
            };

            request.onerror = function() {
                results.push({
                    type: 'IndexedDB',
                    status: 'ERROR',
                    data: 'Could not access IndexedDB',
                    class: 'warning'
                });
                displayResults(results);
            };
        }

        function displayResults(results) {
            const container = document.getElementById('testResults');
            let html = '<h5><i class="fas fa-clipboard-list me-2"></i>Test Data Status</h5>';
            
            results.forEach(result => {
                html += `
                    <div class="alert alert-${result.class} mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <strong>${result.type}:</strong>
                            <span class="badge bg-${result.class === 'success' ? 'success' : 'danger'}">${result.status}</span>
                        </div>
                        ${result.data ? `<small class="text-muted d-block mt-1">Data: ${JSON.stringify(result.data, null, 2)}</small>` : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function clearTestData() {
            try {
                // Clear only test data
                localStorage.removeItem('test_cache_management');
                sessionStorage.removeItem('test_session_data');
                
                // Clear test cookies
                document.cookie = 'test_cookie=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
                document.cookie = 'cache_test=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';

                // Clear test IndexedDB
                const deleteRequest = indexedDB.deleteDatabase('TestCacheDB');
                deleteRequest.onsuccess = function() {
                    showToast('Test data cleared successfully!', 'success');
                    setTimeout(checkTestData, 500);
                };

            } catch (error) {
                console.error('Error clearing test data:', error);
                showToast('Error clearing test data: ' + error.message, 'danger');
            }
        }

        function showToast(message, type) {
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            const container = document.getElementById('toast-container');
            container.insertAdjacentHTML('beforeend', toastHtml);

            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
            toast.show();

            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // Auto-check data on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkTestData, 1000);
        });
    </script>
</body>
</html>