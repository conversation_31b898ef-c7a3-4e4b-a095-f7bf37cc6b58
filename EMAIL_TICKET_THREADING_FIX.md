# Email Ticket Threading Fix

## Problem Description
The email integration system was creating new ticket numbers for every incoming email instead of checking if the email was a reply to an existing ticket. This caused email conversations to be broken into separate threads instead of being properly threaded together.

## Root Cause
The issue was in the regex patterns used to parse ticket numbers from email subjects. The patterns were too restrictive and not properly matching the three ticket number formats:

1. **Global tickets**: `[RER-2025-001-ZIK2WU]`
2. **Admin tickets**: `[RER-A25-01-001-ZIK2WU]`  
3. **Coordinator tickets**: `[RER-C25-01-001-ZIK2WU]`

The system was supposed to:
- Extract the ticket number (everything up to and including the 3-digit sequence)
- Extract the security token (last 6 characters)
- Check the database for existing tickets with matching ticket number and security token
- If found, use the existing ticket; if not found, create a new one

## Files Modified

### 1. `models/EmailProcessingEngine.php`

**Method: `extractOrGenerateTicketNumber()`**
- **Old regex**: `/\[(RER-.+-[A-Z0-9]{6})\]/` (too generic)
- **New regex**: Comprehensive pattern that handles all validation in one regex (see Technical Details below)

**Method: `validateTicketSecurity()`**
- **Old pattern**: `/^(RER-[AC]?\d{2,4}-(?:\d+-)?(?:\d{3}))-([A-Z0-9]{6})$/`
- **New pattern**: `/^(RER-(?:[AC]?\d{2,4}-)?(?:\d+-)?(?:\d{3}))-([A-Z0-9]{6})$/` (handles optional admin/coordinator prefixes)

**Method: `performSecurityCheck()`**
- Updated both regex patterns to match the new format specifications

### 2. `controllers/NotificationCenterController.php`

**Method: `extractTicketNumber()`**
- **Old regex**: `/\[([RER-]+[A-Z0-9-]+)\]/` (too generic)
- **New regex**: Multiple patterns to handle all formats with and without security tokens
- Added proper fallback patterns for unbracketed ticket numbers

## Technical Details

### Ticket Number Formats Supported
```
Format 1 (Global):      RER-2025-001-ZIK2WU
Format 2 (Admin):       RER-A25-01-001-ABC123  
Format 3 (Coordinator): RER-C25-01-001-XYZ789
```

### Comprehensive Regex Pattern
The fix now uses a comprehensive regex pattern that handles all validation in one step:

```regex
/^(?:(?!\[RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6})\]).)*\[(RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6}))\](?:(?!\[RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6})\]).)*$|^(?!.*\[RER-(?:\d{4}-\d+|[AC]\d{2}-\d+-\d+)\])(?!.*\[RER-).*$/
```

This pattern:
- **Matches valid tickets**: `RER-2025-001-ZIK2WU`, `RER-A25-01-001-ABC123`, `RER-C25-01-001-XYZ789`
- **Rejects invalid tickets**: `RER-2025-001`, `RER-A25-01-001`, `RER-C25-01-001` (missing security tokens)
- **Allows new emails**: Emails with no RER ticket patterns (for new ticket generation)
- **Prevents security threats**: Blocks emails with malformed ticket attempts

### Security Validation
The system now properly:
1. Extracts ticket number and security token separately
2. Validates both against the database
3. Rejects emails with invalid or missing security tokens
4. Allows emails with valid ticket/token combinations to be threaded

## Testing
Created `test_ticket_parsing.php` to verify:
- Regex patterns match all three ticket formats correctly
- Security tokens are properly extracted
- Invalid tickets are properly rejected
- New tickets are generated when no existing ticket is found

## Expected Behavior After Fix

### ✅ ALLOW (Thread with existing conversation)
- `Re: Your inquiry [RER-2025-001-ZIK2WU]`
- `Re: Show registration [RER-A25-22-001-ABC123]`
- `Re: Coordinator message [RER-C25-22-001-XYZ789]`

### ❌ REJECT (Security threat - missing token)
- `Re: Your inquiry [RER-2025-001]`
- `Re: Show registration [RER-A25-22-001]`
- `Re: Coordinator message [RER-C25-22-001]`

### 🆕 NEW TICKET (Generate new conversation)
- `New inquiry about car show`
- `Question about registration`

## Benefits
1. **Proper Email Threading**: Replies are now correctly threaded with original messages
2. **Security Maintained**: Invalid tickets are rejected to prevent spoofing
3. **Format Support**: All three ticket formats are properly supported
4. **Database Efficiency**: Reduces duplicate tickets and improves conversation tracking

## Files to Copy to Server
- `models/EmailProcessingEngine.php`
- `controllers/NotificationCenterController.php`
- `test_ticket_parsing.php` (for testing)
