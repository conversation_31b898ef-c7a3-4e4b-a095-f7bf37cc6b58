<?php
/**
 * Test Script for Unified Message System
 * 
 * This script tests the new unified message system to ensure it works correctly.
 */

// Initialize the system
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>Unified Message System Test</h1>\n";

try {
    $messageModel = new UnifiedMessageModel();
    
    echo "<h2>1. Testing Message Model Initialization</h2>\n";
    echo "<p style='color: green;'>✓ UnifiedMessageModel loaded successfully</p>\n";
    
    echo "<h2>2. Testing Database Tables</h2>\n";
    
    $db = new Database();
    
    // Check if tables exist
    $tables = ['messages', 'message_deliveries', 'notification_settings'];
    
    foreach ($tables as $table) {
        $db->query("SHOW TABLES LIKE '$table'");
        $result = $db->single();
        
        if ($result) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>\n";
        }
    }
    
    echo "<h2>3. Testing Message Counts</h2>\n";
    
    // Test with user ID 1 (should exist)
    $counts = $messageModel->getMessageCounts(1);
    
    if ($counts) {
        echo "<p style='color: green;'>✓ Message counts retrieved successfully</p>\n";
        echo "<ul>\n";
        echo "<li>Total messages: " . $counts['total_count'] . "</li>\n";
        echo "<li>Unread messages: " . $counts['total_unread'] . "</li>\n";
        echo "<li>Archived messages: " . $counts['archived_count'] . "</li>\n";
        echo "</ul>\n";
    } else {
        echo "<p style='color: orange;'>⚠ No message counts found (this is normal for new installation)</p>\n";
    }
    
    echo "<h2>4. Testing User Messages Retrieval</h2>\n";
    
    $messages = $messageModel->getUserMessages(1, 'all', 5, 0);
    
    if (is_array($messages)) {
        echo "<p style='color: green;'>✓ User messages retrieved successfully</p>\n";
        echo "<p>Found " . count($messages) . " messages</p>\n";
        
        if (count($messages) > 0) {
            echo "<h3>Sample Messages:</h3>\n";
            foreach (array_slice($messages, 0, 3) as $message) {
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0;'>\n";
                echo "<strong>From:</strong> " . htmlspecialchars($message->from_user_name ?? 'System') . "<br>\n";
                echo "<strong>Subject:</strong> " . htmlspecialchars($message->subject) . "<br>\n";
                echo "<strong>Date:</strong> " . $message->created_at . "<br>\n";
                echo "<strong>Read:</strong> " . ($message->is_read ? 'Yes' : 'No') . "\n";
                echo "</div>\n";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to retrieve user messages</p>\n";
    }
    
    echo "<h2>5. Testing Notification Preferences</h2>\n";
    
    $canSend = $messageModel->canUserSendNotifications(1);
    
    if ($canSend) {
        echo "<p style='color: green;'>✓ User can send notifications</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ User cannot send notifications (check notification settings)</p>\n";
    }
    
    echo "<h2>Test Results Summary</h2>\n";
    echo "<p style='color: green; font-weight: bold;'>✅ Basic system tests completed!</p>\n";
    
    echo "<h2>Next Steps</h2>\n";
    echo "<ul>\n";
    echo "<li><a href='/notification_center'>Visit Notification Center</a></li>\n";
    echo "<li><a href='/judge_management'>Test Judge Messaging</a></li>\n";
    echo "<li>Send a test message to verify delivery methods work</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Error details: " . htmlspecialchars($e->getTraceAsString()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>