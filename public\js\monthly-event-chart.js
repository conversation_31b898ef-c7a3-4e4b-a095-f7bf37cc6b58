/**
 * Monthly Event Chart JavaScript
 * 
 * A responsive, mobile-first Event chart for Events and Shows Management System
 * 
 * Version 3.48.1 - Complete Timezone Implementation
 * Implementation Date: 2025-01-27
 * 
 * Latest Updates (v3.48.1):
 * - COMPLETED: Full timezone implementation with TimezoneHelper integration
 * - Replaced ALL toLocaleTimeString() calls with TimezoneHelper.formatTime() or manual formatting
 * - Replaced ALL toLocaleDateString() calls with TimezoneHelper.formatDate() or manual formatting
 * - Enhanced mobile and desktop date/time formatting with timezone awareness
 * - Added fallback mechanisms when TimezoneHelper is not available
 * - Ensures complete compatibility with server-side timezone helper functions
 * 
 * Previous Updates (v3.48.0):
 * - IMPLEMENTED: Proper timezone handling for time and date formatting
 * - Replaced toLocaleTimeString() with manual time formatting to avoid browser locale issues
 * - Replaced toLocaleDateString() with manual date formatting for consistency
 * - Added formatDateString() helper method for consistent date display
 * - Ensures compatibility with server-side timezone helper functions
 * 
 * Previous Updates (v3.47.2):
 * - COMPLETED: Event spanner bar colors now working correctly
 * - Cleaned up debug logging after successful color fix implementation
 * - Maintained multi-layered color approach for reliability
 * - Event bars display individual colors with nice gradient/fade effect
 * 
 * Previous Updates (v3.47.1):
 * - ENHANCED FIX: Multiple approaches to ensure event colors display correctly
 * - Added CSS custom properties (--event-color) as fallback mechanism
 * - Enhanced JavaScript with multiple color-setting methods
 * - Added computed style debugging to identify CSS override issues
 * - Added dynamic-color class for better CSS targeting
 * - Comprehensive color application: inline styles + CSS properties + data attributes
 * 
 * Previous Updates (v3.47.0):
 * - FIX: Event spanner bars now display correct individual event colors
 * - Removed default background-color from CSS to allow dynamic color setting
 * - Enhanced JavaScript color handling with !important override
 * - Added comprehensive debug logging for color troubleshooting
 * - Fixed CSS specificity issues that were overriding event colors
 * 
 * Previous Updates (v3.46.5):
 * - FIX: Event details popup now shows both start and end dates for multi-day events
 * - Enhanced formatEventTime() method to display complete date ranges
 * - Multi-day events now show "Start Date, Time - End Date, Time" format
 * - Improved readability and user experience for event duration display
 * 
 * Previous Updates (v3.46.4):
 * - FIX: Disabled AJAX mouseovers on mobile devices to improve mobile UX
 * - Added isMobileDevice() method for comprehensive mobile detection
 * - Hover popups now only show on desktop/non-mobile devices
 * - Touch events still work on mobile for event interaction
 * - Enhanced mobile-first responsive design approach
 * 
 * Previous Updates (v3.46.3):
 * - FEATURE: Added car badge to event spanner bars for events with show_id
 * - Car badge displays as small gold circle with car icon next to event number
 * - Includes tooltip "Car Show Event" for accessibility
 * 
 * Previous Updates (v3.46.2):
 * - FIX: Calendar now properly includes events on the last day of the month
 * - Added end-of-day time (23:59:59) to monthEnd date calculation
 * - Enhanced debug logging for date range calculations
 * - Fixed timezone-related issues with month-end date filtering
 * 
 * Previous Updates (v3.46.1):
 * - FIX: Navigation buttons now apply current filters when changing months
 * - FEATURE: Enhanced filter system integration (v3.46.0)
 * - Added refetchEvents() method for filter system compatibility
 * - Added clearEvents() and getEvents() methods for filter system
 * - Added loadEventsFromAPI() fallback method
 * - Improved filter system detection and communication
 * - Enhanced debug logging for filter integration
 * 
 * Previous Updates (v3.45.1):
 * - BUGFIX: Fixed null pointer errors when handling blank days
 * - Fixed debug logging to handle null values properly
 * - Enhanced isSameDay method to handle null values
 * - Fixed duplicate createDayHeader method to handle blank days
 * - Improved weekend detection logic for blank days
 * - Added comprehensive debug logging for troubleshooting
 * 
 * Previous Updates (v3.45.0):
 * - MAJOR: Implemented Sunday-to-Saturday calendar layout
 * - Added blank day cells for days before 1st of month
 * - Calendar now starts with Sunday and ends with Saturday
 * - Enhanced day header handling for null values (blank days)
 * - Updated event positioning logic to handle blank days
 * - Added CSS styling for blank day cells with dashed borders
 * 
 * Previous Updates (v3.44.2):
 * - Fixed week 2 events not displaying issue
 * - Enhanced date normalization in getEventsForWeek method
 * - Added comprehensive debugging for event filtering
 * - Improved event overlap detection logic
 * 
 * ⚠️  CRITICAL FIXES: See CRITICAL_FIXES_DO_NOT_REMOVE.md for fixes that must not be removed
 * 
 * Previous Updates (v3.44.0):
 * - Added formatTimelineDates() method for timeline date/time display
 * - Timeline dates show event's full start/end span in all week sections
 * - Implemented data-timeline-dates attribute for CSS ::after content
 * 
 * Previous Updates (v3.43.1):
 * - Fixed timeline background styling (CSS-only change)
 * 
 * Previous Updates (v3.43.0):
 * - Fixed spanner bar sizing to fill day columns properly
 * - Removed event titles from spanner bars (badges only)
 * - Improved CSS Grid integration for proper positioning
 * - Enhanced badge styling for better visibility
 * - Removed alternating row colors for cleaner appearance
 * 
 * Previous Updates (v3.42.0):
 * - Added colored backgrounds to week headers
 * - Implemented numbered badges for events and spanner bars
 * - Enhanced event info with start/end dates and times (left justified)
 * - Added city/state information (right justified)
 * - Fixed today line indicator logic (was inverted)
 * - Implemented proper drag and drop functionality
 * - Made event names clickable to open event popup
 * - Improved mobile responsiveness and touch support
 */

class MonthlyEventChart {
    constructor(desktopContainerId, options = {}) {
        this.desktopContainer = document.getElementById(desktopContainerId);
        this.options = {
            firstDayOfWeek: 0,
            timeFormat: '12',
            showWeekends: true,
            showTodayLine: false, // Default to false - will be set by settings
            showEventHover: true, // Default to true - will be set by settings
            enableDragDrop: false,
            enableResize: false,
            enableEdit: false,
            userPermissions: {
                isAdmin: false,
                isCoordinator: false,
                userId: null,
                userRole: 'guest'
            },
            onEventClick: null,
            onEventEdit: null,
            onEventDrop: null,
            onEventResize: null,
            onMonthChange: null,
            ...options
        };
        
        this.currentDate = new Date();
        this.events = [];
        this.isLoading = false;
        
        this.init();
        
        console.log('=== MONTHLY-EVENT-CHART.JS LOADED - VERSION 3.48.1 - COMPLETE TIMEZONE IMPLEMENTATION ===');
    }
    
    init() {
        try {
            if (DEBUG_MODE) {
                console.log('Monthly Event Chart: Starting initialization');
            }
            
            this.setupEventListeners();
            this.render();
            this.updateTodayLines();
            
            // Update today line every minute
            setInterval(() => this.updateTodayLines(), 60000);
            
            if (DEBUG_MODE) {
                console.log('Monthly Event Chart: Initialization completed successfully');
            }
        } catch (error) {
            console.error('Error during Monthly Event Chart initialization:', error);
            if (DEBUG_MODE) {
                console.error('Stack trace:', error.stack);
            }
            throw error; // Re-throw to maintain error visibility
        }
    }
    
    setupEventListeners() {
        // Navigation controls
        const prevBtn = document.getElementById('eventPrevMonth');
        const nextBtn = document.getElementById('eventNextMonth');
        const todayBtn = document.getElementById('eventToday');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.previousMonth());
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextMonth());
        }
        
        if (todayBtn) {
            todayBtn.addEventListener('click', () => this.goToToday());
        }
        
        // Window resize handler
        window.addEventListener('resize', () => {
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => this.handleResize(), 250);
        });
        
        // Listen for calendar events loaded (compatibility with filter system)
        window.addEventListener('calendarEventsLoaded', (e) => {
            this.loadEvents(e.detail.events);
        });
    }
    
    previousMonth() {
        console.log('=== EVENT NAVIGATION: Previous month clicked ===');
        this.showLoadingAnimation();
        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        setTimeout(() => {
            this.render();
            // Reload events with current filters for the new month
            this.loadEventsFromAPI();
            if (this.options.onMonthChange) {
                this.options.onMonthChange(this.currentDate.getFullYear(), this.currentDate.getMonth());
            }
        }, 150);
    }
    
    nextMonth() {
        console.log('=== EVENT NAVIGATION: Next month clicked ===');
        this.showLoadingAnimation();
        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        setTimeout(() => {
            this.render();
            // Reload events with current filters for the new month
            this.loadEventsFromAPI();
            if (this.options.onMonthChange) {
                this.options.onMonthChange(this.currentDate.getFullYear(), this.currentDate.getMonth());
            }
        }, 150);
    }
    
    goToToday() {
        console.log('=== EVENT NAVIGATION: Today button clicked ===');
        this.showLoadingAnimation();
        this.currentDate = new Date();
        setTimeout(() => {
            this.render();
            // Reload events with current filters for the current month
            this.loadEventsFromAPI();
            if (this.options.onMonthChange) {
                this.options.onMonthChange(this.currentDate.getFullYear(), this.currentDate.getMonth());
            }
        }, 150);
    }
    
    showLoadingAnimation() {
        const eventsContainer = document.getElementById('eventEventsContainer');
        if (eventsContainer) {
            eventsContainer.style.opacity = '0.5';
            eventsContainer.style.transform = 'scale(0.98)';
            eventsContainer.style.transition = 'all 0.15s ease';
            
            setTimeout(() => {
                eventsContainer.style.opacity = '1';
                eventsContainer.style.transform = 'scale(1)';
            }, 300);
        }
    }
    
    render() {
        if (DEBUG_MODE) {
            console.log('Monthly Event Chart: Starting render process');
        }
        
        this.updateMonthTitle();
        this.renderDesktopEvent();
        this.updateTodayLines();
        
        if (DEBUG_MODE) {
            console.log('Monthly Event Chart: Render process completed');
        }
    }
    
    updateMonthTitle() {
        const titleElement = document.getElementById('eventMonthTitle');
        if (titleElement) {
            const monthNames = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            titleElement.textContent = `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
        }
    }
    
    renderDesktopEvent() {
        if (DEBUG_MODE) {
            console.log('Monthly Event Chart: Rendering desktop event with weekly layout');
            console.log('Desktop container:', this.desktopContainer);
        }
        
        if (!this.desktopContainer) {
            if (DEBUG_MODE) {
                console.error('Desktop container not found!');
            }
            return;
        }
        
        // Use new weekly layout approach
        this.renderWeeklyEvent();
    }
    
    renderWeeklyEvent() {
        if (DEBUG_MODE) {
            console.log('Monthly Event Chart: Starting weekly event render');
        }
        
        try {
            const weeks = this.getWeeksInMonth();
            const eventContainer = document.getElementById('eventContainer');
            
            if (!eventContainer) {
                if (DEBUG_MODE) {
                    console.error('Event container not found!');
                }
                return;
            }
            
            if (DEBUG_MODE) {
                console.log('Found event container, clearing content');
                console.log('Generated weeks:', weeks.length);
            }
            
            // Clear existing content
            eventContainer.innerHTML = '';
            
            // Render each week section
            weeks.forEach((week, weekIndex) => {
                if (DEBUG_MODE) {
                    console.log(`Rendering week ${weekIndex + 1}:`, week.map(d => d ? d.getDate() : 'blank'));
                }
                const weekSection = this.createWeekSection(week, weekIndex);
                eventContainer.appendChild(weekSection);
            });
            
            if (DEBUG_MODE) {
                console.log('Successfully rendered', weeks.length, 'week sections');
            }
        } catch (error) {
            console.error('Error in renderWeeklyEvent:', error);
            if (DEBUG_MODE) {
                console.error('Stack trace:', error.stack);
            }
        }
    }
    
    renderTimelineHeader(daysInMonth) {
        const headerContainer = document.getElementById('eventTimelineHeader');
        
        if (DEBUG_MODE) {
            console.log('Monthly Event Chart: Rendering timeline header');
            console.log('Header container:', headerContainer);
            console.log('Days in month:', daysInMonth.length);
        }
        
        if (!headerContainer) {
            if (DEBUG_MODE) {
                console.error('Timeline header container not found!');
            }
            return;
        }
        
        // Filter out hidden weekend days to get actual visible days (handle null values)
        const visibleDays = daysInMonth.filter((day, dayIndex) => {
            if (day === null) {
                // For blank days, the position in array corresponds to day of week
                // Since we start with Sunday: 0=Sun, 1=Mon, 2=Tue, 3=Wed, 4=Thu, 5=Fri, 6=Sat
                const dayOfWeek = dayIndex % 7;
                const isWeekend = dayOfWeek === 0 || dayOfWeek === 6; // Sunday or Saturday
                return !(isWeekend && !this.options.showWeekends);
            }
            const isWeekend = day.getDay() === 0 || day.getDay() === 6;
            return !(isWeekend && !this.options.showWeekends);
        });
        
        if (DEBUG_MODE) {
            console.log('Visible days count:', visibleDays.length);
            console.log('Show weekends:', this.options.showWeekends);
        }
        
        headerContainer.innerHTML = `
            <div class="event-event-column-header">
                <i class="fas fa-calendar-alt me-2"></i>
                Events
            </div>
            <div class="event-timeline-days">
                ${daysInMonth.map(day => this.createDayHeader(day)).join('')}
            </div>
        `;
        
        // Set dynamic grid columns based on visible days
        const timelineDays = headerContainer.querySelector('.event-timeline-days');
        if (timelineDays) {
            timelineDays.style.gridTemplateColumns = `repeat(${visibleDays.length}, minmax(40px, 1fr))`;
        }
        
        // Also update all timeline rows to match
        this.updateTimelineGridColumns(visibleDays.length);
    }
    
    updateTimelineGridColumns(visibleDaysCount) {
        // Update all timeline elements to use the same grid
        const timelines = document.querySelectorAll('.event-timeline');
        timelines.forEach(timeline => {
            timeline.style.gridTemplateColumns = `repeat(${visibleDaysCount}, minmax(40px, 1fr))`;
        });
        
        if (DEBUG_MODE) {
            console.log(`Updated ${timelines.length} timeline grids to ${visibleDaysCount} columns`);
        }
    }
    
    createDayHeader(day) {
        // Handle blank days (null values)
        if (day === null) {
            return `
                <div class="event-day-header blank-day">
                    <div class="day-name">&nbsp;</div>
                    <div class="day-number">&nbsp;</div>
                </div>
            `;
        }
        
        const today = new Date();
        const isToday = this.isToday(day);
        const isWeekend = day.getDay() === 0 || day.getDay() === 6;
        
        if (DEBUG_MODE && isWeekend) {
            console.log('Weekend day:', day.toDateString(), 'showWeekends:', this.options.showWeekends);
        }
        
        const classes = ['event-day-header'];
        if (isToday) classes.push('today');
        if (isWeekend && !this.options.showWeekends) {
            if (DEBUG_MODE) {
                console.log('Hiding weekend day:', day.toDateString());
            }
            return ''; // Hide weekends if disabled
        }
        if (isWeekend) classes.push('weekend');
        
        const dayNum = day.getDate();
        // Use manual formatting instead of toLocaleDateString for timezone consistency
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const dayName = dayNames[day.getDay()];
        
        return `
            <div class="${classes.join(' ')}" data-date="${day.toISOString().split('T')[0]}">
                <div class="day-number">${dayNum}</div>
                <div class="day-name">${dayName}</div>
            </div>
        `;
    }
    

    

    
    createEventInfo(event) {
        const eventColor = event.backgroundColor || '#3788d8';
        const eventColorDark = this.darkenColor(eventColor, 20);
        
        return `
            <div class="event-event-info" style="--event-color: ${eventColor}; --event-color-dark: ${eventColorDark};">
                <div class="event-event-title">${event.title || 'Untitled Event'}</div>
                <div class="event-event-details">
                    <div class="event-event-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${this.formatLocation(event)}</span>
                    </div>
                    ${event.venue ? `
                        <div class="event-event-location">
                            <i class="fas fa-building"></i>
                            <span>${event.venue}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    

    
    formatEventTime(event, startDate = null, endDate = null) {
        if (event.allDay) {
            return 'All Day';
        }
        
        // If startDate and endDate are not provided, get them from the event
        // Controller handles all timezone conversion - dates are already in correct timezone
        if (!startDate) {
            if (event.start) {
                startDate = new Date(event.start);
            } else {
                startDate = null;
            }
        }
        if (!endDate) {
            if (event.end) {
                endDate = new Date(event.end);
            } else {
                endDate = startDate;
            }
        }
        
        // If we still don't have valid dates, return a fallback
        if (!startDate || isNaN(startDate.getTime())) {
            return 'Time not available';
        }
        if (!endDate || isNaN(endDate.getTime())) {
            endDate = startDate; // Use start date as fallback
        }
        
        const formatTime = (date) => {
            if (!date || isNaN(date.getTime())) {
                return 'N/A';
            }
            
            // Direct formatting - no timezone conversion since controller already converted
            const hours = date.getHours();
            const minutes = date.getMinutes();

            if (this.options.timeFormat === '24') {
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
            } else {
                const ampm = hours >= 12 ? 'PM' : 'AM';
                const displayHours = hours % 12 || 12;
                return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
            }
        };
        
        const startTime = formatTime(startDate);
        const endTime = formatTime(endDate);
        
        // If same day, show start - end
        if (startDate.toDateString() === endDate.toDateString()) {
            return `${startTime} - ${endTime}`;
        } else {
            // Multi-day event, show start date/time - end date/time
            // Use timezone helper if available, otherwise fallback to manual formatting
            let startDateStr, endDateStr;
            
            // Direct formatting - no timezone conversion since controller already converted
            startDateStr = this.formatDateString(startDate);
            endDateStr = this.formatDateString(endDate);
            
            return `${startDateStr} ${startTime} - ${endDateStr} ${endTime}`;
        }
    }
    
    /**
     * Format date string manually to avoid locale timezone issues
     * @param {Date} date - The date to format
     * @returns {string} Formatted date string (e.g., "Dec 25, 2024")
     */
    formatDateString(date) {
        if (!date || isNaN(date.getTime())) {
            return 'N/A';
        }
        
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        const month = months[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear();
        
        return `${month} ${day}, ${year}`;
    }
    
    /**
     * Detect if the current device is a mobile device
     * Uses multiple detection methods for comprehensive mobile detection
     * @returns {boolean} True if mobile device, false otherwise
     */
    isMobileDevice() {
        // Method 1: User agent detection
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
        
        // Method 2: Touch capability detection
        const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Method 3: Screen width detection (mobile-first breakpoint)
        const isSmallScreen = window.innerWidth <= 768;
        
        // Method 4: Orientation change capability (mobile devices typically support this)
        const hasOrientationChange = 'orientation' in window;
        
        // Combine detection methods
        const isMobileUA = mobileRegex.test(userAgent);
        const isMobileScreen = isSmallScreen && hasTouch;
        const isMobileCapabilities = hasTouch && hasOrientationChange && isSmallScreen;
        
        // Return true if any mobile detection method is positive
        const isMobile = isMobileUA || isMobileScreen || isMobileCapabilities;
        
        if (DEBUG_MODE) {
            console.log('Mobile Detection Results:', {
                userAgent: isMobileUA,
                screenSize: isSmallScreen,
                touchCapable: hasTouch,
                orientationChange: hasOrientationChange,
                finalResult: isMobile
            });
        }
        
        return isMobile;
    }
    
    addEventBarListeners() {
        const eventBars = document.querySelectorAll('.event-event-bar');
        eventBars.forEach(bar => {
            const eventId = bar.getAttribute('data-event-id');
            const event = this.events.find(e => e.id == eventId);
            
            if (event) {
                // Click handler
                bar.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (this.options.onEventClick) {
                        this.options.onEventClick(event);
                    }
                });
                
                // Hover handlers (only if enabled and not on mobile)
                if (this.options.showEventHover && !this.isMobileDevice()) {
                    bar.addEventListener('mouseenter', (e) => {
                        this.showEventHover(event, bar, e);
                    });
                    
                    bar.addEventListener('mouseleave', (e) => {
                        this.hideEventHover();
                    });
                }
                
                // Hover handlers (only on non-mobile devices)
                if (!this.isMobileDevice()) {
                    bar.addEventListener('mouseenter', (e) => {
                        if (window.showEventEventPopup) {
                            window.showEventEventPopup(event, bar, e.clientX, e.clientY);
                        }
                    });
                    
                    bar.addEventListener('mouseleave', () => {
                        if (window.hideEventEventPopup) {
                            window.hideEventEventPopup();
                        }
                    });
                }
                
                // Touch support
                bar.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    if (window.showEventEventPopup) {
                        window.showEventEventPopup(event, bar, touch.clientX, touch.clientY);
                    }
                });
                
                // Drag and drop support (if enabled)
                if (this.options.enableDragDrop && this.checkEventEditPermission(event)) {
                    this.addDragDropSupport(bar, event);
                }
            }
        });
    }
    
    addDragDropSupport(element, event) {
        element.draggable = true;
        
        element.addEventListener('dragstart', (e) => {
            element.classList.add('dragging');
            e.dataTransfer.setData('text/plain', event.id);
        });
        
        element.addEventListener('dragend', () => {
            element.classList.remove('dragging');
        });
        
        // Add drop zones (simplified implementation)
        // This would need more sophisticated implementation for production
    }
    
    updateTodayLines() {
        // Check if today line should be shown
        if (!this.options.showTodayLine) {
            // Remove any existing today lines
            const existingLines = document.querySelectorAll('.event-today-line');
            existingLines.forEach(line => line.remove());
            return;
        }
        
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Normalize to start of day
        
        // Find all week sections and add today indicators where needed
        const weekSections = document.querySelectorAll('.event-week-section');
        
        weekSections.forEach((weekSection, weekIndex) => {
            this.updateTodayLineForWeek(weekSection, today, weekIndex);
        });
    }
    
    updateTodayLineForWeek(weekSection, today, weekIndex) {
        // Remove existing today line in this week
        const existingLine = weekSection.querySelector('.event-today-line');
        if (existingLine) {
            existingLine.remove();
        }

        // Get day headers for this week to check if today is in this week
        const dayHeaders = weekSection.querySelectorAll('.event-day-header');
        let todayIndex = -1;



        dayHeaders.forEach((header, index) => {
            const dateString = header.getAttribute('data-date');

            // Skip blank days or headers without data-date
            if (!dateString) {
                header.classList.remove('today');
                return;
            }

            // Parse the date string consistently in local timezone
            const [year, month, day] = dateString.split('-').map(Number);
            const headerDate = new Date(year, month - 1, day); // month is 0-based

            const isSame = this.isSameDay(headerDate, today);

            if (isSame) {
                todayIndex = index;
                header.classList.add('today');
            } else {
                header.classList.remove('today');
            }
        });
        
        if (todayIndex === -1) {
            return; // Today is not in this week
        }
        
        // Create today line for this week
        const todayLine = document.createElement('div');
        todayLine.className = 'event-today-line';
        
        // Position the line
        const weekEvents = weekSection.querySelector('.event-week-events');
        if (weekEvents) {
            weekEvents.style.position = 'relative';

            // Get the actual day header element to position relative to it
            const todayHeader = dayHeaders[todayIndex];
            const dayHeadersContainer = weekSection.querySelector('.event-day-headers');

            if (todayHeader && dayHeadersContainer) {
                // Get the position of the today header relative to the day headers container
                const headerRect = todayHeader.getBoundingClientRect();
                const containerRect = dayHeadersContainer.getBoundingClientRect();
                const relativeLeft = headerRect.left - containerRect.left;
                const headerWidth = headerRect.width;

                // Position the line at the center of the day header
                const linePosition = relativeLeft + (headerWidth / 2);



                todayLine.style.left = `calc(300px + ${linePosition}px)`; // After event column + exact position
            } else {
                // Fallback to percentage calculation
                const dayWidth = 100 / dayHeaders.length;
                const leftPosition = (todayIndex * dayWidth) + (dayWidth / 2);
                todayLine.style.left = `calc(300px + ${leftPosition}%)`;


            }

            todayLine.style.top = '0';
            todayLine.style.bottom = '0';
            todayLine.style.position = 'absolute';
            todayLine.style.width = '2px';
            todayLine.style.backgroundColor = '#dc3545';
            todayLine.style.zIndex = '10';
            todayLine.style.pointerEvents = 'none';

            weekEvents.appendChild(todayLine);
            

        }
    }
    
    loadEvents(events) {
        this.events = events || [];
        
        if (DEBUG_MODE) {
            console.log('loadEvents called with:', events ? events.length : 0, 'events');
            if (events && events.length > 0) {
                console.log('Sample events:', events.slice(0, 3).map(e => ({
                    title: e.title,
                    start: e.start,
                    end: e.end
                })));
            }
        }
        
        this.render();
        
        // Trigger event for table update
        window.dispatchEvent(new CustomEvent('eventEventsLoaded', {
            detail: { events: this.events }
        }));
    }
    
    getDaysInMonth() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        
        const days = [];
        
        // Add blank days at the beginning to start with Sunday
        const firstDayOfWeek = firstDay.getDay(); // 0 = Sunday, 1 = Monday, etc.
        for (let i = 0; i < firstDayOfWeek; i++) {
            // Add null values for blank days before the 1st
            days.push(null);
        }
        
        // Add actual days of the month
        for (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 1)) {
            days.push(new Date(d));
        }
        
        // Add blank days at the end to complete the last week (end with Saturday)
        const totalDays = days.length;
        const remainingDays = 7 - (totalDays % 7);
        if (remainingDays < 7) {
            for (let i = 0; i < remainingDays; i++) {
                days.push(null);
            }
        }
        
        if (DEBUG_MODE) {
            console.log('Generated calendar days:', {
                totalDays: days.length,
                actualDays: days.filter(d => d !== null).length,
                blankDaysStart: firstDayOfWeek,
                blankDaysEnd: remainingDays < 7 ? remainingDays : 0,
                firstFewDays: days.slice(0, 10).map(d => d ? d.getDate() : 'blank'),
                lastFewDays: days.slice(-10).map(d => d ? d.getDate() : 'blank')
            });
        }
        
        return days;
    }
    
    formatLocation(event) {
        if (event.city && event.state) {
            return `${event.city}, ${event.state}`;
        } else if (event.location) {
            return event.location;
        } else {
            return 'Location TBD';
        }
    }
    
    checkEventEditPermission(event) {
        const permissions = this.options.userPermissions;
        
        if (!permissions.userId) return false; // Not logged in
        if (permissions.isAdmin) return true; // Admin can edit all events
        
        // Check if user owns this event
        if (event.extendedProps?.user_id && event.extendedProps.user_id == permissions.userId) {
            return true;
        }
        
        // Check if user is coordinator for this show/event
        if (permissions.isCoordinator && event.extendedProps?.show_id) {
            return event.extendedProps?.coordinators && 
                   event.extendedProps.coordinators.includes(permissions.userId);
        }
        
        return false;
    }
    
    darkenColor(color, percent) {
        // Simple color darkening function
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    handleResize() {
        this.updateTodayLines();
        this.updateTimelineDatesFormat();
        // Additional resize handling if needed
    }
    
    updateTimelineDatesFormat() {
        // Update all timeline dates when screen size changes
        const timelineDatesElements = document.querySelectorAll('.event-timeline-dates');
        timelineDatesElements.forEach(element => {
            // Find the corresponding event data and update the text
            const eventRow = element.closest('.event-event-row');
            if (eventRow && eventRow.eventData) {
                element.textContent = this.formatTimelineDates(eventRow.eventData);
            }
        });
    }
    
    showEventHover(event, element, mouseEvent) {
        // Don't show hover popups on mobile devices for better UX
        if (this.isMobileDevice()) {
            if (DEBUG_MODE) {
                console.log('Hover popup blocked on mobile device for better UX');
            }
            return;
        }
        
        // Remove any existing hover popup
        this.hideEventHover();
        
        // Create hover popup
        const popup = document.createElement('div');
        popup.className = 'event-event-hover-popup';
        const showId = event.show_id || (event.extendedProps && event.extendedProps.show_id);
        const hasShow = showId && showId !== null && showId !== '';
        popup.innerHTML = `
            <div class="hover-popup-header">
                <h6 class="mb-1">
                    ${this.escapeHtml(event.title)}
                    ${hasShow ? '<i class="fas fa-car event-car-badge ms-2" title="Car Show Event"></i>' : ''}
                </h6>
            </div>
            <div class="hover-popup-body">
                ${event.start ? `<div><i class="fas fa-clock me-2"></i>${this.formatEventTime(event)}</div>` : ''}
                ${event.location ? `<div><i class="fas fa-map-marker-alt me-2"></i>${this.escapeHtml(event.location)}</div>` : ''}
                ${event.city && event.state ? `<div><i class="fas fa-location-dot me-2"></i>${this.escapeHtml(event.city)}, ${this.escapeHtml(event.state)}</div>` : ''}
                ${event.venue ? `<div><i class="fas fa-building me-2"></i>${this.escapeHtml(event.venue)}</div>` : ''}
            </div>
        `;
        
        // Position popup
        const rect = element.getBoundingClientRect();
        popup.style.position = 'fixed';
        popup.style.left = `${rect.left + rect.width / 2}px`;
        popup.style.top = `${rect.top - 10}px`;
        popup.style.transform = 'translateX(-50%) translateY(-100%)';
        popup.style.zIndex = '1000';
        
        document.body.appendChild(popup);
        this.currentHoverPopup = popup;
        
        // Auto-hide after 5 seconds
        this.hoverTimeout = setTimeout(() => {
            this.hideEventHover();
        }, 5000);
    }
    
    hideEventHover() {
        if (this.currentHoverPopup) {
            this.currentHoverPopup.remove();
            this.currentHoverPopup = null;
        }
        
        if (this.hoverTimeout) {
            clearTimeout(this.hoverTimeout);
            this.hoverTimeout = null;
        }
    }
    
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    getEventBadgeNumber(event) {
        // Find the index of this event in the sorted events array
        const sortedEvents = [...this.events].sort((a, b) => {
            return new Date(a.start) - new Date(b.start);
        });
        
        const index = sortedEvents.findIndex(e => e.id === event.id);
        return index + 1; // 1-based numbering
    }
    
    formatTime(date) {
        if (!date || isNaN(date.getTime())) {
            return 'N/A';
        }
        
        // Direct formatting - no timezone conversion since controller already converted
        const hours = date.getHours();
        const minutes = date.getMinutes();

        if (this.options.timeFormat === '24') {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        } else {
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours % 12 || 12;
            return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
        }
    }
    
    formatTimelineDates(event) {
        if (!event || !event.start) {
            return '';
        }
        
        // Parse dates from controller - already in correct timezone (user's timezone for logged-in users, Eastern for guests)
        // NO timezone conversion needed - controller handles all timezone conversion
        let startDate = new Date(event.start);
        let endDate = event.end ? new Date(event.end) : startDate;
        
        // Check if mobile device (screen width <= 768px)
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // Mobile format: "12/31, 2:02pm - 3:15pm"
            const formatMobileDate = (date) => {
                // Use manual formatting instead of toLocaleDateString for timezone consistency
                return `${date.getMonth() + 1}/${date.getDate()}`;
            };
            
            const formatMobileTime = (date) => {
                // Direct formatting - no timezone conversion since controller already converted
                const hours = date.getHours();
                const minutes = date.getMinutes();
                const ampm = hours >= 12 ? 'pm' : 'am';
                const displayHours = hours % 12 || 12;
                return `${displayHours}:${minutes.toString().padStart(2, '0')}${ampm}`;
            };
            
            const startDateStr = formatMobileDate(startDate);
            const endDateStr = formatMobileDate(endDate);
            const startTimeStr = formatMobileTime(startDate);
            const endTimeStr = formatMobileTime(endDate);
            
            // Check if it's a single day event
            if (this.isSameDay(startDate, endDate)) {
                // Single-day mobile format: "12/31, 2:02pm - 3:15pm"
                return `${startDateStr}, ${startTimeStr} - ${endTimeStr}`;
            } else {
                // Multi-day mobile format: "12/31, 2:02pm - 1/2, 3:15pm"
                return `${startDateStr}, ${startTimeStr} - ${endDateStr}, ${endTimeStr}`;
            }
        } else {
            // Desktop format: "Dec 1, 2024 • 9:00 AM - 5:00 PM"
            const formatDate = (date) => {
                // Direct formatting - no timezone conversion since controller already converted
                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                               'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
            };
            
            const startDateStr = formatDate(startDate);
            const endDateStr = formatDate(endDate);
            const startTimeStr = this.formatTime(startDate);
            const endTimeStr = this.formatTime(endDate);
            
            // Check if it's a single day event
            if (this.isSameDay(startDate, endDate)) {
                // Single-day desktop format: "Dec 1, 2024 • 9:00 AM - 5:00 PM"
                return `${startDateStr} • ${startTimeStr} - ${endTimeStr}`;
            } else {
                // Multi-day desktop format: "Dec 1, 9:00 AM - Dec 3, 5:00 PM"
                return `${startDateStr}, ${startTimeStr} - ${endDateStr}, ${endTimeStr}`;
            }
        }
    }
    
    // Weekly Event Chart Methods
    getWeeksInMonth() {
        const daysInMonth = this.getDaysInMonth();
        const weeks = [];
        let currentWeek = [];
        
        // Group days into weeks (7 days each, or remaining days for last week)
        for (let i = 0; i < daysInMonth.length; i++) {
            currentWeek.push(daysInMonth[i]);
            
            // If we have 7 days or we're at the end of the month
            if (currentWeek.length === 7 || i === daysInMonth.length - 1) {
                weeks.push([...currentWeek]);
                currentWeek = [];
            }
        }
        
        if (DEBUG_MODE) {
            console.log('Generated weeks:', weeks.map((week, index) => ({
                weekIndex: index,
                start: week[0] ? week[0].getDate() : 'blank',
                end: week[week.length - 1] ? week[week.length - 1].getDate() : 'blank',
                count: week.length,
                actualDays: week.filter(d => d !== null).length,
                allDays: week.map(d => d ? d.getDate() : 'blank')
            })));
        }
        
        return weeks;
    }
    
    createWeekSection(week, weekIndex) {
        const weekContainer = document.createElement('div');
        weekContainer.className = 'event-week-section';
        weekContainer.setAttribute('data-week', weekIndex);
        
        // Filter visible days for this week (handle null values for blank days)
        const visibleDays = week.filter((day, dayIndex) => {
            if (day === null) {
                // For blank days, dayIndex corresponds to day of week
                // dayIndex 0 = Sunday, 1 = Monday, ..., 6 = Saturday
                const isWeekend = dayIndex === 0 || dayIndex === 6; // Sunday or Saturday
                return !(isWeekend && !this.options.showWeekends);
            }
            const isWeekend = day.getDay() === 0 || day.getDay() === 6;
            return !(isWeekend && !this.options.showWeekends);
        });
        
        if (DEBUG_MODE) {
            console.log(`Week ${weekIndex + 1} filtering:`, {
                originalDays: week.length,
                visibleDays: visibleDays.length,
                showWeekends: this.options.showWeekends,
                weekDays: week.map((d, i) => ({ index: i, day: d ? d.getDate() : 'blank' })),
                visibleDays: visibleDays.map((d, i) => ({ index: i, day: d ? d.getDate() : 'blank' }))
            });
        }
        
        if (visibleDays.length === 0) {
            if (DEBUG_MODE) {
                console.log(`Week ${weekIndex + 1} has no visible days, returning empty container`);
            }
            return weekContainer; // Return empty container if no visible days
        }
        
        // Create week header
        const weekHeader = this.createWeekHeader(visibleDays, weekIndex);
        weekContainer.appendChild(weekHeader);
        
        // Create events section for this week
        const weekEvents = this.createWeekEvents(visibleDays, weekIndex);
        weekContainer.appendChild(weekEvents);
        
        return weekContainer;
    }
    
    createWeekHeader(visibleDays, weekIndex) {
        const header = document.createElement('div');
        header.className = 'event-week-header';
        
        // Find first and last actual dates (non-null) for week label
        const actualDays = visibleDays.filter(day => day !== null);
        let weekLabel = `Week ${weekIndex + 1}`;
        
        if (actualDays.length > 0) {
            const startDate = actualDays[0].getDate();
            const endDate = actualDays[actualDays.length - 1].getDate();
            weekLabel = `${startDate}${endDate !== startDate ? `-${endDate}` : ''}`;
        }
        
        // Generate a color for the week header based on week index
        const weekColors = [
            '#e3f2fd', // Light blue
            '#f3e5f5', // Light purple
            '#e8f5e8', // Light green
            '#fff3e0', // Light orange
            '#fce4ec'  // Light pink
        ];
        const weekColor = weekColors[weekIndex % weekColors.length];
        
        header.innerHTML = `
            <div class="event-week-title" style="background-color: ${weekColor};">
                <h5 class="mb-0">Week ${weekIndex + 1}: ${weekLabel}</h5>
            </div>
            <div class="event-week-days">
                <div class="event-event-column-header">Events</div>
                <div class="event-day-headers">
                    ${visibleDays.map(day => this.createDayHeader(day)).join('')}
                </div>
            </div>
        `;
        
        // Set grid columns for day headers
        const dayHeaders = header.querySelector('.event-day-headers');
        if (dayHeaders) {
            dayHeaders.style.gridTemplateColumns = `repeat(${visibleDays.length}, 1fr)`;
        }
        
        return header;
    }
    
    createDayHeader(day) {
        // Handle blank days (null values)
        if (day === null) {
            return `
                <div class="event-day-header blank-day">
                    <div class="day-name">&nbsp;</div>
                    <div class="day-number">&nbsp;</div>
                </div>
            `;
        }
        
        // Use manual formatting instead of toLocaleDateString for timezone consistency
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const dayName = dayNames[day.getDay()];
        const dayNumber = day.getDate();
        const isToday = this.isToday(day);
        const isWeekend = day.getDay() === 0 || day.getDay() === 6;
        
        // Create date string in local timezone format for consistency
        const year = day.getFullYear();
        const month = String(day.getMonth() + 1).padStart(2, '0');
        const date = String(day.getDate()).padStart(2, '0');
        const dateString = `${year}-${month}-${date}`;

        return `
            <div class="event-day-header ${isToday ? 'today' : ''} ${isWeekend ? 'weekend' : ''}"
                 data-date="${dateString}">
                <div class="day-name">${dayName}</div>
                <div class="day-number">${dayNumber}</div>
            </div>
        `;
    }
    
    createWeekEvents(visibleDays, weekIndex) {
        const eventsContainer = document.createElement('div');
        eventsContainer.className = 'event-week-events';
        
        if (DEBUG_MODE) {
            console.log(`Creating week events for week ${weekIndex + 1}`);
            console.log('Visible days:', visibleDays.map(d => d ? d.toDateString() : 'blank'));
        }
        
        // Get events for this week
        const weekEvents = this.getEventsForWeek(visibleDays);
        
        if (DEBUG_MODE) {
            console.log(`Week ${weekIndex + 1} has ${weekEvents.length} events`);
        }
        
        if (weekEvents.length === 0) {
            if (DEBUG_MODE) {
                console.log(`No events found for week ${weekIndex + 1}, showing empty message`);
            }
            eventsContainer.innerHTML = `
                <div class="event-no-events">
                    <div class="event-event-column">No events this week</div>
                    <div class="event-timeline-empty" style="grid-template-columns: repeat(${visibleDays.length}, 1fr);"></div>
                </div>
            `;
            return eventsContainer;
        }
        
        // Render each event as a row with badges
        weekEvents.forEach((event, eventIndex) => {
            if (DEBUG_MODE) {
                console.log(`Rendering event ${eventIndex + 1} for week ${weekIndex + 1}:`, event.title);
            }
            const eventRow = this.createEventRow(event, visibleDays, weekIndex, eventIndex);
            eventsContainer.appendChild(eventRow);
        });
        
        return eventsContainer;
    }
    
    getEventsForWeek(weekDays) {
        // Filter out null values (blank days) to get actual date range
        const actualDays = weekDays.filter(day => day !== null);
        
        if (actualDays.length === 0) {
            // Week has no actual days (all blank), return empty array
            return [];
        }
        
        const weekStart = actualDays[0];
        const weekEnd = actualDays[actualDays.length - 1];
        
        if (DEBUG_MODE) {
            console.log('getEventsForWeek called:');
            console.log('Week start:', weekStart.toDateString());
            console.log('Week end:', weekEnd.toDateString());
            console.log('Total events to check:', this.events.length);
            console.log('Blank days in week:', weekDays.length - actualDays.length);
        }
        
        const filteredEvents = this.events.filter(event => {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end || event.start);
            
            // CRITICAL FIX: Date normalization for proper week filtering (v3.44.2)
            // DO NOT REMOVE: This fixes the "Week 2 events not showing" issue
            // 
            // Problem: Date objects with time components (14:30:00) don't match properly 
            // with week boundaries that might be at start of day (00:00:00)
            // 
            // Solution: Normalize all dates to consistent day boundaries:
            // - Week/Event start: 00:00:00 (start of day)  
            // - Week/Event end: 23:59:59 (end of day)
            // This ensures events on boundary days are properly included
            const weekStartNorm = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
            const weekEndNorm = new Date(weekEnd.getFullYear(), weekEnd.getMonth(), weekEnd.getDate(), 23, 59, 59);
            const eventStartNorm = new Date(eventStart.getFullYear(), eventStart.getMonth(), eventStart.getDate());
            const eventEndNorm = new Date(eventEnd.getFullYear(), eventEnd.getMonth(), eventEnd.getDate(), 23, 59, 59);
            
            // Event overlaps with this week if:
            // Event starts before week ends AND event ends after week starts
            const overlaps = eventStartNorm <= weekEndNorm && eventEndNorm >= weekStartNorm;
            
            if (DEBUG_MODE) {
                console.log(`Event "${event.title}":`, {
                    eventStart: eventStartNorm.toDateString(),
                    eventEnd: eventEndNorm.toDateString(),
                    weekStart: weekStartNorm.toDateString(),
                    weekEnd: weekEndNorm.toDateString(),
                    overlaps: overlaps
                });
            }
            
            return overlaps;
        });
        
        if (DEBUG_MODE) {
            console.log(`Found ${filteredEvents.length} events for this week`);
        }
        
        return filteredEvents;
    }
    
    createEventRow(event, visibleDays, weekIndex, eventIndex = 0) {
        const row = document.createElement('div');
        row.className = 'event-event-row';
        row.setAttribute('data-event-id', event.id);
        row.setAttribute('data-week', weekIndex);
        
        // Calculate badge number (global event index across all weeks)
        const badgeNumber = this.getEventBadgeNumber(event);
        
        // Event info column
        const eventInfo = document.createElement('div');
        eventInfo.className = 'event-event-info';
        eventInfo.style.cursor = 'pointer'; // Make clickable
        
        eventInfo.innerHTML = `
            <div class="event-title-row">
                <span class="event-badge">${badgeNumber}</span>
                <span class="event-title">${this.escapeHtml(event.title)}</span>
            </div>
        `;
        
        // Add click handler to event info
        eventInfo.addEventListener('click', (e) => {
            e.stopPropagation();
            if (this.options.onEventClick) {
                this.options.onEventClick(event);
            }
        });
        
        // Add hover handlers to event info (if enabled and not on mobile)
        if (this.options.showEventHover && !this.isMobileDevice()) {
            eventInfo.addEventListener('mouseenter', (e) => {
                this.showEventHover(event, eventInfo, e);
            });
            
            eventInfo.addEventListener('mouseleave', (e) => {
                this.hideEventHover();
            });
        }
        
        // Timeline column
        const timeline = document.createElement('div');
        timeline.className = 'event-timeline';
        timeline.style.gridTemplateColumns = `repeat(${visibleDays.length}, 1fr)`;
        
        // Create event bar with badge
        const eventBar = this.createEventBar(event, visibleDays, badgeNumber);
        timeline.appendChild(eventBar);
        
        // Create timeline dates element
        const timelineDates = document.createElement('div');
        timelineDates.className = 'event-timeline-dates';
        timelineDates.textContent = this.formatTimelineDates(event);
        
        // Add drop zone functionality if drag and drop is enabled
        if (this.options.enableDragDrop) {
            this.addDropZoneListeners(timeline, visibleDays);
        }
        
        // Append timeline dates back to timeline for responsive positioning
        timeline.appendChild(timelineDates);
        
        row.appendChild(eventInfo);
        row.appendChild(timeline);
        
        // Store event data on row for resize updates
        row.eventData = event;
        
        return row;
    }
    
    createEventBar(event, visibleDays, badgeNumber) {
        // Parse dates from controller - already in correct timezone (user's timezone for logged-in users, Eastern for guests)
        // NO timezone conversion needed - controller handles all timezone conversion
        let eventStart = new Date(event.start);
        let eventEnd = event.end ? new Date(event.end) : eventStart;
        
        // Calculate which days this event spans in this week
        let startCol = 0;
        let endCol = 0; // Default to single day
        
        // Find start column - first day the event appears (skip null values)
        for (let i = 0; i < visibleDays.length; i++) {
            if (visibleDays[i] !== null && 
                (this.isSameDay(visibleDays[i], eventStart) || 
                (visibleDays[i] >= eventStart && eventStart < visibleDays[i]))) {
                startCol = i;
                break;
            }
        }
        
        // Find end column - last day the event appears (skip null values)
        // For single day events, endCol should equal startCol
        if (this.isSameDay(eventStart, eventEnd)) {
            // Single day event - fill the entire day column
            endCol = startCol;
        } else {
            // Multi-day event - span across days
            for (let i = visibleDays.length - 1; i >= 0; i--) {
                if (visibleDays[i] !== null && 
                    (this.isSameDay(visibleDays[i], eventEnd) || 
                    (visibleDays[i] <= eventEnd && eventEnd > visibleDays[i]))) {
                    endCol = i;
                    break;
                }
            }
        }
        
        const bar = document.createElement('div');
        bar.className = 'event-event-bar dynamic-color';
        
        // Use CSS Grid positioning to fill day columns properly
        bar.style.gridColumnStart = startCol + 1;
        bar.style.gridColumnEnd = endCol + 2; // +2 because grid-column-end is exclusive
        
        // Enhanced color handling with better debugging and fallback
        const eventColor = event.color || event.backgroundColor || '#3788d8';
        
        // Try multiple approaches to set the background color
        bar.style.setProperty('background-color', eventColor, 'important');
        bar.style.backgroundColor = eventColor;
        bar.setAttribute('data-color', eventColor);
        
        // Also set a CSS custom property for potential CSS usage
        bar.style.setProperty('--event-color', eventColor);
        
        if (DEBUG_MODE) {
            console.log('Event bar created:', {
                eventId: event.id,
                eventTitle: event.title,
                startCol: startCol + 1,
                endCol: endCol + 2,
                eventColor: eventColor,
                isSingleDay: this.isSameDay(eventStart, eventEnd)
            });
        }
        
        bar.setAttribute('data-event-id', event.id);
        
        // Make bar draggable if drag and drop is enabled
        if (this.options.enableDragDrop) {
            bar.draggable = true;
            bar.classList.add('draggable');
        }
        
        // Show badge and car icon if event has show_id
        const showId = event.show_id || (event.extendedProps && event.extendedProps.show_id);
        const hasShow = showId && showId !== null && showId !== '';
        
        bar.innerHTML = `
            <span class="event-bar-badge">${badgeNumber}</span>
            ${hasShow ? '<i class="fas fa-car event-car-badge" title="Car Show Event"></i>' : ''}
        `;
        
        // Add event listeners
        this.addEventBarListeners(bar, event);
        
        return bar;
    }
    
    addEventBarListeners(bar, event) {
        // Click handler
        bar.addEventListener('click', (e) => {
            e.stopPropagation();
            if (this.options.onEventClick) {
                this.options.onEventClick(event);
            }
        });
        
        // Hover handlers (only if enabled and not on mobile)
        if (this.options.showEventHover && !this.isMobileDevice()) {
            bar.addEventListener('mouseenter', (e) => {
                this.showEventHover(event, bar, e);
            });
            
            bar.addEventListener('mouseleave', (e) => {
                this.hideEventHover();
            });
        }
        
        // Drag and drop handlers (if enabled)
        if (this.options.enableDragDrop && this.checkEventEditPermission(event)) {
            bar.addEventListener('dragstart', (e) => {
                bar.classList.add('dragging');
                e.dataTransfer.setData('text/plain', JSON.stringify({
                    eventId: event.id,
                    type: 'event-event'
                }));
                e.dataTransfer.effectAllowed = 'move';
                
                if (DEBUG_MODE) {
                    console.log('Drag started for event:', event.title);
                }
            });
            
            bar.addEventListener('dragend', (e) => {
                bar.classList.remove('dragging');
                
                if (DEBUG_MODE) {
                    console.log('Drag ended for event:', event.title);
                }
            });
        }
        
        // Touch support for mobile (no hover popups on mobile)
        bar.addEventListener('touchstart', (e) => {
            // On mobile devices, we don't show hover popups to avoid interference with touch scrolling
            // Touch events are preserved for potential future touch-specific interactions
            if (DEBUG_MODE && this.isMobileDevice()) {
                console.log('Touch event on mobile device - hover disabled for better UX');
            }
        });
    }
    
    addDropZoneListeners(timeline, visibleDays) {
        timeline.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            timeline.classList.add('drop-zone');
        });
        
        timeline.addEventListener('dragenter', (e) => {
            e.preventDefault();
            timeline.classList.add('drop-zone-active');
        });
        
        timeline.addEventListener('dragleave', (e) => {
            // Only remove if we're actually leaving the timeline
            if (!timeline.contains(e.relatedTarget)) {
                timeline.classList.remove('drop-zone', 'drop-zone-active');
            }
        });
        
        timeline.addEventListener('drop', (e) => {
            e.preventDefault();
            timeline.classList.remove('drop-zone', 'drop-zone-active');
            
            try {
                const data = JSON.parse(e.dataTransfer.getData('text/plain'));
                if (data.type === 'event-event') {
                    this.handleEventDrop(data.eventId, timeline, visibleDays, e);
                }
            } catch (error) {
                if (DEBUG_MODE) {
                    console.error('Error handling drop:', error);
                }
            }
        });
    }
    
    handleEventDrop(eventId, timeline, visibleDays, dropEvent) {
        const event = this.events.find(e => e.id == eventId);
        if (!event) {
            if (DEBUG_MODE) {
                console.error('Event not found for drop:', eventId);
            }
            return;
        }
        
        // Calculate which day was dropped on
        const rect = timeline.getBoundingClientRect();
        const x = dropEvent.clientX - rect.left;
        const dayWidth = rect.width / visibleDays.length;
        const dayIndex = Math.floor(x / dayWidth);
        
        if (dayIndex >= 0 && dayIndex < visibleDays.length) {
            const newDate = visibleDays[dayIndex];
            // Parse dates from controller - already in correct timezone (user's timezone for logged-in users, Eastern for guests)
            // NO timezone conversion needed - controller handles all timezone conversion
            let originalStart = new Date(event.start);
            let originalEnd = event.end ? new Date(event.end) : originalStart;
            const duration = originalEnd.getTime() - originalStart.getTime();
            
            // Create new start and end dates
            const newStart = new Date(newDate);
            newStart.setHours(originalStart.getHours(), originalStart.getMinutes(), originalStart.getSeconds());
            
            const newEnd = new Date(newStart.getTime() + duration);
            
            if (DEBUG_MODE) {
                console.log('Event dropped:', {
                    event: event.title,
                    oldStart: originalStart,
                    newStart: newStart,
                    newEnd: newEnd
                });
            }
            
            // Call the onEventDrop callback if provided
            if (this.options.onEventDrop) {
                this.options.onEventDrop(event, newStart, newEnd);
            }
        }
    }
    
    isToday(date) {
        const today = new Date();
        return this.isSameDay(date, today);
    }

    isSameDay(date1, date2) {
        // Handle null values (blank days)
        if (date1 === null || date2 === null) {
            return false;
        }

        // Direct comparison - no timezone conversion since controller already converted

        // Fallback: Compare date components in local timezone
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }
    
    // Public API methods for compatibility
    refetchEvents() {
        // Only trigger filter system if we're not in the middle of applying filters
        if (window.calendarFilters && typeof window.calendarFilters.applyFilters === 'function') {
            // Check if filters are currently being applied to prevent loops
            if (window.calendarFilters.isApplyingFilters) {
                if (DEBUG_MODE) {
                    console.log('Event refetchEvents: Skipping filter application - filters already being applied');
                }
                return;
            }
            
            if (DEBUG_MODE) {
                console.log('Event refetchEvents: Triggering filter application');
            }
            window.calendarFilters.applyFilters();
        }
    }
    
    getEvents() {
        return this.events;
    }
    
    gotoDate(date) {
        this.currentDate = new Date(date);
        this.render();
    }
    
    // Method to load events directly without triggering filter system
    loadEventsDirectly(events) {
        console.log('=== EVENT CHART: loadEventsDirectly called ===');
        console.log('Events received:', events ? events.length : 0);
        if (DEBUG_MODE && events && events.length > 0) {
            console.log('Sample events:', events.slice(0, 3).map(e => ({
                title: e.title,
                start: e.start,
                end: e.end
            })));
        }
        this.loadEvents(events);
        console.log('=== EVENT CHART: loadEventsDirectly completed ===');
    }
    
    // Method to refresh events from filter system
    refetchEvents() {
        console.log('=== EVENT CHART: refetchEvents called by filter system ===');
        
        // Don't call back to filter system to avoid infinite loop
        // Instead, get current filter parameters and load events directly
        this.loadEventsFromAPI();
    }
    
    // Method to load events directly from API (fallback)
    loadEventsFromAPI() {
        console.log('=== EVENT CHART: Loading events directly from API ===');
        
        const currentDate = this.currentDate || new Date();
        const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        
        // Set monthEnd to end of day to ensure we capture events on the last day
        monthEnd.setHours(23, 59, 59, 999);
        
        if (DEBUG_MODE) {
            console.log('=== EVENT CHART: Date Range Debug ===');
            console.log('Current date:', currentDate);
            console.log('Month start:', monthStart);
            console.log('Month end:', monthEnd);
            console.log('Month start ISO:', monthStart.toISOString());
            console.log('Month end ISO:', monthEnd.toISOString());
            console.log('Month start date string:', monthStart.toISOString().split('T')[0]);
            console.log('Month end date string:', monthEnd.toISOString().split('T')[0]);
            console.log('=== END Date Range Debug ===');
        }
        
        // Get filter parameters if available
        let filterParams = '';
        if (window.calendarFilters && typeof window.calendarFilters.getFilterParams === 'function') {
            filterParams = window.calendarFilters.getFilterParams();
            console.log('Event chart: Using filter parameters:', filterParams);
        } else {
            // Default to all calendars if no filter system
            filterParams = 'calendars[]=1&calendars[]=2';
            console.log('Event chart: Using default parameters (all calendars)');
        }
        
        const urlParams = new URLSearchParams(filterParams);
        urlParams.set('start', monthStart.toISOString().split('T')[0]);
        // For monthly loading, send the end date with time to ensure we capture events on the last day
        urlParams.set('end', monthEnd.toISOString().split('T')[0] + ' 23:59:59');
        urlParams.set('_t', Date.now());
        
        const url = `${URLROOT}/calendar/getEvents?${urlParams.toString()}`;
        
        console.log('Event chart: Fetching from:', url);

        if (DEBUG_MODE) {
            console.log('=== EVENT CHART: Final URL Parameters ===');
            console.log('Start parameter:', urlParams.get('start'));
            console.log('End parameter:', urlParams.get('end'));
            console.log('Full URL:', url);
            console.log('=== END URL Parameters ===');
        }
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(events => {
                console.log(`=== EVENT CHART: Received ${events.length} events from API ===`);
                if (DEBUG_MODE && events.length > 0) {
                    console.log('Sample events:', events.slice(0, 3).map(e => ({
                        title: e.title,
                        start: e.start,
                        end: e.end
                    })));
                }

                // Fix timezone issue: Parse dates without timezone conversion
                events = events.map(event => {
                    // Extract just the date/time part without timezone offset
                    if (event.start && event.start.includes('T') && event.start.includes('-')) {
                        // Remove timezone offset to prevent browser conversion
                        event.start = event.start.split('-')[0] + event.start.split('-')[1].substring(0, 2) + ':' + event.start.split('-')[1].substring(3, 5);
                        // Actually, let's just take the date/time part before the timezone
                        event.start = event.start.substring(0, 19); // Gets "2025-07-01T16:00:00"
                    }
                    if (event.end && event.end.includes('T') && event.end.includes('-')) {
                        event.end = event.end.substring(0, 19);
                    }
                    return event;
                });

                // Debug Las Vegas event after fix
                const lasVegasEvent = events.find(e => e.title && e.title.includes('Las Vegas'));
                if (lasVegasEvent) {
                    console.log('=== LAS VEGAS EVENT DEBUG AFTER FIX ===');
                    console.log('Fixed event.start:', lasVegasEvent.start);

                    const startDate = new Date(lasVegasEvent.start);
                    console.log('Parsed startDate:', startDate);
                    console.log('startDate.getHours():', startDate.getHours());
                    console.log('=== END LAS VEGAS DEBUG ===');
                }

                this.loadEvents(events);
                console.log('=== EVENT CHART: API loading completed ===');
            })
            .catch(error => {
                console.error('Event chart: Error loading events from API:', error);
                // Clear events on error
                this.clearEvents();
            });
    }
    
    // Clear events method for filter system compatibility
    clearEvents() {
        if (DEBUG_MODE) {
            console.log('Event chart: clearEvents called');
        }
        this.events = [];
        this.render();
    }
    
    // Get events method for filter system compatibility
    getEvents() {
        return this.events;
    }
    
    // Debug method to check current state
    debugCurrentState() {
        console.log('=== EVENT CHART DEBUG STATE ===');
        console.log('Current date:', this.currentDate.toDateString());
        console.log('Total events loaded:', this.events.length);
        console.log('Events:', this.events.map(e => ({
            title: e.title,
            start: e.start,
            end: e.end || e.start
        })));
        
        const weeks = this.getWeeksInMonth();
        console.log('Weeks in month:', weeks.length);
        weeks.forEach((week, index) => {
            console.log(`Week ${index + 1}:`, week.map(d => d.toDateString()));
            const weekEvents = this.getEventsForWeek(week);
            console.log(`  Events in week ${index + 1}:`, weekEvents.length);
            weekEvents.forEach(e => console.log(`    - ${e.title} (${e.start} to ${e.end || e.start})`));
        });
        console.log('=== END DEBUG STATE ===');
    }
    
    // Compatibility methods for existing filter system
    get options() {
        return this._options || {
            eventSources: []
        };
    }
    
    set options(opts) {
        this._options = opts;
    }
}

// Make class available globally
window.MonthlyEventChart = MonthlyEventChart;

// Global helper functions for event actions
window.editEvent = function(eventId) {
    window.location.href = `${URLROOT}/calendar/editEvent/${eventId}`;
};

window.viewEvent = function(eventId) {
    window.location.href = `${URLROOT}/calendar/event/${eventId}`;
};

console.log('Monthly Event Chart v3.47.3 loaded successfully');