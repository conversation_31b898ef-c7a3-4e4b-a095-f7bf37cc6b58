<?php
/**
 * Simple push notification test
 */

// Prevent direct access via web browser without key
if (isset($_SERVER['HTTP_HOST'])) {
    if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'test_push_now_2025') {
        http_response_code(404);
        exit('Not Found');
    }
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';

// Initialize core classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Controller.php';

// Load models and helpers
require_once APPROOT . '/models/NotificationModel.php';
require_once APPROOT . '/models/NotificationService.php';
require_once APPROOT . '/helpers/notification_helper.php';

// Initialize notification helper
initializeNotificationHelper();

$userId = 3; // Test user ID

echo "=== PUSH NOTIFICATION TEST ===\n\n";

try {
    echo "1. SENDING IMMEDIATE PUSH NOTIFICATION\n";
    
    $title = "Test Push Notification";
    $message = "This is a test push notification sent at " . date('Y-m-d H:i:s');
    
    // Send using helper function
    $result = sendPushNotification($userId, $title, $message);
    
    echo "   Helper function result: " . ($result ? "SUCCESS" : "FAILED") . "\n";
    
    echo "\n2. SENDING VIA NOTIFICATION SERVICE\n";
    
    $notificationService = new NotificationService();
    $directResult = $notificationService->sendTestPushNotification($userId, $title, $message);
    
    echo "   Direct service result: " . ($directResult ? "SUCCESS" : "FAILED") . "\n";
    
    echo "\n3. CHECKING RECENT NOTIFICATIONS\n";
    
    $db = new Database();
    
    // Check recent push notifications
    $db->query("SELECT COUNT(*) as count FROM user_push_notifications WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 10 MINUTE)");
    $db->bind(1, $userId);
    $db->execute();
    $recentPush = $db->single()->count;
    
    echo "   Recent push notifications (last 10 min): $recentPush\n";
    
    // Check push subscriptions
    $db->query("SELECT COUNT(*) as count FROM push_subscriptions WHERE user_id = ?");
    $db->bind(1, $userId);
    $db->execute();
    $subscriptions = $db->single()->count;
    
    echo "   Total push subscriptions: $subscriptions\n";
    
    echo "\n4. DIAGNOSIS\n";
    
    if ($result && $directResult) {
        echo "✅ NOTIFICATIONS SENT SUCCESSFULLY!\n";
        echo "   - Both helper function and direct service worked\n";
        echo "   - Check your browser for push notifications\n";
        echo "   - Make sure notifications are enabled for this site\n";
        
        if ($subscriptions > 50) {
            echo "⚠️  WARNING: You have $subscriptions push subscriptions\n";
            echo "   - This is likely from testing\n";
            echo "   - Notifications are being sent to many old browser instances\n";
            echo "   - Clear browser data and re-enable notifications\n";
        }
    } else {
        echo "❌ NOTIFICATION SENDING FAILED\n";
        echo "   - Helper result: " . ($result ? "OK" : "FAILED") . "\n";
        echo "   - Service result: " . ($directResult ? "OK" : "FAILED") . "\n";
    }
    
    echo "\n5. BROWSER CHECKLIST\n";
    echo "   □ Browser notifications enabled for this site\n";
    echo "   □ Do Not Disturb mode disabled\n";
    echo "   □ Browser supports push notifications\n";
    echo "   □ Clear browser cache and re-enable notifications\n";
    echo "   □ Try different browser (Chrome, Firefox, Edge)\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== END TEST ===\n";
?>
