<?php

/**
 * Show Role Assignment System Installer
 * 
 * This script installs the show role assignment system
 * Run this once to set up the database tables and system
 * 
 * @version 1.0
 * @date 2025-07-10
 */

// Include the application bootstrap
require_once 'app/bootstrap.php';

// Set up error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Show Role Assignment System Installer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-cogs"></i> Show Role Assignment System Installer</h3>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                            
                            <?php
                            $useForeignKeys = isset($_POST['use_foreign_keys']) && $_POST['use_foreign_keys'] === '1';
                            $results = [];
                            $errors = [];
                            
                            try {
                                $db = new Database();
                                
                                // Read the appropriate SQL file
                                $sqlFile = $useForeignKeys ? 'sql/create_show_role_system.sql' : 'sql/create_show_role_system_no_fk.sql';
                                
                                if (!file_exists($sqlFile)) {
                                    throw new Exception("SQL file not found: $sqlFile");
                                }
                                
                                $sql = file_get_contents($sqlFile);
                                
                                // Split SQL into individual statements
                                $statements = array_filter(array_map('trim', explode(';', $sql)));
                                
                                foreach ($statements as $statement) {
                                    if (empty($statement) || strpos($statement, '--') === 0) {
                                        continue;
                                    }
                                    
                                    try {
                                        $db->query($statement);
                                        $db->execute();
                                        
                                        // Extract table/view name for reporting
                                        if (preg_match('/CREATE TABLE.*?`([^`]+)`/i', $statement, $matches)) {
                                            $results[] = "Created table: " . $matches[1];
                                        } elseif (preg_match('/CREATE.*?VIEW.*?`([^`]+)`/i', $statement, $matches)) {
                                            $results[] = "Created view: " . $matches[1];
                                        } elseif (preg_match('/CREATE INDEX.*?`([^`]+)`/i', $statement, $matches)) {
                                            $results[] = "Created index: " . $matches[1];
                                        } elseif (preg_match('/ALTER TABLE.*?`([^`]+)`/i', $statement, $matches)) {
                                            $results[] = "Modified table: " . $matches[1];
                                        }
                                        
                                    } catch (Exception $e) {
                                        // Some statements might fail if they already exist, that's okay
                                        if (strpos($e->getMessage(), 'already exists') === false && 
                                            strpos($e->getMessage(), 'Duplicate') === false) {
                                            $errors[] = "Error executing statement: " . $e->getMessage();
                                        }
                                    }
                                }
                                
                                // Test the installation by checking if tables exist
                                $testTables = ['show_role_requests', 'show_role_assignments'];
                                foreach ($testTables as $table) {
                                    $db->query("SHOW TABLES LIKE '$table'");
                                    $result = $db->single();
                                    if (!$result) {
                                        $errors[] = "Table $table was not created successfully";
                                    }
                                }
                                
                                // Update notification categories if needed
                                try {
                                    $db->query("SELECT notification_category FROM notification_queue WHERE notification_category = 'role_assignment' LIMIT 1");
                                    $categoryExists = $db->single();
                                    
                                    if (!$categoryExists) {
                                        // Add the new category to the enum if possible
                                        $db->query("ALTER TABLE notification_queue MODIFY COLUMN notification_category 
                                                   ENUM('event_reminder','registration_deadline','test','role_assignment') 
                                                   NOT NULL DEFAULT 'event_reminder'");
                                        $db->execute();
                                        $results[] = "Updated notification_queue table to support role_assignment category";
                                    }
                                } catch (Exception $e) {
                                    $errors[] = "Warning: Could not update notification categories. You may need to manually add 'role_assignment' to the notification_category enum in the notification_queue table.";
                                }
                                
                            } catch (Exception $e) {
                                $errors[] = "Fatal error: " . $e->getMessage();
                            }
                            ?>
                            
                            <!-- Installation Results -->
                            <?php if (!empty($results)): ?>
                                <div class="alert alert-success">
                                    <h5><i class="fas fa-check-circle"></i> Installation Successful!</h5>
                                    <ul class="mb-0">
                                        <?php foreach ($results as $result): ?>
                                            <li><?= htmlspecialchars($result) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-warning">
                                    <h5><i class="fas fa-exclamation-triangle"></i> Warnings/Errors</h5>
                                    <ul class="mb-0">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= htmlspecialchars($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (empty($errors) || count($results) > count($errors)): ?>
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> Next Steps</h5>
                                    <ol>
                                        <li><strong>Set up cron job:</strong> Add the following line to your crontab to run daily cleanup:
                                            <code class="d-block mt-2 p-2 bg-light">0 2 * * * /usr/bin/php <?= realpath('cron/cleanup_show_roles.php') ?></code>
                                        </li>
                                        <li><strong>Test the system:</strong> 
                                            <ul>
                                                <li>Go to any show and click "Role Assignments" in the management menu</li>
                                                <li>Try assigning a role to a user</li>
                                                <li>Check "My Show Roles" in the user dashboard</li>
                                            </ul>
                                        </li>
                                        <li><strong>Documentation:</strong> Read <code>docs/show_role_assignment_system.md</code> for complete usage instructions</li>
                                    </ol>
                                </div>
                                
                                <div class="text-center">
                                    <a href="<?= URLROOT ?>/user/dashboard" class="btn btn-primary">
                                        <i class="fas fa-home"></i> Go to Dashboard
                                    </a>
                                    <a href="<?= URLROOT ?>/show_roles/myRequests" class="btn btn-outline-primary">
                                        <i class="fas fa-user-tag"></i> View My Show Roles
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                        <?php else: ?>
                            
                            <!-- Installation Form -->
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> About This System</h5>
                                <p>The Show Role Assignment System allows coordinators and administrators to assign temporary, show-specific roles to users while preserving their primary system roles.</p>
                                <p><strong>Features:</strong></p>
                                <ul>
                                    <li>Per-show role assignments (coordinator, judge, staff)</li>
                                    <li>Approval workflow for coordinator assignments</li>
                                    <li>Automatic cleanup of expired assignments</li>
                                    <li>User privacy protection (coordinators use user IDs)</li>
                                    <li>Admin user search functionality</li>
                                    <li>Email notifications for all actions</li>
                                </ul>
                            </div>
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <h5>Database Configuration</h5>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="1" id="use_foreign_keys" name="use_foreign_keys" checked>
                                        <label class="form-check-label" for="use_foreign_keys">
                                            Use Foreign Key Constraints
                                        </label>
                                        <div class="form-text">
                                            Recommended for data integrity. Uncheck if your database doesn't support foreign keys.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Before Installing</h6>
                                    <ul class="mb-0">
                                        <li>Backup your database</li>
                                        <li>Ensure you have admin access to the system</li>
                                        <li>Make sure the <code>sql/</code> directory contains the installation files</li>
                                        <li>Verify your notification system is working</li>
                                    </ul>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-download"></i> Install Show Role Assignment System
                                    </button>
                                </div>
                            </form>
                            
                        <?php endif; ?>
                        
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        Show Role Assignment System v1.0 | 
                        <a href="docs/show_role_assignment_system.md" target="_blank">Documentation</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
