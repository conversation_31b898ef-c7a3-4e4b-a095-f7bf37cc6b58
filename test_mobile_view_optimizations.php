<?php
/**
 * Test Mobile View.php Optimizations
 * 
 * Tests the mobile optimizations for the message view page
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>📱 Test Mobile View.php Optimizations</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Mobile View Improvements Applied</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Issues Fixed:</h3>";
    echo "<ul>";
    echo "<li><strong>Header optimization:</strong> Removed 'Conversation:' text on mobile, just show subject</li>";
    echo "<li><strong>Message meta layout:</strong> Made sender and badges horizontal instead of stacked</li>";
    echo "<li><strong>Send Reply button:</strong> Full-width on mobile, no crowding or double lines</li>";
    echo "<li><strong>Compact styling:</strong> Smaller fonts and better spacing for mobile</li>";
    echo "<li><strong>Responsive design:</strong> Separate layouts for desktop and mobile</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    // Get messages that allow replies
    $db->query("SELECT COUNT(*) as reply_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_archived = 0
                AND (requires_reply = 1 OR message_type = 'direct')");
    $db->bind(':user_id', $userId);
    $replyResult = $db->single();
    $replyCount = $replyResult ? $replyResult->reply_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Good for testing' : '⚠️ Could use more') . "</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Messages with Reply Functionality</td>";
    echo "<td style='color: " . ($replyCount > 0 ? 'blue' : 'orange') . ";'>{$replyCount}</td>";
    echo "<td>" . ($replyCount > 0 ? '✅ Perfect for testing' : '⚠️ Need direct message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($replyCount === 0) {
        echo "<h2>📤 Creating Test Message with Reply</h2>";
        echo "<p>Creating a direct message to test the mobile view optimizations...</p>";
        
        $subject = "📱 Mobile View Test";
        $message = "This is a test message to demonstrate the mobile view optimizations including the improved header, horizontal meta layout, and compact reply button.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message, null, 'direct');
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $replyCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Mobile View Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile Testing Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile device</strong></li>";
    echo "<li><strong>Navigate to Messages</strong> and tap on a message to view it</li>";
    echo "<li><strong>Check header:</strong> Should show just subject, not 'Conversation:'</li>";
    echo "<li><strong>Check message meta:</strong> Sender and badges should be horizontal</li>";
    echo "<li><strong>Scroll to reply section:</strong> Send Reply button should be full-width</li>";
    echo "<li><strong>Test reply form:</strong> Should be mobile-friendly and not crowded</li>";
    echo "<li><strong>Check overall layout:</strong> Should look mobile-optimized</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Mobile Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Element</th><th>Before</th><th>After</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Header</strong></td>";
    echo "<td style='color: red;'>❌ 'Conversation: Subject' (too long)</td>";
    echo "<td style='color: green;'>✅ Just 'Subject' (clean)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message Meta</strong></td>";
    echo "<td style='color: red;'>❌ Sender and badges stacked vertically</td>";
    echo "<td style='color: green;'>✅ Horizontal layout with proper spacing</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Send Reply Button</strong></td>";
    echo "<td style='color: red;'>❌ Crowded, text wrapping to double line</td>";
    echo "<td style='color: green;'>✅ Full-width, clean single line</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Overall Layout</strong></td>";
    echo "<td style='color: red;'>❌ Desktop layout on mobile</td>";
    echo "<td style='color: green;'>✅ Mobile-optimized responsive design</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Implementation</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile Header Optimization:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Desktop: Show 'Conversation:' -->
<h5 class=\"mb-0 d-none d-md-block\">
    <i class=\"fas fa-comments text-primary me-2\"></i>
    Conversation: <?php echo htmlspecialchars(\$message->subject); ?>
</h5>

<!-- Mobile: Just show subject -->
<h5 class=\"mb-0 d-md-none\">
    <i class=\"fas fa-comments text-primary me-2\"></i>
    <?php echo htmlspecialchars(\$message->subject); ?>
</h5>";
    echo "</pre>";
    
    echo "<h3>🔘 Mobile Message Meta Layout:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Mobile Layout - Horizontal -->
<div class=\"d-md-none\">
    <div class=\"d-flex align-items-center flex-wrap\">
        <i class=\"fas fa-envelope me-2\" style=\"font-size: 14px;\"></i>
        <strong class=\"me-2\" style=\"font-size: 14px;\">Sender</strong>
        <span class=\"badge bg-primary me-2\" style=\"font-size: 10px;\">Original</span>
        <span class=\"badge bg-danger me-2\" style=\"font-size: 10px;\">Unread</span>
    </div>
    <small class=\"text-muted d-block mt-1\" style=\"font-size: 12px;\">
        Date and time
    </small>
</div>";
    echo "</pre>";
    
    echo "<h3>📤 Mobile Send Reply Button:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Mobile Layout - Full Width -->
<div class=\"d-md-none\">
    <button type=\"submit\" class=\"btn btn-primary w-100 mb-2\">
        <i class=\"fas fa-paper-plane me-1\"></i>Send Reply
    </button>
    <small class=\"text-muted d-block text-center\" style=\"font-size: 11px;\">
        Sent using recipient's notification preferences
    </small>
</div>";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>🎨 Mobile CSS Optimizations</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile-Specific CSS:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "@media (max-width: 768px) {
    /* Compact content */
    .message-content {
        font-size: 14px;
        padding: 0.75rem;
        line-height: 1.4;
    }
    
    /* Mobile header */
    .card-header h5 {
        font-size: 16px;
        line-height: 1.3;
    }
    
    /* Horizontal meta layout */
    .message-meta .d-flex.flex-wrap {
        gap: 0.25rem;
    }
    
    .message-meta .badge {
        font-size: 10px !important;
        padding: 0.2rem 0.4rem;
    }
    
    /* Mobile buttons */
    .btn {
        font-size: 14px;
        padding: 0.5rem 1rem;
    }
}";
    echo "</pre>";
    echo "</div>";
    
    if ($replyCount > 0) {
        echo "<h2>✅ Ready for Mobile View Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$replyCount} message(s) to test the mobile view.</p>";
        echo "<p><strong>What you should see on mobile:</strong></p>";
        echo "<ul>";
        echo "<li>📱 <strong>Clean header:</strong> Just subject, no 'Conversation:' text</li>";
        echo "<li>🔘 <strong>Horizontal meta:</strong> Sender and badges in a row, not stacked</li>";
        echo "<li>📤 <strong>Full-width reply button:</strong> No crowding or text wrapping</li>";
        echo "<li>📏 <strong>Compact design:</strong> Optimized fonts and spacing for mobile</li>";
        echo "<li>📱 <strong>Mobile-first:</strong> Responsive design that looks native</li>";
        echo "</ul>";
        echo "<p><strong>The mobile view should now look much more polished and mobile-friendly!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated file:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/notification_center/view.php</code> - Complete mobile optimization</li>";
    echo "</ul>";
    echo "<p><strong>Changes made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Removed 'Conversation:' text on mobile header</li>";
    echo "<li>✅ Added horizontal message meta layout for mobile</li>";
    echo "<li>✅ Made Send Reply button full-width on mobile</li>";
    echo "<li>✅ Added mobile-specific CSS optimizations</li>";
    echo "<li>✅ Improved responsive design with separate layouts</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Mobile view optimization test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
