<?php
/**
 * Examples of how to properly use the notification helper
 * in different contexts throughout the application
 */

// Example 1: Using in a standalone script (like this one)
// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load notification helper (it will auto-initialize dependencies)
require_once APPROOT . '/helpers/notification_helper.php';

echo "<h1>Notification Helper Usage Examples</h1>";

// Example 2: Using in a controller (where APPROOT is already defined)
function exampleControllerUsage() {
    // In a controller, you just need to include the helper
    require_once APPROOT . '/helpers/notification_helper.php';
    
    $userId = 3; // Get from your logic
    
    // Send notification respecting user preferences
    $result = sendNotification($userId, "Controller Notification", "This was sent from a controller");
    
    if ($result['success']) {
        error_log("Notification sent via: " . implode(', ', $result['sent_types']));
        return true;
    } else {
        error_log("Failed to send notification: " . implode(', ', $result['errors']));
        return false;
    }
}

// Example 3: Using in a model or service class
class ExampleService {
    public function __construct() {
        // Include helper in constructor if you'll use it multiple times
        require_once APPROOT . '/helpers/notification_helper.php';
    }
    
    public function approveUser($userId, $itemName) {
        // Your business logic here...
        
        // Send notification
        $title = "Approval Notification";
        $message = "Your $itemName has been approved!";
        
        return sendNotification($userId, $title, $message);
    }
    
    public function sendQuickToast($userId, $message) {
        return sendToastNotification($userId, "Quick Update", $message);
    }
}

// Example 4: Using in a cron job or background script
function exampleCronJobUsage() {
    // In cron jobs, make sure to define APPROOT and load config
    if (!defined('APPROOT')) {
        define('APPROOT', dirname(__FILE__));
    }
    require_once APPROOT . '/config/config.php';
    require_once APPROOT . '/helpers/notification_helper.php';
    
    // Send notifications to multiple users
    $users = [1, 2, 3, 4, 5]; // Get from database
    
    foreach ($users as $userId) {
        $result = sendNotification($userId, "Daily Reminder", "Don't forget to check your events!");
        echo "User $userId: " . ($result['success'] ? "✅" : "❌") . "\n";
    }
}

// Example 5: Real-world judge approval scenario
function approveJudgeForShow($judgeUserId, $showId, $showName) {
    require_once APPROOT . '/helpers/notification_helper.php';
    
    // Your approval logic here...
    // Update database, etc.
    
    // Send notification
    $title = "Judge Assignment Approved";
    $message = "Congratulations! You have been approved as a judge for '$showName'. Please check your dashboard for more details.";
    
    $result = sendNotification($judgeUserId, $title, $message);
    
    // Log the result
    if ($result['success']) {
        error_log("Judge approval notification sent to user $judgeUserId via: " . implode(', ', $result['sent_types']));
    } else {
        error_log("Failed to send judge approval notification to user $judgeUserId: " . implode(', ', $result['errors']));
    }
    
    return $result['success'];
}

// Example 6: Event reminder system
function sendEventReminders($eventId, $eventName, $eventDate) {
    require_once APPROOT . '/helpers/notification_helper.php';
    
    // Get all users registered for this event (your logic here)
    $registeredUsers = []; // Get from database
    
    $title = "Event Reminder";
    $message = "Reminder: '$eventName' is scheduled for $eventDate. Don't miss it!";
    
    $results = [];
    foreach ($registeredUsers as $userId) {
        $result = sendNotification($userId, $title, $message);
        $results[$userId] = $result;
    }
    
    return $results;
}

// Test the examples if accessed directly
if (isset($_GET['test'])) {
    echo "<h2>Testing Examples</h2>";
    
    $userId = 3; // Test user ID
    
    // Test basic notification
    echo "<h3>1. Basic Notification</h3>";
    $result = sendNotification($userId, "Test Notification", "This is a test message");
    echo "<pre>" . print_r($result, true) . "</pre>";
    
    // Test toast notification
    echo "<h3>2. Toast Notification</h3>";
    $success = sendToastNotification($userId, "Toast Test", "This is a toast notification");
    echo "Toast sent: " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    // Test push notification
    echo "<h3>3. Push Notification</h3>";
    $success = sendPushNotification($userId, "Push Test", "This is a push notification");
    echo "Push sent: " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    // Test service class
    echo "<h3>4. Service Class Example</h3>";
    $service = new ExampleService();
    $result = $service->approveUser($userId, "Test Item");
    echo "<pre>" . print_r($result, true) . "</pre>";
    
    // Test judge approval
    echo "<h3>5. Judge Approval Example</h3>";
    $success = approveJudgeForShow($userId, 123, "Test Car Show");
    echo "Judge approval notification: " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Notification Helper Usage Examples</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
        .example { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <h1>Notification Helper Usage Guide</h1>
    
    <div class="example">
        <h2>Quick Start</h2>
        <p>To use the notification helper anywhere in your application:</p>
        <pre><code>// Include the helper
require_once APPROOT . '/helpers/notification_helper.php';

// Send notification respecting user preferences
$result = sendNotification($userId, "Your Title", "Your message");

// Check if it was successful
if ($result['success']) {
    echo "Sent via: " . implode(', ', $result['sent_types']);
}</code></pre>
    </div>
    
    <div class="example">
        <h2>Available Functions</h2>
        <ul>
            <li><code>sendNotification($userId, $title, $message)</code> - Sends all enabled notification types</li>
            <li><code>sendToastNotification($userId, $title, $message)</code> - Sends toast only</li>
            <li><code>sendPushNotification($userId, $title, $message)</code> - Sends push only</li>
            <li><code>sendEmailNotification($userId, $title, $message)</code> - Sends email only</li>
            <li><code>sendSmsNotification($userId, $title, $message)</code> - Sends SMS only</li>
        </ul>
    </div>
    
    <div class="example">
        <h2>Test the Examples</h2>
        <p><a href="?test=1" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔔 Run Test Examples</a></p>
    </div>
    
</body>
</html>
