# Notification System Diagnosis & Fix Plan

## Current Problems to Fix

1. **Push notifications broken** - Need to identify what broke them
2. **Clunky database design** - Multiple tables for same purpose
3. **Complex user experience** - Artificial type distinctions
4. **Duplicate data** - Same message in multiple places

## Diagnosis Steps

1. Check PWA push notification setup
2. Verify notification queue processing
3. Test judge messaging flow
4. Identify broken components

## Fix Strategy

1. **Immediate**: Fix broken push notifications
2. **Short-term**: Simplify database design with proper migration
3. **Long-term**: Clean, unified system that "just works"

## Target Architecture

**Single Messages Table** → **Delivery Methods** → **User Sees "Messages"**

- One place for all communications
- Multiple delivery methods (email, push, toast, SMS)
- User sees unified message center
- Clean, maintainable code