<?php
/**
 * Test Positioning and Bell Color Fixes
 * 
 * Tests the improved positioning and bell color changes
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🎯 Test Positioning and Bell Color Fixes</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🔧 Positioning Fixes Applied</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📍 Desktop Count Position:</h3>";
    echo "<ul>";
    echo "<li><strong>Before:</strong> <code>top: -5px; right: -10px;</code> (too far away)</li>";
    echo "<li><strong>After:</strong> <code>top: 2px; right: 2px;</code> (much closer to bell)</li>";
    echo "<li><strong>Size:</strong> Reduced to <code>font-size: 12px</code> for better fit</li>";
    echo "<li><strong>Line height:</strong> Added <code>line-height: 1</code> for tighter spacing</li>";
    echo "</ul>";
    
    echo "<h3>🔔 Bell Color Changes:</h3>";
    echo "<ul>";
    echo "<li><strong>Desktop bell:</strong> Now changes to bright yellow when unread messages</li>";
    echo "<li><strong>Mobile bell:</strong> Already working, kept the same</li>";
    echo "<li><strong>Both use:</strong> <code>!important</code> to override Bootstrap</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the positioning and color fixes...</p>";
        
        $subject = "🎯 Position & Color Test";
        $message = "This tests the improved count positioning and bell color changes on both desktop and mobile.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🖥️ Desktop Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Look at the bell icon in header</strong></li>";
    echo "<li><strong>Check count position:</strong> Should be very close to bell (top-right corner)</li>";
    echo "<li><strong>Check bell color:</strong> Bell icon should turn bright yellow</li>";
    echo "<li><strong>Check count color:</strong> Number should be bright yellow with shadow</li>";
    echo "<li><strong>Overall look:</strong> Count should clearly belong to the bell</li>";
    echo "</ol>";
    
    echo "<h3>📱 Mobile Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile</strong></li>";
    echo "<li><strong>Look at bottom navigation Messages tab</strong></li>";
    echo "<li><strong>Check bell color:</strong> Should turn bright yellow</li>";
    echo "<li><strong>No count badge:</strong> Should be clean with just color change</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Visual Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Platform</th><th>Element</th><th>Expected Appearance</th></tr>";
    
    echo "<tr>";
    echo "<td rowspan='3'><strong>🖥️ Desktop</strong></td>";
    echo "<td>Bell Icon</td>";
    echo "<td style='color: #FFD700;'>🔔 Bright yellow bell</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Count Number</td>";
    echo "<td style='color: #FFD700; font-weight: bold;'>1 (bright yellow, close to bell)</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Overall</td>";
    echo "<td>Count appears to be part of the bell icon</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td rowspan='2'><strong>📱 Mobile</strong></td>";
    echo "<td>Bell Icon</td>";
    echo "<td style='color: #FFD700;'>🔔 Bright yellow bell</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Count</td>";
    echo "<td>No count displayed (clean look)</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Changes Made</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📍 Positioning (header.php):</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// CHANGED FROM:
top: -5px; right: -10px; font-size: 14px;

// TO:
top: 2px; right: 2px; font-size: 12px; line-height: 1;";
    echo "</pre>";
    
    echo "<h3>🔔 Bell Colors (notification-center.js):</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// ADDED desktop bell color change:
const desktopBellIcon = document.querySelector('.racing-notification .fas.fa-bell');
desktopBellIcon.style.setProperty('color', '#FFD700', 'important');

// KEPT mobile bell color change:
mobileBellIcon.style.setProperty('color', '#FFD700', 'important');";
    echo "</pre>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Position & Color Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>What you should see now:</strong></p>";
        echo "<ul>";
        echo "<li>🖥️ <strong>Desktop:</strong> Yellow bell + yellow number very close to bell</li>";
        echo "<li>📱 <strong>Mobile:</strong> Yellow bell (no count)</li>";
        echo "<li>🎯 <strong>Positioning:</strong> Count should look like it belongs to the bell</li>";
        echo "<li>✨ <strong>Colors:</strong> Both bells should be bright yellow</li>";
        echo "</ul>";
        echo "<p><strong>The count should now be positioned much better!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files to Copy</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated files:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/includes/header.php</code> - Better count positioning</li>";
    echo "<li><code>public/js/notification-center.js</code> - Desktop bell color change</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🎨 Visual Comparison</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<table border='1' cellpadding='10' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Before</th><th>After</th></tr>";
    echo "<tr>";
    echo "<td style='text-align: center;'>";
    echo "<div style='position: relative; display: inline-block; font-size: 20px;'>";
    echo "🔔<span style='position: absolute; top: -10px; right: -15px; color: red; font-size: 12px;'>1</span>";
    echo "</div><br>";
    echo "<small>Count far from bell</small>";
    echo "</td>";
    echo "<td style='text-align: center;'>";
    echo "<div style='position: relative; display: inline-block; font-size: 20px; color: #FFD700;'>";
    echo "🔔<span style='position: absolute; top: 2px; right: 2px; color: #FFD700; font-size: 12px; font-weight: bold;'>1</span>";
    echo "</div><br>";
    echo "<small>Count close to yellow bell</small>";
    echo "</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Position and color test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
