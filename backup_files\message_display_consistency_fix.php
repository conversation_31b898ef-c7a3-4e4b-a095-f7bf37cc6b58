<?php
// Message Display Consistency Fix
// Date: <?php echo date('Y-m-d H:i:s'); ?>

// ISSUE: 
// - Email messages were showing latest reply content (working correctly)
// - Other message types (direct, system, etc.) were showing first message content instead of latest

// SOLUTION:
// Modified views/notification_center/index.php to use latest reply content for ALL message types

// Changes made:
// 1. Added logic to determine $displayMessage (latest reply if available, otherwise root message)
// 2. Updated mobile layout to use $displayMessage->subject and $displayMessage->message
// 3. Updated desktop layout to use $displayMessage->subject and $displayMessage->message

// Files modified:
// - views/notification_center/index.php

// Result: All message types now consistently show latest reply content like emails were already doing

?>