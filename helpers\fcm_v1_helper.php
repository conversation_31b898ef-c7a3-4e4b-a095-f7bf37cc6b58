<?php
/**
 * FCM HTTP v1 API Helper
 * Uses OAuth2 instead of VAPID for push notifications
 */

class FCMv1Helper {
    
    private $serviceAccountPath;
    private $projectId;
    private $accessToken;
    private $tokenExpiry;
    
    public function __construct($serviceAccountPath) {
        $this->serviceAccountPath = $serviceAccountPath;
        
        if (!file_exists($serviceAccountPath)) {
            throw new Exception("Service account file not found: $serviceAccountPath");
        }
        
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
        if (!$serviceAccount) {
            throw new Exception("Invalid service account JSON file");
        }
        
        $this->projectId = $serviceAccount['project_id'];
    }
    
    /**
     * Get OAuth2 access token for FCM
     */
    private function getAccessToken() {
        // Check if we have a valid cached token
        if ($this->accessToken && $this->tokenExpiry > time()) {
            return $this->accessToken;
        }
        
        $serviceAccount = json_decode(file_get_contents($this->serviceAccountPath), true);
        
        // Create JWT for OAuth2
        $now = time();
        $jwtPayload = [
            'iss' => $serviceAccount['client_email'],
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => 'https://oauth2.googleapis.com/token',
            'iat' => $now,
            'exp' => $now + 3600 // 1 hour
        ];
        
        // Create JWT using RS256 (RSA with SHA-256)
        $jwt = $this->createJWT($jwtPayload, $serviceAccount['private_key']);
        
        // Exchange JWT for access token
        $tokenData = [
            'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion' => $jwt
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($tokenData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("Failed to get access token: HTTP $httpCode - $response");
        }
        
        $tokenResponse = json_decode($response, true);
        if (!$tokenResponse || !isset($tokenResponse['access_token'])) {
            throw new Exception("Invalid token response: $response");
        }
        
        $this->accessToken = $tokenResponse['access_token'];
        $this->tokenExpiry = time() + ($tokenResponse['expires_in'] - 300); // 5 min buffer
        
        return $this->accessToken;
    }
    
    /**
     * Create JWT using RS256 (simplified version)
     */
    private function createJWT($payload, $privateKey) {
        $header = json_encode(['alg' => 'RS256', 'typ' => 'JWT']);
        $payload = json_encode($payload);
        
        $headerEncoded = $this->base64UrlEncode($header);
        $payloadEncoded = $this->base64UrlEncode($payload);
        
        $signatureData = $headerEncoded . '.' . $payloadEncoded;
        
        // Sign with RSA private key
        $signature = '';
        if (!openssl_sign($signatureData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            throw new Exception('Failed to sign JWT');
        }
        
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }
    
    /**
     * Base64url encode
     */
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Send push notification using FCM HTTP v1 API
     */
    public function sendNotification($fcmToken, $title, $body, $data = []) {
        try {
            $accessToken = $this->getAccessToken();
            
            // Log the attempt
            error_log("FCM: Sending to token: " . substr($fcmToken, 0, 20) . "...");
            
            $message = [
                'message' => [
                    'token' => $fcmToken,
                    'notification' => [
                        'title' => $title,
                        'body' => $body
                    ],
                    'webpush' => [
                        'notification' => [
                            'icon' => '/public/images/icons/icon-192x192.png',
                            'badge' => '/public/images/icons/badge-72x72.png',
                            'tag' => 'fcm-' . time(),
                            'requireInteraction' => false
                        ]
                    ]
                ]
            ];
            
            if (!empty($data)) {
                $stringData = [];
                foreach ($data as $key => $value) {
                    $stringData[$key] = (string)$value;
                }
                $message['message']['data'] = $stringData;
            }
            
            $url = "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send";
            
            // Log the request
            error_log("FCM: Sending to URL: " . $url);
            error_log("FCM: Message payload: " . json_encode($message));
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // Log the response
            error_log("FCM: Response code: " . $httpCode);
            error_log("FCM: Response body: " . $response);
            
            if ($error) {
                error_log("FCM: cURL error: " . $error);
                throw new Exception("cURL error: $error");
            }
            
            if ($httpCode >= 200 && $httpCode < 300) {
                return ['success' => true, 'response' => $response];
            } else {
                error_log("FCM: HTTP error " . $httpCode . ": " . $response);
                return ['success' => false, 'error' => "HTTP $httpCode: $response"];
            }
            
        } catch (Exception $e) {
            error_log("FCM: Exception: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Send notification to multiple tokens
     */
    public function sendToMultipleTokens($fcmTokens, $title, $body, $data = []) {
        $results = [];
        
        foreach ($fcmTokens as $token) {
            $result = $this->sendNotification($token, $title, $body, $data);
            $results[] = [
                'token' => substr($token, -10) . '...',
                'success' => $result['success'],
                'error' => $result['error'] ?? null
            ];
        }
        
        return $results;
    }
    
    /**
     * Extract FCM token from push subscription endpoint
     */
    public static function extractFCMToken($endpoint) {
        if (strpos($endpoint, 'fcm.googleapis.com') !== false) {
            $parts = explode('/', $endpoint);
            return end($parts);
        }
        return null;
    }
}
?>

