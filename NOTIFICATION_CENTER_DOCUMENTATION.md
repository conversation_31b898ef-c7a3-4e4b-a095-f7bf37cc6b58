# Notification Center Documentation

## Overview
The notification center displays threaded conversations in a unified interface. It handles different message types (email, direct, system) with consistent display logic while preserving type-specific behaviors.

## Key Variables Explained

### Core Data Variables
```php
$message = $conversation['root_message'];           // The original/first message in the thread
$replies = $conversation['replies'] ?? [];         // Array of reply messages (chronological order)
$totalMessages = $conversation['total_messages'] ?? 1; // Total count of messages in this thread
$hasUnread = $conversation['has_unread'] ?? !$message->is_read; // Does thread have unread messages?
$ticketNumber = $conversation['ticket_number'] ?? null; // Email ticket number (if applicable)
```

### Display Logic Variables
```php
$displayMessage = $message; // Default: use root message for display

if (!empty($replies) && $message->message_type !== 'email') {
    // For NON-email messages: show latest reply content in list view
    $displayMessage = end($replies); // end() gets the last element (latest reply)
}
```

## Critical Display Logic

### Why Different Logic for Emails vs Other Types?

**EMAIL MESSAGES:**
- Use `$message` (root message) for display
- They have special logic elsewhere that was already working correctly
- Preserve existing functionality that shows latest reply content

**OTHER MESSAGE TYPES (direct, system, etc.):**
- Use `$displayMessage` (latest reply) for display
- This shows the most recent content instead of the original message content
- Makes all message types behave consistently

### What Uses Which Variable?

#### Always Use `$message` (Root Message):
- **Navigation links** - `href="...viewMessage/<?php echo $message->id; ?>"`
- **Sender information** - Original sender, not latest replier
- **Message metadata** - Type, ownership, show info
- **Action buttons** - Archive, delete, etc. (operate on entire thread)
- **Timestamps** - Original creation time
- **Checkboxes** - Thread selection

#### Use `$displayMessage` (Latest Reply for Non-Emails):
- **Subject display** - `<?php echo htmlspecialchars($displayMessage->subject); ?>`
- **Message preview** - `ContentCleaner::cleanMessageContent($displayMessage->message)`

## File Structure

### Main Files
- `views/notification_center/index.php` - Main list view with threading logic
- `controllers/NotificationCenterController.php` - Handles all notification actions
- `models/UnifiedMessageModel.php` - Database operations for unified messaging

### Key Methods in UnifiedMessageModel
- `getUserMessages()` - Gets threaded conversations for display
- `getMessageById()` - Gets single message with permissions
- `getMessageThread()` - Gets full conversation thread
- `markAsRead()` - Marks individual message as read
- `markAllAsRead()` - Marks all messages as read (added during cleanup)
- `sendReply()` - Sends reply with proper type inheritance
- `archiveThread()` - Archives entire conversation
- `deleteThread()` - Deletes entire conversation

## Message Types

### Email (`message_type = 'email'`)
- External emails from contact forms or email system
- Have ticket numbers for tracking
- Special display logic (use root message)
- Can be assigned to admins
- Support email-specific actions (templates, reminders)

### Direct (`message_type = 'direct'`)
- Internal messages between users
- Use latest reply for display
- Standard threading behavior

### System (`message_type = 'system'`)
- Automated system notifications
- Use latest reply for display
- Usually from system user (ID 1)

## Threading Logic

### Conversation Grouping
Messages are grouped into conversations based on:
1. **Parent-child relationships** - `parent_message_id`
2. **Ticket numbers** - For email messages
3. **Thread roots** - Finding the original message in a chain

### Display Order
- **List view**: Shows one row per conversation (thread)
- **Thread view**: Shows all messages in chronological order
- **Latest activity**: Conversations sorted by most recent activity

## CSS Classes

### Conversation Classes
```php
$conversationClasses = '';
if ($totalMessages > 1) {
    $conversationClasses .= ' conversation-thread'; // Multi-message thread styling
}
if ($ticketNumber) {
    $conversationClasses .= ' ticket-thread'; // Email ticket styling
}
```

### Unread Styling
```php
$showAsUnread = $hasUnread; // Controls unread badges and styling
```

## Security Notes

### Permission Checks
- All message access goes through `getMessageById()` with user permission checks
- Actions (archive, delete) verify user ownership
- Email management restricted to admin/coordinator roles

### Data Sanitization
- All output uses `htmlspecialchars()` for XSS protection
- Message content cleaned with `ContentCleaner::cleanMessageContent()`
- SQL queries use parameterized statements

## Debugging Tips

### Common Issues
1. **Wrong content displayed**: Check if using `$message` vs `$displayMessage`
2. **Broken threading**: Verify `parent_message_id` relationships
3. **Permission errors**: Check user access in `getMessageById()`
4. **Email logic broken**: Ensure email-specific logic uses `$message`

### Debug Variables
```php
error_log("Message ID: " . $message->id);
error_log("Display Message ID: " . $displayMessage->id);
error_log("Message Type: " . $message->message_type);
error_log("Total Messages: " . $totalMessages);
error_log("Has Unread: " . ($hasUnread ? 'YES' : 'NO'));
```

## Future Development Notes

### Adding New Message Types
1. Add type to switch statements for icons and badges
2. Decide if it should use latest reply display logic
3. Add any type-specific actions or metadata
4. Update CSS classes if needed

### Modifying Display Logic
- **BE CAREFUL** with email message logic - it has special requirements
- Always test both single messages and threaded conversations
- Verify both mobile and desktop layouts
- Check that navigation links still work properly

### Performance Considerations
- Conversation grouping happens in the model layer
- Large threads may need pagination in the future
- Consider caching for frequently accessed conversations

## Version History
- **v1.0**: Initial unified messaging system
- **v1.1**: Fixed display consistency between message types
- **v1.2**: Added comprehensive documentation and comments