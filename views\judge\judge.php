<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-0">
    <!-- Mobile-friendly header with vehicle info -->
    <div class="vehicle-header bg-dark text-white p-3 mb-3 position-relative">
        <?php if (!empty($data['images']) && isset($data['images'][0])) : ?>
            <div class="vehicle-header-bg" style="background-image: url('<?php echo URLROOT; ?>/uploads/vehicles/<?php echo $data['images'][0]->filename; ?>');"></div>
        <?php endif; ?>
        <div class="vehicle-header-content position-relative">
            <div class="d-flex align-items-center mb-2">
                <a href="<?php echo URLROOT; ?>/judge/show/<?php echo $data['show']->id; ?>" class="btn btn-sm btn-outline-light me-2">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="h4 mb-0 flex-grow-1"><?php echo $data['registration']->year; ?> <?php echo $data['registration']->make; ?> <?php echo $data['registration']->model; ?></h1>
            </div>
            <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2">#<?php echo $data['registration']->registration_number; ?></span>
                <span class="badge bg-info"><?php echo $data['registration']->category_name; ?></span>
            </div>
        </div>
    </div>
    
    <?php flash('judge_message'); ?>
            
            <?php if (isset($data['has_auto_saved']) && $data['has_auto_saved']) : ?>
                <div class="alert alert-info alert-dismissible fade show mx-2 mb-3" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Auto-saved data restored!</strong> Your previous judging session was automatically saved.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="px-2">
                <!-- Auto-save status indicator -->
                <div id="auto-save-status" class="d-flex align-items-center mb-3 text-muted small">
                    <i class="fas fa-save me-1"></i>
                    <span id="auto-save-message">Auto-save ready</span>
                </div>
                
                <!-- Vehicle Details Section -->
                <div class="vehicle-details-section mb-4">
                    <div class="row">
                        <div class="col-md-5">
                            <?php if (!empty($data['images'])) : ?>
                                <?php 
                                // Find primary image first
                                $primaryImage = isset($data['images'][0]) ? $data['images'][0] : null;
                                
                                if ($primaryImage) {
                                    // Create a unique gallery ID for this vehicle
                                    $galleryId = 'vehicle-' . $data['registration']->id;
                                    
                                    // Get the filename with fallbacks
                                    $imageFilename = isset($primaryImage->filename) ? $primaryImage->filename : 
                                                   (isset($primaryImage->file_name) ? $primaryImage->file_name : 
                                                   (isset($primaryImage->image_path) ? $primaryImage->image_path : 'default.png'));
                                    
                                    // Display the primary image
                                    echo '<div class="text-center mb-3">';
                                    echo '<a href="' . URLROOT . '/uploads/vehicles/' . $imageFilename . '" 
                                            data-image-viewer 
                                            data-gallery-id="' . $galleryId . '" 
                                            class="vehicle-image-link">';
                                    echo '<img id="main-vehicle-image" src="' . URLROOT . '/uploads/vehicles/' . $imageFilename . '" 
                                            class="img-fluid rounded shadow" alt="Vehicle" style="cursor: zoom-in;">';
                                    echo '</a>';
                                    echo '</div>';
                                    
                                    // Display thumbnails if there are multiple images
                                    if (count($data['images']) > 1) {
                                        echo '<div class="d-flex flex-wrap gap-2 justify-content-center mb-3">';
                                        foreach ($data['images'] as $index => $image) {
                                            // Get the filename with fallbacks for each image
                                            $thumbFilename = isset($image->filename) ? $image->filename : 
                                                           (isset($image->file_name) ? $image->file_name : 
                                                           (isset($image->image_path) ? $image->image_path : 'default.png'));
                                            
                                            echo '<a href="' . URLROOT . '/uploads/vehicles/' . $thumbFilename . '" 
                                                    data-image-viewer 
                                                    data-gallery-id="' . $galleryId . '" 
                                                    class="thumbnail-container" 
                                                    style="width: 60px; height: 45px; cursor: pointer;">';
                                            echo '<img src="' . URLROOT . '/uploads/vehicles/thumbnails/' . $thumbFilename . '" 
                                                    class="img-thumbnail" 
                                                    style="width: 100%; height: 100%; object-fit: cover;" 
                                                    alt="Thumbnail">';
                                            echo '</a>';
                                        }
                                        echo '</div>';
                                    }
                                }
                                ?>
                            <?php else : ?>
                                <!-- Try to get primary image from registration -->
                                <?php 
                                $primaryImageFound = false;
                                
                                // First check if we have a primary image in the images array
                                if (!empty($data['images'])) {
                                    foreach ($data['images'] as $image) {
                                        if (isset($image->is_primary) && $image->is_primary) {
                                            $primaryImageFound = true;
                                            $imageFilename = isset($image->filename) ? $image->filename : 
                                                           (isset($image->file_name) ? $image->file_name : 
                                                           (isset($image->image_path) ? $image->image_path : ''));
                                            ?>
                                            <div class="text-center mb-3">
                                                <img src="<?php echo URLROOT; ?>/uploads/vehicles/<?php echo $imageFilename; ?>" 
                                                     class="img-fluid rounded shadow" alt="Vehicle">
                                            </div>
                                            <?php
                                            break;
                                        }
                                    }
                                    
                                    // If no primary image was found but we have images, use the first one
                                    if (!$primaryImageFound && isset($data['images'][0])) {
                                        $primaryImageFound = true;
                                        $imageFilename = isset($data['images'][0]->filename) ? $data['images'][0]->filename : 
                                                       (isset($data['images'][0]->file_name) ? $data['images'][0]->file_name : 
                                                       (isset($data['images'][0]->image_path) ? $data['images'][0]->image_path : ''));
                                        ?>
                                        <div class="text-center mb-3">
                                            <img src="<?php echo URLROOT; ?>/uploads/vehicles/<?php echo $imageFilename; ?>" 
                                                 class="img-fluid rounded shadow" alt="Vehicle">
                                        </div>
                                        <?php
                                    }
                                }
                                
                                // If still no image found, try to get the primary image from the vehicle
                                if (!$primaryImageFound && isset($data['registration']->vehicle_id)) {
                                    // We need to fetch the vehicle's primary image
                                    $db = new Database();
                                    $db->query('SELECT primary_image FROM vehicles WHERE id = :vehicle_id AND primary_image IS NOT NULL AND primary_image != ""');
                                    $db->bind(':vehicle_id', $data['registration']->vehicle_id);
                                    $vehicle = $db->single();
                                    
                                    if ($vehicle && isset($vehicle->primary_image) && !empty($vehicle->primary_image)) {
                                        $primaryImageFound = true;
                                        ?>
                                        <div class="text-center mb-3">
                                            <img src="<?php echo URLROOT; ?>/uploads/vehicles/<?php echo $vehicle->primary_image; ?>" 
                                                 class="img-fluid rounded shadow" alt="Vehicle">
                                        </div>
                                        <?php
                                    }
                                }
                                
                                // If no primary image was found, show the default car icon
                                if (!$primaryImageFound) : ?>
                                    <div class="bg-secondary text-white d-flex align-items-center justify-content-center rounded mb-3 shadow" style="height: 200px;">
                                        <i class="fas fa-car fa-3x"></i>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-7">
                            <div class="vehicle-specs">
                                <!-- Display Number Card (First for prominence) -->
                                <div class="spec-item display-number-card">
                                    <div class="spec-icon"><i class="fas fa-id-card"></i></div>
                                    <div class="spec-label">Display Number</div>
                                    <div class="spec-value">
                                        <?php 
                                        if (isset($data['registration']->display_number) && !empty($data['registration']->display_number)) {
                                            echo '<span class="display-number-badge">' . $data['registration']->display_number . '</span>';
                                        } else {
                                            echo '<span class="text-muted">Not assigned</span>';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <!-- Registration Number Card -->
                                <div class="spec-item">
                                    <div class="spec-icon"><i class="fas fa-hashtag"></i></div>
                                    <div class="spec-label">Registration</div>
                                    <div class="spec-value"><?php echo $data['registration']->registration_number; ?></div>
                                </div>
                                <div class="spec-item">
                                    <div class="spec-icon"><i class="fas fa-tag"></i></div>
                                    <div class="spec-label">Category</div>
                                    <div class="spec-value"><?php echo $data['registration']->category_name; ?></div>
                                </div>
                                <div class="spec-item">
                                    <div class="spec-icon"><i class="fas fa-check-circle"></i></div>
                                    <div class="spec-label">Status</div>
                                    <div class="spec-value">
                                        <?php if (isset($data['registration']->checked_in) && $data['registration']->checked_in) : ?>
                                            <span class="badge bg-success">Checked In</span>
                                        <?php else : ?>
                                            <span class="badge bg-secondary">Not Checked In</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (isset($data['registration']->payment_status) && ($data['registration']->payment_status == 'completed' || $data['registration']->payment_status == 'paid' || $data['registration']->payment_status == 'free')) : ?>
                                <div class="d-grid gap-2 mt-3">
                                    <?php if (!isset($data['registration']->checked_in) || !$data['registration']->checked_in) : ?>
                                        <form action="<?php echo URLROOT; ?>/judge/checkIn/<?php echo $data['registration']->id; ?>" method="post">
                                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                            <button type="submit" class="btn btn-success w-100">
                                                <i class="fas fa-clipboard-check me-1"></i> Check In Vehicle
                                            </button>
                                        </form>
                                    <?php else : ?>
                                        <form action="<?php echo URLROOT; ?>/judge/undoCheckIn/<?php echo $data['registration']->id; ?>" method="post">
                                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                            <button type="submit" class="btn btn-warning w-100">
                                                <i class="fas fa-undo me-1"></i> Undo Check-in
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($data['registration']->description) || !empty($data['registration']->modifications)) : ?>
                                <div class="vehicle-details mt-3">
                                    <div class="accordion" id="vehicleDetailsAccordion">
                                        <?php if (!empty($data['registration']->description)) : ?>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="descriptionHeading">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#descriptionCollapse" aria-expanded="false" aria-controls="descriptionCollapse">
                                                        <i class="fas fa-info-circle me-2"></i> Description
                                                    </button>
                                                </h2>
                                                <div id="descriptionCollapse" class="accordion-collapse collapse" aria-labelledby="descriptionHeading" data-bs-parent="#vehicleDetailsAccordion">
                                                    <div class="accordion-body">
                                                        <?php echo $data['registration']->description; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($data['registration']->modifications)) : ?>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="modificationsHeading">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#modificationsCollapse" aria-expanded="false" aria-controls="modificationsCollapse">
                                                        <i class="fas fa-wrench me-2"></i> Modifications
                                                    </button>
                                                </h2>
                                                <div id="modificationsCollapse" class="accordion-collapse collapse" aria-labelledby="modificationsHeading" data-bs-parent="#vehicleDetailsAccordion">
                                                    <div class="accordion-body">
                                                        <?php echo $data['registration']->modifications; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            

            
            <form action="<?php echo URLROOT; ?>/judge/judge/<?php echo $data['registration']->id; ?>" method="post" id="judgingForm">
                <?php echo csrfTokenField(); ?>
                
                <div class="card shadow-sm mb-4 mx-2">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Judging Form</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($data['metrics'])) : ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No judging metrics have been defined for this show.
                            </div>
                        <?php else : ?>
                            <div class="progress mb-4" style="height: 8px;">
                                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" id="judging-progress-bar"></div>
                            </div>
                            <div class="text-end mb-3">
                                <span class="badge bg-secondary" id="judging-progress-text">0% Complete</span>
                            </div>
                            <?php foreach ($data['metrics'] as $metric) : ?>
                                <?php 
                                $currentScore = 0; // Default score is 0 (not judged yet)
                                $currentComments = '';
                                
                                // Check if we have existing scores
                                foreach ($data['scores'] as $score) {
                                    if ($score->metric_id == $metric->id) {
                                        $currentScore = $score->score;
                                        $currentComments = $score->comments;
                                        break;
                                    }
                                }
                                ?>
                                <div class="metric-card mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <label for="metric_<?php echo $metric->id; ?>" class="form-label fw-bold mb-0">
                                            <?php echo $metric->name; ?>
                                        </label>
                                        <span class="badge bg-primary">Max: <?php echo $metric->max_score; ?> points</span>
                                    </div>
                                    
                                    <?php if ($metric->description) : ?>
                                        <p class="text-muted small mb-2"><?php echo $metric->description; ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="score-input-group">
                                        <div class="d-flex align-items-center">
                                            <button type="button" class="btn btn-outline-secondary score-decrement" data-target="metric_<?php echo $metric->id; ?>">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            
                                            <div class="score-display-container mx-2 flex-grow-1">
                                                <input type="range" class="form-range score-slider" 
                                                    id="metric_slider_<?php echo $metric->id; ?>" 
                                                    min="1" max="<?php echo $metric->max_score; ?>" step="1"
                                                    value="<?php echo $currentScore; ?>"
                                                    data-target="metric_<?php echo $metric->id; ?>">
                                                
                                                <div class="score-value-display text-center fw-bold fs-4" id="display_metric_<?php echo $metric->id; ?>">
                                                    <?php echo $currentScore; ?>
                                                </div>
                                            </div>
                                            
                                            <button type="button" class="btn btn-outline-secondary score-increment" data-target="metric_<?php echo $metric->id; ?>">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        
                                        <input type="hidden" id="metric_<?php echo $metric->id; ?>" 
                                               name="score_<?php echo $metric->id; ?>" 
                                               value="<?php echo $currentScore; ?>" 
                                               <?php echo ($currentScore > 0) ? 'data-was-modified="true"' : ''; ?>
                                               required>
                                               
                                        <?php if (isset($data['errors']['score_' . $metric->id])) : ?>
                                            <div class="invalid-feedback d-block">
                                                <?php echo $data['errors']['score_' . $metric->id]; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <label for="comments_<?php echo $metric->id; ?>" class="form-label">Comments:</label>
                                        <textarea class="form-control" id="comments_<?php echo $metric->id; ?>" 
                                                  name="comments_<?php echo $metric->id; ?>" 
                                                  rows="2" placeholder="Add your comments here..."
                                                  <?php echo (!empty($currentComments)) ? 'data-has-content="true"' : ''; ?>><?php echo $currentComments; ?></textarea>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check-circle me-2"></i> Submit Final Scores
                            </button>
                            <button type="submit" name="save_draft" class="btn btn-outline-secondary">
                                <i class="fas fa-save me-2"></i> Save as Draft
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Custom styles for mobile-friendly judging -->
<style>
/* Mobile-first styles */
.vehicle-header {
    overflow: hidden;
}

.vehicle-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    filter: blur(5px);
}

/* Image viewer styles */
.vehicle-image-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.vehicle-image-link:hover {
    opacity: 0.95;
}

.thumbnail-container {
    display: block;
    text-decoration: none;
    border: 2px solid transparent;
    transition: border-color 0.2s ease;
}

.thumbnail-container:hover {
    border-color: #0d6efd;
}

.metric-card {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card.active {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #0d6efd;
}

.score-display-container {
    position: relative;
    padding: 10px 0;
}

.score-value-display {
    margin-top: 5px;
    color: #0d6efd;
    transition: transform 0.2s ease, color 0.2s ease;
}

.score-value-display.changed {
    transform: scale(1.2);
    color: #198754;
}

.vehicle-specs {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

/* Enhanced Display Number Styling */
.display-number-card {
    position: relative;
    z-index: 1;
    border-left: 4px solid #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
    padding-left: 8px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.display-number-card .spec-icon {
    color: #0d6efd;
    font-size: 1.2em;
}

.display-number-card .spec-label {
    font-weight: 600;
    color: #0d6efd;
}

.display-number-badge {
    display: inline-block;
    font-size: 1.3em;
    font-weight: bold;
    color: #fff;
    background-color: #0d6efd;
    padding: 5px 14px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
}

.display-number-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
    gap: 15px;
    margin-top: 15px;
}

.spec-item {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.spec-icon {
    font-size: 24px;
    color: #0d6efd;
    margin-bottom: 5px;
}

.spec-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.spec-value {
    font-weight: bold;
}

/* Responsive adjustments */
@media (min-width: 768px) {
    .container-fluid {
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .vehicle-specs {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .vehicle-specs {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Floating action button for quick save */
.floating-save-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #198754;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    z-index: 1000;
    transition: transform 0.2s ease, background-color 0.2s ease;
    transform: scale(0);
}

.floating-save-btn.visible {
    transform: scale(1);
}

.floating-save-btn:hover {
    background-color: #146c43;
    transform: scale(1.1);
}

.floating-save-btn:active {
    transform: scale(0.95);
}
</style>

<!-- Custom JavaScript for interactive scoring -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add floating action button for quick save
    const floatingBtn = document.createElement('button');
    floatingBtn.type = 'button';
    floatingBtn.className = 'floating-save-btn';
    floatingBtn.innerHTML = '<i class="fas fa-save fa-lg"></i>';
    floatingBtn.setAttribute('data-bs-toggle', 'tooltip');
    floatingBtn.setAttribute('data-bs-placement', 'left');
    floatingBtn.setAttribute('title', 'Quick Save as Draft');
    document.body.appendChild(floatingBtn);
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto-save status elements
    const autoSaveStatus = document.getElementById('auto-save-status');
    const autoSaveMessage = document.getElementById('auto-save-message');
    
    // Auto-save configuration
    const AUTO_SAVE_INTERVAL = 30000; // 30 seconds
    let autoSaveTimer = null;
    let lastAutoSaveTime = null;
    let formChanged = false;
    
    // Handle floating button click
    floatingBtn.addEventListener('click', function() {
        // Add hidden input for draft
        const draftInput = document.createElement('input');
        draftInput.type = 'hidden';
        draftInput.name = 'save_draft';
        draftInput.value = '1';
        document.getElementById('judgingForm').appendChild(draftInput);
        
        // Submit the form
        document.getElementById('judgingForm').submit();
    });
    
    // Track changes to show/hide floating button
    function checkFormChanged() {
        if (formChanged) {
            floatingBtn.classList.add('visible');
            
            // Start auto-save timer if not already running
            if (autoSaveTimer === null) {
                startAutoSaveTimer();
            }
        } else {
            floatingBtn.classList.remove('visible');
        }
    }
    
    // Auto-save function
    function autoSaveForm() {
        if (!formChanged) {
            updateAutoSaveStatus('No changes to save');
            return;
        }
        
        updateAutoSaveStatus('Saving...', 'text-warning');
        
        // Create form data
        const formData = new FormData(document.getElementById('judgingForm'));
        formData.append('auto_save', '1');
        
        // Send AJAX request
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                lastAutoSaveTime = data.timestamp;
                updateAutoSaveStatus('Last saved at ' + formatTime(lastAutoSaveTime), 'text-success');
                setTimeout(() => {
                    updateAutoSaveStatus('Auto-save enabled', 'text-muted');
                }, 3000);
            } else {
                updateAutoSaveStatus('Auto-save failed: ' + data.message, 'text-danger');
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
            updateAutoSaveStatus('Auto-save failed', 'text-danger');
        });
    }
    
    // Format time for display
    function formatTime(timeString) {
        // Convert UTC database timestamp to local timezone
        let date;
        if (window.TimezoneHelper && window.TimezoneHelper.parseMySQLDateTime) {
            date = window.TimezoneHelper.parseMySQLDateTime(timeString);
        } else {
            // Fallback: assume UTC and convert to local
            date = new Date(timeString + (timeString.includes('T') ? '' : 'T00:00:00Z'));
        }
        // Manual formatting to avoid locale conversion
        const hours = date.getHours();
        const minutes = date.getMinutes();
        const ampm = hours >= 12 ? 'PM' : 'AM';
        const displayHours = hours % 12 || 12;
        return displayHours + ':' + String(minutes).padStart(2, '0') + ' ' + ampm;
    }
    
    // Update auto-save status display
    function updateAutoSaveStatus(message, className = '') {
        autoSaveMessage.textContent = message;
        
        // Reset classes
        autoSaveStatus.className = 'd-flex align-items-center mb-3 small';
        
        // Add new class if provided
        if (className) {
            autoSaveStatus.classList.add(className);
        } else {
            autoSaveStatus.classList.add('text-muted');
        }
    }
    
    // Start auto-save timer
    function startAutoSaveTimer() {
        updateAutoSaveStatus('Auto-save enabled');
        
        // Clear any existing timer
        if (autoSaveTimer) {
            clearInterval(autoSaveTimer);
        }
        
        // Set new timer
        autoSaveTimer = setInterval(autoSaveForm, AUTO_SAVE_INTERVAL);
        
        // Also save when user leaves the page
        window.addEventListener('beforeunload', autoSaveForm);
    }
    
    // Initialize all sliders
    document.querySelectorAll('.score-slider').forEach(slider => {
        const targetId = slider.dataset.target;
        const hiddenInput = document.getElementById(targetId);
        const displayElement = document.getElementById('display_' + targetId);
        const metricCard = slider.closest('.metric-card');
        
        // Update display and hidden input when slider changes
        slider.addEventListener('input', function() {
            hiddenInput.value = this.value;
            hiddenInput.dataset.wasModified = 'true';
            displayElement.textContent = this.value;
            
            // Visual feedback
            displayElement.classList.add('changed');
            setTimeout(() => {
                displayElement.classList.remove('changed');
            }, 300);
            
            formChanged = true;
            checkFormChanged();
            updateProgress();
        });
        
        // Focus effect on metric card
        slider.addEventListener('focus', function() {
            metricCard.classList.add('active');
        });
        
        slider.addEventListener('blur', function() {
            metricCard.classList.remove('active');
        });
    });
    
    // Handle increment buttons
    document.querySelectorAll('.score-increment').forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const hiddenInput = document.getElementById(targetId);
            const slider = document.getElementById('metric_slider_' + targetId.split('_')[1]);
            const displayElement = document.getElementById('display_' + targetId);
            
            let currentValue = parseInt(hiddenInput.value) || 0;
            const maxValue = parseInt(slider.max);
            
            if (currentValue < maxValue) {
                currentValue++;
                hiddenInput.value = currentValue;
                hiddenInput.dataset.wasModified = 'true';
                slider.value = currentValue;
                displayElement.textContent = currentValue;
                
                // Visual feedback
                displayElement.classList.add('changed');
                setTimeout(() => {
                    displayElement.classList.remove('changed');
                }, 300);
                
                formChanged = true;
                checkFormChanged();
                updateProgress();
            }
        });
    });
    
    // Handle decrement buttons
    document.querySelectorAll('.score-decrement').forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const hiddenInput = document.getElementById(targetId);
            const slider = document.getElementById('metric_slider_' + targetId.split('_')[1]);
            const displayElement = document.getElementById('display_' + targetId);
            
            let currentValue = parseInt(hiddenInput.value) || 0;
            const minValue = parseInt(slider.min);
            
            if (currentValue > minValue) {
                currentValue--;
                hiddenInput.value = currentValue;
                hiddenInput.dataset.wasModified = 'true';
                slider.value = currentValue;
                displayElement.textContent = currentValue;
                
                // Visual feedback
                displayElement.classList.add('changed');
                setTimeout(() => {
                    displayElement.classList.remove('changed');
                }, 300);
                
                formChanged = true;
                checkFormChanged();
                updateProgress();
            }
        });
    });
    
    // Handle textarea changes
    document.querySelectorAll('textarea').forEach(textarea => {
        textarea.addEventListener('input', function() {
            // Get the related metric ID from the textarea name
            const metricId = this.name.split('_')[1];
            const hiddenInput = document.getElementById('metric_' + metricId);
            if (hiddenInput) {
                hiddenInput.dataset.wasModified = 'true';
            }
            
            formChanged = true;
            checkFormChanged();
            updateProgress();
        });
    });
    
    // Update progress bar
    function updateProgress() {
        const totalMetrics = document.querySelectorAll('.metric-card').length;
        let completedMetrics = 0;
        let metricsWithScores = new Set();
        
        // Count metrics with scores that have been actively set (not default values)
        document.querySelectorAll('[id^="metric_"]:not([id^="metric_slider_"])').forEach(input => {
            const metricId = input.id.split('_')[1];
            // Only count if the score has been changed from default or has comments
            const relatedTextarea = document.querySelector(`textarea[name="comments_${metricId}"]`);
            const hasComments = relatedTextarea && (relatedTextarea.value.trim().length > 0 || relatedTextarea.dataset.hasContent === 'true');
            
            // Check if this is a score that was loaded from the database or set by the user
            const scoreWasSet = input.dataset.wasModified === 'true' || hasComments;
            
            if (parseInt(input.value) > 0 && scoreWasSet) {
                metricsWithScores.add(metricId);
                completedMetrics++;
            }
        });
        
        const progressPercent = Math.round((completedMetrics / totalMetrics) * 100);
        
        // Update progress bar
        const progressBar = document.getElementById('judging-progress-bar');
        const progressText = document.getElementById('judging-progress-text');
        
        progressBar.style.width = progressPercent + '%';
        progressBar.setAttribute('aria-valuenow', progressPercent);
        progressText.textContent = progressPercent + '% Complete';
        
        // Update progress bar color
        if (progressPercent < 30) {
            progressBar.className = 'progress-bar bg-danger';
        } else if (progressPercent < 70) {
            progressBar.className = 'progress-bar bg-warning';
        } else {
            progressBar.className = 'progress-bar bg-success';
        }
    }
    
    // Initialize progress on page load
    updateProgress();
    
    // If auto-saved data was restored, update the form changed state
    <?php if (isset($data['has_auto_saved']) && $data['has_auto_saved']) : ?>
    // Check if any scores were loaded
    let hasLoadedScores = false;
    document.querySelectorAll('[id^="metric_"]:not([id^="metric_slider_"])').forEach(input => {
        if (parseInt(input.value) > 0) {
            hasLoadedScores = true;
            formChanged = true;
        }
    });
    
    if (hasLoadedScores) {
        checkFormChanged();
        updateProgress();
    }
    <?php endif; ?>
    
    // Start auto-save timer if there are existing scores
    if (document.querySelectorAll('.score-slider[value]:not([value="0"])').length > 0 || 
        document.querySelectorAll('textarea:not(:empty)').length > 0) {
        formChanged = true;
        checkFormChanged();
    }
    
    // Initialize image viewer if available
    if (typeof initImageViewer === 'function') {
        setTimeout(function() {
            initImageViewer();
        }, 100);
    }
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>