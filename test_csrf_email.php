<?php
/**
 * Test CSRF Token for Email Test Functionality
 * 
 * This file tests the CSRF token validation for the email test feature.
 */

// Start session and include necessary files
session_start();

// Define constants
define('APPROOT', dirname(__FILE__));
define('BASE_URL', 'https://events.rowaneliterides.com');
define('DEBUG_MODE', true);

// Include required files
require_once APPROOT . '/helpers/csrf_helper.php';

echo "<h1>🔒 CSRF Token Test for Email Functionality</h1>";

echo "<h2>📋 Current Session Status</h2>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";

echo "<h2>🎯 CSRF Token Generation</h2>";

// Generate a CSRF token
$token1 = generateCsrfToken();
echo "<p><strong>Generated Token 1:</strong> " . htmlspecialchars($token1) . "</p>";

// Generate another token (should be the same)
$token2 = generateCsrfToken();
echo "<p><strong>Generated Token 2:</strong> " . htmlspecialchars($token2) . "</p>";

echo "<p><strong>Tokens Match:</strong> " . ($token1 === $token2 ? '✅ Yes' : '❌ No') . "</p>";

echo "<h2>🧪 CSRF Token Verification Tests</h2>";

// Test 1: Verify with correct token from POST
$_POST['csrf_token'] = $token1;
$result1 = verifyCsrfToken();
echo "<p><strong>Test 1 - POST verification:</strong> " . ($result1 ? '✅ Pass' : '❌ Fail') . "</p>";

// Test 2: Verify with correct token from header
unset($_POST['csrf_token']);
$_SERVER['HTTP_X_CSRF_TOKEN'] = $token1;
$result2 = verifyCsrfToken(null, 'header');
echo "<p><strong>Test 2 - Header verification:</strong> " . ($result2 ? '✅ Pass' : '❌ Fail') . "</p>";

// Test 3: Test AJAX detection and validation
$_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
$result3 = validateCsrfToken();
echo "<p><strong>Test 3 - AJAX validation:</strong> " . ($result3 ? '✅ Pass' : '❌ Fail') . "</p>";

// Test 4: Test with wrong token
$_SERVER['HTTP_X_CSRF_TOKEN'] = 'wrong_token';
$result4 = verifyCsrfToken(null, 'header');
echo "<p><strong>Test 4 - Wrong token:</strong> " . ($result4 ? '❌ Should Fail' : '✅ Correctly Failed') . "</p>";

echo "<h2>🌐 Simulated AJAX Request Test</h2>";

// Reset to correct token
$_SERVER['HTTP_X_CSRF_TOKEN'] = $token1;
$_POST['csrf_token'] = $token1;

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Simulated Request Headers:</h3>";
echo "<pre>";
echo "X-Requested-With: XMLHttpRequest\n";
echo "X-CSRF-Token: " . htmlspecialchars($token1) . "\n";
echo "Content-Type: application/x-www-form-urlencoded\n";
echo "</pre>";

echo "<h3>Simulated POST Data:</h3>";
echo "<pre>";
echo "email: <EMAIL>\n";
echo "csrf_token: " . htmlspecialchars($token1) . "\n";
echo "</pre>";

echo "<h3>Validation Results:</h3>";
$ajaxResult = validateCsrfToken();
echo "<p><strong>validateCsrfToken():</strong> " . ($ajaxResult ? '✅ Pass' : '❌ Fail') . "</p>";

$headerResult = verifyCsrfToken(null, 'header');
echo "<p><strong>verifyCsrfToken(header):</strong> " . ($headerResult ? '✅ Pass' : '❌ Fail') . "</p>";

$postResult = verifyCsrfToken(null, 'post');
echo "<p><strong>verifyCsrfToken(post):</strong> " . ($postResult ? '✅ Pass' : '❌ Fail') . "</p>";
echo "</div>";

echo "<h2>🔧 Debug Information</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Server Variables (CSRF related):</h3>";
echo "<pre>";
echo "HTTP_X_REQUESTED_WITH: " . ($_SERVER['HTTP_X_REQUESTED_WITH'] ?? 'Not set') . "\n";
echo "HTTP_X_CSRF_TOKEN: " . ($_SERVER['HTTP_X_CSRF_TOKEN'] ?? 'Not set') . "\n";
echo "</pre>";

echo "<h3>POST Data:</h3>";
echo "<pre>";
print_r($_POST);
echo "</pre>";
echo "</div>";

echo "<h2>✅ Test Summary</h2>";
echo "<div style='background: " . ($ajaxResult ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
if ($ajaxResult) {
    echo "<h3 style='color: #155724;'>🎉 CSRF Token System is Working!</h3>";
    echo "<p style='color: #155724;'>The CSRF token validation should work correctly for the email test functionality.</p>";
} else {
    echo "<h3 style='color: #721c24;'>❌ CSRF Token System has Issues</h3>";
    echo "<p style='color: #721c24;'>There may be an issue with the CSRF token validation that needs to be fixed.</p>";
}
echo "</div>";

echo "<h2>🚀 Next Steps</h2>";
echo "<ol>";
echo "<li>Copy this file to your server</li>";
echo "<li>Access it via browser to run the tests</li>";
echo "<li>If tests pass, try the email test functionality again</li>";
echo "<li>If tests fail, check the debug information above</li>";
echo "</ol>";

// Clean up test data
unset($_POST['csrf_token']);
unset($_SERVER['HTTP_X_CSRF_TOKEN']);
unset($_SERVER['HTTP_X_REQUESTED_WITH']);

// Self-delete after 5 minutes
if (file_exists(__FILE__) && (time() - filemtime(__FILE__)) > 300) {
    unlink(__FILE__);
}
?>
