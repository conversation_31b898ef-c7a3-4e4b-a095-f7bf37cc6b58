# Show Roles System Implementation

## Overview

The Show Roles System provides per-show role assignments with an approval workflow for coordinators, judges, and staff. This system allows show coordinators and administrators to assign roles to users for specific shows, with automatic cleanup after show completion.

## Features

### Core Functionality
- **Per-show role assignments** - Roles are specific to individual shows, not system-wide
- **Approval workflow** - Users must approve role assignment requests
- **Automatic cleanup** - Role assignments are automatically removed 1 week after show end date
- **Notification system** - Users receive notifications about role requests and assignments
- **Admin bypass** - Administrators can assign roles directly without approval

### Supported Roles
- **Coordinator** - Can manage show details, registrations, and assign other roles
- **Judge** - Can score vehicles and access judging interface
- **Staff** - Can assist with show operations and registrations

## Database Structure

### Tables Created

#### `show_role_requests`
Stores role assignment requests that require user approval.

**Key Fields:**
- `show_id` - The show for which the role is requested
- `user_id` - The user being assigned the role
- `requested_role` - The role being requested (coordinator, judge, staff)
- `requested_by` - The admin/coordinator making the request
- `status` - Request status (pending, approved, declined, expired)
- `expires_at` - When the request expires if not responded to

#### `show_role_assignments`
Stores active, approved role assignments.

**Key Fields:**
- `show_id` - The show for which the role is assigned
- `user_id` - The user with the assigned role
- `assigned_role` - The assigned role (coordinator, judge, staff)
- `assigned_by` - The admin/coordinator who made the assignment
- `is_active` - Whether the assignment is currently active
- `expires_at` - When the assignment expires (show end date + 1 day)
- `auto_cleanup_date` - When the assignment will be automatically removed (show end date + 1 week)

#### `user_show_roles` (View)
Provides an easy way to query user show roles with joined data from users and shows tables.

## File Structure

### Controllers
- `controllers/ShowRolesController.php` - Main controller handling all role management operations

### Models
- `models/ShowRoleModel.php` - Database operations for role requests and assignments

### Views
- `views/show_roles/manage.php` - Role management interface for coordinators/admins
- `views/show_roles/admin_overview.php` - System-wide role overview for administrators
- `views/show_roles/my_requests.php` - User interface to view and respond to role requests

### SQL Scripts
- `sql/create_show_role_system.sql` - Complete database setup with foreign keys
- `sql/create_show_role_system_no_fk.sql` - Database setup without foreign keys (for compatibility)

## Installation

### Automatic Installation
Run the installation script:
```
/install_show_roles_system.php
```

### Manual Installation
Execute the SQL script:
```sql
-- Run either create_show_role_system.sql or create_show_role_system_no_fk.sql
-- depending on your database configuration
```

### Optional: Notification Enhancement
To enable role assignment notifications, update the notification_queue table:
```sql
ALTER TABLE notification_queue MODIFY COLUMN notification_category 
ENUM('event_reminder','registration_deadline','test','role_assignment') NOT NULL DEFAULT 'event_reminder';
```

## Usage

### For Administrators

#### System Overview
Access the admin overview at: `/show_roles/admin_overview`
- View all role assignments across all shows
- See pending requests requiring attention
- Access bulk management tools

#### Assigning Roles
1. Navigate to a show's role management: `/show_roles/manage/{show_id}`
2. Use the "Assign New Role" form
3. Search for users (admins can search by name/email, coordinators need user ID)
4. Select the role to assign
5. Choose assignment type:
   - **Direct Assignment** - Immediate assignment (admin only)
   - **Request Approval** - Sends request to user for approval

### For Coordinators

#### Managing Show Roles
1. Access your show's role management from the coordinator dashboard
2. Assign judges and staff using user ID lookup
3. All assignments require user approval (except admin assignments)
4. Monitor pending requests and active assignments

### For Users

#### Responding to Role Requests
1. Check notifications for role assignment requests
2. Visit `/show_roles/my_requests` to see all pending requests
3. Review request details and respond with approval or decline
4. Optionally provide a response message

## API Endpoints

### User Search (Admin Only)
- **URL:** `/show_roles/searchUsers`
- **Method:** POST
- **Purpose:** Search for users by name, email, or ID

### Role Assignment
- **URL:** `/show_roles/assign`
- **Method:** POST
- **Purpose:** Create role assignment or request

### Role Removal
- **URL:** `/show_roles/remove`
- **Method:** POST
- **Purpose:** Remove active role assignment

### Request Response
- **URL:** `/show_roles/respond`
- **Method:** POST
- **Purpose:** Approve or decline role requests

## Security Features

- **Authentication required** - All endpoints require user login
- **Role-based access control** - Different permissions for admins, coordinators, and users
- **CSRF protection** - All forms include CSRF tokens
- **Input validation** - All user inputs are validated and sanitized
- **Access verification** - Users can only manage shows they coordinate or have admin access

## Automatic Cleanup

The system includes automatic cleanup mechanisms:

1. **Request Expiration** - Unanswered requests expire after 7 days
2. **Assignment Expiration** - Role assignments expire 1 day after show end date
3. **Automatic Removal** - Expired assignments are automatically removed 1 week after show end date

## Testing

### Test Scripts
- `test_show_roles_system.php` - Comprehensive system test
- Verifies database tables, model loading, and basic functionality

### Manual Testing
1. Create a test show
2. Assign roles to test users
3. Test the approval workflow
4. Verify notifications are sent
5. Check automatic cleanup after show dates

## Integration Points

### Existing Systems
- **User Management** - Integrates with existing user roles and permissions
- **Show Management** - Works with existing show creation and editing
- **Notification System** - Uses existing notification queue for alerts
- **Authentication** - Uses existing Auth class and session management

### Navigation Integration
- Role management links added to coordinator dashboard
- Admin overview accessible from admin dashboard
- User requests accessible from user dashboard

## Troubleshooting

### Common Issues

1. **Tables not found** - Run the installation script
2. **Permission denied** - Ensure user has appropriate role (admin/coordinator)
3. **Notifications not working** - Check notification_queue table structure
4. **Search not working** - Verify user has admin role for user search

### Debug Mode
Enable DEBUG_MODE in config.php for detailed error logging.

## Version Information

- **Version:** 1.0
- **Date:** 2025-07-10
- **Compatibility:** Events and Shows Management System v3.64.0+
- **Dependencies:** Existing notification system, user management, show management

## Support

For issues or questions:
1. Check the test script output: `/test_show_roles_system.php`
2. Review error logs with DEBUG_MODE enabled
3. Verify database table structure matches the SQL scripts
4. Ensure all required files are present and properly configured
