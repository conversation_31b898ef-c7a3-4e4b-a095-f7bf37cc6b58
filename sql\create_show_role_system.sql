-- Show Role Assignment System
-- This script creates the database tables for per-show role assignments with approval workflow
-- Version: 1.0
-- Date: 2025-07-10

-- Create show_role_requests table for handling role assignment requests and approval workflow
CREATE TABLE IF NOT EXISTS `show_role_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `requested_role` enum('coordinator','judge','staff') NOT NULL,
  `requested_by` int(11) NOT NULL COMMENT 'User ID of coordinator/admin who made the request',
  `status` enum('pending','approved','declined','expired') NOT NULL DEFAULT 'pending',
  `request_message` text DEFAULT NULL COMMENT 'Optional message from requester',
  `response_message` text DEFAULT NULL COMMENT 'Optional message from user when responding',
  `requested_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `responded_at` datetime DEFAULT NULL,
  `expires_at` datetime NOT NULL COMMENT 'When this request expires if not responded to',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_pending_request` (`show_id`, `user_id`, `requested_role`, `status`),
  KEY `show_id` (`show_id`),
  KEY `user_id` (`user_id`),
  KEY `requested_by` (`requested_by`),
  KEY `status` (`status`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stores role assignment requests awaiting user approval';

-- Create show_role_assignments table for active, approved role assignments
CREATE TABLE IF NOT EXISTS `show_role_assignments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `assigned_role` enum('coordinator','judge','staff') NOT NULL,
  `assigned_by` int(11) NOT NULL COMMENT 'User ID of coordinator/admin who made the assignment',
  `request_id` int(11) DEFAULT NULL COMMENT 'Reference to the original request',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `assigned_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` datetime NOT NULL COMMENT 'Show end date + 1 week for automatic cleanup',
  `auto_cleanup_date` datetime NOT NULL COMMENT 'Date when this assignment will be automatically removed',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_active_assignment` (`show_id`, `user_id`, `assigned_role`),
  KEY `show_id` (`show_id`),
  KEY `user_id` (`user_id`),
  KEY `assigned_by` (`assigned_by`),
  KEY `request_id` (`request_id`),
  KEY `is_active` (`is_active`),
  KEY `auto_cleanup_date` (`auto_cleanup_date`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stores active show-specific role assignments';

-- Add foreign key constraints (optional - remove if your database doesn't support them)
ALTER TABLE `show_role_requests`
  ADD CONSTRAINT `show_role_requests_show_fk` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `show_role_requests_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `show_role_requests_requester_fk` FOREIGN KEY (`requested_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `show_role_assignments`
  ADD CONSTRAINT `show_role_assignments_show_fk` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `show_role_assignments_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `show_role_assignments_assigner_fk` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `show_role_assignments_request_fk` FOREIGN KEY (`request_id`) REFERENCES `show_role_requests` (`id`) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX `idx_show_role_requests_pending` ON `show_role_requests` (`status`, `expires_at`);
CREATE INDEX `idx_show_role_assignments_active` ON `show_role_assignments` (`is_active`, `show_id`);
CREATE INDEX `idx_show_role_assignments_cleanup` ON `show_role_assignments` (`auto_cleanup_date`, `is_active`);

-- Insert initial data or update existing data if needed
-- This section can be used for data migration from existing staff_assignments table

-- Example: Migrate existing staff assignments to new system
-- INSERT INTO show_role_assignments (show_id, user_id, assigned_role, assigned_by, assigned_at, expires_at, auto_cleanup_date)
-- SELECT 
--   sa.show_id,
--   sa.staff_id as user_id,
--   'staff' as assigned_role,
--   sa.assigned_by,
--   sa.created_at as assigned_at,
--   DATE_ADD(s.end_date, INTERVAL 1 DAY) as expires_at,
--   DATE_ADD(s.end_date, INTERVAL 1 WEEK) as auto_cleanup_date
-- FROM staff_assignments sa
-- JOIN shows s ON sa.show_id = s.id
-- WHERE NOT EXISTS (
--   SELECT 1 FROM show_role_assignments sra 
--   WHERE sra.show_id = sa.show_id 
--   AND sra.user_id = sa.staff_id 
--   AND sra.assigned_role = 'staff'
-- );

-- Add notification categories for the new role assignment system
-- This extends the existing notification system to handle role assignment notifications
-- Note: You may need to manually add 'role_assignment' to the notification_category enum
-- ALTER TABLE notification_queue MODIFY COLUMN notification_category
-- ENUM('event_reminder','registration_deadline','test','role_assignment') NOT NULL DEFAULT 'event_reminder';

-- Create a view for easy querying of user show roles
CREATE OR REPLACE VIEW `user_show_roles` AS
SELECT 
  sra.user_id,
  sra.show_id,
  s.name as show_name,
  sra.assigned_role,
  sra.is_active,
  sra.assigned_at,
  sra.expires_at,
  u.name as user_name,
  u.email as user_email,
  assigner.name as assigned_by_name
FROM show_role_assignments sra
JOIN shows s ON sra.show_id = s.id
JOIN users u ON sra.user_id = u.id
JOIN users assigner ON sra.assigned_by = assigner.id
WHERE sra.is_active = 1;

-- Create a view for pending role requests
CREATE OR REPLACE VIEW `pending_role_requests` AS
SELECT 
  srr.id,
  srr.user_id,
  srr.show_id,
  s.name as show_name,
  srr.requested_role,
  srr.requested_at,
  srr.expires_at,
  u.name as user_name,
  u.email as user_email,
  requester.name as requested_by_name
FROM show_role_requests srr
JOIN shows s ON srr.show_id = s.id
JOIN users u ON srr.user_id = u.id
JOIN users requester ON srr.requested_by = requester.id
WHERE srr.status = 'pending' AND srr.expires_at > NOW();
