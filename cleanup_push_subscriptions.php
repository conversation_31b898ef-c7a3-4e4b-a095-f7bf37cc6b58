<?php
/**
 * Clean up old/invalid push subscriptions
 */

// Prevent direct access via web browser
if (isset($_SERVER['HTTP_HOST'])) {
    // Allow only if accessed with specific parameter
    if (!isset($_GET['cleanup_key']) || $_GET['cleanup_key'] !== 'cleanup_push_2025') {
        http_response_code(404);
        exit('Not Found');
    }
}

// Initialize the application
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';

// Initialize core classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Controller.php';

$userId = 3;

echo "=== CLEANING UP PUSH SUBSCRIPTIONS ===\n\n";

try {
    $db = new Database();
    
    // Get all subscriptions for user
    $db->query("SELECT * FROM push_subscriptions WHERE user_id = ? ORDER BY created_at DESC");
    $db->bind(1, $userId);
    $subscriptions = $db->resultSet();
    
    echo "Found " . count($subscriptions) . " push subscriptions for user $userId\n\n";
    
    if (count($subscriptions) > 5) {
        echo "Keeping the 5 most recent subscriptions and removing the rest...\n\n";
        
        // Keep only the 5 most recent
        $toKeep = array_slice($subscriptions, 0, 5);
        $toRemove = array_slice($subscriptions, 5);
        
        echo "KEEPING:\n";
        foreach ($toKeep as $sub) {
            echo "- ID: {$sub->id}, Created: {$sub->created_at}, Agent: " . substr($sub->user_agent, 0, 50) . "...\n";
        }
        
        echo "\nREMOVING:\n";
        foreach ($toRemove as $sub) {
            echo "- ID: {$sub->id}, Created: {$sub->created_at}\n";
        }
        
        // Remove old subscriptions
        $removeIds = array_map(function($sub) { return $sub->id; }, $toRemove);
        $placeholders = str_repeat('?,', count($removeIds) - 1) . '?';
        
        $db->query("DELETE FROM push_subscriptions WHERE id IN ($placeholders)");
        foreach ($removeIds as $index => $id) {
            $db->bind($index + 1, $id);
        }
        $db->execute();
        
        echo "\n✅ Removed " . count($toRemove) . " old subscriptions\n";
        echo "✅ Kept " . count($toKeep) . " recent subscriptions\n";
        
    } else {
        echo "✅ Subscription count is reasonable, no cleanup needed\n";
    }
    
    // Show final count
    $db->query("SELECT COUNT(*) as count FROM push_subscriptions WHERE user_id = ?");
    $db->bind(1, $userId);
    $db->execute();
    $finalCount = $db->single()->count;
    
    echo "\nFinal subscription count: $finalCount\n";
    
    echo "\n=== NEXT STEPS ===\n";
    echo "1. Clear your browser cache and data for this site\n";
    echo "2. Visit the main site and re-enable push notifications\n";
    echo "3. Test sending a push notification\n";
    echo "4. Check browser notification settings\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== END CLEANUP ===\n";
?>
