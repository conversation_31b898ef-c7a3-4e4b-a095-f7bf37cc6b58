-- Fixed Email Folder System for Admin Email Management
-- Run this SQL to add email folder functionality (fixed version)

-- Drop tables if they exist (to start fresh)
DROP TABLE IF EXISTS email_statistics;
DROP TABLE IF EXISTS email_reminders;
DROP TABLE IF EXISTS email_templates;
DROP TABLE IF EXISTS email_folders;

-- Email folders table
CREATE TABLE email_folders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#007bff',
    icon VARCHAR(50) DEFAULT 'fas fa-folder',
    is_system TINYINT(1) DEFAULT 0,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_created_by (created_by)
);

-- Insert default system folders
INSERT INTO email_folders (id, name, description, color, icon, is_system, created_by) VALUES
(1, 'Inbox', 'Default inbox for new emails', '#007bff', 'fas fa-inbox', 1, 1),
(2, 'Important', 'High priority emails', '#dc3545', 'fas fa-star', 1, 1),
(3, 'Follow Up', 'Emails requiring follow-up', '#ffc107', 'fas fa-clock', 1, 1),
(4, 'Resolved', 'Completed email conversations', '#28a745', 'fas fa-check-circle', 1, 1),
(5, 'Spam', 'Suspected spam emails', '#6c757d', 'fas fa-ban', 1, 1),
(6, 'Archive', 'Archived emails', '#17a2b8', 'fas fa-archive', 1, 1);

-- Email templates table
CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    template_variables TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_active TINYINT(1) DEFAULT 1,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
);

-- Insert default email templates
INSERT INTO email_templates (name, subject, body, template_variables, category, created_by) VALUES
('Welcome Response', 'Welcome to Events and Shows Platform', 
'Hello,

Thank you for contacting us regarding {{subject}}.

We have received your message and will respond within 24 hours.

Best regards,
{{admin_name}}
Events and Shows Platform', 
'subject,admin_name,date,ticket_number', 'general', 1),

('Event Information', 'Re: {{subject}} - Event Information', 
'Hello,

Thank you for your inquiry about our events.

Here is the information you requested:

[Please add specific event details here]

If you have any other questions, please do not hesitate to ask.

Best regards,
{{admin_name}}
Events and Shows Platform', 
'subject,admin_name,date,ticket_number', 'events', 1),

('Registration Confirmation', 'Registration Confirmation - {{subject}}', 
'Hello,

Your registration has been confirmed for the event.

Ticket Number: {{ticket_number}}
Date: {{date}}

We look forward to seeing you at the event!

Best regards,
{{admin_name}}
Events and Shows Platform', 
'subject,admin_name,date,ticket_number', 'registration', 1),

('Follow Up Required', 'Re: {{subject}} - Follow Up Required', 
'Hello,

We are following up on your previous message regarding {{subject}}.

Please let us know if you need any additional assistance.

Best regards,
{{admin_name}}
Events and Shows Platform', 
'subject,admin_name,date,ticket_number', 'followup', 1);

-- Email reminders table
CREATE TABLE email_reminders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    user_id INT NOT NULL,
    reminder_date DATETIME NOT NULL,
    reminder_text TEXT,
    is_completed TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_message_id (message_id),
    INDEX idx_user_id (user_id),
    INDEX idx_reminder_date (reminder_date),
    INDEX idx_is_completed (is_completed)
);

-- Email statistics table
CREATE TABLE email_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    emails_received INT DEFAULT 0,
    emails_sent INT DEFAULT 0,
    emails_replied INT DEFAULT 0,
    avg_response_time_hours DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (date)
);

-- Check if messages table exists before altering
SELECT 'Checking messages table...' as status;

-- Note: Run these ALTER TABLE statements manually after checking if columns exist
-- Check existing columns first: DESCRIBE messages;

-- Add folder_id column if it doesn't exist
-- ALTER TABLE messages ADD COLUMN folder_id INT DEFAULT 1;
-- ALTER TABLE messages ADD INDEX idx_folder_id (folder_id);

-- Add reminder columns if they don't exist
-- ALTER TABLE messages ADD COLUMN has_reminder TINYINT(1) DEFAULT 0;
-- ALTER TABLE messages ADD COLUMN reminder_date DATETIME NULL;
-- ALTER TABLE messages ADD INDEX idx_has_reminder (has_reminder);
-- ALTER TABLE messages ADD INDEX idx_reminder_date (reminder_date);

SELECT 'Email folder system tables created successfully!' as status;
