<?php
require_once dirname(__DIR__) . '/config/config.php';
require_once APPROOT . '/core/ContentCleaner.php';

// Your exact database content
$testContent = '------sinikael-?=_1-17528868592260.1560288133272248
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 7bit
test message  
 asking a question
------sinikael-?=_1-17528868592260.1560288133272248
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable
<html><head></head><body><div>
    <div dir="auto" id="compose-body-wrapper">test message </div><div dir="auto" id="compose-body-wrapper">asking a question</div><div dir="auto" id="compose-body-wrapper"><br/>
      <div dir="auto"><br/></div>
      <div dir="auto" id="tmjah_g_1299">
      Get <a href="https://bluemail.me/download/" target="_blank">BlueMail for Mobile</a>
      </div>
      <div dir="auto"><br/></div>
    </div>
  </div></body></html>
------sinikael-?=_1-17528868592260.1560288133272248--';

echo "=== DEBUGGING CONTENT CLEANER ===\n\n";

echo "1. Content analysis:\n";
echo "Length: " . strlen($testContent) . "\n";
echo "Contains 'Content-Type:': " . (strpos($testContent, 'Content-Type:') !== false ? 'YES' : 'NO') . "\n";
echo "Contains 'text/plain': " . (strpos($testContent, 'text/plain') !== false ? 'YES' : 'NO') . "\n";
echo "Contains '------': " . (strpos($testContent, '------') !== false ? 'YES' : 'NO') . "\n\n";

echo "2. Testing containsMimeContent:\n";
// We can't call private method directly, so let's test the logic
$containsMime = (strpos($testContent, 'Content-Type:') !== false);
echo "Should detect as MIME: " . ($containsMime ? 'YES' : 'NO') . "\n\n";

echo "3. Testing regex patterns:\n";

// Pattern 1: Current pattern
echo "Pattern 1 (current): ";
if (preg_match('/Content-Type:\s*text\/plain.*?\nContent-Transfer-Encoding:.*?\n(.*?)(?=\n------)/s', $testContent, $matches)) {
    echo "MATCHED\n";
    echo "Captured: '" . trim($matches[1]) . "'\n\n";
} else {
    echo "NO MATCH\n\n";
}

// Pattern 2: Simpler pattern
echo "Pattern 2 (simple): ";
if (preg_match('/Content-Type:\s*text\/plain.*?\n.*?\n(.+?)(?=\n------)/s', $testContent, $matches)) {
    echo "MATCHED\n";
    echo "Captured: '" . trim($matches[1]) . "'\n\n";
} else {
    echo "NO MATCH\n\n";
}

// Pattern 3: Even simpler
echo "Pattern 3 (very simple): ";
if (preg_match('/text\/plain.*?\n.*?\n(.+?)(?=\n------)/s', $testContent, $matches)) {
    echo "MATCHED\n";
    echo "Captured: '" . trim($matches[1]) . "'\n\n";
} else {
    echo "NO MATCH\n\n";
}

// Pattern 4: Manual extraction
echo "4. Manual extraction:\n";
$lines = explode("\n", $testContent);
$inPlainText = false;
$plainTextContent = [];

foreach ($lines as $line) {
    if (strpos($line, 'Content-Type: text/plain') !== false) {
        $inPlainText = true;
        continue;
    }
    if (strpos($line, 'Content-Transfer-Encoding:') !== false && $inPlainText) {
        continue;
    }
    if (strpos($line, '------') !== false && $inPlainText) {
        break;
    }
    if ($inPlainText && !empty(trim($line))) {
        $plainTextContent[] = trim($line);
    }
}

echo "Manual extraction result: '" . implode(' ', $plainTextContent) . "'\n\n";

echo "5. Testing ContentCleaner::cleanMessageContent:\n";
$result = ContentCleaner::cleanMessageContent($testContent);
echo "Result: '" . substr($result, 0, 100) . "...'\n";
echo "Is HTML: " . (strpos($result, '<html>') !== false ? 'YES' : 'NO') . "\n";
echo "Contains 'test message': " . (strpos($result, 'test message') !== false ? 'YES' : 'NO') . "\n";
?>