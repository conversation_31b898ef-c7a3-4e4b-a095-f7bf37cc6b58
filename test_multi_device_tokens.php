<?php
/**
 * Test Multi-Device FCM Token Management
 * 
 * This script tests that users can have multiple active FCM tokens (one per device)
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationModel.php';

echo "<h1>📱 Test Multi-Device FCM Token Management</h1>";

try {
    $notificationModel = new NotificationModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🔍 Current Token Status</h2>";
    
    // Show current tokens for the user
    $db->query("SELECT * FROM fcm_tokens WHERE user_id = :user_id ORDER BY created_at DESC");
    $db->bind(':user_id', $userId);
    $currentTokens = $db->resultSet();
    
    if (!empty($currentTokens)) {
        echo "<p>Current tokens for User {$userId}:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Token (First 20)</th><th>User Agent</th><th>Active</th><th>Created</th></tr>";
        foreach ($currentTokens as $token) {
            $activeStatus = $token->active ? '✅ Active' : '❌ Inactive';
            echo "<tr>";
            echo "<td>{$token->id}</td>";
            echo "<td>" . htmlspecialchars(substr($token->token, 0, 20)) . "...</td>";
            echo "<td>" . htmlspecialchars($token->user_agent) . "</td>";
            echo "<td>{$activeStatus}</td>";
            echo "<td>{$token->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No existing tokens for User {$userId}</p>";
    }
    
    echo "<h2>🧪 Simulating Multiple Device Registrations</h2>";
    
    // Simulate different devices registering FCM tokens
    $testDevices = [
        [
            'name' => 'iPhone 15',
            'token' => 'test_token_iphone_' . time(),
            'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        ],
        [
            'name' => 'Android Phone',
            'token' => 'test_token_android_' . time(),
            'user_agent' => 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'
        ],
        [
            'name' => 'Desktop Chrome',
            'token' => 'test_token_desktop_' . time(),
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ],
        [
            'name' => 'iPad',
            'token' => 'test_token_ipad_' . time(),
            'user_agent' => 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        ]
    ];
    
    echo "<p>Registering tokens for different devices...</p>";
    
    foreach ($testDevices as $device) {
        echo "<h3>📱 Registering: {$device['name']}</h3>";
        
        $result = $notificationModel->registerFCMToken($userId, $device['token'], $device['user_agent']);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Successfully registered token for {$device['name']} (Token ID: {$result})</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to register token for {$device['name']}</p>";
        }
        
        // Small delay to ensure different timestamps
        usleep(100000); // 0.1 second
    }
    
    echo "<h2>📊 Final Token Status</h2>";
    
    // Show final tokens for the user
    $db->query("SELECT * FROM fcm_tokens WHERE user_id = :user_id ORDER BY created_at DESC");
    $db->bind(':user_id', $userId);
    $finalTokens = $db->resultSet();
    
    if (!empty($finalTokens)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Token (First 20)</th><th>Device Type</th><th>Active</th><th>Created</th></tr>";
        
        $activeCount = 0;
        foreach ($finalTokens as $token) {
            if ($token->active) $activeCount++;
            
            // Identify device type from user agent
            $deviceType = 'Unknown';
            if (strpos($token->user_agent, 'iPhone') !== false) $deviceType = 'iPhone';
            elseif (strpos($token->user_agent, 'iPad') !== false) $deviceType = 'iPad';
            elseif (strpos($token->user_agent, 'Android') !== false) $deviceType = 'Android';
            elseif (strpos($token->user_agent, 'Windows') !== false) $deviceType = 'Desktop';
            
            $activeStatus = $token->active ? '✅ Active' : '❌ Inactive';
            $rowColor = $token->active ? '' : 'background: #ffeeee;';
            
            echo "<tr style='{$rowColor}'>";
            echo "<td>{$token->id}</td>";
            echo "<td>" . htmlspecialchars(substr($token->token, 0, 20)) . "...</td>";
            echo "<td>{$deviceType}</td>";
            echo "<td>{$activeStatus}</td>";
            echo "<td>{$token->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>📈 Summary</h3>";
        echo "<p><strong>Total tokens:</strong> " . count($finalTokens) . "</p>";
        echo "<p><strong>Active tokens:</strong> {$activeCount}</p>";
        echo "<p><strong>Inactive tokens:</strong> " . (count($finalTokens) - $activeCount) . "</p>";
        
        if ($activeCount >= 4) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
            echo "<h4>✅ SUCCESS: Multi-Device Support Working!</h4>";
            echo "<p>User now has {$activeCount} active tokens, allowing push notifications on multiple devices.</p>";
            echo "<p>Each device can receive notifications independently.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;'>";
            echo "<h4>❌ Issue: Expected 4 active tokens, got {$activeCount}</h4>";
            echo "<p>The multi-device support may not be working correctly.</p>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ No tokens found after registration</p>";
    }
    
    echo "<h2>🧪 Testing Push Notification Delivery</h2>";
    
    // Test sending a notification to all active tokens
    $activeTokens = $notificationModel->getUserFCMTokens($userId);
    
    if (!empty($activeTokens)) {
        echo "<p style='color: green;'>✅ Found " . count($activeTokens) . " active token(s) for push notification delivery</p>";
        echo "<p>When a message is sent, the user will receive " . count($activeTokens) . " push notification(s) (one per device).</p>";
        
        echo "<h4>📱 Active Devices:</h4>";
        echo "<ul>";
        foreach ($activeTokens as $token) {
            $deviceType = 'Unknown';
            if (strpos($token->user_agent, 'iPhone') !== false) $deviceType = '📱 iPhone';
            elseif (strpos($token->user_agent, 'iPad') !== false) $deviceType = '📱 iPad';
            elseif (strpos($token->user_agent, 'Android') !== false) $deviceType = '📱 Android';
            elseif (strpos($token->user_agent, 'Windows') !== false) $deviceType = '💻 Desktop';
            
            echo "<li>{$deviceType} - Token: " . substr($token->token, 0, 20) . "...</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>❌ No active tokens found for push notification delivery</p>";
    }
    
    echo "<h2>✅ Expected Behavior</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h4>🎯 Multi-Device Push Notifications:</h4>";
    echo "<ul>";
    echo "<li>✅ User can have multiple active FCM tokens (one per device)</li>";
    echo "<li>✅ iPhone, Android, Desktop, iPad can all receive notifications</li>";
    echo "<li>✅ When a message is sent, ALL devices get the notification</li>";
    echo "<li>✅ Registering a new token on the same device deactivates the old one</li>";
    echo "<li>✅ Registering on a different device keeps other devices active</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
