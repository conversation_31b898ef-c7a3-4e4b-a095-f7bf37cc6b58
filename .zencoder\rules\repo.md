---
description: Repository Information Overview
alwaysApply: true
---

# Events and Shows Management System Information

## Summary
A comprehensive web application for managing car shows, events, judging, registrations, and vehicle displays. The system provides features for event management, user registration, payment processing, notification systems, and mobile-responsive interfaces.

## Structure
- **config/**: Configuration files including database settings and application parameters
- **controllers/**: MVC controllers handling request processing and business logic
- **core/**: Core application classes including routing, database, and authentication
- **helpers/**: Utility functions for common tasks (CSRF, URL, session, timezone)
- **models/**: Data models for database interaction
- **public/**: Publicly accessible assets (CSS, JS, images)
- **views/**: Template files organized by feature area
- **uploads/**: User-uploaded content (images, files)
- **api/**: API endpoints for mobile and PWA functionality

## Language & Runtime
**Language**: PHP
**Version**: PHP 7.3+ (supports features up to PHP 7.3)
**Framework**: Custom MVC framework
**Database**: MySQL

## Dependencies
**Main Dependencies**:
- PHPMailer: Email sending functionality
- Firebase/FCM: Push notification delivery
- Google Maps API: Location and mapping features
- Facebook PHP API : Social login integration (does not use SDK)

**JavaScript Libraries**:
- jQuery: DOM manipulation and AJAX
- Bootstrap: UI framework
- Service Worker API: PWA functionality

## Build & Installation
```bash
# Manual installation
php install.php
# Or using the batch file
run_installer.bat
```

## Configuration
**Main Config File**: `config/config.php`
**Environment-specific Config**: `config/env.config.php` (optional)
**Database Settings**:
- Host, username, password, and database name in config.php
- Table structure defined in SQL files in database/ directory

## Entry Points
**Main Entry Point**: `index.php`
**Routing System**: URL format `/controller/method/parameters`
**API Endpoints**: `/api/endpoint/action`

## Features
**Authentication**: Traditional login and Facebook OAuth integration
**Notifications**: Email and push notification system (FCM)
**Payment Processing**: Support for multiple payment methods
**Progressive Web App**: Service worker implementation with offline support
**Timezone Management**: User-specific timezone handling
**Event Management**: Complete event lifecycle with registration and judging

## Testing
**Test Files**: Located in `/test/` directory
**Debug Tools**: Various debug_*.php files for testing specific components
**Test Data**: Sample data available in database/demo_data/

## Security
**CSRF Protection**: Token-based CSRF protection system
**Input Validation**: Server-side validation for all user inputs
**Session Management**: Secure session handling with regeneration
**Error Handling**: Comprehensive error logging and reporting
```