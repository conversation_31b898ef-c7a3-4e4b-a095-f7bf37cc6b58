-- Add denial functionality columns to judges table
-- This migration adds support for denying judge requests with notes
-- Updated to handle existing columns and fix data type issues

-- Check and add denial_note column to judges table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'judges'
     AND COLUMN_NAME = 'denial_note') = 0,
    'ALTER TABLE judges ADD COLUMN denial_note TEXT NULL AFTER is_active',
    'SELECT "denial_note column already exists in judges table"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add denied_by column to judges table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'judges'
     AND COLUMN_NAME = 'denied_by') = 0,
    'ALTER TABLE judges ADD COLUMN denied_by INT(10) UNSIGNED NULL AFTER denial_note',
    'SELECT "denied_by column already exists in judges table"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add denied_at column to judges table if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'judges'
     AND COLUMN_NAME = 'denied_at') = 0,
    'ALTER TABLE judges ADD COLUMN denied_at TIMESTAMP NULL AFTER denied_by',
    'SELECT "denied_at column already exists in judges table"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Fix data type for denied_by if it exists but has wrong type
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'judges'
     AND COLUMN_NAME = 'denied_by'
     AND COLUMN_TYPE != 'int(10) unsigned') > 0,
    'ALTER TABLE judges MODIFY COLUMN denied_by INT(10) UNSIGNED NULL',
    'SELECT "denied_by column has correct data type"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key for denied_by if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'judges'
     AND CONSTRAINT_NAME = 'fk_judges_denied_by') = 0,
    'ALTER TABLE judges ADD CONSTRAINT fk_judges_denied_by FOREIGN KEY (denied_by) REFERENCES users(id) ON DELETE SET NULL',
    'SELECT "fk_judges_denied_by constraint already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for better performance if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'judges'
     AND INDEX_NAME = 'idx_judges_is_active_denied') = 0,
    'ALTER TABLE judges ADD INDEX idx_judges_is_active_denied (is_active, denied_at)',
    'SELECT "idx_judges_is_active_denied index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Handle show_role_requests table if it exists
-- Check if show_role_requests table exists first
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests') > 0,
    'SELECT "show_role_requests table exists, proceeding with column additions"',
    'SELECT "show_role_requests table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add denial_note to show_role_requests if table and column don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests') > 0
    AND
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests'
     AND COLUMN_NAME = 'denial_note') = 0,
    'ALTER TABLE show_role_requests ADD COLUMN denial_note TEXT NULL AFTER status',
    'SELECT "show_role_requests denial_note column already exists or table does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add denied_by to show_role_requests if table and column don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests') > 0
    AND
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests'
     AND COLUMN_NAME = 'denied_by') = 0,
    'ALTER TABLE show_role_requests ADD COLUMN denied_by INT(10) UNSIGNED NULL AFTER denial_note',
    'SELECT "show_role_requests denied_by column already exists or table does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add denied_at to show_role_requests if table and column don't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests') > 0
    AND
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests'
     AND COLUMN_NAME = 'denied_at') = 0,
    'ALTER TABLE show_role_requests ADD COLUMN denied_at TIMESTAMP NULL AFTER denied_by',
    'SELECT "show_role_requests denied_at column already exists or table does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key for show_role_requests if table exists and constraint doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests') > 0
    AND
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests'
     AND CONSTRAINT_NAME = 'fk_show_role_requests_denied_by') = 0,
    'ALTER TABLE show_role_requests ADD CONSTRAINT fk_show_role_requests_denied_by FOREIGN KEY (denied_by) REFERENCES users(id) ON DELETE SET NULL',
    'SELECT "show_role_requests foreign key already exists or table does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for show_role_requests if table exists and index doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests') > 0
    AND
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'show_role_requests'
     AND INDEX_NAME = 'idx_show_role_requests_status_denied') = 0,
    'ALTER TABLE show_role_requests ADD INDEX idx_show_role_requests_status_denied (status, denied_at)',
    'SELECT "show_role_requests index already exists or table does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
