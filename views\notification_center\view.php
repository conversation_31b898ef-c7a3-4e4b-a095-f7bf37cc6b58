<?php
require_once APPROOT . '/views/includes/header.php';
require_once APPROOT . '/helpers/timezone_helper.php';
require_once APPROOT . '/core/ContentCleaner.php';

// Email content processing is now handled by:
// 1. EmailRetrievalService - parses emails when fetched from server
// 2. ContentCleaner - handles display formatting and cleanup

// Removed unused MIME parsing function - now handled by EmailRetrievalService and ContentCleaner

// Removed unused HTML processing function - now handled by ContentCleaner
?>

<?php
// Debug: Log what message data we received
error_log("VIEW DEBUG - Message ID: " . ($message->id ?? 'NULL'));
error_log("VIEW DEBUG - Message Subject: " . ($message->subject ?? 'NULL'));
error_log("VIEW DEBUG - Message Type: " . ($message->message_type ?? 'NULL'));

// Check if we have the backup originalMessage from controller
if (isset($originalMessage)) {
    error_log("VIEW DEBUG - Original Message from controller ID: " . ($originalMessage->id ?? 'NULL'));
    error_log("VIEW DEBUG - Original Message from controller Subject: " . ($originalMessage->subject ?? 'NULL'));
    // Use the backup message from controller
    $message = $originalMessage;
} else {
    error_log("VIEW DEBUG - No originalMessage backup found, using message variable");
}

// Preserve the original message data to prevent it from being overwritten in loops
$correctMessage = $message;
$correctSubject = $correctMessage->subject;
$currentUserId = $data['currentUserId'] ?? 0;

error_log("VIEW DEBUG - Final Subject to display: " . $correctSubject);
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="<?php echo BASE_URL; ?>/notification_center" class="btn btn-outline-secondary btn-sm me-3">
                        <i class="fas fa-arrow-left me-1"></i>Back to Messages
                    </a>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Conversation Thread -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <!-- Desktop: Show "Conversation:" -->
                            <h5 class="mb-0 d-none d-md-block">
                                <i class="fas fa-comments text-primary me-2"></i>
                                Conversation: <?php echo htmlspecialchars($correctSubject); ?>
                            </h5>
                            <!-- Mobile: Just show subject -->
                            <h5 class="mb-0 d-md-none">
                                <i class="fas fa-comments text-primary me-2"></i>
                                <?php echo htmlspecialchars($correctSubject); ?>
                            </h5>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-info"><?php echo count($messageThread); ?> message<?php echo count($messageThread) !== 1 ? 's' : ''; ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    <!-- Thread Messages -->
                    <?php if (!empty($messageThread)): ?>
                        <?php foreach ($messageThread as $index => $threadMessage): ?>
                            <?php 
                            $isCurrentUser = ($threadMessage->from_user_id == $currentUserId);
                            $isOriginal = ($index === 0);
                            $isReply = !$isOriginal;
                            ?>
                            
                            <div class="message-item <?php echo $isReply ? 'reply-message' : 'original-message'; ?> <?php echo $isCurrentUser ? 'from-current-user' : 'from-other-user'; ?>" 
                                 style="<?php echo $isReply ? 'margin-left: 2rem; border-left: 3px solid #dee2e6;' : ''; ?>">
                                
                                <div class="p-4 <?php echo $index < count($messageThread) - 1 ? 'border-bottom' : ''; ?>">
                                    <!-- Message Header -->
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="message-meta">
                                            <!-- Desktop Layout -->
                                            <div class="d-none d-md-block">
                                                <div class="d-flex align-items-center mb-1">
                                                    <?php if ($isReply): ?>
                                                        <i class="fas fa-reply text-muted me-2"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-envelope text-primary me-2"></i>
                                                    <?php endif; ?>

                                                    <strong class="<?php echo $isCurrentUser ? 'text-success' : 'text-primary'; ?>">
                                                        <?php if ($isCurrentUser): ?>
                                                            You
                                                        <?php else: ?>
                                                            <?php
                                                            // Display sender name with small role badge
                                                            $senderName = htmlspecialchars($threadMessage->from_user_name ?? 'System');
                                                            $messageType = $threadMessage->message_type ?? 'direct';

                                                            echo $senderName;

                                                            // Add small role badge after name
                                                            switch($messageType) {
                                                                case 'admin':
                                                                    echo ' <span class="badge bg-danger ms-1" style="font-size: 9px;">Admin</span>';
                                                                    break;
                                                                case 'coordinator':
                                                                    echo ' <span class="badge bg-primary ms-1" style="font-size: 9px;">Coordinator</span>';
                                                                    break;
                                                                case 'judge':
                                                                    echo ' <span class="badge bg-success ms-1" style="font-size: 9px;">Judge</span>';
                                                                    break;
                                                                case 'staff':
                                                                    echo ' <span class="badge bg-warning text-dark ms-1" style="font-size: 9px;">Staff</span>';
                                                                    break;
                                                                case 'system':
                                                                    echo ' <span class="badge bg-info ms-1" style="font-size: 9px;">System</span>';
                                                                    break;
                                                            }
                                                            ?>
                                                        <?php endif; ?>
                                                    </strong>

                                                    <?php if ($isOriginal): ?>
                                                        <span class="badge bg-primary ms-2">Original</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary ms-2">Reply</span>
                                                    <?php endif; ?>

                                                    <?php if (!$threadMessage->is_read && $threadMessage->to_user_id == $currentUserId): ?>
                                                        <span class="badge bg-danger ms-2">Unread</span>
                                                    <?php endif; ?>
                                                </div>

                                                <small class="text-muted">
                                                    <?php echo formatDateTimeForUser($threadMessage->created_at, $_SESSION['user_id'], 'F j, Y \a\t g:i A T'); ?>
                                                    <?php if ($isCurrentUser): ?>
                                                        <span class="ms-2">• Sent to <?php echo htmlspecialchars($threadMessage->to_user_id == $currentUserId ? 'You' : 'Other User'); ?></span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>

                                            <!-- Mobile Layout - Horizontal -->
                                            <div class="d-md-none">
                                                <div class="d-flex align-items-center flex-wrap">
                                                    <?php if ($isReply): ?>
                                                        <i class="fas fa-reply text-muted me-2" style="font-size: 14px;"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-envelope text-primary me-2" style="font-size: 14px;"></i>
                                                    <?php endif; ?>

                                                    <strong class="<?php echo $isCurrentUser ? 'text-success' : 'text-primary'; ?> me-2" style="font-size: 14px;">
                                                        <?php if ($isCurrentUser): ?>
                                                            You
                                                        <?php else: ?>
                                                            <?php
                                                            // Display sender name with small role badge
                                                            $senderName = htmlspecialchars($threadMessage->from_user_name ?? 'System');
                                                            $messageType = $threadMessage->message_type ?? 'direct';

                                                            echo $senderName;

                                                            // Add small role badge after name
                                                            switch($messageType) {
                                                                case 'admin':
                                                                    echo ' <span class="badge bg-danger ms-1" style="font-size: 8px;">Admin</span>';
                                                                    break;
                                                                case 'coordinator':
                                                                    echo ' <span class="badge bg-primary ms-1" style="font-size: 8px;">Coordinator</span>';
                                                                    break;
                                                                case 'judge':
                                                                    echo ' <span class="badge bg-success ms-1" style="font-size: 8px;">Judge</span>';
                                                                    break;
                                                                case 'staff':
                                                                    echo ' <span class="badge bg-warning text-dark ms-1" style="font-size: 8px;">Staff</span>';
                                                                    break;
                                                                case 'system':
                                                                    echo ' <span class="badge bg-info ms-1" style="font-size: 8px;">System</span>';
                                                                    break;
                                                            }
                                                            ?>
                                                        <?php endif; ?>
                                                    </strong>

                                                    <?php if ($isOriginal): ?>
                                                        <span class="badge bg-primary me-2" style="font-size: 10px;">Original</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary me-2" style="font-size: 10px;">Reply</span>
                                                    <?php endif; ?>

                                                    <?php if (!$threadMessage->is_read && $threadMessage->to_user_id == $currentUserId): ?>
                                                        <span class="badge bg-danger me-2" style="font-size: 10px;">Unread</span>
                                                    <?php endif; ?>
                                                </div>

                                                <small class="text-muted d-block mt-1" style="font-size: 12px;">
                                                    <?php echo formatDateTimeForUser($threadMessage->created_at, $_SESSION['user_id'], 'M j, Y g:i A T'); ?>
                                                    <?php if ($isCurrentUser): ?>
                                                        <span class="ms-2">• Sent to <?php echo htmlspecialchars($threadMessage->to_user_id == $currentUserId ? 'You' : 'Other User'); ?></span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Show Info (only for original message) -->
                                    <?php if ($isOriginal): ?>
                                        <?php if (!empty($threadMessage->show_title)): ?>
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <i class="fas fa-car me-1"></i>
                                                <strong>Related Show:</strong>
                                                <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $threadMessage->show_id; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($threadMessage->show_title); ?>
                                                </a>
                                                <?php if (!empty($threadMessage->show_location)): ?>
                                                    <span class="text-muted">• <?php echo htmlspecialchars($threadMessage->show_location); ?></span>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Show Assignment Interface (for email messages and admin/coordinator roles) -->
                                        <?php if ($threadMessage->message_type === 'email' && in_array($user_role, ['admin', 'coordinator'])): ?>
                                        <div class="mb-3" id="show-assignment-section">
                                            <div class="d-flex align-items-center gap-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-link me-1"></i>
                                                    <strong>Assign Show:</strong>
                                                </small>

                                                <?php if ($user_role === 'admin'): ?>
                                                    <!-- Admin: Search interface for all shows -->
                                                    <div class="position-relative" style="min-width: 300px;">
                                                        <input type="text"
                                                               id="show-search-input"
                                                               class="form-control form-control-sm"
                                                               placeholder="Search shows..."
                                                               autocomplete="off"
                                                               style="font-size: 12px;">
                                                        <div id="show-search-results"
                                                             class="position-absolute w-100 bg-white border rounded shadow-sm d-none"
                                                             style="z-index: 1000; max-height: 200px; overflow-y: auto; top: 100%;">
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <!-- Coordinator: Dropdown for their shows only -->
                                                    <select id="coordinator-show-select"
                                                            class="form-select form-select-sm"
                                                            style="min-width: 300px; font-size: 12px;">
                                                        <option value="">Loading shows...</option>
                                                    </select>
                                                <?php endif; ?>

                                                <button type="button"
                                                        id="assign-show-btn"
                                                        class="btn btn-primary btn-sm"
                                                        onclick="assignShowToMessage()"
                                                        style="font-size: 11px;">
                                                    Assign
                                                </button>

                                                <?php if (!empty($threadMessage->show_id)): ?>
                                                <button type="button"
                                                        id="remove-show-btn"
                                                        class="btn btn-outline-secondary btn-sm"
                                                        onclick="removeShowAssignment()"
                                                        style="font-size: 11px;">
                                                    Remove
                                                </button>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Current assignment display -->
                                            <div id="current-show-assignment" class="mt-2">
                                                <?php if (empty($threadMessage->show_title)): ?>
                                                    <small class="text-muted">No show assigned</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Email Ownership Section (for email messages and admin/coordinator users) -->
                                        <?php if ($threadMessage->message_type === 'email' && in_array($user_role, ['admin', 'coordinator'])): ?>
                                        <div class="mb-3" id="email-ownership-section">
                                            <div class="d-flex align-items-center gap-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-user-shield me-1"></i>
                                                    <strong>Ownership:</strong>
                                                </small>

                                                <?php if (!empty($threadMessage->owned_by_admin_id)): ?>
                                                    <!-- Show current owner -->
                                                    <span class="badge bg-info" style="font-size: 11px;">
                                                        Owned by: <?php echo htmlspecialchars($threadMessage->owner_name ?? 'User #' . $threadMessage->owned_by_admin_id); ?>
                                                    </span>

                                                    <?php if ($threadMessage->owned_by_admin_id != $currentUserId): ?>
                                                        <!-- Take ownership button if not current owner -->
                                                        <button type="button"
                                                                class="btn btn-outline-primary btn-sm"
                                                                onclick="takeEmailOwnership()"
                                                                style="font-size: 11px;">
                                                            Take Ownership
                                                        </button>
                                                    <?php else: ?>
                                                        <!-- Release ownership button if current owner -->
                                                        <button type="button"
                                                                class="btn btn-outline-secondary btn-sm"
                                                                onclick="releaseEmailOwnership()"
                                                                style="font-size: 11px;">
                                                            Release Ownership
                                                        </button>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <!-- No owner - show take ownership button -->
                                                    <span class="text-muted" style="font-size: 12px;">Unassigned</span>
                                                    <button type="button"
                                                            class="btn btn-primary btn-sm"
                                                            onclick="takeEmailOwnership()"
                                                            style="font-size: 11px;">
                                                        Take Ownership
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <!-- Message Content -->
                                    <div class="message-content <?php echo $isCurrentUser ? 'bg-light' : ''; ?>"
                                         style="<?php echo $isCurrentUser ? 'padding: 1rem; border-radius: 0.5rem; background-color: #f8f9fa !important;' : ''; ?>">
                                        <?php
                                        // Use ContentCleaner for consistent content processing
                                        echo ContentCleaner::cleanForWeb($threadMessage->message);
                                        ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="p-4 text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-3"></i>
                            <p>No messages found in this conversation.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Reply Section -->
            <?php if (!empty($messageThread)): ?>
                <?php
                $lastMessage = end($messageThread);
                // Allow replies for: email messages (always), direct messages, or messages that require reply
                $canReply = ($lastMessage->message_type === 'email' || $lastMessage->requires_reply || $lastMessage->message_type === 'direct');
                ?>
                <?php if ($canReply): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-reply me-2"></i>Reply to this conversation
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (isset($can_send_notifications) && $can_send_notifications): ?>
                            <form action="<?php echo BASE_URL; ?>/notification_center/reply" method="POST">
                                <input type="hidden" name="parent_message_id" value="<?php echo $lastMessage->parent_message_id ?: $lastMessage->id; ?>">
                                <input type="hidden" name="to_user_id" value="<?php echo $lastMessage->from_user_id == $currentUserId ? $lastMessage->to_user_id : $lastMessage->from_user_id; ?>">
                                <input type="hidden" name="ticket_number" value="<?php echo htmlspecialchars($message->ticket_number ?? ''); ?>">
                                <input type="hidden" name="security_token" value="<?php echo htmlspecialchars($message->security_token ?? ''); ?>">

                                <!-- Email Template Selection (Admin/Coordinator only for email messages) -->
                                <?php if ($message->message_type === 'email' && isset($has_email_access) && $has_email_access): ?>
                                <div class="mb-3">
                                    <label for="email_template" class="form-label">Use Email Template (Optional)</label>
                                    <select class="form-select" id="email_template" onchange="loadEmailTemplate()">
                                        <option value="">Select a template...</option>
                                        <!-- Templates will be loaded via JavaScript -->
                                    </select>
                                    <div class="form-text">
                                        Templates help you respond quickly with pre-written content.
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="mb-3">
                                    <label for="reply_message" class="form-label">Your Reply
                                        <?php if ($lastMessage->message_type === 'email' && isset($has_email_access) && $has_email_access): ?>
                                        <span class="badge bg-info ms-2">Email Reply</span>
                                        <?php endif; ?>
                                    </label>
                                    <textarea class="form-control" id="reply_message" name="message" rows="4"
                                              placeholder="Type your reply here..." required></textarea>
                                    <div class="form-text">
                                        Your reply will be added to this conversation thread.
                                        <?php if ($lastMessage->message_type === 'email' && isset($has_email_access) && $has_email_access): ?>
                                        <br><strong>Note:</strong> This will send an email to the original sender.
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Desktop Layout -->
                                <div class="d-none d-md-flex justify-content-between align-items-center">
                                    <div class="btn-group">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-1"></i>Send Reply
                                        </button>
                                        <?php if ($lastMessage->message_type === 'email' && isset($has_email_access) && $has_email_access): ?>
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearReplyForm()">
                                            <i class="fas fa-eraser me-1"></i>Clear
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        Replies are sent using the recipient's notification preferences.
                                        <?php if ($lastMessage->message_type === 'email' && isset($has_email_access) && $has_email_access): ?>
                                        <br><strong>Email:</strong> Will also send to original sender.
                                        <?php endif; ?>
                                    </small>
                                </div>

                                <!-- Mobile Layout - Compact -->
                                <div class="d-md-none">
                                    <div class="btn-group w-100 mb-2">
                                        <button type="submit" class="btn btn-primary flex-fill">
                                            <i class="fas fa-paper-plane me-1"></i>Send Reply
                                        </button>
                                        <?php if ($lastMessage->message_type === 'email' && isset($has_email_access) && $has_email_access): ?>
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearReplyForm()">
                                            <i class="fas fa-eraser"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted d-block text-center" style="font-size: 11px;">
                                        Sent using recipient's notification preferences
                                        <?php if ($lastMessage->message_type === 'email' && isset($has_email_access) && $has_email_access): ?>
                                        <br>Email will also be sent to original sender
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Cannot Send Replies
                                </h6>
                                <p class="mb-2">You cannot send replies because all your notification types are disabled.</p>
                                <p class="mb-0">
                                    <strong>To send replies:</strong> Enable at least one notification type in your 
                                    <a href="<?php echo BASE_URL; ?>/profile/notifications" class="alert-link">notification preferences</a>.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <div class="d-flex flex-column flex-md-row justify-content-center align-items-center gap-2">
                        <?php if (!empty($messageThread)): ?>
                            <?php $firstMessage = $messageThread[0]; ?>
                            <?php if ($firstMessage->show_id): ?>
                                <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $firstMessage->show_id; ?>"
                                   class="btn btn-primary action-button">
                                    <i class="fas fa-car me-1"></i>View Related Show
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>

                        <button type="button" class="btn btn-outline-secondary action-button"
                                onclick="archiveMessage(<?php echo $message->id; ?>)">
                            <i class="fas fa-archive me-1"></i>Archive Conversation
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Threaded Message Styles */
.message-item {
    transition: background-color 0.2s ease;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.original-message {
    background-color: #fff;
}

.reply-message {
    background-color: #fafbfc;
    position: relative;
}

.reply-message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.from-current-user .message-content {
    background-color: #e3f2fd !important;
    border-left: 3px solid #2196f3;
}

.from-other-user .message-content {
    background-color: #f5f5f5;
    border-left: 3px solid #6c757d;
}

.message-content {
    font-size: 1rem;
    line-height: 1.6;
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
}

.alert-warning {
    border-left: 4px solid #ffc107;
}

/* Thread indicators */
.reply-message {
    border-left: 3px solid #dee2e6;
    margin-left: 2rem;
}

.message-meta strong {
    font-weight: 600;
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .message-content {
        font-size: 14px;
        padding: 0.75rem;
        line-height: 1.4;
    }

    .reply-message {
        margin-left: 1rem !important;
    }

    /* Mobile header optimizations */
    .card-header h5 {
        font-size: 16px;
        line-height: 1.3;
    }

    /* Mobile message meta - horizontal layout */
    .message-meta .d-flex.flex-wrap {
        gap: 0.25rem;
    }

    .message-meta .badge {
        font-size: 10px !important;
        padding: 0.2rem 0.4rem;
    }

    /* Mobile reply section */
    .card-body {
        padding: 1rem;
    }

    /* Mobile form elements */
    .form-control {
        font-size: 14px;
    }

    .btn {
        font-size: 14px;
        padding: 0.5rem 1rem;
    }

    /* Mobile back button */
    .btn-sm {
        font-size: 12px;
        padding: 0.25rem 0.5rem;
    }

    /* Mobile action buttons - same size */
    .action-button {
        width: 100%;
        max-width: 250px;
        margin-bottom: 0.5rem;
    }

    .action-button:last-child {
        margin-bottom: 0;
    }
}

/* Action buttons - consistent sizing on all devices */
.action-button {
    min-width: 180px;
    padding: 0.75rem 1.5rem;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

/* Desktop action buttons */
@media (min-width: 769px) {
    .action-button {
        width: auto;
        margin: 0 0.5rem;
    }

    .action-button:first-child {
        margin-left: 0;
    }

    .action-button:last-child {
        margin-right: 0;
    }
}
</style>

<script>
function archiveMessage(messageId) {
    if (!confirm('Archive this message?')) {
        return;
    }
    
    fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '<?php echo BASE_URL; ?>/notification_center';
        } else {
            alert('Failed to archive: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to archive message');
    });
}

// Auto-resize textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('reply_message');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
});

// Enhanced Email Template Support for Admin/Coordinator
document.addEventListener('DOMContentLoaded', function() {
    // Check if this is an email message and user has email access
    const messageType = '<?= $message->message_type ?? '' ?>';
    const hasEmailAccess = <?= isset($has_email_access) && $has_email_access ? 'true' : 'false' ?>;

    if (messageType === 'email' && hasEmailAccess) {
        loadEmailTemplates();
        addEmailQuickActions();
    }
});

// Load email templates for admin/coordinator
function loadEmailTemplates() {
    fetch('<?= BASE_URL ?>/notification_center/getEmailTemplates')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.templates) {
                const templateSelect = document.getElementById('email_template');
                if (templateSelect) {
                    // Clear existing options except the first one
                    templateSelect.innerHTML = '<option value="">Select a template...</option>';

                    // Add templates
                    data.templates.forEach(template => {
                        const option = document.createElement('option');
                        option.value = template.id;
                        option.textContent = template.name;
                        option.setAttribute('data-subject', template.subject);
                        option.setAttribute('data-body', template.body);
                        templateSelect.appendChild(option);
                    });
                }
            }
        })
        .catch(error => {
            console.error('Error loading email templates:', error);
        });
}

// Load selected email template
function loadEmailTemplate() {
    const templateSelect = document.getElementById('email_template');
    const selectedOption = templateSelect.options[templateSelect.selectedIndex];

    if (selectedOption.value) {
        const subject = selectedOption.getAttribute('data-subject') || '';
        const body = selectedOption.getAttribute('data-body') || '';

        // Replace template variables
        const replacedSubject = replaceTemplateVariables(subject);
        const replacedBody = replaceTemplateVariables(body);

        // Fill form fields
        const subjectField = document.getElementById('reply_subject') || document.querySelector('input[name="subject"]');
        const messageField = document.getElementById('reply_message') || document.querySelector('textarea[name="message"]');

        if (subjectField) subjectField.value = replacedSubject;
        if (messageField) messageField.value = replacedBody;

        // Show feedback
        showToast('success', 'Template loaded: ' + selectedOption.textContent);
    }
}

// Replace template variables with actual values
function replaceTemplateVariables(text) {
    const replacements = {
        '{{subject}}': '<?= htmlspecialchars($correctMessage->subject ?? '') ?>',
        '{{ticket_number}}': '<?= htmlspecialchars($correctMessage->ticket_number ?? '') ?>',
        '{{date}}': new Date().toLocaleString(),
        '{{site_name}}': 'Events and Shows Platform',
        '{{admin_name}}': '<?= htmlspecialchars($current_user_name ?? 'Admin') ?>'
    };

    let result = text;
    for (const [variable, value] of Object.entries(replacements)) {
        result = result.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
    }

    return result;
}

// Simple toast notification function
function showToast(type, message) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(toast);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Show Assignment Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize show assignment interface
    initializeShowAssignment();
});

/**
 * Initialize show assignment interface based on user role
 */
function initializeShowAssignment() {
    const userRole = '<?= $user_role ?? '' ?>';
    const messageType = '<?= $correctMessage->message_type ?? '' ?>';

    // Only initialize for email messages and admin/coordinator roles
    if (messageType !== 'email' || !['admin', 'coordinator'].includes(userRole)) {
        return;
    }

    if (userRole === 'admin') {
        initializeAdminShowSearch();
    } else if (userRole === 'coordinator') {
        loadCoordinatorShows();
    }
}

/**
 * Initialize admin show search functionality
 */
function initializeAdminShowSearch() {
    const searchInput = document.getElementById('show-search-input');
    const resultsDiv = document.getElementById('show-search-results');

    if (!searchInput || !resultsDiv) return;

    let searchTimeout;

    // Search as user types
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            resultsDiv.classList.add('d-none');
            return;
        }

        searchTimeout = setTimeout(() => {
            searchShows(query);
        }, 300);
    });

    // Hide results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
            resultsDiv.classList.add('d-none');
        }
    });
}

/**
 * Search shows for admin users
 */
function searchShows(query) {
    const resultsDiv = document.getElementById('show-search-results');

    fetch(`<?= BASE_URL ?>/notification_center/searchShowsForAssignment?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.shows) {
                displayShowSearchResults(data.shows);
            } else {
                resultsDiv.innerHTML = '<div class="p-2 text-muted">No shows found</div>';
                resultsDiv.classList.remove('d-none');
            }
        })
        .catch(error => {
            console.error('Error searching shows:', error);
            resultsDiv.innerHTML = '<div class="p-2 text-danger">Error searching shows</div>';
            resultsDiv.classList.remove('d-none');
        });
}

/**
 * Display show search results for admin
 */
function displayShowSearchResults(shows) {
    const resultsDiv = document.getElementById('show-search-results');

    if (shows.length === 0) {
        resultsDiv.innerHTML = '<div class="p-2 text-muted">No shows found</div>';
    } else {
        let html = '';
        shows.forEach(show => {
            const statusBadge = show.status === 'completed' ? ' <small class="text-muted">(Completed)</small>' : '';
            html += `
                <div class="p-2 border-bottom show-search-item"
                     onclick="selectShowForAssignment(${show.id}, '${show.name.replace(/'/g, "\\'")}', '${show.location || ''}')"
                     style="cursor: pointer;">
                    <div class="fw-bold" style="font-size: 12px;">${show.name}${statusBadge}</div>
                    <small class="text-muted" style="font-size: 10px;">${show.start_date} - ${show.end_date}</small>
                    ${show.location ? `<br><small class="text-muted" style="font-size: 10px;"><i class="fas fa-map-marker-alt"></i> ${show.location}</small>` : ''}
                </div>
            `;
        });
        resultsDiv.innerHTML = html;
    }

    resultsDiv.classList.remove('d-none');
}

/**
 * Select a show from search results (admin)
 */
function selectShowForAssignment(showId, showName, showLocation) {
    const searchInput = document.getElementById('show-search-input');
    const resultsDiv = document.getElementById('show-search-results');

    searchInput.value = showName;
    searchInput.dataset.selectedShowId = showId;
    searchInput.dataset.selectedShowName = showName;
    searchInput.dataset.selectedShowLocation = showLocation;

    resultsDiv.classList.add('d-none');
}

/**
 * Load coordinator shows for dropdown
 */
function loadCoordinatorShows() {
    const selectElement = document.getElementById('coordinator-show-select');

    if (!selectElement) return;

    fetch('<?= BASE_URL ?>/notification_center/getCoordinatorShows')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.shows) {
                selectElement.innerHTML = '<option value="">Select a show...</option>';

                data.shows.forEach(show => {
                    const option = document.createElement('option');
                    option.value = show.id;
                    option.textContent = show.name;
                    option.dataset.location = show.location || '';
                    selectElement.appendChild(option);
                });

                // Pre-select current show if assigned
                const currentShowId = '<?= $correctMessage->show_id ?? '' ?>';
                if (currentShowId) {
                    selectElement.value = currentShowId;
                }
            } else {
                selectElement.innerHTML = '<option value="">No shows available</option>';
            }
        })
        .catch(error => {
            console.error('Error loading coordinator shows:', error);
            selectElement.innerHTML = '<option value="">Error loading shows</option>';
        });
}

/**
 * Assign selected show to the message
 */
function assignShowToMessage() {
    const userRole = '<?= $user_role ?? '' ?>';
    const messageId = '<?= $correctMessage->id ?? '' ?>';
    let showId = '';
    let showName = '';
    let showLocation = '';

    if (userRole === 'admin') {
        const searchInput = document.getElementById('show-search-input');
        showId = searchInput.dataset.selectedShowId || '';
        showName = searchInput.dataset.selectedShowName || '';
        showLocation = searchInput.dataset.selectedShowLocation || '';

        if (!showId) {
            showToast('warning', 'Please search and select a show first');
            return;
        }
    } else if (userRole === 'coordinator') {
        const selectElement = document.getElementById('coordinator-show-select');
        const selectedOption = selectElement.options[selectElement.selectedIndex];

        showId = selectElement.value;
        showName = selectedOption.textContent;
        showLocation = selectedOption.dataset.location || '';

        if (!showId) {
            showToast('warning', 'Please select a show');
            return;
        }
    }

    // Disable button during request
    const assignBtn = document.getElementById('assign-show-btn');
    assignBtn.disabled = true;
    assignBtn.textContent = 'Assigning...';

    const formData = new FormData();
    formData.append('message_id', messageId);
    formData.append('show_id', showId);

    fetch('<?= BASE_URL ?>/notification_center/assignShowToMessage', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
            updateShowAssignmentDisplay(showId, showName, showLocation);
        } else {
            showToast('error', data.message || 'Failed to assign show');
        }
    })
    .catch(error => {
        console.error('Error assigning show:', error);
        showToast('error', 'Error assigning show');
    })
    .finally(() => {
        assignBtn.disabled = false;
        assignBtn.textContent = 'Assign';
    });
}

/**
 * Remove show assignment from the message
 */
function removeShowAssignment() {
    const messageId = '<?= $correctMessage->id ?? '' ?>';

    // Disable button during request
    const removeBtn = document.getElementById('remove-show-btn');
    removeBtn.disabled = true;
    removeBtn.textContent = 'Removing...';

    const formData = new FormData();
    formData.append('message_id', messageId);
    formData.append('show_id', '0'); // 0 means remove assignment

    fetch('<?= BASE_URL ?>/notification_center/assignShowToMessage', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
            updateShowAssignmentDisplay('', '', '');
        } else {
            showToast('error', data.message || 'Failed to remove show assignment');
        }
    })
    .catch(error => {
        console.error('Error removing show assignment:', error);
        showToast('error', 'Error removing show assignment');
    })
    .finally(() => {
        removeBtn.disabled = false;
        removeBtn.textContent = 'Remove';
    });
}

/**
 * Update the show assignment display after assignment/removal
 */
function updateShowAssignmentDisplay(showId, showName, showLocation) {
    const currentAssignmentDiv = document.getElementById('current-show-assignment');
    const removeBtn = document.getElementById('remove-show-btn');

    if (showId && showName) {
        // Show assigned
        currentAssignmentDiv.innerHTML = `
            <small class="text-success">
                <i class="fas fa-check-circle me-1"></i>
                Assigned to: <strong>${showName}</strong>
                ${showLocation ? ` • ${showLocation}` : ''}
            </small>
        `;

        if (removeBtn) {
            removeBtn.style.display = 'inline-block';
        }
    } else {
        // No show assigned
        currentAssignmentDiv.innerHTML = '<small class="text-muted">No show assigned</small>';

        if (removeBtn) {
            removeBtn.style.display = 'none';
        }
    }

    // Clear search input for admin
    const searchInput = document.getElementById('show-search-input');
    if (searchInput) {
        searchInput.value = '';
        delete searchInput.dataset.selectedShowId;
        delete searchInput.dataset.selectedShowName;
        delete searchInput.dataset.selectedShowLocation;
    }

    // Reset coordinator dropdown
    const selectElement = document.getElementById('coordinator-show-select');
    if (selectElement && !showId) {
        selectElement.value = '';
    }
}

// Email Ownership Functionality

/**
 * Take ownership of the current email message/thread
 */
function takeEmailOwnership() {
    const messageId = '<?= $correctMessage->id ?? '' ?>';

    if (!messageId) {
        showToast('error', 'Invalid message ID');
        return;
    }

    if (!confirm('Take ownership of this email conversation? This will assign all messages in this thread to you.')) {
        return;
    }

    const formData = new FormData();
    formData.append('message_id', messageId);
    formData.append('action', 'take_ownership');

    fetch('<?= BASE_URL ?>/notification_center/manageEmailOwnership', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message || 'Ownership taken successfully');
            // Refresh the page to show updated ownership
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast('error', data.message || 'Failed to take ownership');
        }
    })
    .catch(error => {
        console.error('Error taking ownership:', error);
        showToast('error', 'Error taking ownership');
    });
}

/**
 * Release ownership of the current email message/thread
 */
function releaseEmailOwnership() {
    const messageId = '<?= $correctMessage->id ?? '' ?>';

    if (!messageId) {
        showToast('error', 'Invalid message ID');
        return;
    }

    if (!confirm('Release ownership of this email conversation? This will make the conversation unassigned.')) {
        return;
    }

    const formData = new FormData();
    formData.append('message_id', messageId);
    formData.append('action', 'release_ownership');

    fetch('<?= BASE_URL ?>/notification_center/manageEmailOwnership', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message || 'Ownership released successfully');
            // Refresh the page to show updated ownership
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast('error', data.message || 'Failed to release ownership');
        }
    })
    .catch(error => {
        console.error('Error releasing ownership:', error);
        showToast('error', 'Error releasing ownership');
    });
}
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>