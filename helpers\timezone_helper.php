<?php
/**
 * Timezone Helper Functions
 * 
 * This file contains helper functions for handling user timezones
 * and converting dates/times to user's local timezone.
 */

/**
 * Get user's timezone from database
 * 
 * @param int $userId User ID
 * @return string User's timezone or default
 */
function getUserTimezone($userId) {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("getUserTimezone() called with userId: " . ($userId ?? 'null'));
    }

    if (empty($userId)) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("getUserTimezone() - Empty userId, returning default: America/New_York");
        }
        return 'America/New_York'; // Default timezone
    }

    try {
        $db = new Database();
        $db->query('SELECT timezone FROM users WHERE id = :id');
        $db->bind(':id', $userId);
        $user = $db->single();

        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("getUserTimezone() - Database query result: " . print_r($user, true));
        }

        if ($user && !empty($user->timezone)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("getUserTimezone() - Returning user timezone: " . $user->timezone);
            }
            return $user->timezone;
        } else {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("getUserTimezone() - No timezone found for user, returning default: America/New_York");
            }
        }
    } catch (Exception $e) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Error getting user timezone: " . $e->getMessage());
            error_log("getUserTimezone() - Exception occurred, returning default: America/New_York");
        }
    }

    return 'America/New_York'; // Default timezone
}

/**
 * Format date/time for user's timezone
 * 
 * @param string $datetime Date/time string (assumed to be in UTC)
 * @param int $userId User ID (optional, uses current user if not provided)
 * @param string $format Output format (default: 'M j, Y g:i A')
 * @return string Formatted date/time in user's timezone
 * 
 * Note: Input datetime should be in UTC format. Use gmdate() instead of date() 
 * when passing current time to ensure UTC input.
 */
function formatDateTimeForUser($datetime, $userId = null, $format = 'M j, Y g:i A') {
    if (empty($datetime)) {
        return '';
    }
    
    // Get user ID if not provided
    if ($userId === null) {
        // Check if session is active
        if (session_status() === PHP_SESSION_ACTIVE) {
            try {
                // Load Auth class if not already loaded
                if (!class_exists('Auth') && defined('APPROOT')) {
                    $authFile = APPROOT . '/core/Auth.php';
                    if (file_exists($authFile)) {
                        require_once $authFile;
                    }
                }
                
                if (class_exists('Auth')) {
                    $auth = new Auth();
                    if ($auth->isLoggedIn()) {
                        $userId = $auth->getCurrentUserId();
                    }
                }
            } catch (Exception $e) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("Error getting current user ID: " . $e->getMessage());
                }
                // Continue with null userId
            }
        }
    }
    
    // Get user's timezone
    $userTimezone = getUserTimezone($userId);
    
    try {
        // Create DateTime object from the input
        $date = new DateTime($datetime, new DateTimeZone('UTC'));
        
        // Convert to user's timezone
        $date->setTimezone(new DateTimeZone($userTimezone));
        
        // Return formatted date
        return $date->format($format);
    } catch (Exception $e) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Error formatting date for user timezone: " . $e->getMessage());
        }
        
        // Fallback to original datetime
        return date($format, strtotime($datetime));
    }
}

/**
 * Convert user input datetime to UTC for database storage
 * 
 * @param string $datetime Date/time string from user input (in user's timezone)
 * @param int $userId User ID (optional, uses current user if not provided)
 * @return string UTC datetime string for database storage
 * 
 * Note: This function assumes the input datetime is in the user's local timezone
 * and converts it to UTC for database storage.
 */
function convertUserDateTimeToUTC($datetime, $userId = null) {
    if (empty($datetime)) {
        return null;
    }
    
    // Get user ID if not provided
    if ($userId === null) {
        // Check if session is active
        if (session_status() === PHP_SESSION_ACTIVE) {
            try {
                // Load Auth class if not already loaded
                if (!class_exists('Auth') && defined('APPROOT')) {
                    $authFile = APPROOT . '/core/Auth.php';
                    if (file_exists($authFile)) {
                        require_once $authFile;
                    }
                }
                
                if (class_exists('Auth')) {
                    $auth = new Auth();
                    if ($auth->isLoggedIn()) {
                        $userId = $auth->getCurrentUserId();
                    }
                }
            } catch (Exception $e) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("Error getting current user ID: " . $e->getMessage());
                }
                // Continue with null userId
            }
        }
    }
    
    // Get user's timezone
    $userTimezone = getUserTimezone($userId);
    
    try {
        // Create DateTime object assuming user's timezone
        $date = new DateTime($datetime, new DateTimeZone($userTimezone));
        
        // Convert to UTC
        $date->setTimezone(new DateTimeZone('UTC'));
        
        // Return in MySQL datetime format
        return $date->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Error converting user datetime to UTC: " . $e->getMessage());
        }
        
        // Fallback to original datetime
        return date('Y-m-d H:i:s', strtotime($datetime));
    }
}

/**
 * Convert UTC datetime from database to user's timezone for form display
 * 
 * @param string $utcDateTime UTC datetime string from database
 * @param int $userId User ID (optional, uses current user if not provided)
 * @param string $format Output format (default: 'Y-m-d\TH:i' for datetime-local inputs)
 * @return string Datetime string in user's timezone
 * 
 * Note: This function assumes the input datetime is in UTC format from database
 * and converts it to user's local timezone for form display.
 */
function convertUTCToUserDateTime($utcDateTime, $userId = null, $format = 'Y-m-d\TH:i') {
    if (empty($utcDateTime)) {
        return '';
    }
    
    // Get user ID if not provided
    if ($userId === null) {
        // Check if session is active
        if (session_status() === PHP_SESSION_ACTIVE) {
            try {
                // Load Auth class if not already loaded
                if (!class_exists('Auth') && defined('APPROOT')) {
                    $authFile = APPROOT . '/core/Auth.php';
                    if (file_exists($authFile)) {
                        require_once $authFile;
                    }
                }
                
                if (class_exists('Auth')) {
                    $auth = new Auth();
                    if ($auth->isLoggedIn()) {
                        $userId = $auth->getCurrentUserId();
                    }
                }
            } catch (Exception $e) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("Error getting current user ID: " . $e->getMessage());
                }
                // Continue with null userId
            }
        }
    }
    
    // Get user's timezone
    $userTimezone = getUserTimezone($userId);
    
    try {
        // Create DateTime object from UTC
        $date = new DateTime($utcDateTime, new DateTimeZone('UTC'));
        
        // Convert to user's timezone
        $date->setTimezone(new DateTimeZone($userTimezone));
        
        // Return in requested format
        return $date->format($format);
    } catch (Exception $e) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Error converting UTC to user datetime: " . $e->getMessage());
        }
        
        // Fallback to original datetime
        return date($format, strtotime($utcDateTime));
    }
}

/**
 * Get list of USA timezones
 * 
 * @return array Array of timezone values and labels
 */
function getUSATimezones() {
    return [
        'America/New_York' => 'Eastern Time (ET)',
        'America/Chicago' => 'Central Time (CT)', 
        'America/Denver' => 'Mountain Time (MT)',
        'America/Phoenix' => 'Mountain Time - Arizona (MST)',
        'America/Los_Angeles' => 'Pacific Time (PT)',
        'America/Anchorage' => 'Alaska Time (AKT)',
        'Pacific/Honolulu' => 'Hawaii Time (HST)',
        'America/Detroit' => 'Eastern Time - Michigan (ET)',
        'America/Indiana/Indianapolis' => 'Eastern Time - Indiana (ET)',
        'America/Kentucky/Louisville' => 'Eastern Time - Kentucky (ET)',
        'America/North_Dakota/Center' => 'Central Time - North Dakota (CT)',
        'America/Menominee' => 'Central Time - Michigan (CT)',
        'America/Boise' => 'Mountain Time - Idaho (MT)',
        'America/Juneau' => 'Alaska Time - Juneau (AKT)'
    ];
}

/**
 * Validate if timezone is a valid USA timezone
 * 
 * @param string $timezone Timezone to validate
 * @return bool True if valid, false otherwise
 */
function isValidUSATimezone($timezone) {
    $validTimezones = array_keys(getUSATimezones());
    return in_array($timezone, $validTimezones);
}

/**
 * Get timezone abbreviation for display
 * 
 * @param string $timezone Timezone identifier
 * @return string Timezone abbreviation
 */
function getTimezoneAbbreviation($timezone) {
    try {
        $tz = new DateTimeZone($timezone);
        $now = new DateTime('now', $tz);
        return $now->format('T');
    } catch (Exception $e) {
        return '';
    }
}