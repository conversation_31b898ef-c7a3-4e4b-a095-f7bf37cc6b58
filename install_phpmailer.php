<?php
/**
 * Install PHPMailer
 * 
 * This script helps install PHPMailer for better email functionality.
 */

// Define constants
define('APPROOT', dirname(__FILE__));

echo "<h1>📧 PHPMailer Installation Helper</h1>";

echo "<h2>🔍 Current Status</h2>";

// Check current status
$vendorDir = APPROOT . '/vendor';
$autoloadPath = $vendorDir . '/autoload.php';
$phpmailerDir = $vendorDir . '/phpmailer';

echo "<p><strong>Vendor directory:</strong> " . ($vendorDir) . "</p>";
echo "<p><strong>Vendor directory exists:</strong> " . (is_dir($vendorDir) ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Autoload file exists:</strong> " . (file_exists($autoloadPath) ? '✅ Yes' : '❌ No') . "</p>";

if (file_exists($autoloadPath)) {
    require_once $autoloadPath;
    $phpmailerAvailable = class_exists('PHPMailer\\PHPMailer\\PHPMailer');
    echo "<p><strong>PHPMailer available:</strong> " . ($phpmailerAvailable ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($phpmailerAvailable) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "<h3>✅ PHPMailer is Already Installed!</h3>";
        echo "<p>PHPMailer is available and ready to use. Your email system will use PHPMailer for enhanced functionality.</p>";
        echo "</div>";
        
        // Self-delete since PHPMailer is already available
        if (file_exists(__FILE__)) {
            unlink(__FILE__);
        }
        exit;
    }
}

echo "<h2>📦 Installation Options</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Option 1: Using Composer (Recommended)</h3>";
echo "<p>If you have Composer installed, run this command in your project root:</p>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'><code>composer require phpmailer/phpmailer</code></pre>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Option 2: Manual Installation</h3>";
echo "<p>If you don't have Composer, you can manually install PHPMailer:</p>";
echo "<ol>";
echo "<li>Download PHPMailer from <a href='https://github.com/PHPMailer/PHPMailer/releases' target='_blank'>GitHub Releases</a></li>";
echo "<li>Extract the files to <code>vendor/phpmailer/phpmailer/</code></li>";
echo "<li>Update your autoloader to include PHPMailer</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Option 3: Simple Download and Setup</h3>";
echo "<p>Click the button below to automatically download and set up PHPMailer:</p>";
echo "<button onclick='downloadPHPMailer()' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>Download and Install PHPMailer</button>";
echo "<div id='download-status' style='margin-top: 10px;'></div>";
echo "</div>";

echo "<h2>🔧 Current Email System Status</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>⚠️ Using Fallback Email System</h3>";
echo "<p>Your system is currently using PHP's built-in <code>mail()</code> function for sending emails. This works but has limitations:</p>";
echo "<ul>";
echo "<li>❌ No SMTP authentication</li>";
echo "<li>❌ Limited email formatting options</li>";
echo "<li>❌ No attachment support</li>";
echo "<li>❌ May be blocked by spam filters</li>";
echo "</ul>";
echo "<p><strong>With PHPMailer, you get:</strong></p>";
echo "<ul>";
echo "<li>✅ SMTP authentication support</li>";
echo "<li>✅ Better email formatting and HTML support</li>";
echo "<li>✅ Attachment support</li>";
echo "<li>✅ Better deliverability</li>";
echo "<li>✅ Error handling and debugging</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🚀 After Installation</h2>";
echo "<p>Once PHPMailer is installed:</p>";
echo "<ol>";
echo "<li>Your email system will automatically detect and use PHPMailer</li>";
echo "<li>Configure your SMTP settings in <code>/admin/settings_email</code></li>";
echo "<li>Test the email functionality</li>";
echo "<li>Enjoy improved email delivery!</li>";
echo "</ol>";

?>

<script>
async function downloadPHPMailer() {
    const statusDiv = document.getElementById('download-status');
    statusDiv.innerHTML = '<p style="color: #007bff;">⏳ Downloading PHPMailer...</p>';
    
    try {
        const response = await fetch('<?php echo $_SERVER['PHP_SELF']; ?>?action=download', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'download=1'
        });
        
        const result = await response.text();
        statusDiv.innerHTML = result;
    } catch (error) {
        statusDiv.innerHTML = '<p style="color: #dc3545;">❌ Download failed: ' + error.message + '</p>';
    }
}
</script>

<?php

// Handle download request
if (isset($_POST['download']) && $_POST['download'] == '1') {
    echo "<h3>📥 Downloading PHPMailer...</h3>";
    
    try {
        // Create vendor directory if it doesn't exist
        if (!is_dir($vendorDir)) {
            mkdir($vendorDir, 0755, true);
            echo "<p>✅ Created vendor directory</p>";
        }
        
        // Download PHPMailer
        $phpmailerUrl = 'https://github.com/PHPMailer/PHPMailer/archive/refs/tags/v6.8.0.zip';
        $zipFile = $vendorDir . '/phpmailer.zip';
        
        echo "<p>⏳ Downloading from GitHub...</p>";
        $zipContent = file_get_contents($phpmailerUrl);
        
        if ($zipContent === false) {
            throw new Exception('Failed to download PHPMailer from GitHub');
        }
        
        file_put_contents($zipFile, $zipContent);
        echo "<p>✅ Downloaded PHPMailer</p>";
        
        // Extract the zip file
        $zip = new ZipArchive;
        if ($zip->open($zipFile) === TRUE) {
            $zip->extractTo($vendorDir);
            $zip->close();
            echo "<p>✅ Extracted PHPMailer</p>";
            
            // Move files to correct location
            $extractedDir = $vendorDir . '/PHPMailer-6.8.0';
            $targetDir = $vendorDir . '/phpmailer/phpmailer';
            
            if (is_dir($extractedDir)) {
                if (!is_dir(dirname($targetDir))) {
                    mkdir(dirname($targetDir), 0755, true);
                }
                rename($extractedDir, $targetDir);
                echo "<p>✅ Moved files to correct location</p>";
            }
            
            // Clean up
            unlink($zipFile);
            
            // Create simple autoloader
            $autoloadContent = "<?php\n";
            $autoloadContent .= "// Simple autoloader for PHPMailer\n";
            $autoloadContent .= "spl_autoload_register(function (\$class) {\n";
            $autoloadContent .= "    if (strpos(\$class, 'PHPMailer\\\\') === 0) {\n";
            $autoloadContent .= "        \$file = __DIR__ . '/phpmailer/phpmailer/src/' . str_replace('\\\\', '/', substr(\$class, 10)) . '.php';\n";
            $autoloadContent .= "        if (file_exists(\$file)) {\n";
            $autoloadContent .= "            require_once \$file;\n";
            $autoloadContent .= "        }\n";
            $autoloadContent .= "    }\n";
            $autoloadContent .= "});\n";
            
            file_put_contents($autoloadPath, $autoloadContent);
            echo "<p>✅ Created autoloader</p>";
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
            echo "<h3>🎉 PHPMailer Installed Successfully!</h3>";
            echo "<p>PHPMailer has been installed and is ready to use. Your email system will now use PHPMailer for enhanced functionality.</p>";
            echo "<p><strong>Next steps:</strong></p>";
            echo "<ol>";
            echo "<li>Go to <code>/admin/settings_email</code> to configure SMTP settings</li>";
            echo "<li>Test the email functionality</li>";
            echo "</ol>";
            echo "</div>";
            
        } else {
            throw new Exception('Failed to extract PHPMailer zip file');
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "<h3>❌ Installation Failed</h3>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Please try manual installation or use Composer.</p>";
        echo "</div>";
    }
    
    // Self-delete after successful installation
    if (file_exists(__FILE__)) {
        unlink(__FILE__);
    }
    exit;
}

// Self-delete after 10 minutes if not used
if (file_exists(__FILE__) && (time() - filemtime(__FILE__)) > 600) {
    unlink(__FILE__);
}
?>
