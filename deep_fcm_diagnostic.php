<?php
/**
 * Deep FCM diagnostic to find the real issue
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Prevent direct access without security key
if (!isset($_GET['diag_key']) || $_GET['diag_key'] !== 'deep_fcm_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/helpers/fcm_v1_helper.php';

echo "=== DEEP FCM DIAGNOSTIC ===\n\n";

try {
    echo "1. FIREBASE PROJECT VERIFICATION\n";
    
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    $projectId = $serviceAccount['project_id'];
    
    echo "   Project ID: $projectId\n";
    echo "   Client Email: {$serviceAccount['client_email']}\n";
    
    echo "\n2. TESTING FIREBASE CONNECTIVITY\n";
    
    // Test if we can get OAuth token
    $fcm = new FCMv1Helper($serviceAccountPath);
    echo "   ✅ FCM Helper initialized\n";
    
    // Try to get access token (this will test OAuth)
    $reflection = new ReflectionClass($fcm);
    $method = $reflection->getMethod('getAccessToken');
    $method->setAccessible(true);
    
    try {
        $accessToken = $method->invoke($fcm);
        echo "   ✅ OAuth2 access token obtained\n";
        echo "   Token length: " . strlen($accessToken) . " characters\n";
    } catch (Exception $e) {
        echo "   ❌ OAuth2 failed: " . $e->getMessage() . "\n";
        exit;
    }
    
    echo "\n3. TESTING WITH KNOWN INVALID TOKEN\n";
    
    // Test with obviously invalid token to see Firebase response
    $invalidToken = "INVALID_TOKEN_TEST_123";
    $result = $fcm->sendNotification($invalidToken, "Test", "Test message");
    
    echo "   Invalid token result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
    if (!$result['success']) {
        echo "   Error: " . $result['error'] . "\n";
    }
    
    echo "\n4. CHECKING FIREBASE PROJECT SETTINGS\n";
    
    // Test FCM API endpoint directly
    $testUrl = "https://fcm.googleapis.com/v1/projects/$projectId/messages:send";
    echo "   FCM API URL: $testUrl\n";
    
    // Test with minimal payload
    $testPayload = json_encode([
        'message' => [
            'token' => 'test_token_123',
            'notification' => [
                'title' => 'Test',
                'body' => 'Test'
            ]
        ]
    ]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $testPayload);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "   Direct API test: HTTP $httpCode\n";
    echo "   Response: $response\n";
    
    echo "\n5. POSSIBLE ISSUES ANALYSIS\n";
    
    if ($httpCode === 404 && strpos($response, 'UNREGISTERED') !== false) {
        echo "   ✅ Firebase API is working correctly\n";
        echo "   ✅ Authentication is successful\n";
        echo "   ❌ Issue: Browser tokens are not being registered properly\n\n";
        
        echo "   LIKELY CAUSES:\n";
        echo "   1. Browser is creating tokens with old/cached VAPID key\n";
        echo "   2. Service worker is not using updated VAPID key\n";
        echo "   3. Browser subscription process has a bug\n";
        echo "   4. Firebase Web SDK version compatibility issue\n\n";
        
        echo "   SOLUTIONS TO TRY:\n";
        echo "   1. Complete browser reset (incognito mode)\n";
        echo "   2. Different browser entirely\n";
        echo "   3. Check service worker is using correct VAPID key\n";
        echo "   4. Wait 5-10 minutes for Firebase propagation\n";
        
    } else if ($httpCode === 403) {
        echo "   ❌ Authentication issue with service account\n";
        
    } else if ($httpCode === 400) {
        echo "   ❌ Request format issue\n";
        
    } else {
        echo "   ❌ Unexpected response: HTTP $httpCode\n";
        echo "   Response: $response\n";
    }
    
    echo "\n6. IMMEDIATE TESTS TO TRY\n";
    
    echo "   TEST A: Incognito mode\n";
    echo "   1. Open incognito/private browser window\n";
    echo "   2. Go to your site\n";
    echo "   3. Allow notifications\n";
    echo "   4. Test immediately\n\n";
    
    echo "   TEST B: Different browser\n";
    echo "   1. Try Firefox if using Chrome (or vice versa)\n";
    echo "   2. Allow notifications\n";
    echo "   3. Test immediately\n\n";
    
    echo "   TEST C: Wait and retry\n";
    echo "   1. Wait 10 minutes\n";
    echo "   2. Clear browser data\n";
    echo "   3. Test again\n\n";
    
    echo "7. SERVICE WORKER CHECK\n";
    echo "   Check if your service worker is using the correct VAPID key:\n";
    echo "   1. Press F12 in browser\n";
    echo "   2. Go to Application tab\n";
    echo "   3. Click Service Workers\n";
    echo "   4. Look for any errors\n";
    echo "   5. Check if SW is using updated VAPID key\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== END DIAGNOSTIC ===\n";
?>
