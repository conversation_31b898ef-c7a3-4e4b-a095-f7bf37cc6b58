<?php
/**
 * Check FCM Tokens
 * 
 * This script checks how many FCM tokens a user has, which might explain multiple push notifications
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>📱 Check FCM Tokens</h1>";

try {
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🔍 FCM Token Analysis for User {$userId}</h2>";
    
    // Check if fcm_tokens table exists
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    $tableExists = $db->single();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ fcm_tokens table does not exist!</p>";
        exit;
    }
    
    // Get all FCM tokens for the user
    $db->query("SELECT * FROM fcm_tokens WHERE user_id = :user_id ORDER BY created_at DESC");
    $db->bind(':user_id', $userId);
    $tokens = $db->resultSet();
    
    if (empty($tokens)) {
        echo "<p style='color: orange;'>⚠️ No FCM tokens found for user {$userId}</p>";
        echo "<p>This means the user won't receive push notifications.</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($tokens) . " FCM token(s) for user {$userId}</p>";
        
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Token (First 20 chars)</th><th>Active</th><th>Device Info</th><th>Created</th><th>Last Used</th>";
        echo "</tr>";
        
        $activeCount = 0;
        foreach ($tokens as $token) {
            if ($token->active) $activeCount++;
            
            $rowColor = $token->active ? '' : 'background: #ffeeee;';
            echo "<tr style='{$rowColor}'>";
            echo "<td>{$token->id}</td>";
            echo "<td>" . htmlspecialchars(substr($token->token, 0, 20)) . "...</td>";
            echo "<td>" . ($token->active ? '✅ Yes' : '❌ No') . "</td>";
            echo "<td>" . htmlspecialchars($token->device_info ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($token->created_at) . "</td>";
            echo "<td>" . htmlspecialchars($token->last_used ?? 'Never') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>📊 Summary</h3>";
        echo "<p><strong>Total tokens:</strong> " . count($tokens) . "</p>";
        echo "<p><strong>Active tokens:</strong> {$activeCount}</p>";
        echo "<p><strong>Inactive tokens:</strong> " . (count($tokens) - $activeCount) . "</p>";
        
        if ($activeCount > 1) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
            echo "<h4>⚠️ Multiple Active Tokens Found!</h4>";
            echo "<p><strong>This could explain why you're getting multiple push notifications!</strong></p>";
            echo "<p>Each active FCM token will receive a separate push notification.</p>";
            echo "<p><strong>Possible causes:</strong></p>";
            echo "<ul>";
            echo "<li>User opened the PWA on multiple devices/browsers</li>";
            echo "<li>User refreshed the page multiple times (creating new tokens)</li>";
            echo "<li>Old tokens weren't properly cleaned up</li>";
            echo "</ul>";
            echo "</div>";
        } else if ($activeCount === 1) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
            echo "<h4>✅ Single Active Token</h4>";
            echo "<p>This is correct - user should receive exactly 1 push notification.</p>";
            echo "<p>If you're still getting 3 notifications, the issue is elsewhere.</p>";
            echo "</div>";
        }
        
        echo "<h3>🔧 Token Cleanup</h3>";
        echo "<p>To clean up old/inactive tokens:</p>";
        echo "<code>DELETE FROM fcm_tokens WHERE user_id = {$userId} AND active = 0;</code>";
        
        if ($activeCount > 1) {
            echo "<p>To keep only the most recent active token:</p>";
            echo "<code>DELETE FROM fcm_tokens WHERE user_id = {$userId} AND id NOT IN (SELECT * FROM (SELECT MAX(id) FROM fcm_tokens WHERE user_id = {$userId} AND active = 1) as temp);</code>";
        }
    }
    
    echo "<h2>🌐 Global FCM Token Statistics</h2>";
    
    // Get overall statistics
    $db->query("SELECT 
                    COUNT(*) as total_tokens,
                    SUM(active) as active_tokens,
                    COUNT(DISTINCT user_id) as unique_users
                FROM fcm_tokens");
    $stats = $db->single();
    
    echo "<p><strong>Total FCM tokens in system:</strong> {$stats->total_tokens}</p>";
    echo "<p><strong>Active FCM tokens:</strong> {$stats->active_tokens}</p>";
    echo "<p><strong>Users with tokens:</strong> {$stats->unique_users}</p>";
    
    // Check for users with multiple active tokens
    $db->query("SELECT user_id, COUNT(*) as token_count 
                FROM fcm_tokens 
                WHERE active = 1 
                GROUP BY user_id 
                HAVING COUNT(*) > 1 
                ORDER BY token_count DESC");
    $multipleTokenUsers = $db->resultSet();
    
    if (!empty($multipleTokenUsers)) {
        echo "<h4>👥 Users with Multiple Active Tokens</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>User ID</th><th>Active Token Count</th></tr>";
        foreach ($multipleTokenUsers as $user) {
            echo "<tr><td>{$user->user_id}</td><td>{$user->token_count}</td></tr>";
        }
        echo "</table>";
        echo "<p style='color: orange;'>⚠️ These users will receive multiple push notifications!</p>";
    } else {
        echo "<p style='color: green;'>✅ No users have multiple active tokens</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Check completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
