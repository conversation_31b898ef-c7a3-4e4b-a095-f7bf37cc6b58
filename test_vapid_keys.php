<?php
/**
 * Test VAPID Keys Configuration
 * This script tests if VAPID keys are properly configured and valid
 */

require_once 'config/config.php';

echo "<h1>VAPID Keys Test</h1>\n";

// Check if VAPID keys are defined
echo "<h2>1. VAPID Key Configuration</h2>\n";
if (defined('VAPID_PUBLIC_KEY')) {
    echo "<p style='color: green;'>✓ VAPID_PUBLIC_KEY is defined</p>\n";
    echo "<p><strong>Public Key:</strong> " . VAPID_PUBLIC_KEY . "</p>\n";
    echo "<p><strong>Length:</strong> " . strlen(VAPID_PUBLIC_KEY) . " characters</p>\n";
} else {
    echo "<p style='color: red;'>✗ VAPID_PUBLIC_KEY is NOT defined</p>\n";
}

if (defined('VAPID_PRIVATE_KEY')) {
    echo "<p style='color: green;'>✓ VAPID_PRIVATE_KEY is defined</p>\n";
    echo "<p><strong>Private Key:</strong> " . substr(VAPID_PRIVATE_KEY, 0, 20) . "...</p>\n";
    echo "<p><strong>Length:</strong> " . strlen(VAPID_PRIVATE_KEY) . " characters</p>\n";
} else {
    echo "<p style='color: red;'>✗ VAPID_PRIVATE_KEY is NOT defined</p>\n";
}

// Test VAPID key format
echo "<h2>2. VAPID Key Format Validation</h2>\n";

if (defined('VAPID_PUBLIC_KEY')) {
    $publicKey = VAPID_PUBLIC_KEY;
    
    // Check if it's base64url encoded
    $isValidBase64 = preg_match('/^[A-Za-z0-9_-]+$/', $publicKey);
    if ($isValidBase64) {
        echo "<p style='color: green;'>✓ Public key format appears valid (base64url)</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Public key format appears invalid</p>\n";
    }
    
    // Check length (should be around 87-88 characters for P-256 key)
    $length = strlen($publicKey);
    if ($length >= 85 && $length <= 90) {
        echo "<p style='color: green;'>✓ Public key length is appropriate ($length chars)</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ Public key length might be incorrect ($length chars, expected ~87)</p>\n";
    }
}

// Test API endpoint
echo "<h2>3. VAPID Key API Endpoint Test</h2>\n";

$apiUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/api/pwa/vapid-key';
echo "<p><strong>Testing URL:</strong> $apiUrl</p>\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p style='color: red;'>✗ cURL Error: $error</p>\n";
} else {
    echo "<p><strong>HTTP Status:</strong> $httpCode</p>\n";
    
    if ($httpCode == 200) {
        echo "<p style='color: green;'>✓ API endpoint is accessible</p>\n";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "<p><strong>Response:</strong></p>\n";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>\n";
            
            if (isset($data['success']) && $data['success'] && isset($data['publicKey'])) {
                echo "<p style='color: green;'>✓ API returns valid VAPID public key</p>\n";
                
                // Compare with config
                if ($data['publicKey'] === VAPID_PUBLIC_KEY) {
                    echo "<p style='color: green;'>✓ API key matches config key</p>\n";
                } else {
                    echo "<p style='color: red;'>✗ API key does NOT match config key</p>\n";
                }
            } else {
                echo "<p style='color: red;'>✗ API response is invalid or missing publicKey</p>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ Invalid JSON response</p>\n";
            echo "<p><strong>Raw Response:</strong> " . htmlspecialchars($response) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ API endpoint returned HTTP $httpCode</p>\n";
        echo "<p><strong>Response:</strong> " . htmlspecialchars($response) . "</p>\n";
    }
}

// Test VAPID key conversion
echo "<h2>4. VAPID Key Conversion Test</h2>\n";

if (defined('VAPID_PUBLIC_KEY')) {
    try {
        $publicKey = VAPID_PUBLIC_KEY;
        
        // Test base64url to binary conversion
        $padding = str_repeat('=', (4 - strlen($publicKey) % 4) % 4);
        $base64 = strtr($publicKey . $padding, '-_', '+/');
        $binary = base64_decode($base64);
        
        if ($binary !== false) {
            echo "<p style='color: green;'>✓ VAPID key can be converted to binary</p>\n";
            echo "<p><strong>Binary length:</strong> " . strlen($binary) . " bytes</p>\n";
            
            // P-256 public key should be 65 bytes (uncompressed) or 33 bytes (compressed)
            $binaryLength = strlen($binary);
            if ($binaryLength == 65 || $binaryLength == 33) {
                echo "<p style='color: green;'>✓ Binary key length is correct for P-256 key</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠ Binary key length ($binaryLength bytes) is unusual for P-256</p>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ VAPID key cannot be converted to binary</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error testing VAPID key conversion: " . $e->getMessage() . "</p>\n";
    }
}

// Recommendations
echo "<h2>5. Recommendations</h2>\n";

if (!defined('VAPID_PUBLIC_KEY') || !defined('VAPID_PRIVATE_KEY')) {
    echo "<p style='color: red;'><strong>Action Required:</strong> Generate VAPID keys using the generate_vapid_keys_web.php script</p>\n";
} else {
    echo "<p style='color: green;'>VAPID keys are configured. If you're still getting pushManager errors, the issue is likely in the client-side implementation or service worker registration.</p>\n";
}

echo "<h2>6. FCM v1 + OAuth2 Notes</h2>\n";
echo "<p><strong>Important:</strong> Even though you're using FCM v1 with OAuth2 on the server side, you still need VAPID keys for client-side Firebase SDK token generation.</p>\n";
echo "<p>The OAuth2 is only used for server-to-FCM communication, not for client-side registration.</p>\n";
echo "<p>The pushManager error is likely occurring in the Firebase SDK's internal token generation process.</p>\n";

?>