<?php
/**
 * Example: How to properly send notifications using the notification system
 */

// Method 1: Using NotificationModel to queue notifications
require_once 'models/NotificationModel.php';

$notificationModel = new NotificationModel();

// Queue a notification for immediate processing
$success = $notificationModel->queueTestNotification(
    $userId = 3,           // User ID
    $type = 'toast',       // Notification type: 'email', 'sms', 'push', 'toast'
    $subject = 'Test Toast Notification',
    $message = 'This is a test toast notification message.',
    $scheduledTime = null  // null = immediate, or datetime string for scheduled
);

// Queue a push notification
$success = $notificationModel->queueTestNotification(
    $userId = 3,
    $type = 'push',
    $subject = 'Test Push Notification',
    $message = 'This is a test push notification message.',
    $scheduledTime = null
);

// Method 2: Using NotificationService directly for immediate sending
require_once 'models/NotificationService.php';

$notificationService = new NotificationService();

// Send test toast notification immediately
$success = $notificationService->sendTestToastNotification(
    $userId = 3,
    $title = 'Immediate Toast',
    $message = 'This toast will be sent immediately'
);

// Send test push notification immediately  
$success = $notificationService->sendTestPushNotification(
    $userId = 3,
    $title = 'Immediate Push',
    $message = 'This push will be sent immediately'
);

// Method 3: Using NotificationController endpoints (via HTTP)
// POST to /notification/test with:
// - user_id: 3
// - type: 'toast' or 'push'
// - subject: 'Test Subject'
// - message: 'Test Message'
// - delivery_method: 'immediate' or 'queue'

// Method 4: For event-based notifications
$notificationModel->scheduleEventNotifications(
    $eventId = 123,
    $eventType = 'calendar_event', // or 'car_show'
    $eventDate = '2025-07-15 10:00:00'
);

echo "Notifications queued/sent successfully!";
?>
