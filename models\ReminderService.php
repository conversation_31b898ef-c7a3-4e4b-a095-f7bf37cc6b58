<?php
/**
 * Reminder Service
 * 
 * Handles reminder scheduling, notifications, and management for email messages
 */
class ReminderService {
    private $db;
    private $unifiedMessageModel;
    
    public function __construct() {
        $this->db = new Database();
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        $this->unifiedMessageModel = new UnifiedMessageModel();
    }
    
    /**
     * Create a new reminder
     * User inputs reminder time in their timezone, convert to UTC for storage
     */
    public function createReminder($messageId, $adminUserId, $reminderTime, $note = '') {
        try {
            // Convert user input time to UTC for storage
            require_once APPROOT . '/helpers/timezone_helper.php';
            $reminderTimeUTC = convertUserDateTimeToUTC($reminderTime, $adminUserId);

            // Validate reminder time is in the future (compare UTC times)
            $currentUTC = gmdate('Y-m-d H:i:s');
            if (strtotime($reminderTimeUTC) <= strtotime($currentUTC)) {
                throw new Exception('Reminder time must be in the future');
            }
            
            // Check if message exists and admin has access
            $sql = "SELECT id FROM messages WHERE id = :message_id AND to_user_id = :admin_id";
            $this->db->query($sql);
            $this->db->bind(':message_id', $messageId);
            $this->db->bind(':admin_id', $adminUserId);
            
            if (!$this->db->single()) {
                throw new Exception('Message not found or access denied');
            }
            
            // Create reminder (store UTC time in database)
            $sql = "INSERT INTO message_reminders (message_id, admin_user_id, reminder_time, note)
                    VALUES (:message_id, :admin_id, :reminder_time, :note)";

            $this->db->query($sql);
            $this->db->bind(':message_id', $messageId);
            $this->db->bind(':admin_id', $adminUserId);
            $this->db->bind(':reminder_time', $reminderTimeUTC);
            $this->db->bind(':note', $note);
            
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            }
            
            throw new Exception('Failed to create reminder');
            
        } catch (Exception $e) {
            error_log("ReminderService::createReminder - Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Update an existing reminder
     */
    public function updateReminder($reminderId, $adminUserId, $reminderTime = null, $note = null) {
        try {
            // Check if reminder exists and belongs to admin
            $sql = "SELECT id FROM message_reminders WHERE id = :reminder_id AND admin_user_id = :admin_id";
            $this->db->query($sql);
            $this->db->bind(':reminder_id', $reminderId);
            $this->db->bind(':admin_id', $adminUserId);
            
            if (!$this->db->single()) {
                throw new Exception('Reminder not found or access denied');
            }
            
            $updates = [];
            $params = [':reminder_id' => $reminderId, ':admin_id' => $adminUserId];
            
            if ($reminderTime !== null) {
                if (strtotime($reminderTime) <= time()) {
                    throw new Exception('Reminder time must be in the future');
                }
                $updates[] = "reminder_time = :reminder_time";
                $params[':reminder_time'] = $reminderTime;
            }
            
            if ($note !== null) {
                $updates[] = "note = :note";
                $params[':note'] = $note;
            }
            
            if (empty($updates)) {
                return true; // Nothing to update
            }
            
            $sql = "UPDATE message_reminders SET " . implode(', ', $updates) . " 
                    WHERE id = :reminder_id AND admin_user_id = :admin_id";
            
            $this->db->query($sql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            return $this->db->execute();
            
        } catch (Exception $e) {
            error_log("ReminderService::updateReminder - Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Delete a reminder
     */
    public function deleteReminder($reminderId, $adminUserId) {
        try {
            $sql = "DELETE FROM message_reminders WHERE id = :reminder_id AND admin_user_id = :admin_id";
            $this->db->query($sql);
            $this->db->bind(':reminder_id', $reminderId);
            $this->db->bind(':admin_id', $adminUserId);
            
            return $this->db->execute();
            
        } catch (Exception $e) {
            error_log("ReminderService::deleteReminder - Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Snooze a reminder
     */
    public function snoozeReminder($reminderId, $adminUserId, $snoozeMinutes) {
        try {
            $newReminderTime = date('Y-m-d H:i:s', time() + ($snoozeMinutes * 60));
            
            $sql = "UPDATE message_reminders 
                    SET reminder_time = :new_time, is_sent = 0 
                    WHERE id = :reminder_id AND admin_user_id = :admin_id";
            
            $this->db->query($sql);
            $this->db->bind(':new_time', $newReminderTime);
            $this->db->bind(':reminder_id', $reminderId);
            $this->db->bind(':admin_id', $adminUserId);
            
            return $this->db->execute();
            
        } catch (Exception $e) {
            error_log("ReminderService::snoozeReminder - Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get pending reminders for an admin
     */
    public function getPendingReminders($adminUserId, $limit = 10) {
        $sql = "SELECT r.*, m.subject, m.ticket_number, m.id as message_id
                FROM message_reminders r
                JOIN messages m ON r.message_id = m.id
                WHERE r.admin_user_id = :admin_id AND r.is_sent = 0 AND r.reminder_time <= NOW()
                ORDER BY r.reminder_time ASC
                LIMIT :limit";

        $this->db->query($sql);
        $this->db->bind(':admin_id', $adminUserId);
        $this->db->bind(':limit', $limit);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get all reminders for an admin
     */
    public function getAdminReminders($adminUserId, $includeCompleted = false) {
        $whereClause = "WHERE r.admin_user_id = :admin_id";
        if (!$includeCompleted) {
            $whereClause .= " AND r.is_sent = 0";
        }
        
        $sql = "SELECT r.*, m.subject, m.ticket_number, m.id as message_id
                FROM message_reminders r
                JOIN messages m ON r.message_id = m.id
                {$whereClause}
                ORDER BY r.reminder_time ASC";
        
        $this->db->query($sql);
        $this->db->bind(':admin_id', $adminUserId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Process due reminders (called by cron job)
     */
    public function processDueReminders() {
        try {
            // Get all due reminders
            $sql = "SELECT r.*, m.subject, m.ticket_number, m.id as message_id, u.name as admin_name, u.id as admin_id
                    FROM message_reminders r
                    JOIN messages m ON r.message_id = m.id
                    JOIN users u ON r.admin_user_id = u.id
                    WHERE r.is_sent = 0 AND r.reminder_time <= NOW()
                    ORDER BY r.reminder_time ASC";

            $this->db->query($sql);
            $dueReminders = $this->db->resultSet();
            
            $processedCount = 0;
            $failedCount = 0;
            
            foreach ($dueReminders as $reminder) {
                try {
                    $this->sendReminderNotification($reminder);
                    $this->markReminderAsSent($reminder->id);
                    $processedCount++;
                } catch (Exception $e) {
                    error_log("ReminderService::processDueReminders - Failed to process reminder {$reminder->id}: " . $e->getMessage());
                    $failedCount++;
                }
            }
            
            return [
                'processed' => $processedCount,
                'failed' => $failedCount,
                'total' => count($dueReminders)
            ];
            
        } catch (Exception $e) {
            error_log("ReminderService::processDueReminders - Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Send reminder notification
     */
    private function sendReminderNotification($reminder) {
        // Create reminder message
        $subject = "Reminder: " . $reminder->subject;
        if ($reminder->ticket_number) {
            $subject .= " [" . $reminder->ticket_number . "]";
        }
        
        $message = "This is a reminder about the following message:\n\n";
        $message .= "Subject: " . $reminder->subject . "\n";
        if ($reminder->ticket_number) {
            $message .= "Ticket: " . $reminder->ticket_number . "\n";
        }
        // Display reminder time in admin's timezone
        require_once APPROOT . '/helpers/timezone_helper.php';
        $reminderTimeFormatted = formatDateTimeForUser($reminder->reminder_time, $reminder->admin_id, 'M j, Y g:i A T');
        $message .= "Reminder set for: " . $reminderTimeFormatted . "\n\n";
        
        if (!empty($reminder->note)) {
            $message .= "Your note: " . $reminder->note . "\n\n";
        }
        
        $message .= "View message: " . URLROOT . "/admin/email_dashboard/viewMessage/" . $reminder->message_id . "\n\n";
        $message .= "---\n";
        $message .= "This is an automated reminder notification.";
        
        // Send notification using unified messaging system
        $messageId = $this->unifiedMessageModel->sendMessage(
            1, // System user
            $reminder->admin_id,
            $subject,
            $message,
            null, // No show ID
            'system', // System message
            false, // No reply required
            null // Not a reply
        );
        
        if (!$messageId) {
            throw new Exception('Failed to send reminder notification');
        }
        
        return $messageId;
    }
    
    /**
     * Mark reminder as sent
     */
    private function markReminderAsSent($reminderId) {
        $sql = "UPDATE message_reminders SET is_sent = 1, sent_at = NOW() WHERE id = :reminder_id";
        $this->db->query($sql);
        $this->db->bind(':reminder_id', $reminderId);

        return $this->db->execute();
    }
    
    /**
     * Get reminder statistics
     */
    public function getReminderStats($adminUserId = null) {
        $whereClause = $adminUserId ? "WHERE admin_user_id = :admin_id" : "";
        
        $sql = "SELECT
                    COUNT(*) as total_reminders,
                    SUM(CASE WHEN is_sent = 0 AND reminder_time <= NOW() THEN 1 ELSE 0 END) as overdue,
                    SUM(CASE WHEN is_sent = 0 AND reminder_time > NOW() THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN is_sent = 1 THEN 1 ELSE 0 END) as completed
                FROM message_reminders {$whereClause}";

        $this->db->query($sql);
        if ($adminUserId) {
            $this->db->bind(':admin_id', $adminUserId);
        }
        
        return $this->db->single();
    }
    
    /**
     * Clean up old completed reminders
     */
    public function cleanupOldReminders($daysOld = 90) {
        $sql = "DELETE FROM message_reminders
                WHERE is_sent = 1 AND sent_at < DATE_SUB(NOW(), INTERVAL :days DAY)";

        $this->db->query($sql);
        $this->db->bind(':days', $daysOld);

        return $this->db->execute();
    }
    
    /**
     * Get upcoming reminders for dashboard
     */
    public function getUpcomingReminders($adminUserId, $hours = 24, $limit = 5) {
        $sql = "SELECT r.*, m.subject, m.ticket_number, m.id as message_id
                FROM message_reminders r
                JOIN messages m ON r.message_id = m.id
                WHERE r.admin_user_id = :admin_id
                AND r.is_sent = 0
                AND r.reminder_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL :hours HOUR)
                ORDER BY r.reminder_time ASC
                LIMIT :limit";

        $this->db->query($sql);
        $this->db->bind(':admin_id', $adminUserId);
        $this->db->bind(':hours', $hours);
        $this->db->bind(':limit', $limit);
        
        return $this->db->resultSet();
    }
}
