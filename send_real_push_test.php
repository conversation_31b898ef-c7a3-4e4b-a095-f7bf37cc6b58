<?php
/**
 * Send a real push notification test
 */

// Prevent direct access via web browser without key
if (isset($_SERVER['HTTP_HOST'])) {
    if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'real_push_test_2025') {
        http_response_code(404);
        exit('Not Found');
    }
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';

// Initialize core classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Controller.php';

// Load models and helpers
require_once APPROOT . '/models/NotificationModel.php';
require_once APPROOT . '/models/NotificationService.php';
require_once APPROOT . '/helpers/notification_helper.php';

// Initialize notification helper
initializeNotificationHelper();

$userId = 3; // Test user ID

echo "=== REAL PUSH NOTIFICATION TEST ===\n\n";

try {
    $notificationModel = new NotificationModel();
    $notificationService = new NotificationService();
    
    echo "1. CHECKING CURRENT SUBSCRIPTIONS\n";
    $subscriptions = $notificationModel->getUserPushSubscriptions($userId);
    echo "   Active subscriptions: " . count($subscriptions) . "\n";
    
    if (empty($subscriptions)) {
        echo "❌ NO SUBSCRIPTIONS FOUND\n";
        echo "   Please visit the site and enable notifications first\n";
        exit;
    }
    
    // Show the most recent subscription
    $latestSub = $subscriptions[0];
    echo "   Latest subscription endpoint: " . substr($latestSub->endpoint, 0, 60) . "...\n";
    echo "   Created: " . $latestSub->created_at . "\n";
    
    echo "\n2. SENDING REAL PUSH NOTIFICATION\n";
    
    $title = "🔔 REAL PUSH TEST";
    $message = "This is a REAL push notification sent at " . date('H:i:s') . ". If you see this, push notifications are working!";
    
    echo "   Title: $title\n";
    echo "   Message: $message\n";
    echo "   Sending to $userId...\n";
    
    // Send using the notification service directly
    $result = $notificationService->sendTestPushNotification($userId, $title, $message);
    
    echo "   Result: " . ($result ? "✅ SUCCESS" : "❌ FAILED") . "\n";
    
    if ($result) {
        echo "\n3. NOTIFICATION SENT SUCCESSFULLY!\n";
        echo "   🔍 CHECK YOUR BROWSER NOW!\n";
        echo "   📱 Check your phone if using PWA\n";
        echo "   🔔 Look for the notification popup\n";
        echo "   ⏰ Notification should appear within 5-10 seconds\n";
        
        echo "\n4. TROUBLESHOOTING IF YOU DON'T SEE IT:\n";
        echo "   □ Check browser notification settings\n";
        echo "   □ Check Do Not Disturb mode\n";
        echo "   □ Check if notifications are enabled for this site\n";
        echo "   □ Try refreshing the page and allowing notifications again\n";
        echo "   □ Check if you have multiple browser tabs/windows open\n";
        
        echo "\n5. TECHNICAL DETAILS:\n";
        echo "   - Notification sent to " . count($subscriptions) . " subscription(s)\n";
        echo "   - Using Firebase Cloud Messaging (FCM)\n";
        echo "   - VAPID authentication enabled\n";
        echo "   - Notification stored in database as backup\n";
        
    } else {
        echo "\n❌ NOTIFICATION SENDING FAILED\n";
        echo "   Check server logs for detailed error information\n";
    }
    
    echo "\n6. CHECKING RECENT NOTIFICATIONS\n";
    $db = new Database();
    $db->query("SELECT COUNT(*) as count FROM user_push_notifications WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
    $db->bind(1, $userId);
    $db->execute();
    $recentCount = $db->single()->count;
    
    echo "   Push notifications in last 5 minutes: $recentCount\n";
    
    if ($recentCount > 0) {
        echo "   ✅ Notifications are being stored in database\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== END TEST ===\n";
?>
