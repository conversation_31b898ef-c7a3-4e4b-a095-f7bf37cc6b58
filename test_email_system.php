<?php
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/SettingsModel.php';

echo "=== Email System Test ===\n\n";

// Test 1: IMAP Extension
echo "1. Testing IMAP extension...\n";
if (extension_loaded('imap')) {
    echo "   ✓ IMAP extension loaded\n";
} else {
    echo "   ✗ IMAP extension not loaded\n";
}

if (function_exists('imap_open')) {
    echo "   ✓ imap_open function available\n";
} else {
    echo "   ✗ imap_open function not available\n";
}

// Test 2: Database tables
echo "\n2. Testing database tables...\n";
$db = new Database();

$tables = [
    'messages',
    'admin_email_folders', 
    'message_reminders',
    'email_templates',
    'email_processing_log',
    'ticket_numbers',
    'message_attachments'
];

foreach ($tables as $table) {
    try {
        $db->query("SHOW TABLES LIKE '$table'");
        if ($db->single()) {
            echo "   ✓ Table '$table' exists\n";
        } else {
            echo "   ✗ Table '$table' missing\n";
        }
    } catch (Exception $e) {
        echo "   ✗ Error checking table '$table': " . $e->getMessage() . "\n";
    }
}

// Test 3: Settings
echo "\n3. Testing email settings...\n";
$settingsModel = new SettingsModel();
$testSettings = ['email_processing_enabled', 'email_server_protocol', 'ticket_number_prefix'];

foreach ($testSettings as $setting) {
    try {
        $value = $settingsModel->getSetting($setting);
        echo "   ✓ Setting '$setting' = '$value'\n";
    } catch (Exception $e) {
        echo "   ✗ Error getting setting '$setting': " . $e->getMessage() . "\n";
    }
}

// Test 4: Email templates
echo "\n4. Testing email templates...\n";
try {
    $db->query("SELECT COUNT(*) as count FROM email_templates");
    $result = $db->single();
    echo "   ✓ Email templates: " . $result->count . " found\n";
} catch (Exception $e) {
    echo "   ✗ Error checking templates: " . $e->getMessage() . "\n";
}

// Test 5: Admin folders
echo "\n5. Testing admin folders...\n";
try {
    $db->query("SELECT COUNT(*) as count FROM admin_email_folders");
    $result = $db->single();
    echo "   ✓ Admin folders: " . $result->count . " found\n";
} catch (Exception $e) {
    echo "   ✗ Error checking folders: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "\nIf all tests pass, your email integration system is ready!\n";
echo "Next steps:\n";
echo "1. Go to /admin/settings_email_server to configure your email server\n";
echo "2. Test the connection\n";
echo "3. Enable email processing\n";
echo "4. Set up the cron jobs\n";
?>