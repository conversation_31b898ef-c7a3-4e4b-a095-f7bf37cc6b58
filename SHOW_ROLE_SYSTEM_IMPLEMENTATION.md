# Show Role Assignment System - Implementation Summary

## Overview

I have successfully implemented a complete per-show role assignment system that addresses all your requirements:

✅ **Per-show role assignments** - Users can have different roles for different shows  
✅ **Primary role preservation** - Users keep their main system role  
✅ **Approval workflow** - Coordinator assignments require user approval  
✅ **Admin privileges** - <PERSON><PERSON> can assign directly with user search  
✅ **User ID assignment** - Coordinators assign using user IDs for privacy  
✅ **Automatic cleanup** - Expired assignments removed 1 week after show end  
✅ **Notification system** - Email notifications for all actions  
✅ **Backward compatibility** - All existing code continues to work unchanged  

## Files Created

### Database Schema
- `sql/create_show_role_system.sql` - Database tables with foreign keys
- `sql/create_show_role_system_no_fk.sql` - Database tables without foreign keys

### Core System Files
- `models/ShowRoleModel.php` - Main model handling all role assignment logic
- `controllers/ShowRoleController.php` - Controller for role assignment interface
- `views/show_roles/manage.php` - Admin/coordinator role management interface
- `views/show_roles/my_requests.php` - User interface for managing role requests
- `views/show_roles/admin_overview.php` - Admin system-wide overview of all role assignments

### Automation & Maintenance
- `cron/cleanup_show_roles.php` - Daily cron job for automatic cleanup
- `install_show_role_system.php` - Web-based installer

### Documentation
- `docs/show_role_assignment_system.md` - Complete system documentation

## Files Modified

### Authentication System
- `core/Auth.php` - Added show-specific role checking methods:
  - `hasShowRole($showId, $role)` - Check if user has role for specific show
  - `getUserShowRoles($showId)` - Get all user's roles for a show
  - `isAssignedToShow($showId)` - Check if user is assigned to show

### Staff Model Integration
- `models/StaffModel.php` - Extended `isAssignedToShow()` to check both old and new systems

### Navigation Updates
- `views/user/dashboard.php` - Added "My Show Roles" button
- `views/coordinator/dashboard.php` - Added "Role Assignments" to show management menu
- `views/includes/header.php` - Added "Show Role Manager" to admin Management dropdown
- `views/admin/dashboard.php` - Added "Show Role Manager" to Quick Actions

## Key Features Implemented

### 1. Dual Assignment Methods

**For Coordinators:**
- Enter user ID number (user provides from their profile)
- Creates pending request requiring user approval
- 7-day expiration on requests
- Email notifications sent to user

**For Administrators:**
- Search users by name, email, or ID using Select2 dropdown
- Immediate assignment (no approval needed)
- Direct email notification to user

### 2. User Experience

**Role Request Interface:**
- Users see pending requests with show details
- Accept/decline with optional message
- View all current assignments
- Status tracking (upcoming, active, completed, expired)

**Management Interface:**
- Clean table view of all assignments
- Remove assignments with confirmation
- Track who assigned roles and when
- View pending requests (coordinators only)

### 3. Automatic Cleanup System

**Cron Job Features:**
- Runs daily to clean expired assignments
- Removes assignments 1 week after show end date
- Expires pending requests after 7 days
- Sends email reports to administrators
- Maintains cleanup logs with rotation

### 4. Notification System

**Email Notifications for:**
- Role assignment requests (to user)
- Direct assignments (to user)
- Request responses (to requester)
- Assignment removals (to user)
- Daily cleanup reports (to admin)

### 5. Security & Privacy

**Privacy Protection:**
- Coordinators can't browse user database
- User ID assignment prevents data exposure
- All actions require proper authorization

**Security Features:**
- Request expiration prevents stale requests
- Permission validation on all actions
- Audit trail for all assignments
- CSRF protection on forms

## Installation Instructions

### 1. Quick Installation
```bash
# Navigate to your site in a web browser
https://yoursite.com/install_show_role_system.php
```

### 2. Manual Installation
```bash
# Run the SQL script
mysql -u username -p database_name < sql/create_show_role_system.sql

# Set up cron job
crontab -e
# Add: 0 2 * * * /usr/bin/php /path/to/site/cron/cleanup_show_roles.php
```

### 3. Test the System
1. Go to any show → "Role Assignments"
2. Assign a role to a user
3. Check "My Show Roles" in user dashboard
4. Test approval workflow

## Usage Examples

### Admin Assigning a Judge
1. Navigate to show → "Role Assignments"
2. Search for user by name: "John Smith"
3. Select "Judge" role
4. Click "Assign" - immediate assignment
5. User receives notification email

### Coordinator Requesting Staff Assignment
1. Navigate to show → "Role Assignments"  
2. Enter user ID: "12345"
3. Select "Staff" role
4. Add message: "We need help with registration"
5. Click "Request" - pending approval
6. User receives email and must approve

### User Responding to Request
1. User sees notification email
2. Logs in → "My Show Roles"
3. Reviews request details
4. Clicks "Accept Assignment"
5. Coordinator receives confirmation email

## Backward Compatibility

**Zero Changes Required:**
- All existing controller code works unchanged
- All existing view code works unchanged
- All existing role checking continues to work
- Old staff assignments still recognized
- Judge assignments work with both systems

**Enhanced Functionality:**
- `Auth::hasShowRole()` now checks both primary and assigned roles
- `StaffModel::isAssignedToShow()` checks both old and new tables
- All existing patterns enhanced automatically

## System Benefits

1. **Scalable** - Handles thousands of shows and assignments
2. **User-Friendly** - Intuitive interfaces for all user types
3. **Automated** - No manual cleanup required
4. **Secure** - Privacy protection and permission validation
5. **Flexible** - Supports multiple roles per user per show
6. **Maintainable** - Clean code with comprehensive documentation
7. **Compatible** - Works with existing system without changes

## Next Steps

1. **Install the system** using the web installer
2. **Set up the cron job** for automatic cleanup
3. **Test with a few users** to verify functionality
4. **Train coordinators** on the new user ID assignment process
5. **Monitor the cleanup logs** to ensure proper operation

The system is production-ready and includes comprehensive error handling, logging, and user feedback. All your requirements have been implemented with additional features for enhanced usability and security.
