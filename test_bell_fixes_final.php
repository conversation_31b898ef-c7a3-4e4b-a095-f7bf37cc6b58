<?php
/**
 * Final Test for Notification Bell Fixes
 * 
 * This script tests the comprehensive notification bell fixes
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🔔 Final Test: Notification Bell Fixes</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Comprehensive Fix Strategy</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔧 What We've Implemented:</h3>";
    echo "<ol>";
    echo "<li><strong>Cache-busting:</strong> Added ?v=timestamp to notification-center.js</li>";
    echo "<li><strong>Override system:</strong> Created notification-bell-fixes.js that loads LAST</li>";
    echo "<li><strong>Robust detection:</strong> Preserves PHP initial count, then verifies with server</li>";
    echo "<li><strong>Mobile fixes:</strong> Bright yellow bell + red badge for mobile PWA</li>";
    echo "<li><strong>Desktop fixes:</strong> Prevents count flash, adds pulse animation</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the bell icon fixes...</p>";
        
        $subject = "🔔 Final Bell Test";
        $message = "This message tests the comprehensive notification bell icon fixes. Check both desktop and mobile!";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>📁 Files Updated</h2>";
    
    $files = [
        'views/includes/header.php' => 'Added cache-busting to notification-center.js',
        'views/includes/footer.php' => 'Added notification-bell-fixes.js (loads LAST)',
        'public/js/notification-bell-fixes.js' => 'Override system that fixes all issues',
        'public/js/notification-center.js' => 'Original fixes (may be overridden)',
        'public/css/notifications.css' => 'Badge pulse animations',
        'public/css/pwa-features.css' => 'Mobile bell glow effects'
    ];
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>File</th><th>Purpose</th><th>Status</th></tr>";
    
    foreach ($files as $file => $purpose) {
        $exists = file_exists($file);
        $status = $exists ? '✅ Exists' : '❌ Missing';
        echo "<tr>";
        echo "<td><code>{$file}</code></td>";
        echo "<td>{$purpose}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🖥️ Desktop Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Copy files to server</strong> (especially notification-bell-fixes.js)</li>";
    echo "<li><strong>Open browser dev tools</strong> (F12) → Console tab</li>";
    echo "<li><strong>Refresh the page</strong> and watch console for '[BellFixes]' messages</li>";
    echo "<li><strong>Look for:</strong> Bell count should appear and stay visible</li>";
    echo "<li><strong>Verify:</strong> Console shows 'BellFixes] Desktop badge updated: X'</li>";
    echo "</ol>";
    
    echo "<h3>📱 Mobile PWA Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile</strong></li>";
    echo "<li><strong>Look at bottom navigation</strong> Messages tab</li>";
    echo "<li><strong>Verify:</strong> Bell icon is bright yellow (#FFD700)</li>";
    echo "<li><strong>Check:</strong> Red badge with count appears</li>";
    echo "<li><strong>Test:</strong> Tap to open messages, then return - bell should reset</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🔍 Debug Information</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🛠️ Browser Console Commands:</h3>";
    echo "<p>Open browser console and run these commands to debug:</p>";
    echo "<ul>";
    echo "<li><code>window.notificationBellFixes.fetchCount()</code> - Get current count</li>";
    echo "<li><code>window.notificationBellFixes.updateBadges(5)</code> - Test with count 5</li>";
    echo "<li><code>window.notificationBellFixes.updateBadges(0)</code> - Test with count 0</li>";
    echo "</ul>";
    
    echo "<h3>📱 Mobile Debug:</h3>";
    echo "<p>For mobile debugging, you can use remote debugging:</p>";
    echo "<ul>";
    echo "<li><strong>Android:</strong> Chrome → chrome://inspect → Remote devices</li>";
    echo "<li><strong>iOS:</strong> Safari → Develop → [Device] → [Page]</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Platform</th><th>Element</th><th>Expected Behavior</th></tr>";
    
    echo "<tr>";
    echo "<td rowspan='2'><strong>🖥️ Desktop</strong></td>";
    echo "<td>Bell Icon</td>";
    echo "<td>Red badge with count, stays visible, pulse animation</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Console</td>";
    echo "<td>Shows '[BellFixes]' messages confirming updates</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td rowspan='3'><strong>📱 Mobile PWA</strong></td>";
    echo "<td>Bell Icon</td>";
    echo "<td>Bright yellow (#FFD700) with subtle glow</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Badge</td>";
    echo "<td>Red circle with white count number</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Animation</td>";
    echo "<td>Smooth transitions when count changes</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🚀 Why This Will Work</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Advantages of This Approach:</h3>";
    echo "<ul>";
    echo "<li><strong>Loads LAST:</strong> notification-bell-fixes.js loads after all other scripts</li>";
    echo "<li><strong>Overrides everything:</strong> Intercepts and replaces other notification systems</li>";
    echo "<li><strong>Cache-busted:</strong> All scripts have ?v=timestamp to force fresh loading</li>";
    echo "<li><strong>Robust detection:</strong> Works with or without initial PHP count</li>";
    echo "<li><strong>Fallback safe:</strong> If one method fails, others still work</li>";
    echo "<li><strong>Debug friendly:</strong> Extensive console logging for troubleshooting</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ol>";
        echo "<li>Copy all updated files to your server</li>";
        echo "<li>Test on desktop with browser console open</li>";
        echo "<li>Test on mobile PWA</li>";
        echo "<li>Look for the console messages and visual changes</li>";
        echo "</ol>";
        echo "<p><strong>This comprehensive fix should resolve all notification bell issues!</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Final test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
