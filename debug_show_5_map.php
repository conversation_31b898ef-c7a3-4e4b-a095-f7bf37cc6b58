<?php
/**
 * Debug script to check show ID 5 and its calendar event mapping
 */

// Load configuration
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/ShowModel.php';
require_once 'models/CalendarModel.php';

echo "<h1>Debug: Show ID 5 Map Zoom Issue</h1>";

try {
    // Create model instances
    $showModel = new ShowModel();
    $calendarModel = new CalendarModel();
    
    echo "<h2>1. Check if Show ID 5 exists</h2>";
    $show = $showModel->getShowById(5);
    if ($show) {
        echo "<p style='color: green;'>✓ Show ID 5 found: " . htmlspecialchars($show->name) . "</p>";
        echo "<p>Location: " . htmlspecialchars($show->location ?? 'Not set') . "</p>";
        echo "<p>Start Date: " . htmlspecialchars($show->start_date ?? 'Not set') . "</p>";
        echo "<p>End Date: " . htmlspecialchars($show->end_date ?? 'Not set') . "</p>";
        echo "<p>Status: " . htmlspecialchars($show->status ?? 'Not set') . "</p>";
        echo "<p>Coordinates: lat=" . ($show->lat ?? 'null') . ", lng=" . ($show->lng ?? 'null') . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Show ID 5 not found</p>";
        exit;
    }
    
    echo "<h2>2. Check for Calendar Event associated with Show ID 5</h2>";
    $event = $calendarModel->getEventByShowId(5);
    if ($event) {
        echo "<p style='color: green;'>✓ Calendar event found for show ID 5</p>";
        echo "<p>Event ID: " . $event->id . "</p>";
        echo "<p>Event Title: " . htmlspecialchars($event->title) . "</p>";
        echo "<p>Calendar ID: " . $event->calendar_id . "</p>";
        echo "<p>Calendar Name: " . htmlspecialchars($event->calendar_name ?? 'Unknown') . "</p>";
        echo "<p>Event Coordinates: lat=" . ($event->lat ?? 'null') . ", lng=" . ($event->lng ?? 'null') . "</p>";
        echo "<p>Event Location: " . htmlspecialchars($event->location ?? 'Not set') . "</p>";
        echo "<p>Event Address: " . htmlspecialchars($event->address1 ?? 'Not set') . "</p>";
        echo "<p>Event City: " . htmlspecialchars($event->city ?? 'Not set') . "</p>";
        echo "<p>Event State: " . htmlspecialchars($event->state ?? 'Not set') . "</p>";
        echo "<p>Event Privacy: " . htmlspecialchars($event->privacy ?? 'Not set') . "</p>";
    } else {
        echo "<p style='color: red;'>✗ No calendar event found for show ID 5</p>";
    }
    
    echo "<h2>3. Check all Calendar Events for Show ID 5</h2>";
    $events = $calendarModel->getEventsByShowId(5);
    if (!empty($events)) {
        echo "<p style='color: green;'>✓ Found " . count($events) . " calendar event(s) for show ID 5</p>";
        foreach ($events as $evt) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<p><strong>Event ID:</strong> " . $evt->id . "</p>";
            echo "<p><strong>Title:</strong> " . htmlspecialchars($evt->title) . "</p>";
            echo "<p><strong>Calendar:</strong> " . htmlspecialchars($evt->calendar_name ?? 'Unknown') . "</p>";
            echo "<p><strong>Coordinates:</strong> lat=" . ($evt->lat ?? 'null') . ", lng=" . ($evt->lng ?? 'null') . "</p>";
            echo "<p><strong>Location:</strong> " . htmlspecialchars($evt->location ?? 'Not set') . "</p>";
            echo "<p><strong>Privacy:</strong> " . htmlspecialchars($evt->privacy ?? 'Not set') . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>✗ No calendar events found for show ID 5</p>";
    }
    
    echo "<h2>4. Test Map Events API for Show ID 5</h2>";
    // Simulate the map events API call with show filter
    $filters = ['show_id' => 5];
    $mapEvents = $calendarModel->getEventsWithLocation($filters);
    
    if (!empty($mapEvents)) {
        echo "<p style='color: green;'>✓ Map events API found " . count($mapEvents) . " event(s) for show ID 5</p>";
        foreach ($mapEvents as $mapEvent) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<p><strong>Event ID:</strong> " . $mapEvent->id . "</p>";
            echo "<p><strong>Title:</strong> " . htmlspecialchars($mapEvent->title) . "</p>";
            echo "<p><strong>Coordinates:</strong> lat=" . ($mapEvent->lat ?? 'null') . ", lng=" . ($mapEvent->lng ?? 'null') . "</p>";
            echo "<p><strong>Has Location Data:</strong> " . ((!empty($mapEvent->address1) || !empty($mapEvent->city) || !empty($mapEvent->state)) ? 'Yes' : 'No') . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>✗ Map events API found no events for show ID 5</p>";
    }
    
    echo "<h2>5. Test URL Routing</h2>";
    echo "<p>The URL <code>/calendar/map/show/5</code> should:</p>";
    echo "<ul>";
    echo "<li>Route to CalendarController::map('show', '5')</li>";
    echo "<li>Call \$this->showModel->getShowById(5)</li>";
    echo "<li>Call \$this->calendarModel->getEventByShowId(5)</li>";
    echo "<li>Pass focusEvent data to the view</li>";
    echo "<li>JavaScript should call focusOnEvent() with the event ID</li>";
    echo "</ul>";
    
    if ($event && ($event->lat && $event->lng)) {
        echo "<p style='color: green;'>✓ All data looks good for map zoom functionality</p>";
        echo "<p><strong>Expected behavior:</strong> Map should zoom to coordinates lat=" . $event->lat . ", lng=" . $event->lng . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Missing coordinates - map zoom will not work</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
