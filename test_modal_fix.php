<?php
/**
 * Test Modal Z-Index Fix
 * 
 * This page tests if the modal z-index fix is working properly
 * with the racing header and navigation
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Only allow running by admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

require_once APPROOT . '/views/includes/header.php';
?>

<div class="container mt-4">
    <h1>Modal Z-Index Test</h1>
    <p>This page tests if Bootstrap modals appear correctly above the racing header and navigation.</p>
    
    <div class="alert alert-info">
        <strong>Test Instructions:</strong><br>
        1. Click the "Test Modal" button below<br>
        2. The modal should appear clearly without any grey overlay blocking it<br>
        3. The modal should be fully clickable and accessible<br>
        4. The racing header should not appear on top of the modal
    </div>
    
    <!-- Test Button -->
    <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#testModal">
        <i class="fas fa-test-tube"></i> Test Modal
    </button>
    
    <div class="mt-4">
        <h3>Z-Index Information (Updated)</h3>
        <ul>
            <li><strong>Racing Header:</strong> z-index: 100 (forced down)</li>
            <li><strong>Racing Navigation:</strong> z-index: 9998 (forced down)</li>
            <li><strong>Racing Overlay:</strong> z-index: 9999 (forced down)</li>
            <li><strong>Bootstrap Modal:</strong> z-index: 99999 (fixed)</li>
            <li><strong>Modal Backdrop:</strong> z-index: 99998 (fixed)</li>
            <li><strong>Modal Dialog:</strong> z-index: 100000 (fixed)</li>
            <li><strong>Modal Content:</strong> z-index: 100001 (fixed)</li>
        </ul>
    </div>

    <div class="mt-4">
        <h3>Debug Information</h3>
        <button type="button" class="btn btn-info" onclick="debugZIndex()">Check Z-Index Values</button>
        <div id="debugOutput" class="mt-2"></div>
    </div>
    
    <div class="mt-4">
        <a href="<?= BASE_URL ?>/admin/dashboard" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
        </a>
        <a href="<?= BASE_URL ?>/show_roles/adminOverview" class="btn btn-success">
            <i class="fas fa-users-cog"></i> Test Show Roles Modal
        </a>
    </div>
</div>

<!-- Test Modal -->
<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModalLabel">
                    <i class="fas fa-check-circle text-success"></i> Modal Z-Index Test
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-success">
                    <h4>✅ Success!</h4>
                    <p>If you can see this modal clearly without any grey overlay blocking it, and you can click on all the elements, then the z-index fix is working correctly!</p>
                </div>
                
                <h5>Test Elements:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="testInput" class="form-label">Test Input Field</label>
                            <input type="text" class="form-control" id="testInput" placeholder="Type something here...">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="testSelect" class="form-label">Test Dropdown</label>
                            <select class="form-select" id="testSelect">
                                <option value="">Choose an option...</option>
                                <option value="1">Option 1</option>
                                <option value="2">Option 2</option>
                                <option value="3">Option 3</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Test Checkboxes</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check1">
                                <label class="form-check-label" for="check1">Checkbox 1</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check2">
                                <label class="form-check-label" for="check2">Checkbox 2</label>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label class="form-label">Test Radio Buttons</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="testRadio" id="radio1" value="1">
                                <label class="form-check-label" for="radio1">Radio 1</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="testRadio" id="radio2" value="2">
                                <label class="form-check-label" for="radio2">Radio 2</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label for="testTextarea" class="form-label">Test Textarea</label>
                    <textarea class="form-control" id="testTextarea" rows="3" placeholder="Type a longer message here..."></textarea>
                </div>
                
                <div class="alert alert-warning">
                    <strong>What to check:</strong><br>
                    • All form elements should be clickable<br>
                    • No grey overlay should block the modal<br>
                    • The racing header should not appear on top of this modal<br>
                    • You should be able to scroll within the modal if needed<br>
                    • The close button (X) should work properly
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="alert('Button clicked successfully!')">
                    <i class="fas fa-thumbs-up"></i> Test Button Click
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function debugZIndex() {
    const elements = [
        { name: 'Racing Header', selector: '.racing-header' },
        { name: 'Racing Menu', selector: '.racing-menu' },
        { name: 'Racing Overlay', selector: '.racing-overlay' },
        { name: 'Modal', selector: '.modal' },
        { name: 'Modal Backdrop', selector: '.modal-backdrop' },
        { name: 'Modal Dialog', selector: '.modal-dialog' },
        { name: 'Modal Content', selector: '.modal-content' },
        { name: 'Navbar', selector: '.navbar' }
    ];

    let output = '<div class="alert alert-info"><h5>Current Z-Index Values:</h5><ul>';

    elements.forEach(element => {
        const el = document.querySelector(element.selector);
        if (el) {
            const zIndex = window.getComputedStyle(el).zIndex;
            output += `<li><strong>${element.name}:</strong> ${zIndex}</li>`;
        } else {
            output += `<li><strong>${element.name}:</strong> Not found</li>`;
        }
    });

    output += '</ul></div>';
    document.getElementById('debugOutput').innerHTML = output;
}
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
