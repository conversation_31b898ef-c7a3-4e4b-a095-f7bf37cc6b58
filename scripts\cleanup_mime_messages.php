<?php
/**
 * Cleanup Script: Fix MIME Content in Database
 * 
 * This script processes existing messages in the database that contain
 * raw MIME content and converts them to clean text format.
 * 
 * Run this once to fix messages stored before the EmailRetrievalService fix.
 */

require_once dirname(__DIR__) . '/config/config.php';
require_once APPROOT . '/core/Database.php';

class MimeCleanupService {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Find and fix messages containing MIME content
     */
    public function cleanupMimeMessages() {
        echo "Starting MIME cleanup process...\n";
        
        // Find messages that contain MIME boundaries or headers
        $sql = "SELECT id, message, subject, created_at 
                FROM messages 
                WHERE message LIKE '%Content-Type:%' 
                   OR message LIKE '%boundary=%' 
                   OR message LIKE '%--=-%'
                   OR message LIKE '%Content-Transfer-Encoding:%'
                ORDER BY created_at DESC";
        
        $this->db->query($sql);
        $messages = $this->db->resultSet();
        
        if (empty($messages)) {
            echo "No MIME messages found to cleanup.\n";
            return;
        }
        
        echo "Found " . count($messages) . " messages with MIME content.\n\n";
        
        $processed = 0;
        $skipped = 0;
        
        foreach ($messages as $message) {
            echo "Processing message ID {$message->id} (Subject: " . substr($message->subject, 0, 50) . "...)\n";
            
            $cleanContent = $this->extractCleanContent($message->message);
            
            if ($cleanContent && $cleanContent !== $message->message) {
                // Update the message with clean content
                $updateSql = "UPDATE messages SET message = ? WHERE id = ?";
                $this->db->query($updateSql);
                $this->db->bind(1, $cleanContent);
                $this->db->bind(2, $message->id);
                
                if ($this->db->execute()) {
                    echo "  ✓ Cleaned successfully\n";
                    echo "  Original length: " . strlen($message->message) . " chars\n";
                    echo "  Clean length: " . strlen($cleanContent) . " chars\n";
                    echo "  Clean content preview: " . substr($cleanContent, 0, 100) . "...\n\n";
                    $processed++;
                } else {
                    echo "  ✗ Failed to update database\n\n";
                }
            } else {
                echo "  - Skipped (no clean content extracted)\n\n";
                $skipped++;
            }
        }
        
        echo "Cleanup complete!\n";
        echo "Processed: $processed messages\n";
        echo "Skipped: $skipped messages\n";
    }
    
    /**
     * Extract clean text content from MIME message
     * Uses the same logic as the fixed EmailRetrievalService
     */
    private function extractCleanContent($mimeContent) {
        if (empty($mimeContent)) {
            return '';
        }
        
        // Try to extract plain text content first
        if (preg_match('/Content-Type:\s*text\/plain[^;]*(?:;[^=]*=[^;]+)*.*?\n\n(.+?)(?=\n--|\nContent-Type:|$)/s', $mimeContent, $matches)) {
            $plainText = $matches[1];
            
            // Handle quoted-printable encoding
            if (strpos($mimeContent, 'quoted-printable') !== false) {
                $plainText = quoted_printable_decode($plainText);
            }
            
            // Clean up the text
            $cleanText = trim($plainText);
            $cleanText = preg_replace('/\r\n|\r/', "\n", $cleanText);
            $cleanText = preg_replace('/\n{3,}/', "\n\n", $cleanText);
            
            if (!empty($cleanText) && strlen($cleanText) > 3) {
                return $cleanText;
            }
        }
        
        // Try alternative pattern for different MIME structures
        if (preg_match('/text\/plain[^-]*?\n\n(.+?)(?=\n--|\z)/s', $mimeContent, $matches)) {
            $plainText = $matches[1];
            
            // Handle quoted-printable encoding
            if (strpos($mimeContent, 'quoted-printable') !== false) {
                $plainText = quoted_printable_decode($plainText);
            }
            
            $cleanText = trim($plainText);
            $cleanText = preg_replace('/\r\n|\r/', "\n", $cleanText);
            $cleanText = preg_replace('/\n{3,}/', "\n\n", $cleanText);
            
            if (!empty($cleanText) && strlen($cleanText) > 3) {
                return $cleanText;
            }
        }
        
        // Try to extract HTML content and convert to text
        if (preg_match('/Content-Type:\s*text\/html[^;]*(?:;[^=]*=[^;]+)*.*?\n\n(.+?)(?=\n--|\nContent-Type:|$)/s', $mimeContent, $matches)) {
            $htmlContent = $matches[1];
            
            // Handle quoted-printable encoding
            if (strpos($mimeContent, 'quoted-printable') !== false) {
                $htmlContent = quoted_printable_decode($htmlContent);
            }
            
            // Convert HTML to plain text
            $plainText = strip_tags($htmlContent);
            $plainText = html_entity_decode($plainText, ENT_QUOTES, 'UTF-8');
            $cleanText = trim($plainText);
            $cleanText = preg_replace('/\r\n|\r/', "\n", $cleanText);
            $cleanText = preg_replace('/\n{3,}/', "\n\n", $cleanText);
            $cleanText = preg_replace('/\s+/', ' ', $cleanText);
            
            if (!empty($cleanText) && strlen($cleanText) > 3) {
                return $cleanText;
            }
        }
        
        // Final fallback: try to extract any readable content
        $lines = explode("\n", $mimeContent);
        $extractedContent = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                continue;
            }
            
            // Skip MIME headers and boundaries
            if (strpos($line, 'Content-') === 0 ||
                strpos($line, '--=-') === 0 ||
                strpos($line, '--') === 0 ||
                strpos($line, 'boundary=') !== false ||
                strpos($line, 'charset=') !== false ||
                strpos($line, 'Transfer-Encoding') !== false) {
                continue;
            }
            
            // Skip HTML tags
            if (preg_match('/^<[^>]+>$/', $line)) {
                continue;
            }
            
            // This looks like actual content
            if (strlen($line) > 1 && !preg_match('/^[a-z-]+:\s/i', $line)) {
                $extractedContent[] = $line;
            }
        }
        
        if (!empty($extractedContent)) {
            $result = implode("\n", $extractedContent);
            $result = preg_replace('/\n{3,}/', "\n\n", $result);
            return trim($result);
        }
        
        return '';
    }
    
    /**
     * Preview what would be cleaned without making changes
     */
    public function previewCleanup($limit = 5) {
        echo "Preview mode - showing first $limit messages that would be cleaned:\n\n";
        
        $sql = "SELECT id, message, subject, created_at 
                FROM messages 
                WHERE message LIKE '%Content-Type:%' 
                   OR message LIKE '%boundary=%' 
                   OR message LIKE '%--=-%'
                ORDER BY created_at DESC 
                LIMIT $limit";
        
        $this->db->query($sql);
        $messages = $this->db->resultSet();
        
        foreach ($messages as $message) {
            echo "Message ID: {$message->id}\n";
            echo "Subject: {$message->subject}\n";
            echo "Created: {$message->created_at}\n";
            echo "Original content (first 200 chars):\n";
            echo substr($message->message, 0, 200) . "...\n\n";
            
            $cleanContent = $this->extractCleanContent($message->message);
            if ($cleanContent) {
                echo "Would become:\n";
                echo substr($cleanContent, 0, 200) . "...\n";
            } else {
                echo "No clean content could be extracted.\n";
            }
            echo str_repeat("-", 80) . "\n\n";
        }
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $cleanup = new MimeCleanupService();
    
    $action = $argv[1] ?? 'preview';
    
    switch ($action) {
        case 'preview':
            $limit = isset($argv[2]) ? intval($argv[2]) : 5;
            $cleanup->previewCleanup($limit);
            break;
            
        case 'cleanup':
            echo "Are you sure you want to cleanup MIME messages in the database? (y/N): ";
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            fclose($handle);
            
            if (trim(strtolower($line)) === 'y') {
                $cleanup->cleanupMimeMessages();
            } else {
                echo "Cleanup cancelled.\n";
            }
            break;
            
        default:
            echo "Usage:\n";
            echo "  php cleanup_mime_messages.php preview [limit]  - Preview messages to be cleaned\n";
            echo "  php cleanup_mime_messages.php cleanup          - Actually clean the messages\n";
            break;
    }
} else {
    echo "This script must be run from command line.\n";
}
?>