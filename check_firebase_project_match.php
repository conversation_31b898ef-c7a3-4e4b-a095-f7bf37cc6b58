<?php
/**
 * Check if VAPID keys and service account match the same Firebase project
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Prevent direct access without security key
if (!isset($_GET['check_key']) || $_GET['check_key'] !== 'firebase_match_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';

echo "=== FIREBASE PROJECT MATCH CHECK ===\n\n";

try {
    echo "1. CHECKING SERVICE ACCOUNT\n";
    
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    
    if (!file_exists($serviceAccountPath)) {
        echo "   ❌ Service account file not found\n";
        exit;
    }
    
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    if (!$serviceAccount) {
        echo "   ❌ Invalid service account JSON\n";
        exit;
    }
    
    $projectId = $serviceAccount['project_id'] ?? 'NOT_FOUND';
    $clientEmail = $serviceAccount['client_email'] ?? 'NOT_FOUND';
    
    echo "   ✅ Service account loaded\n";
    echo "   📋 Project ID: $projectId\n";
    echo "   📧 Client Email: $clientEmail\n";
    
    echo "\n2. CHECKING VAPID KEYS\n";
    
    $vapidPublicKey = defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : 'NOT_DEFINED';
    $vapidPrivateKey = defined('VAPID_PRIVATE_KEY') ? VAPID_PRIVATE_KEY : 'NOT_DEFINED';
    $vapidSubject = defined('VAPID_SUBJECT') ? VAPID_SUBJECT : 'NOT_DEFINED';
    
    echo "   📋 Public Key: " . substr($vapidPublicKey, 0, 20) . "...\n";
    echo "   📋 Private Key: " . substr($vapidPrivateKey, 0, 10) . "...\n";
    echo "   📋 Subject: $vapidSubject\n";
    
    echo "\n3. PROJECT MATCH ANALYSIS\n";
    
    if ($projectId === 'NOT_FOUND') {
        echo "   ❌ Cannot determine project ID from service account\n";
    } else {
        echo "   📋 Service Account Project: $projectId\n";
        
        // Check if VAPID subject matches project
        if (strpos($vapidSubject, $projectId) !== false) {
            echo "   ✅ VAPID subject contains project ID - likely match\n";
        } else {
            echo "   ⚠️ VAPID subject doesn't contain project ID\n";
        }
    }
    
    echo "\n4. RECOMMENDATIONS\n";
    
    echo "   To fix the UNREGISTERED token issue:\n\n";
    
    echo "   OPTION A: Generate new VAPID keys for this project\n";
    echo "   1. Go to https://console.firebase.google.com/project/$projectId/settings/cloudmessaging\n";
    echo "   2. In 'Web configuration' section, click 'Generate key pair'\n";
    echo "   3. Copy the new keys\n";
    echo "   4. Update your config.php with new keys\n";
    echo "   5. Clear browser data and re-subscribe\n\n";
    
    echo "   OPTION B: Use service account from VAPID project\n";
    echo "   1. Go to Firebase Console for the project that generated your VAPID keys\n";
    echo "   2. Download new service account JSON\n";
    echo "   3. Replace current service account file\n\n";
    
    echo "5. CURRENT CONFIGURATION SUMMARY\n";
    echo "   Service Account Project: $projectId\n";
    echo "   VAPID Public Key: " . substr($vapidPublicKey, 0, 30) . "...\n";
    echo "   VAPID Subject: $vapidSubject\n";
    
    if ($projectId !== 'NOT_FOUND' && $vapidPublicKey !== 'NOT_DEFINED') {
        echo "\n   🔍 LIKELY ISSUE: Project mismatch\n";
        echo "   Your VAPID keys and service account are from different Firebase projects.\n";
        echo "   This is why tokens show as UNREGISTERED.\n";
    }
    
    echo "\n6. NEXT STEPS\n";
    echo "   1. Choose Option A or B above\n";
    echo "   2. Update your configuration\n";
    echo "   3. Clear browser data completely\n";
    echo "   4. Test fresh subscription again\n";
    echo "   5. Tokens should then be recognized by Firebase\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== END CHECK ===\n";
?>
