<?php
/**
 * Test Mobile Notification Center Improvements
 * 
 * Tests all the mobile UI improvements for the notification center
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>📱 Test Mobile Notification Center Improvements</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Mobile UI Improvements Applied</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Issues Fixed:</h3>";
    echo "<ul>";
    echo "<li><strong>Removed debug text:</strong> No more [Read: N/Y] on any interface</li>";
    echo "<li><strong>Fixed oversized rows:</strong> Compact mobile layout with smaller padding</li>";
    echo "<li><strong>Optimized columns:</strong> Checkbox and icon columns much smaller on mobile</li>";
    echo "<li><strong>Tab icons:</strong> Mobile tabs use icons instead of text (📋 📧 📦)</li>";
    echo "<li><strong>Bulk action buttons:</strong> Horizontal icon-only buttons on mobile</li>";
    echo "<li><strong>No horizontal scrolling:</strong> All content fits mobile screen width</li>";
    echo "<li><strong>Compact text:</strong> Smaller fonts and tighter spacing for mobile</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the mobile improvements...</p>";
        
        $subject = "📱 Mobile UI Test";
        $message = "This tests the improved mobile interface for the notification center with compact layout and better usability.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Mobile Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile PWA Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile device</strong></li>";
    echo "<li><strong>Navigate to Messages</strong> (notification center)</li>";
    echo "<li><strong>Check tabs:</strong> Should show icons (📋 📧 📦) instead of text</li>";
    echo "<li><strong>Check message rows:</strong> Should be compact, not huge</li>";
    echo "<li><strong>Check columns:</strong> Checkbox and icon columns should be narrow</li>";
    echo "<li><strong>Select messages:</strong> Bulk action buttons should be horizontal icons</li>";
    echo "<li><strong>Scroll test:</strong> No horizontal scrolling should occur</li>";
    echo "<li><strong>Text size:</strong> Should be readable but not oversized</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Mobile Layout</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Element</th><th>Before</th><th>After</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Tabs</strong></td>";
    echo "<td style='color: red;'>❌ 'All Messages', 'Unread', 'Archived' (too long)</td>";
    echo "<td style='color: green;'>✅ 📋 📧 📦 (icons only)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message Rows</strong></td>";
    echo "<td style='color: red;'>❌ Huge rows, lots of wasted space</td>";
    echo "<td style='color: green;'>✅ Compact rows, efficient use of space</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Columns</strong></td>";
    echo "<td style='color: red;'>❌ Wide checkbox/icon columns</td>";
    echo "<td style='color: green;'>✅ Narrow columns, more room for content</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Bulk Actions</strong></td>";
    echo "<td style='color: red;'>❌ Huge stacked vertical buttons</td>";
    echo "<td style='color: green;'>✅ Small horizontal icon buttons</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Scrolling</strong></td>";
    echo "<td style='color: red;'>❌ Horizontal scrolling required</td>";
    echo "<td style='color: green;'>✅ No horizontal scrolling</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Debug Text</strong></td>";
    echo "<td style='color: red;'>❌ [Read: N] [Read: Y] visible</td>";
    echo "<td style='color: green;'>✅ Debug text removed</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Changes Made</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile Layout Changes:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Mobile Layout (d-md-none) -->
<div class=\"d-flex align-items-start\">
    <!-- Smaller checkbox -->
    <input class=\"form-check-input\" style=\"margin-top: 2px;\">
    
    <!-- Smaller icon -->
    <i class=\"fas fa-envelope\" style=\"font-size: 14px;\"></i>
    
    <!-- Compact content -->
    <div class=\"flex-grow-1 mobile-message-content\">
        <h6 style=\"font-size: 14px;\">Subject</h6>
        <p style=\"font-size: 12px;\">Message preview...</p>
        <small style=\"font-size: 11px;\">From • Date</small>
    </div>
    
    <!-- Vertical action buttons -->
    <div class=\"btn-group-vertical btn-group-sm\">
        <button class=\"btn btn-sm\" style=\"padding: 2px 6px;\">👁️</button>
        <button class=\"btn btn-sm\" style=\"padding: 2px 6px;\">✅</button>
    </div>
</div>";
    echo "</pre>";
    
    echo "<h3>📋 Tab Icons:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Desktop: Show text -->
<span class=\"d-none d-md-inline\">All Messages</span>

<!-- Mobile: Show icon -->
<span class=\"d-md-none\"><i class=\"fas fa-list\"></i></span>";
    echo "</pre>";
    
    echo "<h3>🔘 Bulk Actions:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Mobile: Icon-only horizontal buttons -->
<div class=\"d-md-none\">
    <div class=\"btn-group btn-group-sm\">
        <button class=\"btn btn-sm\" title=\"Mark Read\">✅</button>
        <button class=\"btn btn-sm\" title=\"Archive\">📦</button>
        <button class=\"btn btn-sm\" title=\"Delete\">🗑️</button>
        <button class=\"btn btn-sm\" title=\"Clear\">❌</button>
    </div>
</div>";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>📱 Mobile CSS Optimizations</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎨 Mobile-Specific CSS:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "@media (max-width: 767.98px) {
    /* Compact padding */
    .card-body {
        padding: 0.75rem !important;
    }
    
    .message-item .card-body {
        padding: 0.5rem !important;
    }
    
    /* Smaller tabs */
    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 14px;
    }
    
    /* No horizontal scrolling */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    /* Compact message content */
    .mobile-message-content h6 {
        line-height: 1.2;
        margin-bottom: 0.25rem;
    }
    
    .mobile-message-content p {
        line-height: 1.3;
        margin-bottom: 0.25rem;
    }
}";
    echo "</pre>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Mobile Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>What you should see on mobile:</strong></p>";
        echo "<ul>";
        echo "<li>📋 <strong>Icon tabs:</strong> 📋 📧 📦 instead of long text</li>";
        echo "<li>📱 <strong>Compact rows:</strong> Much smaller, efficient use of space</li>";
        echo "<li>🔘 <strong>Small columns:</strong> Narrow checkbox/icon, wide content</li>";
        echo "<li>🔘 <strong>Icon buttons:</strong> Horizontal bulk actions with icons</li>";
        echo "<li>📏 <strong>No scrolling:</strong> Everything fits mobile screen width</li>";
        echo "<li>🚫 <strong>No debug text:</strong> Clean interface without [Read: N/Y]</li>";
        echo "</ul>";
        echo "<p><strong>The mobile interface should now be much more user-friendly!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated file:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/notification_center/index.php</code> - Complete mobile optimization</li>";
    echo "</ul>";
    echo "<p><strong>Changes made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Removed debug [Read: N/Y] text</li>";
    echo "<li>✅ Added mobile-specific layout (d-md-none)</li>";
    echo "<li>✅ Icon-only tabs for mobile</li>";
    echo "<li>✅ Compact message cards</li>";
    echo "<li>✅ Horizontal bulk action buttons</li>";
    echo "<li>✅ Mobile-optimized CSS</li>";
    echo "<li>✅ No horizontal scrolling</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Mobile notification center test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
