<?php
/**
 * NotificationHelper - Simple helper class for sending notifications
 *
 * This class provides an easy way to send notifications to users
 * while respecting their notification preferences.
 *
 * Usage:
 * 1. Include this file: require_once 'helpers/notification_helper.php';
 * 2. Use global functions: sendNotification($userId, $title, $message);
 * 3. Or use class directly: $helper = new NotificationHelper();
 */

/**
 * Initialize notification helper dependencies
 * Call this function if you need to ensure all dependencies are loaded
 */
function initializeNotificationHelper() {
    // Ensure APPROOT is defined
    if (!defined('APPROOT')) {
        define('APPROOT', dirname(dirname(__FILE__)));
    }

    // Load Database class if not already loaded
    if (!class_exists('Database')) {
        require_once APPROOT . '/core/Database.php';
    }

    // Load notification classes if not already loaded
    if (!class_exists('NotificationModel')) {
        require_once APPROOT . '/models/NotificationModel.php';
    }

    if (!class_exists('NotificationService')) {
        require_once APPROOT . '/models/NotificationService.php';
    }
}

class NotificationHelper {
    private $notificationModel;
    private $notificationService;
    
    public function __construct() {
        // Initialize dependencies
        initializeNotificationHelper();

        $this->notificationModel = new NotificationModel();
        $this->notificationService = new NotificationService();
    }
    
    /**
     * Send notification immediately to user based on their preferences
     * 
     * @param int $userId User ID
     * @param string $title Notification title/subject
     * @param string $message Notification message
     * @param array $options Optional parameters
     * @return array Results
     */
    public function sendToUser($userId, $title, $message, $options = []) {
        $results = [
            'success' => false,
            'sent_count' => 0,
            'sent_types' => [],
            'skipped_types' => [],
            'errors' => []
        ];
        
        try {
            // Get settings and preferences
            $globalSettings = $this->notificationModel->getNotificationSettings();
            $userPrefs = $this->notificationModel->getUserPreferences($userId);
            
            // Create default preferences if they don't exist
            if (!$userPrefs) {
                $this->notificationModel->createDefaultPreferences($userId);
                $userPrefs = $this->notificationModel->getUserPreferences($userId);
            }
            
            // Determine which types to send
            $enabledTypes = $this->getEnabledNotificationTypes($globalSettings, $userPrefs);
            
            if (empty($enabledTypes)) {
                $results['errors'][] = 'No notification types enabled for user';
                return $results;
            }
            
            // Send each enabled type
            foreach ($enabledTypes as $type) {
                $sent = $this->sendNotificationType($userId, $type, $title, $message);
                
                if ($sent) {
                    $results['sent_types'][] = $type;
                    $results['sent_count']++;
                } else {
                    $results['skipped_types'][] = $type;
                }
            }
            
            $results['success'] = $results['sent_count'] > 0;
            
        } catch (Exception $e) {
            $results['errors'][] = $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Send specific notification type to user (bypasses preferences)
     * 
     * @param int $userId User ID
     * @param string $type Notification type ('email', 'sms', 'push', 'toast')
     * @param string $title Title
     * @param string $message Message
     * @return bool Success
     */
    public function sendType($userId, $type, $title, $message) {
        try {
            return $this->sendNotificationType($userId, $type, $title, $message);
        } catch (Exception $e) {
            error_log("NotificationHelper::sendType error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send toast notification immediately
     * 
     * @param int $userId User ID
     * @param string $title Title
     * @param string $message Message
     * @return bool Success
     */
    public function sendToast($userId, $title, $message) {
        return $this->sendType($userId, 'toast', $title, $message);
    }
    
    /**
     * Send push notification immediately
     * 
     * @param int $userId User ID
     * @param string $title Title
     * @param string $message Message
     * @return bool Success
     */
    public function sendPush($userId, $title, $message) {
        return $this->sendType($userId, 'push', $title, $message);
    }
    
    /**
     * Get enabled notification types for user
     */
    private function getEnabledNotificationTypes($globalSettings, $userPrefs) {
        $enabledTypes = [];
        
        // Check each type against global settings and user preferences
        if ($globalSettings['email_enabled'] && $userPrefs->email_notifications) {
            $enabledTypes[] = 'email';
        }
        
        if ($globalSettings['sms_enabled'] && $userPrefs->sms_notifications) {
            $enabledTypes[] = 'sms';
        }
        
        if ($globalSettings['push_enabled'] && $userPrefs->push_notifications) {
            $enabledTypes[] = 'push';
        }
        
        if ($globalSettings['toast_enabled'] && $userPrefs->toast_notifications) {
            $enabledTypes[] = 'toast';
        }
        
        return $enabledTypes;
    }
    
    /**
     * Send specific notification type
     */
    private function sendNotificationType($userId, $type, $title, $message) {
        switch ($type) {
            case 'email':
                return $this->notificationService->sendTestNotification($userId, 'email', $title, $message);
                
            case 'sms':
                return $this->notificationService->sendTestNotification($userId, 'sms', $title, $message);
                
            case 'push':
                return $this->notificationService->sendTestPushNotification($userId, $title, $message);
                
            case 'toast':
                return $this->notificationService->sendTestToastNotification($userId, $title, $message);
                
            default:
                throw new Exception("Unknown notification type: $type");
        }
    }
}

// Global helper functions for easy use throughout the application

/**
 * Quick function to send notification to user respecting their preferences
 *
 * @param int $userId User ID
 * @param string $title Notification title
 * @param string $message Notification message
 * @return array Results array with success status and details
 */
function sendNotification($userId, $title, $message) {
    initializeNotificationHelper();
    $helper = new NotificationHelper();
    return $helper->sendToUser($userId, $title, $message);
}

/**
 * Quick function to send toast notification immediately
 * 
 * @param int $userId User ID
 * @param string $title Title
 * @param string $message Message
 * @return bool Success status
 */
function sendToastNotification($userId, $title, $message) {
    initializeNotificationHelper();
    $helper = new NotificationHelper();
    return $helper->sendToast($userId, $title, $message);
}

/**
 * Quick function to send push notification immediately
 *
 * @param int $userId User ID
 * @param string $title Title
 * @param string $message Message
 * @return bool Success status
 */
function sendPushNotification($userId, $title, $message) {
    initializeNotificationHelper();
    $helper = new NotificationHelper();
    return $helper->sendPush($userId, $title, $message);
}

/**
 * Send email notification immediately
 *
 * @param int $userId User ID
 * @param string $title Title
 * @param string $message Message
 * @return bool Success status
 */
function sendEmailNotification($userId, $title, $message) {
    initializeNotificationHelper();
    $helper = new NotificationHelper();
    return $helper->sendType($userId, 'email', $title, $message);
}

/**
 * Send SMS notification immediately
 *
 * @param int $userId User ID
 * @param string $title Title
 * @param string $message Message
 * @return bool Success status
 */
function sendSmsNotification($userId, $title, $message) {
    initializeNotificationHelper();
    $helper = new NotificationHelper();
    return $helper->sendType($userId, 'sms', $title, $message);
}
?>
