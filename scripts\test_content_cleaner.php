<?php
/**
 * Test ContentCleaner with specific MIME content
 */

require_once dirname(__DIR__) . '/config/config.php';
require_once APPROOT . '/core/ContentCleaner.php';

// Your specific problematic message (exact from database)
$testContent = '------sinikael-?=_1-17528868592260.1560288133272248
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 7bit
test message  
 asking a question
------sinikael-?=_1-17528868592260.1560288133272248
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable
<html><head></head><body><div>
    <div dir="auto" id="compose-body-wrapper">test message </div><div dir="auto" id="compose-body-wrapper">asking a question</div><div dir="auto" id="compose-body-wrapper"><br/>
      <div dir="auto"><br/></div>
      <div dir="auto" id="tmjah_g_1299">
      Get <a href="https://bluemail.me/download/" target="_blank">BlueMail for Mobile</a>
      </div>
      <div dir="auto"><br/></div>
    </div>
  </div></body></html>
------sinikael-?=_1-17528868592260.1560288133272248--';

echo "Testing specific regex patterns...\n\n";

// Test Pattern 1: Your specific MIME structure
echo "Testing Pattern 1: Your specific MIME structure\n";
if (preg_match('/Content-Type:\s*text\/plain[^;]*(?:;[^=]*=[^;]+)*.*?\nContent-Transfer-Encoding:[^\n]*\n(.+?)(?=\n------)/s', $testContent, $matches)) {
    echo "Pattern 1 matched!\n";
    echo "Captured content: '" . $matches[1] . "'\n\n";
} else {
    echo "Pattern 1 did not match.\n\n";
}

// Test Pattern 2: Standard MIME with blank line
echo "Testing Pattern 2: Standard MIME with blank line\n";
if (preg_match('/Content-Type:\s*text\/plain[^;]*(?:;[^=]*=[^;]+)*.*?\n(?:Content-Transfer-Encoding:[^\n]*\n)?\n(.+?)(?=\n--|\nContent-Type:|$)/s', $testContent, $matches)) {
    echo "Pattern 2 matched!\n";
    echo "Captured content: '" . $matches[1] . "'\n\n";
} else {
    echo "Pattern 2 did not match.\n\n";
}

// Test Pattern 4: HTML extraction
echo "Testing Pattern 4: HTML extraction\n";
if (preg_match('/Content-Type:\s*text\/html[^;]*(?:;[^=]*=[^;]+)*.*?\n(?:Content-Transfer-Encoding:[^\n]*\n)?\s*(.+?)(?=\n--|\nContent-Type:|$)/s', $testContent, $matches)) {
    echo "Pattern 4 (HTML) matched!\n";
    echo "Captured HTML content: '" . substr($matches[1], 0, 100) . "...'\n\n";
} else {
    echo "Pattern 4 (HTML) did not match.\n\n";
}

echo "Testing ContentCleaner with your specific message...\n\n";

echo "Original content length: " . strlen($testContent) . " characters\n";
echo "Original content preview:\n";
echo substr($testContent, 0, 200) . "...\n\n";

echo "Processing with ContentCleaner::cleanForWeb()...\n";
$cleanedContent = ContentCleaner::cleanForWeb($testContent);

echo "Cleaned content length: " . strlen($cleanedContent) . " characters\n";
echo "Cleaned content:\n";
echo $cleanedContent . "\n\n";

echo "Expected result: 'test message asking a question'\n";
echo "Success: " . (strpos($cleanedContent, 'test message') !== false && strpos($cleanedContent, 'asking a question') !== false ? 'YES' : 'NO') . "\n";

// Test what cleanMessageContent returns
echo "\n=== Testing cleanMessageContent directly ===\n";
$directClean = ContentCleaner::cleanMessageContent($testContent);
echo "Direct cleanMessageContent result:\n";
echo "'" . substr($directClean, 0, 200) . "...'\n";
?>