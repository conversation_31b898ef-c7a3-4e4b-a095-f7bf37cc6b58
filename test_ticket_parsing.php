<?php
/**
 * Test script to verify ticket number parsing is working correctly
 */

// Start session
session_start();

// Define the application root directory
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Ticket Number Parsing Test</h1>";

// Test subjects with different ticket formats
$testSubjects = [
    // Valid tickets with security tokens
    'Re: Your inquiry [RER-2025-001-ZIK2WU]',
    'Re: Show registration [RER-A25-22-001-ABC123]',
    'Re: Coordinator message [RER-C25-22-001-XYZ789]',
    
    // Invalid tickets without security tokens
    'Re: Your inquiry [RER-2025-001]',
    'Re: Show registration [RER-A25-22-001]',
    'Re: Coordinator message [RER-C25-22-001]',
    
    // No ticket numbers
    'New inquiry about car show',
    'Question about registration',
    
    // Edge cases
    'Re: [RER-2025-001-ZIK2WU] Follow up',
    'Multiple [RER-2025-001-ABC123] tickets [RER-A25-22-002-DEF456] in subject',
];

echo "<h2>Testing Regex Patterns</h2>";

// Test the comprehensive regex pattern
$comprehensivePattern = '/^(?:(?!\[RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6})\]).)*\[(RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6}))\](?:(?!\[RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6})\]).)*$|^(?!.*\[RER-(?:\d{4}-\d+|[AC]\d{2}-\d+-\d+)\])(?!.*\[RER-).*$/';
$invalidTicketPattern = '/\[RER-(?:\d{4}-\d+|[AC]\d{2}-\d+-\d+)\]/';

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Subject</th><th>Comprehensive Pattern</th><th>Invalid Ticket Check</th><th>Result</th></tr>";

foreach ($testSubjects as $subject) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($subject) . "</td>";

    // Test comprehensive pattern
    $validMatch = '';
    $comprehensiveMatches = false;
    if (preg_match($comprehensivePattern, $subject, $matches)) {
        $comprehensiveMatches = true;
        if (isset($matches[1]) && !empty($matches[1])) {
            $validMatch = $matches[1];
        }
    }
    echo "<td style='color: " . ($validMatch ? 'green' : ($comprehensiveMatches ? 'blue' : 'gray')) . ";'>" .
         htmlspecialchars($validMatch ?: ($comprehensiveMatches ? 'No ticket (allowed)' : 'Pattern failed')) . "</td>";

    // Test for invalid tickets without security tokens
    $invalidMatch = '';
    if (preg_match($invalidTicketPattern, $subject, $matches)) {
        $invalidMatch = $matches[0];
    }
    echo "<td style='color: " . ($invalidMatch ? 'red' : 'gray') . ";'>" . htmlspecialchars($invalidMatch ?: 'None') . "</td>";

    // Determine result based on comprehensive pattern
    $result = '';
    if ($validMatch) {
        $result = "✓ ALLOW (Valid ticket with token)";
        $color = 'green';
    } elseif ($invalidMatch) {
        $result = "✗ REJECT (Invalid ticket without token)";
        $color = 'red';
    } elseif ($comprehensiveMatches) {
        $result = "→ NEW (Generate new ticket)";
        $color = 'blue';
    } else {
        $result = "✗ REJECT (Pattern validation failed)";
        $color = 'red';
    }
    echo "<td style='color: $color; font-weight: bold;'>" . $result . "</td>";

    echo "</tr>";
}

echo "</table>";

echo "<h2>Testing Ticket Validation Pattern</h2>";

// Test the validation pattern for extracting ticket number and security token
$validationPattern = '/^(RER-(?:[AC]?\d{2,4}-)?(?:\d+-)?(?:\d{3}))-([A-Z0-9]{6})$/';

$testTickets = [
    'RER-2025-001-ZIK2WU',
    'RER-A25-22-001-ABC123',
    'RER-C25-22-001-XYZ789',
    'RER-2025-001',  // Should not match (no token)
    'INVALID-FORMAT',  // Should not match
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Full Ticket</th><th>Matches Pattern</th><th>Ticket Number</th><th>Security Token</th></tr>";

foreach ($testTickets as $ticket) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($ticket) . "</td>";

    if (preg_match($validationPattern, $ticket, $matches)) {
        $ticketNumber = $matches[1];
        $securityToken = $matches[2];
        echo "<td style='color: green;'>✓ Yes</td>";
        echo "<td>" . htmlspecialchars($ticketNumber) . "</td>";
        echo "<td>" . htmlspecialchars($securityToken) . "</td>";
    } else {
        echo "<td style='color: red;'>✗ No</td>";
        echo "<td colspan='2'>-</td>";
    }

    echo "</tr>";
}

echo "</table>";

echo "<h2>Summary</h2>";
echo "<p><strong>Comprehensive Pattern:</strong> <code>" . htmlspecialchars($comprehensivePattern) . "</code></p>";
echo "<p><strong>Invalid Ticket Pattern:</strong> <code>" . htmlspecialchars($invalidTicketPattern) . "</code></p>";
echo "<p><strong>Validation Pattern:</strong> <code>" . htmlspecialchars($validationPattern) . "</code></p>";

echo "<h3>Expected Behavior:</h3>";
echo "<ul>";
echo "<li><strong>Green (ALLOW):</strong> Emails with valid ticket numbers and security tokens should be processed and threaded with existing conversations</li>";
echo "<li><strong>Red (REJECT):</strong> Emails with ticket numbers but missing security tokens should be rejected as security threats</li>";
echo "<li><strong>Blue (NEW):</strong> Emails without any ticket numbers should generate new tickets</li>";
echo "</ul>";

echo "<p><strong>The fix ensures that:</strong></p>";
echo "<ul>";
echo "<li>Emails replying to existing tickets (with security tokens) are properly threaded</li>";
echo "<li>Security is maintained by rejecting emails with invalid or missing tokens</li>";
echo "<li>All three ticket formats are supported: RER-2025-001, RER-A25-22-001, RER-C25-22-001</li>";
echo "</ul>";
?>
