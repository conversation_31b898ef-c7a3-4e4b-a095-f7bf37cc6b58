<?php
/**
 * Debug Notification Delivery
 * 
 * Test if notifications are being sent but not displayed
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'helpers/csrf_helper.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die('Please log in first.');
}

$userId = $_SESSION['user_id'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Notification Delivery</title>
    <meta name="csrf-token" content="<?php echo generateCsrfToken(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        #console-logs { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔍 Debug Notification Delivery</h2>
        <p><strong>User ID:</strong> <?php echo $userId; ?></p>
        
        <div class="alert alert-info">
            <h5>This will test:</h5>
            <ol>
                <li>Browser notification permission status</li>
                <li>Service worker message handling</li>
                <li>FCM foreground message reception</li>
                <li>Background notification display</li>
                <li>Server-side sending with detailed logging</li>
            </ol>
        </div>
        
        <div class="debug-section">
            <h3>1. Browser Notification Status</h3>
            <div id="browser-status">Checking...</div>
        </div>
        
        <div class="debug-section">
            <h3>2. Service Worker Message Listener</h3>
            <div id="sw-status">Setting up...</div>
        </div>
        
        <div class="debug-section">
            <h3>3. FCM Foreground Listener</h3>
            <div id="fcm-status">Initializing...</div>
        </div>
        
        <div class="debug-section">
            <h3>4. Send Test Notification</h3>
            <div id="send-status">Ready to test...</div>
            <button id="send-btn" class="btn btn-primary mt-2">Send Test Notification</button>
        </div>
        
        <div class="debug-section">
            <h3>5. Manual Browser Notification Test</h3>
            <div id="manual-status">Ready to test...</div>
            <button id="manual-btn" class="btn btn-success mt-2">Show Manual Notification</button>
        </div>
        
        <div class="debug-section">
            <h3>Console Logs</h3>
            <div id="console-logs"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>

    <script>
        // Console logging
        const logs = [];
        const originalLog = console.log;
        const originalError = console.error;
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateConsoleLogs();
            
            if (type === 'error') {
                originalError(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = function(...args) { addLog('log', args.join(' ')); };
        console.error = function(...args) { addLog('error', args.join(' ')); };
        
        function updateConsoleLogs() {
            document.getElementById('console-logs').innerHTML = logs.slice(-20).join('<br>');
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="${type}">${message}</span>`;
        }
        
        // 1. Check browser notification status
        function checkBrowserStatus() {
            console.log('Checking browser notification status...');
            
            const permission = Notification.permission;
            const supported = 'Notification' in window;
            const swSupported = 'serviceWorker' in navigator;
            
            let status = `
                <strong>Notification API:</strong> ${supported ? '✅ Supported' : '❌ Not supported'}<br>
                <strong>Service Worker:</strong> ${swSupported ? '✅ Supported' : '❌ Not supported'}<br>
                <strong>Permission:</strong> ${permission} ${permission === 'granted' ? '✅' : '❌'}<br>
            `;
            
            updateStatus('browser-status', status, permission === 'granted' ? 'success' : 'error');
        }
        
        // 2. Set up service worker message listener
        function setupServiceWorkerListener() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.addEventListener('message', event => {
                    console.log('🔔 Service Worker message received:', event.data);
                    updateStatus('sw-status', '✅ Service Worker message received: ' + JSON.stringify(event.data), 'success');
                });
                
                updateStatus('sw-status', '✅ Service Worker message listener active', 'success');
            } else {
                updateStatus('sw-status', '❌ Service Worker not supported', 'error');
            }
        }
        
        // 3. Set up FCM foreground listener
        function setupFCMListener() {
            try {
                const firebaseConfig = {
                    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
                    authDomain: "rowaneliterides.firebaseapp.com",
                    projectId: "rowaneliterides",
                    storageBucket: "rowaneliterides.firebasestorage.app",
                    messagingSenderId: "310533125467",
                    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
                };
                
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                
                const messaging = firebase.messaging();
                
                messaging.onMessage((payload) => {
                    console.log('🔔 FCM foreground message received:', payload);
                    updateStatus('fcm-status', '✅ FCM foreground message received: ' + JSON.stringify(payload), 'success');
                    
                    // Show manual notification for foreground messages
                    if (Notification.permission === 'granted') {
                        new Notification(payload.notification?.title || 'FCM Message', {
                            body: payload.notification?.body || 'Foreground message received',
                            icon: '/public/images/icon-192x192.png'
                        });
                    }
                });
                
                updateStatus('fcm-status', '✅ FCM foreground listener active', 'success');
                
            } catch (error) {
                console.error('FCM setup error:', error);
                updateStatus('fcm-status', '❌ FCM setup failed: ' + error.message, 'error');
            }
        }
        
        // 4. Send test notification
        document.getElementById('send-btn').addEventListener('click', async () => {
            try {
                updateStatus('send-status', '📤 Sending test notification...', 'warning');
                
                const response = await fetch('/test_fcm_now_working.php');
                const text = await response.text();
                
                if (text.includes('SUCCESS! Notification sent!')) {
                    updateStatus('send-status', '✅ Server says notification sent successfully', 'success');
                    console.log('Server response indicates success');
                } else {
                    updateStatus('send-status', '❌ Server response unclear', 'warning');
                    console.log('Server response:', text.substring(0, 200) + '...');
                }
                
            } catch (error) {
                console.error('Send test failed:', error);
                updateStatus('send-status', '❌ Send test failed: ' + error.message, 'error');
            }
        });
        
        // 5. Manual browser notification test
        document.getElementById('manual-btn').addEventListener('click', () => {
            if (Notification.permission === 'granted') {
                const notification = new Notification('Manual Test Notification', {
                    body: 'This is a manual browser notification test at ' + new Date().toLocaleTimeString(),
                    icon: '/public/images/icon-192x192.png',
                    tag: 'manual-test'
                });
                
                notification.onclick = () => {
                    console.log('Manual notification clicked');
                    notification.close();
                };
                
                updateStatus('manual-status', '✅ Manual notification shown', 'success');
                console.log('Manual notification created');
            } else {
                updateStatus('manual-status', '❌ Notification permission not granted', 'error');
            }
        });
        
        // Initialize on load
        window.addEventListener('load', () => {
            console.log('Notification delivery debug tool loaded');
            checkBrowserStatus();
            setupServiceWorkerListener();
            setupFCMListener();
        });
    </script>
</body>
</html>