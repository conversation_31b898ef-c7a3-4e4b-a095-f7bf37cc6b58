-- Fix Email Message Types
-- This script changes messages that should be 'email' type but are marked as 'direct'

-- First, let's see what we're working with
-- Run this query first to see the current state:
SELECT 
    id,
    message_type,
    subject,
    from_user_id,
    to_user_id,
    created_at
FROM messages 
WHERE message_type = 'direct' 
ORDER BY created_at DESC;

-- If these look like email messages (external emails, contact form submissions, etc.)
-- then run the update below:

-- BACKUP FIRST (uncomment if you want to create a backup table):
-- CREATE TABLE messages_backup_before_type_fix AS SELECT * FROM messages WHERE message_type = 'direct';

-- Update direct messages to email type if they are actually email messages
-- You'll need to identify which ones based on:
-- 1. Subject patterns (like contact form subjects)
-- 2. Content patterns (like "CONTACT_EMAIL:" marker)
-- 3. from_user_id = 1 (system user for external emails)

-- Example updates (modify the WHERE conditions based on your data):

-- Update contact form messages:
UPDATE messages 
SET message_type = 'email' 
WHERE message_type = 'direct' 
  AND (subject LIKE '%Contact Form%' 
       OR message LIKE '%CONTACT_EMAIL:%'
       OR subject LIKE '%Website Contact%');

-- Update external email messages (if from system user):
UPDATE messages 
SET message_type = 'email' 
WHERE message_type = 'direct' 
  AND from_user_id = 1  -- Assuming 1 is your system user ID
  AND (subject LIKE '%@%' OR message LIKE '%@%');

-- Verify the changes:
SELECT 
    message_type,
    COUNT(*) as count
FROM messages 
GROUP BY message_type;

-- Show the updated records:
SELECT 
    id,
    message_type,
    subject,
    from_user_id,
    to_user_id,
    created_at
FROM messages 
WHERE message_type = 'email' 
ORDER BY created_at DESC;