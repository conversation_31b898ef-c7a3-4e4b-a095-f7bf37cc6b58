<?php
/**
 * Send Test Message
 * 
 * This script helps you send proper MESSAGE type notifications that support replies
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Send Test Message</h1>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to send test messages.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $notificationCenterModel = new NotificationCenterModel();
    $db = new Database();
    
    echo "<p><strong>Sending as User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. Notification Types Explained</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Notification Types:</h4>";
    echo "<ul>";
    echo "<li><strong>message</strong> - Real messages between users (✅ CAN have replies)</li>";
    echo "<li><strong>push</strong> - Push notifications (❌ NO replies)</li>";
    echo "<li><strong>system</strong> - System announcements (❌ NO replies)</li>";
    echo "<li><strong>toast</strong> - In-app notifications (❌ NO replies)</li>";
    echo "</ul>";
    echo "<p><strong>Important:</strong> Only 'message' type notifications show reply forms!</p>";
    echo "</div>";
    
    echo "<h2>2. Your Current Notifications</h2>";
    
    // Get user's notifications by type
    $db->query("SELECT 
                    notification_type, 
                    COUNT(*) as count,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count
                FROM notification_center_items 
                WHERE user_id = :user_id 
                GROUP BY notification_type 
                ORDER BY notification_type");
    $db->bind(':user_id', $userId);
    $typeCounts = $db->resultSet();
    
    if (!empty($typeCounts)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Total</th>";
        echo "<th style='padding: 8px;'>Unread</th>";
        echo "<th style='padding: 8px;'>Can Reply?</th>";
        echo "</tr>";
        
        foreach ($typeCounts as $typeCount) {
            $canReply = ($typeCount->notification_type === 'message') ? '✅ YES' : '❌ NO';
            echo "<tr>";
            echo "<td style='padding: 8px;'><strong>{$typeCount->notification_type}</strong></td>";
            echo "<td style='padding: 8px;'>{$typeCount->count}</td>";
            echo "<td style='padding: 8px;'>{$typeCount->unread_count}</td>";
            echo "<td style='padding: 8px;'>{$canReply}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No notifications found</p>";
    }
    
    echo "<h2>3. Recent Message-Type Notifications</h2>";
    
    // Get recent message-type notifications
    $db->query("SELECT 
                    id, 
                    title, 
                    message,
                    source_table,
                    source_id,
                    created_at
                FROM notification_center_items 
                WHERE user_id = :user_id 
                AND notification_type = 'message'
                ORDER BY created_at DESC 
                LIMIT 5");
    $db->bind(':user_id', $userId);
    $messageNotifications = $db->resultSet();
    
    if (!empty($messageNotifications)) {
        echo "<p>✅ Found " . count($messageNotifications) . " message-type notifications</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Title</th>";
        echo "<th style='padding: 8px;'>Source</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "<th style='padding: 8px;'>Test Reply</th>";
        echo "</tr>";
        
        foreach ($messageNotifications as $notification) {
            $viewUrl = BASE_URL . "/notification_center/viewNotification/" . $notification->id;
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$notification->id}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($notification->title, 0, 30)) . "...</td>";
            echo "<td style='padding: 8px;'>{$notification->source_table}:{$notification->source_id}</td>";
            echo "<td style='padding: 8px;'>" . date('M j H:i', strtotime($notification->created_at)) . "</td>";
            echo "<td style='padding: 8px;'><a href='{$viewUrl}' target='_blank'>View & Reply</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>These should show reply forms when you click 'View & Reply'!</strong></p>";
        
    } else {
        echo "<p>❌ No message-type notifications found</p>";
        echo "<p>This is why you don't see reply forms - you need MESSAGE type notifications!</p>";
    }
    
    echo "<h2>4. Send Test Message</h2>";
    
    // Handle form submission
    if ($_POST['action'] ?? '' === 'send_message') {
        try {
            $subject = trim($_POST['subject'] ?? '');
            $message = trim($_POST['message'] ?? '');
            $includeReply = isset($_POST['include_reply']);
            
            if (empty($subject) || empty($message)) {
                throw new Exception("Subject and message are required");
            }
            
            // Add [reply] if requested
            if ($includeReply && strpos($message, '[reply]') === false) {
                $message .= "\n\n[reply]";
            }
            
            // Send message to yourself for testing
            $messageId = $notificationCenterModel->sendMessage(
                $userId, // from (you)
                $userId, // to (yourself)
                $subject,
                $message,
                null, // no show
                false // doesn't require reply flag
            );
            
            if ($messageId) {
                echo "<div class='alert alert-success'>";
                echo "<h4>✅ Message Sent Successfully!</h4>";
                echo "<p><strong>Message ID:</strong> {$messageId}</p>";
                echo "<p><strong>Type:</strong> message (supports replies)</p>";
                echo "<p><strong>Contains [reply]:</strong> " . ($includeReply ? 'YES' : 'NO') . "</p>";
                echo "<p><a href='" . BASE_URL . "/notification_center' target='_blank'>Go to Message Center</a> to see it</p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h4>❌ Failed to Send Message</h4>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>❌ Error Sending Message</h4>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Send Test Message to Yourself</h4>";
    echo "<p>This will create a proper MESSAGE type notification that supports replies.</p>";
    
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='send_message'>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Subject:</strong></label><br>";
    echo "<input type='text' name='subject' style='width: 100%; padding: 8px;' value='Test Message with Reply - " . date('Y-m-d H:i:s') . "' required>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Message:</strong></label><br>";
    echo "<textarea name='message' rows='4' style='width: 100%; padding: 8px;' required>";
    echo "This is a test message to verify the reply system works correctly.\n\n";
    echo "As an admin, you should be able to reply to this message regardless of the [reply] tag.\n\n";
    echo "Regular users would only be able to reply if this message contains [reply].";
    echo "</textarea>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label>";
    echo "<input type='checkbox' name='include_reply' checked> ";
    echo "Include [reply] tag (allows regular users to reply once)";
    echo "</label>";
    echo "</div>";
    
    echo "<button type='submit' class='btn btn-primary'>Send Test Message</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<h2>5. How to Test Reply System</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Testing Steps:</h4>";
    echo "<ol>";
    echo "<li><strong>Send Message:</strong> Use the form above to send a test message</li>";
    echo "<li><strong>Go to Message Center:</strong> <a href='" . BASE_URL . "/notification_center' target='_blank'>Open Message Center</a></li>";
    echo "<li><strong>Look for 'Messages' Tab:</strong> Click on the 'Messages' tab (not Push/System/Toast)</li>";
    echo "<li><strong>Click 'View':</strong> Click the view button on your test message</li>";
    echo "<li><strong>Scroll Down:</strong> You should see a reply form at the bottom</li>";
    echo "<li><strong>As Admin:</strong> You should ALWAYS see the reply form on message-type notifications</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>6. Judge Management Messages</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Real Message System:</h4>";
    echo "<p>To send real messages between users (not just test messages):</p>";
    echo "<ol>";
    echo "<li><strong>Go to Judge Management:</strong> <a href='" . BASE_URL . "/judge_management' target='_blank'>Judge Management</a></li>";
    echo "<li><strong>Find a Show:</strong> Select any show you coordinate</li>";
    echo "<li><strong>Send Message to Judge:</strong> Use the 'Send Message' button next to any judge</li>";
    echo "<li><strong>Include [reply]:</strong> Add [reply] in your message if you want them to be able to reply</li>";
    echo "<li><strong>Check Their Notifications:</strong> The judge will get a MESSAGE type notification</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>7. Navigation</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Quick links:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Message Center</a> (look for Messages tab)</li>";
    echo "<li><a href='" . BASE_URL . "/judge_management' target='_blank'>Judge Management</a> (send real messages)</li>";
    echo "<li><a href='" . BASE_URL . "/debug_admin_reply.php' target='_blank'>Debug Admin Reply</a> (check your admin status)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
.btn:hover { background: #0056b3; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
