<?php
/**
 * Test push notification sending to browsers
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load core classes
require_once APPROOT . '/core/Database.php';

// Load notification helper
require_once APPROOT . '/helpers/notification_helper.php';

echo "<h1>🔔 Push Notification Test</h1>";

$userId = 3; // Test user ID

try {
    echo "<h2>1. Checking Push Subscription Status</h2>";
    
    require_once APPROOT . '/models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    
    // Check if user has push subscriptions
    $subscriptions = $notificationModel->getUserPushSubscriptions($userId);
    
    echo "<h3>📱 User Push Subscriptions:</h3>";
    if (!empty($subscriptions)) {
        echo "<p>✅ Found " . count($subscriptions) . " active push subscriptions for user $userId</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Endpoint</th><th>User Agent</th><th>Created</th></tr>";
        foreach ($subscriptions as $subscription) {
            $shortEndpoint = substr($subscription->endpoint, 0, 50) . '...';
            echo "<tr>";
            echo "<td>{$subscription->id}</td>";
            echo "<td title='{$subscription->endpoint}'>{$shortEndpoint}</td>";
            echo "<td>{$subscription->user_agent}</td>";
            echo "<td>{$subscription->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No push subscriptions found for user $userId</p>";
        echo "<p><strong>Note:</strong> User needs to:</p>";
        echo "<ol>";
        echo "<li>Visit the main site</li>";
        echo "<li>Allow push notifications when prompted</li>";
        echo "<li>Or manually enable notifications in browser settings</li>";
        echo "</ol>";
    }
    
    echo "<h2>2. Checking VAPID Configuration</h2>";
    
    $vapidPublic = defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : null;
    $vapidPrivate = defined('VAPID_PRIVATE_KEY') ? VAPID_PRIVATE_KEY : null;
    $vapidSubject = defined('VAPID_SUBJECT') ? VAPID_SUBJECT : null;
    
    echo "<h3>🔑 VAPID Keys Status:</h3>";
    echo "<p>Public Key: " . ($vapidPublic ? "✅ Configured" : "❌ Missing") . "</p>";
    echo "<p>Private Key: " . ($vapidPrivate ? "✅ Configured" : "❌ Missing") . "</p>";
    echo "<p>Subject: " . ($vapidSubject ? "✅ $vapidSubject" : "❌ Missing") . "</p>";
    
    if ($vapidPublic) {
        echo "<p><strong>Public Key:</strong> <code style='font-size: 10px;'>" . substr($vapidPublic, 0, 50) . "...</code></p>";
    }
    
    echo "<h2>3. Testing Push Notification Sending</h2>";
    
    if (empty($subscriptions)) {
        echo "<p>⚠️ Cannot test push sending - no subscriptions found</p>";
        echo "<p>Please visit the main site and enable push notifications first.</p>";
    } else {
        echo "<h3>📤 Sending Test Push Notification...</h3>";
        
        // Send a push notification
        $result = sendPushNotification($userId, "Test Push Notification", "This is a test push notification sent directly to your browser!");
        
        echo "<p>Push notification sent: " . ($result ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
        
        if ($result) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>✅ Push Notification Sent Successfully!</h4>";
            echo "<p>The push notification has been sent to your browser. You should see it appear as a system notification.</p>";
            echo "<p><strong>If you don't see the notification:</strong></p>";
            echo "<ul>";
            echo "<li>Check your browser's notification settings</li>";
            echo "<li>Make sure notifications are enabled for this site</li>";
            echo "<li>Check if Do Not Disturb mode is enabled</li>";
            echo "<li>Look in your browser's notification center</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>❌ Push Notification Failed</h4>";
            echo "<p>The push notification could not be sent. Check the error logs for details.</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>4. Checking Database Storage</h2>";
    
    $db = new Database();
    
    // Check if push notifications are being stored
    echo "<h3>💾 Push Notifications in Database:</h3>";
    $db->query("SELECT * FROM user_push_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 3");
    $db->bind(':user_id', $userId);
    $pushNotifications = $db->resultSet();
    
    if (!empty($pushNotifications)) {
        echo "<p>✅ Found " . count($pushNotifications) . " push notifications in database</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Title</th><th>Message</th><th>Is Read</th><th>Created</th></tr>";
        foreach ($pushNotifications as $notification) {
            $readStatus = $notification->is_read ? "✅ Yes" : "❌ No";
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->title}</td>";
            echo "<td>{$notification->message}</td>";
            echo "<td>{$readStatus}</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No push notifications found in database</p>";
    }
    
    echo "<h2>5. Debug Information</h2>";
    
    echo "<h3>🔧 System Status:</h3>";
    echo "<ul>";
    echo "<li>PHP Version: " . PHP_VERSION . "</li>";
    echo "<li>cURL Available: " . (function_exists('curl_init') ? "✅ Yes" : "❌ No") . "</li>";
    echo "<li>OpenSSL Available: " . (extension_loaded('openssl') ? "✅ Yes" : "❌ No") . "</li>";
    echo "<li>Debug Mode: " . (DEBUG_MODE ? "✅ Enabled" : "❌ Disabled") . "</li>";
    echo "</ul>";
    
    if (DEBUG_MODE) {
        echo "<p><strong>💡 Tip:</strong> Check your server error logs for detailed push notification sending information.</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<details><summary>Stack Trace</summary><pre>" . $e->getTraceAsString() . "</pre></details>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Push Notification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        h1, h2, h3 { color: #333; }
        h1 { border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 12px; }
        details { margin: 10px 0; }
        summary { cursor: pointer; font-weight: bold; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <hr>
    <h2>🎯 What This Test Checks</h2>
    <ol>
        <li><strong>Push Subscriptions:</strong> Whether the user has registered for push notifications</li>
        <li><strong>VAPID Configuration:</strong> Whether the server has proper VAPID keys configured</li>
        <li><strong>Push Sending:</strong> Whether push notifications are actually sent to browsers</li>
        <li><strong>Database Storage:</strong> Whether notifications are stored as fallback</li>
    </ol>
    
    <h2>✅ Success Criteria</h2>
    <ul>
        <li>✅ User should have active push subscriptions</li>
        <li>✅ VAPID keys should be configured</li>
        <li>✅ Push notification should be sent successfully</li>
        <li>✅ You should see the notification appear in your browser</li>
    </ul>
    
    <h2>🔧 Troubleshooting</h2>
    <div class="warning">
        <h4>If push notifications aren't working:</h4>
        <ol>
            <li><strong>Enable Notifications:</strong> Visit the main site and allow notifications when prompted</li>
            <li><strong>Check Browser Settings:</strong> Ensure notifications are enabled for this site</li>
            <li><strong>Check VAPID Keys:</strong> Make sure they're properly configured in config.php</li>
            <li><strong>Check Error Logs:</strong> Look for detailed error messages in server logs</li>
            <li><strong>Test Different Browsers:</strong> Try Chrome, Firefox, or Edge</li>
        </ol>
    </div>
    
    <p><a href="simple_notification_test.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔄 Test All Notifications</a></p>
    
</body>
</html>
