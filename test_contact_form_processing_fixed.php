<?php
/**
 * Test Contact Form Processing - Fixed Dependencies
 * 
 * Tests contact form processing with all dependencies properly loaded
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>📧 Test Contact Form Processing - Fixed</h1>";

try {
    $db = new Database();
    
    echo "<h2>🔧 Dependency Fix Applied</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ EmailService Dependency Issue Resolved:</h3>";
    echo "<ul>";
    echo "<li>❌ <strong>Problem:</strong> NotificationService couldn't find EmailService class</li>";
    echo "<li>🔍 <strong>Cause:</strong> Missing require_once statements in NotificationService.php</li>";
    echo "<li>✅ <strong>Solution:</strong> Added required includes at top of NotificationService</li>";
    echo "<li>🎯 <strong>Result:</strong> Contact form processing should now work without errors</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📝 Fix Details</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔧 Added to NotificationService.php:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<?php\n";
    echo "/**\n";
    echo " * Notification Service\n";
    echo " */\n\n";
    echo "// Load required models\n";
    echo "require_once APPROOT . '/models/EmailService.php';\n";
    echo "require_once APPROOT . '/models/SettingsModel.php';\n\n";
    echo "class NotificationService {\n";
    echo "    // ... rest of class\n";
    echo "}";
    echo "</pre>";
    
    echo "<h3>🎯 Why This Fixes the Issue:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>EmailService available:</strong> NotificationService can now instantiate EmailService</li>";
    echo "<li>✅ <strong>SettingsModel available:</strong> EmailService can access email configuration</li>";
    echo "<li>✅ <strong>Proper dependency chain:</strong> All required classes loaded in correct order</li>";
    echo "<li>✅ <strong>Contact forms work:</strong> No more \"Class not found\" errors</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🧪 Test Contact Form Processing</h2>";
    
    // Check for pending notifications
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE status = 'pending' AND notification_type = 'email'");
    $db->execute();
    $pendingEmails = $db->single();
    $pendingCount = $pendingEmails->count ?? 0;
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📊 Current Status:</h3>";
    echo "<p><strong>Pending email notifications:</strong> {$pendingCount}</p>";
    
    if ($pendingCount > 0) {
        // Show details of pending notifications
        $db->query("SELECT id, user_id, subject, notification_category, scheduled_for, created_at FROM notification_queue WHERE status = 'pending' AND notification_type = 'email' ORDER BY created_at DESC LIMIT 5");
        $db->execute();
        $pendingDetails = $db->resultSet();
        
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>User ID</th><th>Subject</th><th>Category</th><th>Scheduled For</th></tr>";
        
        foreach ($pendingDetails as $notification) {
            $category = $notification->notification_category ?? 'standard';
            $categoryColor = $category === 'critical' ? 'red' : 'blue';
            
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->user_id}</td>";
            echo "<td>" . htmlspecialchars(substr($notification->subject, 0, 40)) . "...</td>";
            echo "<td style='color: {$categoryColor};'><strong>{$category}</strong></td>";
            echo "<td>{$notification->scheduled_for}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<form method='post' style='margin: 15px 0;'>";
        echo "<button type='submit' name='test_processing' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Email Processing (Fixed Dependencies)</button>";
        echo "</form>";
        
        if (isset($_POST['test_processing'])) {
            echo "<h4>🔄 Processing Test Results:</h4>";
            
            try {
                // Load required classes with proper error handling
                require_once APPROOT . '/models/NotificationService.php';
                require_once APPROOT . '/models/NotificationModel.php';
                
                $notificationService = new NotificationService();
                $results = $notificationService->processPendingNotifications(5);
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Processing Results:</strong><br>";
                echo "Processed: {$results['processed']}<br>";
                echo "Sent: {$results['sent']}<br>";
                echo "Failed: {$results['failed']}<br>";
                
                if (!empty($results['errors'])) {
                    echo "<strong>Errors:</strong><br>";
                    foreach ($results['errors'] as $error) {
                        echo "• " . htmlspecialchars($error) . "<br>";
                    }
                }
                echo "</div>";
                
                if ($results['processed'] > 0) {
                    if ($results['sent'] > 0) {
                        echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Dependencies fixed - emails processed successfully!</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ Emails processed but not sent. Check SMTP configuration or global email settings.</p>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠️ No emails processed. Check notification settings or user preferences.</p>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "<br><strong>Stack Trace:</strong><br>";
                echo "<pre style='font-size: 12px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
                echo "</div>";
                
                if (strpos($e->getMessage(), 'Class') !== false && strpos($e->getMessage(), 'not found') !== false) {
                    echo "<p style='color: red;'><strong>Still a dependency issue!</strong> Check that all required files are uploaded to the server.</p>";
                } else {
                    echo "<p style='color: orange;'>Different error - dependency loading is working, but there's another issue.</p>";
                }
            }
        }
    } else {
        echo "<p>No pending email notifications. Submit a contact form to test.</p>";
        
        echo "<h4>🎯 To Test Contact Form Processing:</h4>";
        echo "<ol>";
        echo "<li>Go to your contact form page</li>";
        echo "<li>Submit a contact form message</li>";
        echo "<li>Return to this page and click the test button</li>";
        echo "<li>Verify that the contact form email is processed successfully</li>";
        echo "</ol>";
    }
    echo "</div>";
    
    echo "<h2>🔍 Troubleshooting Guide</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 If You Still Get Errors:</h3>";
    
    echo "<h4>1. \"Class not found\" errors:</h4>";
    echo "<ul>";
    echo "<li>Ensure all updated files are uploaded to the server</li>";
    echo "<li>Check file permissions (should be readable)</li>";
    echo "<li>Verify APPROOT constant is defined correctly</li>";
    echo "</ul>";
    
    echo "<h4>2. \"No emails sent\" (but processed > 0):</h4>";
    echo "<ul>";
    echo "<li>Check global email settings in admin panel</li>";
    echo "<li>Verify SMTP configuration</li>";
    echo "<li>Check user notification preferences for admin users</li>";
    echo "</ul>";
    
    echo "<h4>3. \"No emails processed\" (processed = 0):</h4>";
    echo "<ul>";
    echo "<li>Check if notifications are scheduled for future times</li>";
    echo "<li>Verify user exists in users table</li>";
    echo "<li>Check notification_queue table structure</li>";
    echo "</ul>";
    
    echo "<h4>4. Contact form not creating notifications:</h4>";
    echo "<ul>";
    echo "<li>Check contact form submission process</li>";
    echo "<li>Verify UnifiedMessageModel is being called</li>";
    echo "<li>Check for PHP errors in contact form processing</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 System Status Check</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔧 Required Components Status:</h3>";
    
    $components = [
        'Database' => class_exists('Database'),
        'NotificationService' => class_exists('NotificationService'),
        'NotificationModel' => class_exists('NotificationModel'),
        'EmailService' => class_exists('EmailService'),
        'SettingsModel' => class_exists('SettingsModel'),
        'UnifiedMessageModel' => class_exists('UnifiedMessageModel')
    ];
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Component</th><th>Status</th></tr>";
    
    foreach ($components as $component => $exists) {
        $status = $exists ? '✅ Loaded' : '❌ Missing';
        $color = $exists ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td><strong>{$component}</strong></td>";
        echo "<td style='color: {$color};'>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $allLoaded = array_reduce($components, function($carry, $item) { return $carry && $item; }, true);
    
    if ($allLoaded) {
        echo "<p style='color: green;'>✅ <strong>All required components are loaded!</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>Some components are missing.</strong> Upload all required files to the server.</p>";
    }
    echo "</div>";
    
    echo "<h2>📋 Files to Copy to Server</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated files that need to be copied:</strong></p>";
    echo "<ul>";
    echo "<li><code>models/NotificationService.php</code> - Added EmailService and SettingsModel includes</li>";
    echo "<li><code>models/UnifiedMessageModel.php</code> - Contact form critical marking, PHP time usage</li>";
    echo "<li><code>models/EmailService.php</code> - PHP time usage in queue fallback</li>";
    echo "</ul>";
    
    echo "<p><strong>What this provides:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>No dependency errors:</strong> All required classes properly loaded</li>";
    echo "<li>✅ <strong>Contact form bypass:</strong> Critical notifications ignore user preferences</li>";
    echo "<li>✅ <strong>Consistent timing:</strong> PHP server time used throughout</li>";
    echo "<li>✅ <strong>Reliable processing:</strong> Contact forms always queued for delivery</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Contact form processing test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
