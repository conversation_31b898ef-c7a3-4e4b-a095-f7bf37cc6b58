<?php
/**
 * Test Autoloader Fix
 * 
 * This file tests that the autoloader fix resolves the Facebook API conflict
 * while maintaining PHPMailer functionality.
 */

// Start session and include necessary files
session_start();

// Define constants
define('APPROOT', dirname(__FILE__));
define('BASE_URL', 'https://events.rowaneliterides.com');
define('DEBUG_MODE', true);

echo "<h1>🔧 Autoloader Fix Test</h1>";

echo "<h2>📧 Testing PHPMailer (Should Work)</h2>";

// Test PHPMailer autoloading
$vendorAutoload = APPROOT . '/vendor/autoload.php';
if (file_exists($vendorAutoload)) {
    require_once $vendorAutoload;
    echo "<p>✅ Vendor autoloader loaded</p>";
    
    // Test PHPMailer classes
    $phpmailerClasses = [
        'PHPMailer\\PHPMailer\\PHPMailer',
        'PHPMailer\\PHPMailer\\SMTP',
        'PHPMailer\\PHPMailer\\Exception'
    ];
    
    $phpmailerWorking = true;
    foreach ($phpmailerClasses as $class) {
        $exists = class_exists($class);
        echo "<p><strong>{$class}:</strong> " . ($exists ? '✅ Available' : '❌ Not Available') . "</p>";
        if (!$exists) {
            $phpmailerWorking = false;
        }
    }
    
    if ($phpmailerWorking) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "<h3>✅ PHPMailer Working!</h3>";
        echo "<p>PHPMailer classes are properly loaded and available.</p>";
        echo "</div>";
    }
} else {
    echo "<p>❌ Vendor autoloader not found</p>";
    $phpmailerWorking = false;
}

echo "<h2>📘 Testing Facebook API (Should NOT Interfere)</h2>";

// Test that Facebook classes are NOT being autoloaded by our autoloader
$facebookClasses = [
    'Facebook\\Facebook',
    'Facebook\\GraphNodes\\GraphUser',
    'JoelButcher\\Facebook\\Facebook'
];

$facebookConflicts = false;
foreach ($facebookClasses as $class) {
    $exists = class_exists($class, false); // Don't trigger autoloading
    echo "<p><strong>{$class}:</strong> " . ($exists ? '⚠️ Loaded (potential conflict)' : '✅ Not Loaded (good)') . "</p>";
    if ($exists) {
        $facebookConflicts = true;
    }
}

if (!$facebookConflicts) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<h3>✅ No Facebook SDK Conflicts!</h3>";
    echo "<p>The autoloader is not trying to load Facebook SDK classes, which is correct since you use Facebook API directly.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>⚠️ Potential Facebook Conflicts</h3>";
    echo "<p>Some Facebook SDK classes are being loaded, which might cause conflicts.</p>";
    echo "</div>";
}

echo "<h2>🧪 Testing FacebookService (Your Implementation)</h2>";

try {
    // Load your Facebook service
    require_once APPROOT . '/config/config.php';
    require_once APPROOT . '/core/Database.php';
    require_once APPROOT . '/models/SettingsModel.php';
    
    if (file_exists(APPROOT . '/libraries/facebook/FacebookService.php')) {
        echo "<p>✅ FacebookService file found</p>";
        
        // Try to load it (this is where the error was occurring)
        require_once APPROOT . '/libraries/facebook/FacebookService.php';
        echo "<p>✅ FacebookService loaded successfully</p>";
        
        // Try to instantiate it
        $facebookService = new FacebookService();
        echo "<p>✅ FacebookService instantiated successfully</p>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "<h3>🎉 Facebook API Working!</h3>";
        echo "<p>Your Facebook API implementation is working correctly without conflicts.</p>";
        echo "</div>";
        
    } else {
        echo "<p>❌ FacebookService file not found</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Facebook API Error</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h2>📋 What Was Fixed</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>Problem:</h3>";
echo "<p>The original autoloader was trying to load Facebook SDK classes that don't exist in your setup, causing conflicts with your Facebook API implementation.</p>";

echo "<h3>Solution:</h3>";
echo "<ul>";
echo "<li>✅ Removed Facebook SDK autoloaders from vendor/autoload.php</li>";
echo "<li>✅ Removed GuzzleHttp and PSR HTTP autoloaders (not needed)</li>";
echo "<li>✅ Kept only PHPMailer autoloader</li>";
echo "<li>✅ Added comments explaining the changes</li>";
echo "</ul>";

echo "<h3>Result:</h3>";
echo "<ul>";
echo "<li>✅ PHPMailer works for enhanced email functionality</li>";
echo "<li>✅ Facebook API works without conflicts</li>";
echo "<li>✅ No more 'Class Facebook\\Facebook not found' errors</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🚀 Next Steps</h2>";
echo "<ol>";
echo "<li>Copy the updated <code>vendor/autoload.php</code> to your server</li>";
echo "<li>Test your Facebook login functionality</li>";
echo "<li>Test the email functionality at <code>/admin/settings_email</code></li>";
echo "<li>Both should now work without conflicts</li>";
echo "<li>Delete this test file after verification</li>";
echo "</ol>";

// Self-delete after 5 minutes
if (file_exists(__FILE__) && (time() - filemtime(__FILE__)) > 300) {
    unlink(__FILE__);
}
?>
