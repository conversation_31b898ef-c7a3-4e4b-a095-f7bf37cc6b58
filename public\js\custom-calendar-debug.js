/**
 * Custom Calendar Debug Helper
 * 
 * This file provides debugging and troubleshooting functions for the custom calendar implementation.
 * 
 * Version 1.1.0 - Timezone Implementation
 * Updated: 2025-01-27
 * 
 * Updates v1.1.0:
 * - Added TimezoneHelper integration for test event creation
 * - Enhanced date parsing with timezone awareness
 * - Added timezone-aware debugging functions
 */

// Debug mode flag
const CALENDAR_DEBUG = true;

/**
 * Calendar Debug Helper
 */
class CalendarDebugHelper {
    /**
     * Initialize the debug helper
     * 
     * @param {CustomCalendar} calendar - The calendar instance to debug
     */
    constructor(calendar) {
        this.calendar = calendar;
        this.log('Calendar debug helper initialized');
    }

    /**
     * Log a message to the console
     * 
     * @param {string} message - The message to log
     * @param {*} data - Optional data to log
     */
    log(message, data = null) {
        if (!CALENDAR_DEBUG) return;
        
        if (data) {
            console.log(`[Calendar Debug] ${message}`, data);
        } else {
            console.log(`[Calendar Debug] ${message}`);
        }
    }

    /**
     * Log an error to the console
     * 
     * @param {string} message - The error message
     * @param {Error} error - The error object
     */
    error(message, error = null) {
        if (!CALENDAR_DEBUG) return;
        
        if (error) {
            console.error(`[Calendar Error] ${message}`, error);
        } else {
            console.error(`[Calendar Error] ${message}`);
        }
    }

    /**
     * Inspect the calendar state
     * 
     * @returns {Object} Calendar state information
     */
    inspectCalendar() {
        if (!this.calendar) {
            this.error('No calendar instance available');
            return null;
        }
        
        const state = {
            currentDate: this.calendar.currentDate,
            currentView: this.calendar.currentView,
            options: this.calendar.options,
            eventCount: this.calendar.events.length
        };
        
        this.log('Calendar state:', state);
        return state;
    }

    /**
     * Inspect events
     * 
     * @returns {Array} Events array
     */
    inspectEvents() {
        if (!this.calendar) {
            this.error('No calendar instance available');
            return null;
        }
        
        this.log('Calendar events:', this.calendar.events);
        return this.calendar.events;
    }

    /**
     * Test event loading
     * 
     * @param {string} url - The event source URL to test
     */
    testEventLoading(url) {
        this.log(`Testing event loading from: ${url}`);
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                this.log('Event data loaded successfully:', data);
                this.log(`Loaded ${data.length} events`);
                
                // Check for common issues
                if (data.length === 0) {
                    this.log('Warning: No events returned from the server');
                }
                
                // Validate event data
                if (data.length > 0) {
                    const sampleEvent = data[0];
                    this.log('Sample event:', sampleEvent);
                    
                    // Check required fields
                    const requiredFields = ['id', 'title', 'start'];
                    const missingFields = requiredFields.filter(field => !sampleEvent.hasOwnProperty(field));
                    
                    if (missingFields.length > 0) {
                        this.error(`Event is missing required fields: ${missingFields.join(', ')}`);
                    }
                }
            })
            .catch(error => {
                this.error('Error loading events:', error);
            });
    }

    /**
     * Fix common calendar issues
     */
    fixCommonIssues() {
        if (!this.calendar) {
            this.error('No calendar instance available');
            return;
        }
        
        this.log('Attempting to fix common calendar issues...');
        
        // Fix 1: Ensure events are properly formatted
        if (this.calendar.events.length > 0) {
            this.calendar.events.forEach((event, index) => {
                // Ensure start and end are Date objects - controller handles timezone conversion
                if (typeof event.start === 'string') {
                    this.log(`Converting event ${index} start from string to Date (already in correct timezone)`);
                    // Controller handles timezone conversion - just parse the date
                    this.calendar.events[index].start = new Date(event.start);
                }

                if (typeof event.end === 'string') {
                    this.log(`Converting event ${index} end from string to Date (already in correct timezone)`);
                    // Controller handles timezone conversion - just parse the date
                    this.calendar.events[index].end = new Date(event.end);
                } else if (!event.end) {
                    this.log(`Adding missing end date to event ${index}`);
                    this.calendar.events[index].end = new Date(
                        this.calendar.events[index].start.getTime() + 
                        (this.calendar.options.defaultEventDuration * 60 * 1000)
                    );
                }
            });
        }
        
        // Fix 2: Force re-render
        this.log('Forcing calendar re-render');
        this.calendar.renderView();
        
        this.log('Fix attempt completed');
    }

    /**
     * Create test events with timezone awareness
     * @param {number} count - Number of test events to create
     */
    createTestEvents(count = 2) {
        this.log(`Creating ${count} test events with timezone awareness`);
        
        let testEvents;
        if (window.TimezoneHelper) {
            testEvents = window.TimezoneHelper.createTestEvents(count);
            this.log('Test events created using TimezoneHelper');
        } else {
            // Fallback test events
            testEvents = [];
            const now = new Date();
            
            for (let i = 0; i < count; i++) {
                const startDate = new Date(now.getTime() + (i * 24 * 60 * 60 * 1000));
                const endDate = new Date(startDate.getTime() + (3 * 60 * 60 * 1000)); // 3 hours later
                
                testEvents.push({
                    id: `debug-test-${i + 1}`,
                    title: `Debug Test Event ${i + 1}`,
                    start: startDate.toISOString(),
                    end: endDate.toISOString(),
                    backgroundColor: i % 2 === 0 ? '#3788d8' : '#28a745',
                    city: 'Debug City',
                    state: 'DC',
                    venue: 'Debug Venue',
                    allDay: false,
                    extendedProps: {
                        user_id: 999,
                        calendar_name: 'Debug Calendar'
                    }
                });
            }
            this.log('Test events created using fallback method');
        }
        
        if (this.calendar) {
            this.calendar.loadEvents(testEvents);
            this.log('Test events loaded into calendar:', testEvents);
        }
        
        return testEvents;
    }

    /**
     * Add to window for console access
     */
    exposeToConsole() {
        window.calendarDebug = this;
        this.log('Calendar debug helper exposed to console as window.calendarDebug');
        this.log('Available methods: logState(), testEventLoading(url), fixCommonIssues(), createTestEvents(count)');
    }
}

// Automatically initialize when a calendar instance is available
document.addEventListener('DOMContentLoaded', function() {
    // Wait for calendar to initialize
    setTimeout(() => {
        if (window.calendarInstance) {
            const debugHelper = new CalendarDebugHelper(window.calendarInstance);
            debugHelper.exposeToConsole();
        }
    }, 1000);
});