-- Safe Email Folder System for Admin Email Management
-- Run this SQL to add email folder functionality (safe version)

-- Email folders table
CREATE TABLE IF NOT EXISTS email_folders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#007bff',
    icon VARCHAR(50) DEFAULT 'fas fa-folder',
    is_system TINYINT(1) DEFAULT 0,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_created_by (created_by)
);

-- Insert default system folders (use INSERT IGNORE to avoid duplicates)
INSERT IGNORE INTO email_folders (id, name, description, color, icon, is_system, created_by) VALUES
(1, 'Inbox', 'Default inbox for new emails', '#007bff', 'fas fa-inbox', 1, 1),
(2, 'Important', 'High priority emails', '#dc3545', 'fas fa-star', 1, 1),
(3, 'Follow Up', 'Emails requiring follow-up', '#ffc107', 'fas fa-clock', 1, 1),
(4, 'Resolved', 'Completed email conversations', '#28a745', 'fas fa-check-circle', 1, 1),
(5, 'Spam', 'Suspected spam emails', '#6c757d', 'fas fa-ban', 1, 1),
(6, 'Archive', 'Archived emails', '#17a2b8', 'fas fa-archive', 1, 1);

-- Email reminders table
CREATE TABLE IF NOT EXISTS email_reminders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    user_id INT NOT NULL,
    reminder_date DATETIME NOT NULL,
    reminder_text TEXT,
    is_completed TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_message_id (message_id),
    INDEX idx_user_id (user_id),
    INDEX idx_reminder_date (reminder_date),
    INDEX idx_is_completed (is_completed)
);

-- Email templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    variables TEXT COMMENT 'JSON array of available template variables',
    category VARCHAR(50) DEFAULT 'general',
    is_active TINYINT(1) DEFAULT 1,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
);

-- Insert default email templates (use INSERT IGNORE to avoid duplicates)
INSERT IGNORE INTO email_templates (name, subject, body, variables, category, created_by) VALUES
('Welcome Response', 'Welcome to Events and Shows Platform', 
'Hello,\n\nThank you for contacting us regarding {{subject}}.\n\nWe have received your message and will respond within 24 hours.\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 
'["{{subject}}", "{{admin_name}}", "{{date}}", "{{ticket_number}}"]', 'general', 1),

('Event Information', 'Re: {{subject}} - Event Information', 
'Hello,\n\nThank you for your inquiry about our events.\n\nHere is the information you requested:\n\n[Please add specific event details here]\n\nIf you have any other questions, please don\'t hesitate to ask.\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 
'["{{subject}}", "{{admin_name}}", "{{date}}", "{{ticket_number}}"]', 'events', 1),

('Registration Confirmation', 'Registration Confirmation - {{subject}}', 
'Hello,\n\nYour registration has been confirmed for the event.\n\nTicket Number: {{ticket_number}}\nDate: {{date}}\n\nWe look forward to seeing you at the event!\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 
'["{{subject}}", "{{admin_name}}", "{{date}}", "{{ticket_number}}"]', 'registration', 1),

('Follow Up Required', 'Re: {{subject}} - Follow Up Required', 
'Hello,\n\nWe are following up on your previous message regarding {{subject}}.\n\nPlease let us know if you need any additional assistance.\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 
'["{{subject}}", "{{admin_name}}", "{{date}}", "{{ticket_number}}"]', 'followup', 1);

-- Email statistics table
CREATE TABLE IF NOT EXISTS email_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    emails_received INT DEFAULT 0,
    emails_sent INT DEFAULT 0,
    emails_replied INT DEFAULT 0,
    avg_response_time_hours DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (date)
);

-- Note: The following ALTER TABLE statements should be run manually if needed
-- Check if the columns exist first by running: DESCRIBE messages;

-- If folder_id column doesn't exist, run:
-- ALTER TABLE messages ADD COLUMN folder_id INT DEFAULT 1;
-- ALTER TABLE messages ADD INDEX idx_folder_id (folder_id);

-- If reminder columns don't exist, run:
-- ALTER TABLE messages ADD COLUMN has_reminder TINYINT(1) DEFAULT 0;
-- ALTER TABLE messages ADD COLUMN reminder_date DATETIME NULL;
-- ALTER TABLE messages ADD INDEX idx_has_reminder (has_reminder);
-- ALTER TABLE messages ADD INDEX idx_reminder_date (reminder_date);
