<?php
/**
 * Admin Email Server Settings Controller
 * 
 * Handles email server configuration, connection testing, and processing statistics
 */
class AdminEmailServerController extends Controller {
    private $db;
    private $auth;
    private $settingsModel;
    
    public function __construct() {
        $this->db = new Database();
        $this->auth = new Auth();
        
        // Check if user is admin
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Load settings model
        require_once APPROOT . '/models/SettingsModel.php';
        $this->settingsModel = new SettingsModel();
    }
    
    /**
     * Email server settings page
     */
    public function index() {
        // Get all email-related settings
        $settings = $this->getEmailSettings();
        
        // Get processing statistics
        $stats = $this->getProcessingStats();
        
        // Get last processing run info
        $lastRun = $this->getLastProcessingRun();
        
        $data = [
            'title' => 'Email Server Settings',
            'settings' => $settings,
            'stats' => $stats,
            'last_run' => $lastRun,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/settings_email_server', $data);
    }
    
    /**
     * Save email server settings
     */
    public function save() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/settings_email_server');
            return;
        }
        
        if (!$this->verifyCsrfToken()) {
            $this->setFlashMessage('error', 'Invalid request');
            $this->redirect('admin/settings_email_server');
            return;
        }
        
        try {
            // Define settings to save
            $settingsToSave = [
                'email_processing_enabled' => isset($_POST['email_processing_enabled']) ? '1' : '0',
                'email_server_protocol' => $_POST['email_server_protocol'] ?? 'imap',
                'email_server_host' => $_POST['email_server_host'] ?? '',
                'email_server_port' => $_POST['email_server_port'] ?? '993',
                'email_server_username' => $_POST['email_server_username'] ?? '',
                'email_server_password' => $_POST['email_server_password'] ?? '',
                'email_server_encryption' => $_POST['email_server_encryption'] ?? 'ssl',
                'email_delete_after_processing' => isset($_POST['email_delete_after_processing']) ? '1' : '0',
                'email_spam_filtering' => isset($_POST['email_spam_filtering']) ? '1' : '0',
                'email_max_size_mb' => (int)($_POST['email_max_size_mb'] ?? 10),
                'email_fetch_limit' => (int)($_POST['email_fetch_limit'] ?? 50),
                'email_attachment_enabled' => isset($_POST['email_attachment_enabled']) ? '1' : '0',
                'email_attachment_max_size_mb' => (int)($_POST['email_attachment_max_size_mb'] ?? 5),
                'email_auto_reply_enabled' => isset($_POST['email_auto_reply_enabled']) ? '1' : '0',
                'ticket_number_prefix' => strtoupper($_POST['ticket_number_prefix'] ?? 'RER'),
                'email_log_retention_days' => (int)($_POST['email_log_retention_days'] ?? 30)
            ];
            
            // Validate required fields
            if (empty($settingsToSave['email_server_host']) || 
                empty($settingsToSave['email_server_username']) || 
                empty($settingsToSave['email_server_password'])) {
                $this->setFlashMessage('error', 'Server host, username, and password are required');
                $this->redirect('admin/settings_email_server');
                return;
            }
            
            // Validate ticket prefix
            if (!preg_match('/^[A-Z]{2,5}$/', $settingsToSave['ticket_number_prefix'])) {
                $this->setFlashMessage('error', 'Ticket prefix must be 2-5 uppercase letters');
                $this->redirect('admin/settings_email_server');
                return;
            }
            
            // Save all settings
            foreach ($settingsToSave as $key => $value) {
                $this->settingsModel->setSetting($key, $value);
            }
            
            $this->setFlashMessage('success', 'Email server settings saved successfully');
            
        } catch (Exception $e) {
            error_log("AdminEmailServerController::save - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to save settings: ' . $e->getMessage());
        }
        
        $this->redirect('admin/settings_email_server');
    }
    
    /**
     * Test email server connection
     */
    public function testConnection() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        try {
            // Get connection parameters from POST data
            $protocol = $_POST['email_server_protocol'] ?? 'imap';
            $host = $_POST['email_server_host'] ?? '';
            $port = $_POST['email_server_port'] ?? '993';
            $username = $_POST['email_server_username'] ?? '';
            $password = $_POST['email_server_password'] ?? '';
            $encryption = $_POST['email_server_encryption'] ?? 'ssl';
            
            if (empty($host) || empty($username) || empty($password)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Missing required connection parameters']);
                return;
            }
            
            // Test connection using EmailRetrievalService
            require_once APPROOT . '/models/EmailRetrievalService.php';
            
            // Temporarily override settings for testing
            $originalSettings = [
                'email_server_protocol' => $this->settingsModel->getSetting('email_server_protocol'),
                'email_server_host' => $this->settingsModel->getSetting('email_server_host'),
                'email_server_port' => $this->settingsModel->getSetting('email_server_port'),
                'email_server_username' => $this->settingsModel->getSetting('email_server_username'),
                'email_server_password' => $this->settingsModel->getSetting('email_server_password'),
                'email_server_encryption' => $this->settingsModel->getSetting('email_server_encryption')
            ];
            
            // Set test settings
            $this->settingsModel->setSetting('email_server_protocol', $protocol);
            $this->settingsModel->setSetting('email_server_host', $host);
            $this->settingsModel->setSetting('email_server_port', $port);
            $this->settingsModel->setSetting('email_server_username', $username);
            $this->settingsModel->setSetting('email_server_password', $password);
            $this->settingsModel->setSetting('email_server_encryption', $encryption);
            
            // Test connection
            $emailService = new EmailRetrievalService();
            $result = $emailService->testConnection();
            
            // Restore original settings
            foreach ($originalSettings as $key => $value) {
                $this->settingsModel->setSetting($key, $value);
            }
            
            header('Content-Type: application/json');
            echo json_encode($result);
            
        } catch (Exception $e) {
            error_log("AdminEmailServerController::testConnection - Error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Connection test failed: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get email-related settings
     */
    private function getEmailSettings() {
        $settingKeys = [
            'email_processing_enabled',
            'email_server_protocol',
            'email_server_host',
            'email_server_port',
            'email_server_username',
            'email_server_password',
            'email_server_encryption',
            'email_delete_after_processing',
            'email_spam_filtering',
            'email_max_size_mb',
            'email_fetch_limit',
            'email_attachment_enabled',
            'email_attachment_max_size_mb',
            'email_auto_reply_enabled',
            'ticket_number_prefix',
            'email_log_retention_days'
        ];
        
        $settings = [];
        foreach ($settingKeys as $key) {
            $settings[$key] = $this->settingsModel->getSetting($key, $this->getDefaultValue($key));
        }
        
        return $settings;
    }
    
    /**
     * Get default values for settings
     */
    private function getDefaultValue($key) {
        $defaults = [
            'email_processing_enabled' => '0',
            'email_server_protocol' => 'imap',
            'email_server_host' => '',
            'email_server_port' => '993',
            'email_server_username' => '',
            'email_server_password' => '',
            'email_server_encryption' => 'ssl',
            'email_delete_after_processing' => '1',
            'email_spam_filtering' => '1',
            'email_max_size_mb' => '10',
            'email_fetch_limit' => '50',
            'email_attachment_enabled' => '1',
            'email_attachment_max_size_mb' => '5',
            'email_auto_reply_enabled' => '1',
            'ticket_number_prefix' => 'RER',
            'email_log_retention_days' => '30'
        ];
        
        return $defaults[$key] ?? '';
    }
    
    /**
     * Get processing statistics
     */
    private function getProcessingStats() {
        // Today's stats
        $sql = "SELECT 
                    SUM(CASE WHEN processing_status = 'processed' THEN 1 ELSE 0 END) as processed_today,
                    SUM(CASE WHEN processing_status = 'failed' THEN 1 ELSE 0 END) as failed_today
                FROM email_processing_log 
                WHERE DATE(created_at) = CURDATE()";
        
        $this->db->query($sql);
        $todayStats = $this->db->single();
        
        // Total stats
        $sql = "SELECT 
                    SUM(CASE WHEN processing_status = 'processed' THEN 1 ELSE 0 END) as total_processed,
                    SUM(CASE WHEN processing_status = 'failed' THEN 1 ELSE 0 END) as total_failed
                FROM email_processing_log";
        
        $this->db->query($sql);
        $totalStats = $this->db->single();
        
        // Pending reminders
        $sql = "SELECT COUNT(*) as count FROM message_reminders WHERE is_sent = 0 AND reminder_time <= NOW()";
        $this->db->query($sql);
        $pendingReminders = $this->db->single();
        
        return [
            'processed_today' => $todayStats->processed_today ?? 0,
            'failed_today' => $todayStats->failed_today ?? 0,
            'total_processed' => $totalStats->total_processed ?? 0,
            'total_failed' => $totalStats->total_failed ?? 0,
            'pending_reminders' => $pendingReminders->count ?? 0
        ];
    }
    
    /**
     * Get last processing run information
     */
    private function getLastProcessingRun() {
        $heartbeatFile = APPROOT . '/logs/email_processing_heartbeat.txt';
        
        if (!file_exists($heartbeatFile)) {
            return null;
        }
        
        $heartbeatData = json_decode(file_get_contents($heartbeatFile), true);
        
        if (!$heartbeatData) {
            return null;
        }
        
        return $heartbeatData;
    }
    
    /**
     * Generate CSRF token
     */
    private function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}
