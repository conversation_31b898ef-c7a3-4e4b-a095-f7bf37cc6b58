<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PWA Update Banner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>PWA Update Banner Test</h1>
        <p>This page tests the PWA update banner functionality.</p>
        
        <button id="show-banner" class="btn btn-primary">Show Update Banner</button>
        <button id="clear-session" class="btn btn-secondary">Clear Session Storage</button>
        <button id="test-real-update" class="btn btn-warning">Test Real Update Scenario</button>
        
        <div class="mt-3">
            <h3>Console Output:</h3>
            <div id="console-output" class="border p-3" style="height: 300px; overflow-y: auto; background: #f8f9fa;"></div>
        </div>
    </div>

    <script src="/public/js/pwa-features.js"></script>
    
    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };
        
        // Wait for PWA features to load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (window.pwaFeatures) {
                    console.log('PWA Features loaded successfully');
                    
                    // Test button to show update banner
                    document.getElementById('show-banner').addEventListener('click', () => {
                        console.log('Manually triggering update banner...');
                        window.pwaFeatures.showUpdateAvailable();
                    });
                    
                    // Clear session storage
                    document.getElementById('clear-session').addEventListener('click', () => {
                        sessionStorage.removeItem('pwa_update_dismissed');
                        console.log('Session storage cleared');
                    });
                    
                    // Test real update scenario
                    document.getElementById('test-real-update').addEventListener('click', async () => {
                        console.log('Testing real update scenario...');
                        
                        if ('serviceWorker' in navigator) {
                            const registration = await navigator.serviceWorker.getRegistration();
                            console.log('Current registration:', registration);
                            console.log('Waiting worker:', registration?.waiting);
                            console.log('Installing worker:', registration?.installing);
                            console.log('Active worker:', registration?.active);
                            
                            if (registration) {
                                console.log('Registration scope:', registration.scope);
                                console.log('Registration update found:', registration.updatefound);
                            }
                        }
                    });
                } else {
                    console.log('PWA Features not loaded');
                }
            }, 1000);
        });
    </script>
</body>
</html>