<?php
/**
 * Admin Email Templates Controller
 * 
 * Handles email template management for auto-replies and quick responses
 */
class AdminEmailTemplatesController extends Controller {
    private $db;
    private $auth;
    private $currentAdminId;
    
    public function __construct() {
        $this->db = new Database();
        $this->auth = new Auth();
        
        // Check if user is admin
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->currentAdminId = $this->auth->getCurrentUserId();
    }
    
    /**
     * Email templates management page
     */
    public function index() {
        // Get all email templates
        $templates = $this->getEmailTemplates();
        
        // Get template variables for help
        $variables = $this->getTemplateVariables();
        
        $data = [
            'title' => 'Email Templates',
            'templates' => $templates,
            'variables' => $variables,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/settings_email_templates', $data);
    }
    
    /**
     * Create new template
     */
    public function create() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/settings_email_templates');
            return;
        }
        
        if (!$this->verifyCsrfToken()) {
            $this->setFlashMessage('error', 'Invalid request');
            $this->redirect('admin/settings_email_templates');
            return;
        }
        
        $name = trim($_POST['template_name'] ?? '');
        $subject = trim($_POST['template_subject'] ?? '');
        $body = trim($_POST['template_body'] ?? '');
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        if (empty($name) || empty($subject) || empty($body)) {
            $this->setFlashMessage('error', 'Name, subject, and body are required');
            $this->redirect('admin/settings_email_templates');
            return;
        }
        
        // Check if template name already exists
        $sql = "SELECT id FROM email_templates WHERE name = :name";
        $this->db->query($sql);
        $this->db->bind(':name', $name);
        
        if ($this->db->single()) {
            $this->setFlashMessage('error', 'Template name already exists');
            $this->redirect('admin/settings_email_templates');
            return;
        }
        
        // Create template
        $sql = "INSERT INTO email_templates (name, subject, body, is_active, created_by) 
                VALUES (:name, :subject, :body, :is_active, :created_by)";
        
        $this->db->query($sql);
        $this->db->bind(':name', $name);
        $this->db->bind(':subject', $subject);
        $this->db->bind(':body', $body);
        $this->db->bind(':is_active', $isActive);
        $this->db->bind(':created_by', $this->currentAdminId);
        
        if ($this->db->execute()) {
            $this->setFlashMessage('success', 'Template created successfully');
        } else {
            $this->setFlashMessage('error', 'Failed to create template');
        }
        
        $this->redirect('admin/settings_email_templates');
    }
    
    /**
     * Edit template
     */
    public function edit($templateId) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->updateTemplate($templateId);
        }
        
        // Get template
        $template = $this->getTemplate($templateId);
        
        if (!$template) {
            $this->setFlashMessage('error', 'Template not found');
            $this->redirect('admin/settings_email_templates');
            return;
        }
        
        // Get template variables for help
        $variables = $this->getTemplateVariables();
        
        $data = [
            'title' => 'Edit Email Template',
            'template' => $template,
            'variables' => $variables,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/edit_email_template', $data);
    }
    
    /**
     * Update template
     */
    private function updateTemplate($templateId) {
        if (!$this->verifyCsrfToken()) {
            $this->setFlashMessage('error', 'Invalid request');
            $this->redirect('admin/settings_email_templates');
            return;
        }
        
        $name = trim($_POST['template_name'] ?? '');
        $subject = trim($_POST['template_subject'] ?? '');
        $body = trim($_POST['template_body'] ?? '');
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        if (empty($name) || empty($subject) || empty($body)) {
            $this->setFlashMessage('error', 'Name, subject, and body are required');
            $this->redirect('admin/settings_email_templates/edit/' . $templateId);
            return;
        }
        
        // Check if template name already exists (excluding current template)
        $sql = "SELECT id FROM email_templates WHERE name = :name AND id != :template_id";
        $this->db->query($sql);
        $this->db->bind(':name', $name);
        $this->db->bind(':template_id', $templateId);
        
        if ($this->db->single()) {
            $this->setFlashMessage('error', 'Template name already exists');
            $this->redirect('admin/settings_email_templates/edit/' . $templateId);
            return;
        }
        
        // Update template
        $sql = "UPDATE email_templates 
                SET name = :name, subject = :subject, body = :body, is_active = :is_active 
                WHERE id = :template_id";
        
        $this->db->query($sql);
        $this->db->bind(':name', $name);
        $this->db->bind(':subject', $subject);
        $this->db->bind(':body', $body);
        $this->db->bind(':is_active', $isActive);
        $this->db->bind(':template_id', $templateId);
        
        if ($this->db->execute()) {
            $this->setFlashMessage('success', 'Template updated successfully');
        } else {
            $this->setFlashMessage('error', 'Failed to update template');
        }
        
        $this->redirect('admin/settings_email_templates');
    }
    
    /**
     * Delete template
     */
    public function delete() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $templateId = (int)($_POST['template_id'] ?? 0);
        
        if (!$templateId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid template ID']);
            return;
        }
        
        // Check if template exists
        $template = $this->getTemplate($templateId);
        if (!$template) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Template not found']);
            return;
        }
        
        // Don't allow deletion of default templates
        $defaultTemplates = ['Auto Reply Confirmation', 'Welcome Message', 'General Response'];
        if (in_array($template->name, $defaultTemplates)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Cannot delete default template']);
            return;
        }
        
        // Delete template
        $sql = "DELETE FROM email_templates WHERE id = :template_id";
        $this->db->query($sql);
        $this->db->bind(':template_id', $templateId);
        
        if ($this->db->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Template deleted successfully']);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to delete template']);
        }
    }
    
    /**
     * Preview template with sample data
     */
    public function preview() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $subject = $_POST['subject'] ?? '';
        $body = $_POST['body'] ?? '';
        
        // Sample data for preview
        $sampleData = [
            '{{subject}}' => 'Sample Email Subject',
            '{{ticket_number}}' => 'RER-2025-001',
            '{{date}}' => date('Y-m-d H:i:s'),
            '{{site_name}}' => 'Events and Shows Platform',
            '{{name}}' => 'John Doe',
            '{{admin_name}}' => $this->auth->getCurrentUserName(),
            '{{response_content}}' => 'This is a sample response content that would be filled in by the admin when replying to a message.'
        ];
        
        // Replace variables
        $previewSubject = str_replace(array_keys($sampleData), array_values($sampleData), $subject);
        $previewBody = str_replace(array_keys($sampleData), array_values($sampleData), $body);
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'preview' => [
                'subject' => $previewSubject,
                'body' => $previewBody
            ]
        ]);
    }
    
    /**
     * Get all email templates
     */
    private function getEmailTemplates() {
        $sql = "SELECT t.*, u.name as created_by_name 
                FROM email_templates t
                LEFT JOIN users u ON t.created_by = u.id
                ORDER BY t.name ASC";
        
        $this->db->query($sql);
        return $this->db->resultSet();
    }
    
    /**
     * Get single template
     */
    private function getTemplate($templateId) {
        $sql = "SELECT * FROM email_templates WHERE id = :template_id";
        $this->db->query($sql);
        $this->db->bind(':template_id', $templateId);
        return $this->db->single();
    }
    
    /**
     * Get available template variables
     */
    private function getTemplateVariables() {
        return [
            'Message Variables' => [
                '{{subject}}' => 'Original email subject',
                '{{ticket_number}}' => 'Generated ticket number',
                '{{date}}' => 'Current date and time',
                '{{message_content}}' => 'Original message content'
            ],
            'User Variables' => [
                '{{name}}' => 'Sender name',
                '{{email}}' => 'Sender email address',
                '{{admin_name}}' => 'Admin name (for responses)'
            ],
            'System Variables' => [
                '{{site_name}}' => 'Site/application name',
                '{{response_content}}' => 'Admin response content (for replies)',
                '{{contact_email}}' => 'System contact email'
            ],
            'Custom Variables' => [
                '{{custom_field_1}}' => 'Custom field 1 (define as needed)',
                '{{custom_field_2}}' => 'Custom field 2 (define as needed)'
            ]
        ];
    }
    
    /**
     * Generate CSRF token
     */
    private function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}
