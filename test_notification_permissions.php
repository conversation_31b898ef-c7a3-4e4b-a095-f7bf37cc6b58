<?php
/**
 * Test Notification Permissions
 * 
 * This script tests if the notification permission checking is working correctly
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Notification Permissions Test</h1>";

try {
    $db = new Database();
    $notificationCenterModel = new NotificationCenterModel();
    
    // Get current user
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to test notification permissions.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    echo "<p><strong>Testing for User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. Database Table Check</h2>";
    
    // Check if user_notification_preferences table exists
    try {
        $db->query("SHOW TABLES LIKE 'user_notification_preferences'");
        $tableExists = $db->single();
        
        if ($tableExists) {
            echo "<p>✅ user_notification_preferences table exists</p>";
        } else {
            echo "<p>❌ user_notification_preferences table does not exist</p>";
            echo "<p><strong>Solution:</strong> Run the notification preferences migration</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking table: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>2. User Preferences Check</h2>";
    
    // Check if user has preferences
    try {
        $db->query("SELECT * FROM user_notification_preferences WHERE user_id = :user_id");
        $db->bind(':user_id', $userId);
        $preferences = $db->single();
        
        if ($preferences) {
            echo "<p>✅ User has notification preferences</p>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Current Preferences:</h4>";
            echo "<ul>";
            echo "<li><strong>Email:</strong> " . ($preferences->email_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "<li><strong>Push:</strong> " . ($preferences->push_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "<li><strong>Toast:</strong> " . ($preferences->toast_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "<li><strong>SMS:</strong> " . ($preferences->sms_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<p>⚠️ User has no notification preferences - will create defaults</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking preferences: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. Permission Check Test</h2>";
    
    // Test the canUserSendNotifications method
    try {
        $canSend = $notificationCenterModel->canUserSendNotifications($userId);
        
        if ($canSend) {
            echo "<p>✅ User CAN send notifications</p>";
            echo "<div class='alert alert-success'>";
            echo "<h4>Permission Granted</h4>";
            echo "<p>You should be able to send messages and replies through the notification center.</p>";
            echo "</div>";
        } else {
            echo "<p>❌ User CANNOT send notifications</p>";
            echo "<div class='alert alert-danger'>";
            echo "<h4>Permission Denied</h4>";
            echo "<p>All your notification types are disabled. You need to enable at least one to send messages.</p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking permissions: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>4. Detailed Analysis</h2>";
    
    // Get user preferences using the model method
    try {
        $userPrefs = $notificationCenterModel->getUserNotificationPreferences($userId);
        
        if ($userPrefs) {
            echo "<p>✅ Model method works correctly</p>";
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Preferences via Model:</h4>";
            echo "<ul>";
            echo "<li><strong>Name:</strong> " . htmlspecialchars($userPrefs->name ?? 'Unknown') . "</li>";
            echo "<li><strong>Email Notifications:</strong> " . ($userPrefs->email_notifications ? 'Yes' : 'No') . "</li>";
            echo "<li><strong>Push Notifications:</strong> " . ($userPrefs->push_notifications ? 'Yes' : 'No') . "</li>";
            echo "<li><strong>Toast Notifications:</strong> " . ($userPrefs->toast_notifications ? 'Yes' : 'No') . "</li>";
            echo "<li><strong>SMS Notifications:</strong> " . ($userPrefs->sms_notifications ? 'Yes' : 'No') . "</li>";
            echo "</ul>";
            echo "</div>";
            
            // Calculate if user can send
            $canSendCalculated = ($userPrefs->email_notifications == 1 || 
                                $userPrefs->push_notifications == 1 || 
                                $userPrefs->toast_notifications == 1 || 
                                $userPrefs->sms_notifications == 1);
            
            echo "<p><strong>Calculated Permission:</strong> " . ($canSendCalculated ? 'CAN send' : 'CANNOT send') . "</p>";
            
        } else {
            echo "<p>❌ Model method returned null</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error with model method: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Quick Fix Options</h2>";
    
    if (!$canSend) {
        echo "<div class='alert alert-info'>";
        echo "<h4>How to Fix This:</h4>";
        echo "<ol>";
        echo "<li><strong>Enable Notifications:</strong> Go to your <a href='" . BASE_URL . "/user/settings'>account settings</a> and enable at least one notification type</li>";
        echo "<li><strong>Check Preferences:</strong> Make sure email, push, toast, or SMS notifications are enabled</li>";
        echo "<li><strong>Create Defaults:</strong> If no preferences exist, they should be created automatically</li>";
        echo "</ol>";
        echo "</div>";
        
        // Offer to create default preferences
        echo "<div style='margin: 20px 0;'>";
        echo "<h4>Create Default Preferences</h4>";
        echo "<p>Click the button below to create default notification preferences (all types enabled except SMS):</p>";
        echo "<form method='POST' style='display: inline;'>";
        echo "<input type='hidden' name='action' value='create_defaults'>";
        echo "<button type='submit' class='btn btn-primary'>Create Default Preferences</button>";
        echo "</form>";
        echo "</div>";
    }
    
    // Handle form submission
    if ($_POST['action'] ?? '' === 'create_defaults') {
        try {
            $db->query("INSERT INTO user_notification_preferences 
                       (user_id, email_notifications, sms_notifications, push_notifications, toast_notifications,
                        event_reminders, registration_updates, judging_updates, award_notifications, 
                        system_announcements, reminder_times) 
                       VALUES (:user_id, 1, 0, 1, 1, 1, 1, 1, 1, 1, :reminder_times)
                       ON DUPLICATE KEY UPDATE
                       email_notifications = 1,
                       push_notifications = 1,
                       toast_notifications = 1");
            $db->bind(':user_id', $userId);
            $db->bind(':reminder_times', '[1440, 60]');
            
            if ($db->execute()) {
                echo "<div class='alert alert-success'>";
                echo "<h4>Success!</h4>";
                echo "<p>Default notification preferences have been created. <a href=''>Refresh this page</a> to see the changes.</p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h4>Error</h4>";
                echo "<p>Failed to create default preferences.</p>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>Error</h4>";
            echo "<p>Error creating preferences: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>6. Test Links</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Test the notification center:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Open Notification Center</a></li>";
    echo "<li><a href='" . BASE_URL . "/user/settings' target='_blank'>User Settings</a> (to change notification preferences)</li>";
    echo "<li><a href='" . BASE_URL . "/judge_management' target='_blank'>Judge Management</a> (to test sending messages)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
.btn:hover { background: #0056b3; }
</style>
