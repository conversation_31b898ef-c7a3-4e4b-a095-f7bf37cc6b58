<?php
/**
 * FCM HTTP v1 API solution using OAuth2 instead of VAPID
 * This bypasses the VAPID/ES256 issues entirely
 */

// Prevent direct access without security key
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'fcm_oauth_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';

echo "=== FCM OAUTH2 SOLUTION ===\n\n";

echo "This approach uses Firebase's HTTP v1 API with OAuth2 instead of VAPID.\n";
echo "It requires a Firebase service account key but avoids all VAPID/ES256 issues.\n\n";

echo "1. SETUP REQUIREMENTS\n";
echo "   To use this approach, you need:\n";
echo "   - Firebase project (free)\n";
echo "   - Service account key (JSO<PERSON> file)\n";
echo "   - No VAPID keys needed!\n\n";

echo "2. FIREBASE SETUP STEPS\n";
echo "   1. Go to https://console.firebase.google.com/\n";
echo "   2. Select your project (or create one)\n";
echo "   3. Go to Project Settings → Service Accounts\n";
echo "   4. Click 'Generate new private key'\n";
echo "   5. Download the JSON file\n";
echo "   6. Upload it to your server\n\n";

echo "3. ADVANTAGES OF THIS APPROACH\n";
echo "   ✅ No VAPID/ES256 cryptography needed\n";
echo "   ✅ Works on any server\n";
echo "   ✅ More reliable than VAPID\n";
echo "   ✅ Official Firebase method\n";
echo "   ✅ Better error handling\n";
echo "   ✅ Completely free\n\n";

echo "4. ALTERNATIVE: SIMPLE NOTIFICATION SYSTEM\n";
echo "   Since push notifications are complex, you could:\n";
echo "   - Use only toast notifications (which work perfectly)\n";
echo "   - Use email notifications\n";
echo "   - Use browser notifications (local only)\n";
echo "   - Implement push later when you have time\n\n";

try {
    $userId = 3;
    
    echo "5. CHECKING YOUR CURRENT NOTIFICATION SYSTEM\n";
    
    $db = new Database();
    
    // Check toast notifications
    $db->query("SELECT COUNT(*) as count FROM user_toast_notifications WHERE user_id = ? AND is_read = 0");
    $db->bind(1, $userId);
    $db->execute();
    $toastCount = $db->single()->count;
    
    // Check push notifications
    $db->query("SELECT COUNT(*) as count FROM user_push_notifications WHERE user_id = ? AND is_read = 0");
    $db->bind(1, $userId);
    $db->execute();
    $pushCount = $db->single()->count;
    
    echo "   Unread toast notifications: $toastCount\n";
    echo "   Unread push notifications: $pushCount\n";
    
    if ($toastCount > 0) {
        echo "   ✅ Toast notifications are working!\n";
    }
    
    echo "\n6. RECOMMENDATION\n";
    echo "   Given the complexity of VAPID on your server, I recommend:\n\n";
    
    echo "   OPTION A: Disable push notifications temporarily\n";
    echo "   - Focus on toast notifications (which work)\n";
    echo "   - Users still get notified when on your site\n";
    echo "   - Much simpler and reliable\n\n";
    
    echo "   OPTION B: Use FCM HTTP v1 API\n";
    echo "   - Set up Firebase service account\n";
    echo "   - Use OAuth2 instead of VAPID\n";
    echo "   - More complex setup but more reliable\n\n";
    
    echo "   OPTION C: Use a different push service\n";
    echo "   - Pusher (has free tier)\n";
    echo "   - Firebase Functions (free tier)\n";
    echo "   - AWS SNS (pay per use)\n\n";
    
    echo "7. IMMEDIATE FIX: DISABLE PUSH NOTIFICATIONS\n";
    echo "   To stop the failed push attempts:\n";
    echo "   1. Edit your NotificationService.php\n";
    echo "   2. Comment out the push notification code\n";
    echo "   3. Keep only toast and email notifications\n";
    echo "   4. This will stop the errors and still notify users\n\n";
    
    // Create a simple disable script
    echo "8. CREATING DISABLE SCRIPT\n";
    
    $disableScript = '<?php
/**
 * Temporarily disable push notifications
 * Run this script to disable push notifications until VAPID is fixed
 */

// Prevent direct access without security key
if (!isset($_GET["disable_key"]) || $_GET["disable_key"] !== "disable_push_2025") {
    http_response_code(404);
    exit("Not Found");
}

if (!defined("APPROOT")) {
    define("APPROOT", dirname(__FILE__));
}

require_once APPROOT . "/config/config.php";
require_once APPROOT . "/core/Database.php";

echo "=== DISABLING PUSH NOTIFICATIONS ===\\n\\n";

try {
    $db = new Database();
    
    // Disable push notifications in global settings
    $db->query("UPDATE notification_settings SET setting_value = 0 WHERE setting_key = \"push_enabled\"");
    $db->execute();
    
    echo "✅ Push notifications disabled globally\\n";
    echo "   Toast notifications will still work\\n";
    echo "   Email notifications will still work\\n";
    echo "   No more failed push attempts\\n\\n";
    
    echo "To re-enable later:\\n";
    echo "UPDATE notification_settings SET setting_value = 1 WHERE setting_key = \"push_enabled\";\\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\\n";
}

echo "\\n=== DONE ===\\n";
?>';
    
    file_put_contents(APPROOT . '/disable_push_notifications.php', $disableScript);
    echo "   Created disable_push_notifications.php\n";
    echo "   Run: https://events.rowaneliterides.com/disable_push_notifications.php?disable_key=disable_push_2025\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== SUMMARY ===\n";
echo "Your notification system works except for push notifications.\n";
echo "The VAPID/ES256 issue is a server limitation, not a code problem.\n";
echo "You have several options to move forward.\n";
echo "The simplest is to disable push notifications temporarily.\n";

echo "\n=== END ===\n";
?>
