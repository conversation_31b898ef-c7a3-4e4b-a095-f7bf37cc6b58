<?php
/**
 * Test the notification fix - verify notifications are stored and retrieved correctly
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load core classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Controller.php';

// Load notification helper
require_once APPROOT . '/helpers/notification_helper.php';

echo "<h1>🔧 Notification System Fix Test</h1>";

$userId = 3; // Test user ID

try {
    echo "<h2>1. Testing Notification Storage</h2>";
    
    // Send a toast notification
    echo "<h3>Sending Toast Notification...</h3>";
    $toastResult = sendToastNotification($userId, "Test Toast Fix", "This toast should appear in browser");
    echo "Toast sent: " . ($toastResult ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    // Send a push notification
    echo "<h3>Sending Push Notification...</h3>";
    $pushResult = sendPushNotification($userId, "Test Push Fix", "This push should appear in browser");
    echo "Push sent: " . ($pushResult ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    echo "<h2>2. Checking Database Storage</h2>";
    
    $db = new Database();
    
    // Check toast notifications table
    echo "<h3>Toast Notifications in Database:</h3>";
    $db->query("SELECT * FROM user_toast_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
    $db->bind(':user_id', $userId);
    $toastNotifications = $db->resultSet();
    
    if (!empty($toastNotifications)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Is Read</th><th>Created</th></tr>";
        foreach ($toastNotifications as $notification) {
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->title}</td>";
            echo "<td>{$notification->message}</td>";
            echo "<td>" . ($notification->is_read ? "Yes" : "No") . "</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No toast notifications found in database<br>";
    }
    
    // Check push notifications table
    echo "<h3>Push Notifications in Database:</h3>";
    $db->query("SELECT * FROM user_push_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
    $db->bind(':user_id', $userId);
    $pushNotifications = $db->resultSet();
    
    if (!empty($pushNotifications)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Is Read</th><th>Created</th></tr>";
        foreach ($pushNotifications as $notification) {
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->title}</td>";
            echo "<td>{$notification->message}</td>";
            echo "<td>" . ($notification->is_read ? "Yes" : "No") . "</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No push notifications found in database<br>";
    }
    
    echo "<h2>3. Testing Retrieval API</h2>";
    
    require_once APPROOT . '/models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    
    $unreadNotifications = $notificationModel->getUnreadNotifications($userId);
    
    echo "<h3>Unread Notifications Retrieved:</h3>";
    echo "<p>Push notifications: " . count($unreadNotifications['push']) . "</p>";
    echo "<p>Toast notifications: " . count($unreadNotifications['toast']) . "</p>";
    
    if (!empty($unreadNotifications['toast'])) {
        echo "<h4>Toast Notifications:</h4>";
        echo "<pre>" . print_r($unreadNotifications['toast'], true) . "</pre>";
    }
    
    if (!empty($unreadNotifications['push'])) {
        echo "<h4>Push Notifications:</h4>";
        echo "<pre>" . print_r($unreadNotifications['push'], true) . "</pre>";
    }
    
    echo "<h2>4. Testing Frontend API Endpoint</h2>";
    echo "<p>Testing the /notification/getUnread endpoint that the frontend uses...</p>";

    // Test the API endpoint using cURL instead of loading controller directly
    $apiUrl = BASE_URL . '/notification/getUnread';
    echo "<p>Testing: <code>$apiUrl</code></p>";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        echo "<p>❌ cURL Error: $error</p>";
    } else {
        echo "<p>✅ HTTP Status: $httpCode</p>";
        echo "<h4>API Response:</h4>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";

        $jsonResponse = json_decode($response, true);
        if ($jsonResponse) {
            echo "<p>✅ Valid JSON response</p>";
            if (isset($jsonResponse['push']) && isset($jsonResponse['toast'])) {
                echo "<p>Push count: " . count($jsonResponse['push']) . "</p>";
                echo "<p>Toast count: " . count($jsonResponse['toast']) . "</p>";
            }
        } else {
            echo "<p>❌ Invalid JSON response</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Notification Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        h1, h2, h3 { color: #333; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <hr>
    <h2>🔍 What This Test Does</h2>
    <ol>
        <li><strong>Sends notifications</strong> using the helper functions</li>
        <li><strong>Checks database storage</strong> in the correct tables</li>
        <li><strong>Tests retrieval</strong> using the updated model method</li>
        <li><strong>Tests the API endpoint</strong> that the frontend JavaScript uses</li>
    </ol>
    
    <h2>🎯 Expected Results</h2>
    <ul>
        <li>✅ Notifications should be stored in <code>user_toast_notifications</code> and <code>user_push_notifications</code> tables</li>
        <li>✅ The API should return valid JSON with push and toast arrays</li>
        <li>✅ Frontend should now be able to display the notifications</li>
    </ul>
    
    <h2>🔄 Next Steps</h2>
    <p>After running this test successfully:</p>
    <ol>
        <li>Open your main site in another tab</li>
        <li>The notifications should appear automatically (JavaScript polls every 30 seconds)</li>
        <li>Or refresh the page to trigger immediate notification loading</li>
    </ol>
    
    <p><a href="test_notification_helper.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔔 Test Helper Functions</a></p>
    
</body>
</html>
