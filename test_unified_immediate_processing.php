<?php
/**
 * Test Unified Immediate Processing
 * 
 * Tests that the unified messaging system processes all notifications immediately
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>⚡ Test Unified Immediate Processing</h1>";

try {
    $db = new Database();
    
    echo "<h2>🎯 Unified Processing Strategy</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Correct Approach - No Duplicates:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Use existing unified messaging system</strong> - No new methods needed</li>";
    echo "<li>✅ <strong>Process ALL notifications immediately</strong> - Email, push, toast, SMS</li>";
    echo "<li>✅ <strong>Only queue if immediate fails</strong> - Smart fallback system</li>";
    echo "<li>✅ <strong>Contact forms bypass user preferences</strong> - Critical communication override</li>";
    echo "<li>✅ <strong>Use PHP server time</strong> - Consistent scheduling</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔧 How It Works</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📝 Processing Flow:</h3>";
    
    echo "<h4>1. Contact Form Submission:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "Contact Form → UnifiedMessageModel::sendMessage() → deliverViaEmail()\n";
    echo "↓\n";
    echo "sendEmailWithFallback() → EmailService::sendWithFallback()\n";
    echo "↓\n";
    echo "Try immediate SMTP → Success: Delivered instantly\n";
    echo "                  → Fail: Queue for cron processing";
    echo "</pre>";
    
    echo "<h4>2. User Preference Override:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "NotificationService::sendEmailNotification()\n";
    echo "↓\n";
    echo "Check if contact form: strpos(\$subject, 'Contact Form:') !== false\n";
    echo "↓\n";
    echo "Contact Form: Bypass user preferences (always send)\n";
    echo "Regular Message: Check user email_notifications setting";
    echo "</pre>";
    
    echo "<h4>3. Time Consistency:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "All timestamps use: date('Y-m-d H:i:s') // PHP server time\n";
    echo "Never use: NOW() // Database server time\n";
    echo "Result: scheduled_for <= current time = immediate processing";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>📊 Current System Status</h2>";
    
    // Check pending notifications
    $db->query("SELECT 
                    notification_type,
                    COUNT(*) as count,
                    MIN(scheduled_for) as earliest,
                    MAX(scheduled_for) as latest
                FROM notification_queue 
                WHERE status = 'pending' 
                GROUP BY notification_type");
    $db->execute();
    $pendingByType = $db->resultSet();
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Pending Notifications by Type:</h3>";
    
    if (!empty($pendingByType)) {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Type</th><th>Count</th><th>Earliest</th><th>Latest</th><th>Ready for Processing</th></tr>";
        
        $currentTime = date('Y-m-d H:i:s');
        $totalReady = 0;
        
        foreach ($pendingByType as $type) {
            $isReady = ($type->earliest <= $currentTime);
            $readyText = $isReady ? '✅ Ready' : '❌ Future';
            $readyColor = $isReady ? 'green' : 'red';
            
            if ($isReady) {
                $totalReady += $type->count;
            }
            
            echo "<tr>";
            echo "<td><strong>{$type->notification_type}</strong></td>";
            echo "<td>{$type->count}</td>";
            echo "<td>{$type->earliest}</td>";
            echo "<td>{$type->latest}</td>";
            echo "<td style='color: {$readyColor};'>{$readyText}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Total ready for immediate processing:</strong> {$totalReady}</p>";
        echo "<p><strong>Current server time:</strong> {$currentTime}</p>";
        
    } else {
        echo "<p style='color: green;'>✅ No pending notifications</p>";
    }
    echo "</div>";
    
    echo "<h2>🧪 Test Immediate Processing</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📧 Test All Notification Types:</h3>";
    
    // Get ready notifications count
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE status = 'pending' AND scheduled_for <= ?");
    $db->bind(1, date('Y-m-d H:i:s'));
    $db->execute();
    $readyCount = $db->single()->count ?? 0;
    
    echo "<p><strong>Notifications ready for processing:</strong> {$readyCount}</p>";
    
    if ($readyCount > 0) {
        // Show details of ready notifications
        $db->query("SELECT id, notification_type, subject, user_id, scheduled_for FROM notification_queue WHERE status = 'pending' AND scheduled_for <= ? ORDER BY scheduled_for ASC LIMIT 10");
        $db->bind(1, date('Y-m-d H:i:s'));
        $db->execute();
        $readyNotifications = $db->resultSet();
        
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Type</th><th>Subject</th><th>User</th><th>Scheduled</th></tr>";
        
        foreach ($readyNotifications as $notification) {
            $isContactForm = strpos($notification->subject, 'Contact Form:') !== false;
            $subjectColor = $isContactForm ? 'red' : 'black';
            $typeLabel = $isContactForm ? $notification->notification_type . ' (Critical)' : $notification->notification_type;
            
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td><strong>{$typeLabel}</strong></td>";
            echo "<td style='color: {$subjectColor};'>" . htmlspecialchars(substr($notification->subject, 0, 40)) . "...</td>";
            echo "<td>{$notification->user_id}</td>";
            echo "<td>{$notification->scheduled_for}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<form method='post' style='margin: 15px 0;'>";
        echo "<button type='submit' name='test_unified_processing' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Unified Immediate Processing</button>";
        echo "</form>";
        
        if (isset($_POST['test_unified_processing'])) {
            echo "<h4>🔄 Unified Processing Results:</h4>";
            
            try {
                // Load required classes
                require_once APPROOT . '/models/NotificationService.php';
                require_once APPROOT . '/models/NotificationModel.php';
                
                $notificationService = new NotificationService();
                $results = $notificationService->processPendingNotifications(10);
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Processing Results:</strong><br>";
                echo "Processed: {$results['processed']}<br>";
                echo "Sent: {$results['sent']}<br>";
                echo "Failed: {$results['failed']}<br>";
                
                if (!empty($results['errors'])) {
                    echo "<strong>Errors:</strong><br>";
                    foreach ($results['errors'] as $error) {
                        echo "• " . htmlspecialchars($error) . "<br>";
                    }
                }
                echo "</div>";
                
                if ($results['processed'] > 0) {
                    if ($results['sent'] > 0) {
                        echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Unified system processing notifications immediately!</p>";
                        
                        if ($results['sent'] == $results['processed']) {
                            echo "<p style='color: green;'>🎯 <strong>Perfect!</strong> All notifications sent immediately (no queue fallback needed)</p>";
                        } else {
                            echo "<p style='color: orange;'>⚠️ Some notifications sent immediately, others may have been queued for retry</p>";
                        }
                    } else {
                        echo "<p style='color: orange;'>⚠️ Notifications processed but not sent. Check SMTP configuration or global settings.</p>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠️ No notifications processed. Check notification settings or user preferences.</p>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
        }
    } else {
        echo "<p>No notifications ready for processing. Submit a contact form to test.</p>";
        
        echo "<h4>🎯 To Test the System:</h4>";
        echo "<ol>";
        echo "<li>Submit a contact form on your website</li>";
        echo "<li>Return to this page and refresh</li>";
        echo "<li>Click the test button to process notifications</li>";
        echo "<li>Verify immediate processing works for all notification types</li>";
        echo "</ol>";
    }
    echo "</div>";
    
    echo "<h2>🎯 Expected Behavior</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Unified System Should:</h3>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Notification Type</th><th>Processing Method</th><th>User Preferences</th><th>Timing</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Contact Form Emails</strong></td>";
    echo "<td>Immediate → Queue fallback</td>";
    echo "<td style='color: red;'>Bypassed (Critical)</td>";
    echo "<td>Instant when SMTP works</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Regular Emails</strong></td>";
    echo "<td>Immediate → Queue fallback</td>";
    echo "<td style='color: green;'>Respected</td>";
    echo "<td>Instant when SMTP works</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Push Notifications</strong></td>";
    echo "<td>Immediate</td>";
    echo "<td style='color: green;'>Respected</td>";
    echo "<td>Instant</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Toast Notifications</strong></td>";
    echo "<td>Immediate</td>";
    echo "<td style='color: green;'>Respected</td>";
    echo "<td>Instant</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>SMS Notifications</strong></td>";
    echo "<td>Immediate → Queue fallback</td>";
    echo "<td style='color: green;'>Respected</td>";
    echo "<td>Instant when provider works</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h3>🎯 Key Points:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>No duplicate methods</strong> - Uses existing unified system</li>";
    echo "<li>✅ <strong>Immediate processing</strong> - All types processed instantly when possible</li>";
    echo "<li>✅ <strong>Smart fallback</strong> - Queue only when immediate fails</li>";
    echo "<li>✅ <strong>Contact form priority</strong> - Bypasses user preferences</li>";
    echo "<li>✅ <strong>Consistent timing</strong> - PHP server time throughout</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📋 Files Updated</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated files (no duplicates):</strong></p>";
    echo "<ul>";
    echo "<li><code>models/UnifiedMessageModel.php</code> - Restored immediate processing, PHP time usage</li>";
    echo "<li><code>models/NotificationService.php</code> - Contact form preference bypass, dependency includes</li>";
    echo "<li><code>models/EmailService.php</code> - PHP time usage in queue fallback</li>";
    echo "</ul>";
    
    echo "<p><strong>What this provides:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Immediate processing</strong> for all notification types</li>";
    echo "<li>✅ <strong>Contact form priority</strong> regardless of user preferences</li>";
    echo "<li>✅ <strong>No method duplication</strong> - uses existing unified system</li>";
    echo "<li>✅ <strong>Reliable fallback</strong> - queue when immediate fails</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Unified immediate processing test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
