-- Test Email Setup
-- Run this to verify the email folder system is working

-- Check if tables exist
SELECT 'Checking email_folders table...' as status;
SELECT COUNT(*) as folder_count FROM email_folders;

SELECT 'Checking email_templates table...' as status;
SELECT COUNT(*) as template_count FROM email_templates;

SELECT 'Checking email_reminders table...' as status;
SELECT COUNT(*) as reminder_count FROM email_reminders;

SELECT 'Checking email_statistics table...' as status;
SELECT COUNT(*) as stats_count FROM email_statistics;

-- Show sample data
SELECT 'Sample folders:' as status;
SELECT id, name, color, icon FROM email_folders LIMIT 3;

SELECT 'Sample templates:' as status;
SELECT id, name, category FROM email_templates LIMIT 3;

-- Check messages table structure
SELECT 'Checking messages table columns...' as status;
DESCRIBE messages;
