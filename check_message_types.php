<?php
/**
 * Check Message Types - Help<PERSON>t
 * 
 * This script helps you identify which 'direct' messages should be changed to 'email'
 */

// Include configuration
require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Message Type Analysis</h1>";

try {
    $db = new Database();
    
    echo "<h2>Current Message Type Distribution:</h2>";
    
    // Get count by message type
    $db->query("SELECT message_type, COUNT(*) as count FROM messages GROUP BY message_type ORDER BY count DESC");
    $typeCounts = $db->resultSet();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Message Type</th><th>Count</th></tr>";
    foreach ($typeCounts as $type) {
        echo "<tr><td>{$type->message_type}</td><td>{$type->count}</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>Messages Currently Marked as 'direct':</h2>";
    
    // Get all 'direct' messages to review
    $db->query("SELECT 
                    id,
                    message_type,
                    subject,
                    LEFT(message, 100) as message_preview,
                    from_user_id,
                    to_user_id,
                    created_at
                FROM messages 
                WHERE message_type = 'direct' 
                ORDER BY created_at DESC");
    $directMessages = $db->resultSet();
    
    if (empty($directMessages)) {
        echo "<p><strong>✅ No 'direct' messages found!</strong></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Subject</th>";
        echo "<th>Message Preview</th>";
        echo "<th>From User</th>";
        echo "<th>To User</th>";
        echo "<th>Created</th>";
        echo "<th>Likely Type</th>";
        echo "</tr>";
        
        foreach ($directMessages as $msg) {
            $likelyType = 'direct'; // default
            
            // Check if it looks like an email
            if (strpos($msg->subject, 'Contact Form') !== false ||
                strpos($msg->message_preview, 'CONTACT_EMAIL:') !== false ||
                strpos($msg->subject, '@') !== false ||
                $msg->from_user_id == 1) { // assuming 1 is system user
                $likelyType = '<strong style="color: red;">email</strong>';
            }
            
            echo "<tr>";
            echo "<td>{$msg->id}</td>";
            echo "<td>" . htmlspecialchars($msg->subject) . "</td>";
            echo "<td>" . htmlspecialchars($msg->message_preview) . "...</td>";
            echo "<td>{$msg->from_user_id}</td>";
            echo "<td>{$msg->to_user_id}</td>";
            echo "<td>{$msg->created_at}</td>";
            echo "<td>{$likelyType}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>Recommended SQL Updates:</h3>";
        echo "<p>Based on the analysis above, here are the SQL commands to fix the message types:</p>";
        
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        echo "-- Update contact form messages:\n";
        echo "UPDATE messages SET message_type = 'email' WHERE message_type = 'direct' AND (subject LIKE '%Contact Form%' OR message LIKE '%CONTACT_EMAIL:%');\n\n";
        
        echo "-- Update external email messages (from system user):\n";
        echo "UPDATE messages SET message_type = 'email' WHERE message_type = 'direct' AND from_user_id = 1;\n\n";
        
        echo "-- Or update specific IDs if you prefer:\n";
        foreach ($directMessages as $msg) {
            if (strpos($msg->subject, 'Contact Form') !== false ||
                strpos($msg->message_preview, 'CONTACT_EMAIL:') !== false ||
                $msg->from_user_id == 1) {
                echo "UPDATE messages SET message_type = 'email' WHERE id = {$msg->id}; -- {$msg->subject}\n";
            }
        }
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Safety Notes:</h2>";
echo "<ul>";
echo "<li>✅ <strong>Safe to change:</strong> The code now properly handles message_type inheritance</li>";
echo "<li>✅ <strong>No breaking changes:</strong> Existing functionality will continue to work</li>";
echo "<li>✅ <strong>Better display:</strong> Email messages will show latest reply content correctly</li>";
echo "<li>⚠️ <strong>Backup recommended:</strong> Always backup before making database changes</li>";
echo "</ul>";

echo "<p><em>After making changes, the notification center will display these messages with the correct type and behavior.</em></p>";
?>