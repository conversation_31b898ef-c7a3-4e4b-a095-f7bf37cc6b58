<!DOCTYPE html>
<html>
<head>
    <title>Browser Push Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <h1>🔔 Browser Push Notification Test</h1>
    
    <div id="status" class="status info">
        <strong>Status:</strong> Initializing...
    </div>
    
    <div class="status info">
        <h3>This test checks:</h3>
        <ul>
            <li>Browser push notification support</li>
            <li>Current notification permission</li>
            <li>Service worker registration</li>
            <li>Push subscription status</li>
            <li>VAPID key configuration</li>
        </ul>
    </div>
    
    <div>
        <button class="btn-primary" onclick="checkPushSupport()">Check Push Support</button>
        <button class="btn-success" onclick="requestPermission()">Request Permission</button>
        <button class="btn-warning" onclick="testLocalNotification()">Test Local Notification</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <h3>📊 Test Results:</h3>
    <div id="log" class="log"></div>
    
    <script>
        const BASE_URL = 'https://events.rowaneliterides.com';
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            
            if (type === 'error') {
                logEntry.style.color = 'red';
            } else if (type === 'success') {
                logEntry.style.color = 'green';
            } else if (type === 'warning') {
                logEntry.style.color = 'orange';
            }
            
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            statusElement.className = `status ${type}`;
            statusElement.innerHTML = `<strong>Status:</strong> ${message}`;
        }
        
        function clearLog() {
            logElement.innerHTML = '';
        }
        
        async function checkPushSupport() {
            log('🔄 Checking browser push notification support...');
            
            // Check basic support
            if (!('serviceWorker' in navigator)) {
                log('❌ Service Worker not supported', 'error');
                updateStatus('Service Worker not supported', 'error');
                return;
            }
            log('✅ Service Worker supported', 'success');
            
            if (!('PushManager' in window)) {
                log('❌ Push Manager not supported', 'error');
                updateStatus('Push Manager not supported', 'error');
                return;
            }
            log('✅ Push Manager supported', 'success');
            
            if (!('Notification' in window)) {
                log('❌ Notifications not supported', 'error');
                updateStatus('Notifications not supported', 'error');
                return;
            }
            log('✅ Notifications supported', 'success');
            
            // Check permission
            log(`📋 Current permission: ${Notification.permission}`);
            
            // Check service worker registration
            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    log('✅ Service Worker registered', 'success');
                    log(`   Scope: ${registration.scope}`);
                    
                    // Check push subscription
                    const subscription = await registration.pushManager.getSubscription();
                    if (subscription) {
                        log('✅ Push subscription exists', 'success');
                        log(`   Endpoint: ${subscription.endpoint.substring(0, 50)}...`);
                        
                        // Test if subscription is valid
                        try {
                            const response = await fetch(`${BASE_URL}/api/test-push-subscription`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    subscription: subscription
                                })
                            });
                            
                            if (response.ok) {
                                log('✅ Push subscription is valid', 'success');
                            } else {
                                log('⚠️ Push subscription may be invalid', 'warning');
                            }
                        } catch (e) {
                            log('⚠️ Could not validate subscription', 'warning');
                        }
                        
                    } else {
                        log('❌ No push subscription found', 'error');
                        log('   Need to subscribe to push notifications', 'warning');
                    }
                } else {
                    log('❌ No service worker registered', 'error');
                }
            } catch (error) {
                log(`❌ Error checking service worker: ${error.message}`, 'error');
            }
            
            // Check VAPID key
            try {
                const vapidResponse = await fetch(`${BASE_URL}/api/notifications/vapid-key.php`);
                const vapidData = await vapidResponse.json();
                
                if (vapidData.success && vapidData.publicKey) {
                    log('✅ VAPID public key available', 'success');
                    log(`   Key: ${vapidData.publicKey.substring(0, 20)}...`);
                } else {
                    log('❌ VAPID public key not available', 'error');
                }
            } catch (error) {
                log(`❌ Error fetching VAPID key: ${error.message}`, 'error');
            }
            
            updateStatus('Push support check complete', 'success');
        }
        
        async function requestPermission() {
            log('🔄 Requesting notification permission...');
            
            if (!('Notification' in window)) {
                log('❌ Notifications not supported', 'error');
                return;
            }
            
            try {
                const permission = await Notification.requestPermission();
                log(`📋 Permission result: ${permission}`);
                
                if (permission === 'granted') {
                    log('✅ Permission granted!', 'success');
                    updateStatus('Permission granted', 'success');
                    
                    // Try to subscribe to push notifications
                    await subscribeToPush();
                } else if (permission === 'denied') {
                    log('❌ Permission denied', 'error');
                    updateStatus('Permission denied', 'error');
                    log('   You need to enable notifications in browser settings', 'warning');
                } else {
                    log('⚠️ Permission dismissed', 'warning');
                    updateStatus('Permission dismissed', 'warning');
                }
            } catch (error) {
                log(`❌ Error requesting permission: ${error.message}`, 'error');
            }
        }
        
        async function subscribeToPush() {
            log('🔄 Subscribing to push notifications...');
            
            try {
                // Get VAPID key
                const vapidResponse = await fetch(`${BASE_URL}/api/notifications/vapid-key.php`);
                const vapidData = await vapidResponse.json();
                
                if (!vapidData.success || !vapidData.publicKey) {
                    log('❌ Could not get VAPID key', 'error');
                    return;
                }
                
                // Get service worker registration
                const registration = await navigator.serviceWorker.getRegistration();
                if (!registration) {
                    log('❌ No service worker registration', 'error');
                    return;
                }
                
                // Subscribe
                const subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: vapidData.publicKey
                });
                
                log('✅ Successfully subscribed to push notifications', 'success');
                log(`   Endpoint: ${subscription.endpoint.substring(0, 50)}...`);
                
                // Send subscription to server
                const response = await fetch(`${BASE_URL}/notification/subscribe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        subscription: subscription
                    })
                });
                
                if (response.ok) {
                    log('✅ Subscription sent to server', 'success');
                } else {
                    log('⚠️ Failed to send subscription to server', 'warning');
                }
                
            } catch (error) {
                log(`❌ Error subscribing: ${error.message}`, 'error');
            }
        }
        
        function testLocalNotification() {
            log('🔄 Testing local notification...');
            
            if (Notification.permission === 'granted') {
                const notification = new Notification('Test Notification', {
                    body: 'This is a test notification from the browser',
                    icon: `${BASE_URL}/public/images/icons/icon-192x192.png`,
                    badge: `${BASE_URL}/public/images/icons/icon-72x72.png`
                });
                
                log('✅ Local notification sent', 'success');
                
                notification.onclick = function() {
                    log('👆 Notification clicked', 'info');
                    notification.close();
                };
                
                setTimeout(() => {
                    notification.close();
                }, 5000);
                
            } else {
                log('❌ Permission not granted for notifications', 'error');
                log('   Click "Request Permission" first', 'warning');
            }
        }
        
        // Auto-run basic checks
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Browser Push Test initialized');
            log(`📍 Testing site: ${BASE_URL}`);
            
            setTimeout(() => {
                checkPushSupport();
            }, 1000);
        });
    </script>
</body>
</html>
