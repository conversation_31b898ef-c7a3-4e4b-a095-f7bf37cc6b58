<?php
/**
 * Test Final Bell Icon Fixes
 * 
 * Tests the final fixes for notification bell issues:
 * 1. Remove count on mobile (just yellow bell)
 * 2. Fix desktop flickering with aggressive protection
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🔔 Final Bell Icon Fixes Test</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Final Fixes Applied</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Mobile Fix:</h3>";
    echo "<ul>";
    echo "<li><strong>Removed count display</strong> - No more red badge on mobile</li>";
    echo "<li><strong>Yellow bell only</strong> - Clean, simple visual indicator</li>";
    echo "<li><strong>Bright yellow (#FFD700)</strong> when unread messages exist</li>";
    echo "</ul>";
    
    echo "<h3>✅ Desktop Fix:</h3>";
    echo "<ul>";
    echo "<li><strong>Aggressive protection</strong> - MutationObserver watches for interference</li>";
    echo "<li><strong>Style override protection</strong> - Blocks display:none attempts</li>";
    echo "<li><strong>Force visibility</strong> - Uses !important and high z-index</li>";
    echo "<li><strong>Debug logging</strong> - Shows what's trying to hide the badge</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the final fixes...</p>";
        
        $subject = "🔔 Final Fix Test";
        $message = "This tests the final bell fixes: no mobile count, stable desktop count.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🖥️ Desktop Testing (Flickering Fix):</h3>";
    echo "<ol>";
    echo "<li><strong>Open browser dev tools</strong> (F12) → Console tab</li>";
    echo "<li><strong>Refresh the page</strong> and watch the bell icon</li>";
    echo "<li><strong>Look for console messages:</strong>";
    echo "<ul>";
    echo "<li>'[BellFixes] Desktop badge updated: X (protected)'</li>";
    echo "<li>'[BellFixes] Badge visibility attacked! Restoring...' (if something tries to hide it)</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Expected result:</strong> Badge appears once and stays visible (no flickering)</li>";
    echo "</ol>";
    
    echo "<h3>📱 Mobile Testing (No Count):</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile</strong></li>";
    echo "<li><strong>Look at bottom navigation</strong> Messages tab</li>";
    echo "<li><strong>Expected result:</strong>";
    echo "<ul>";
    echo "<li>✅ Bell icon is bright yellow when unread messages exist</li>";
    echo "<li>✅ NO red badge/count displayed</li>";
    echo "<li>✅ Clean, simple visual indicator</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🔍 Debug Commands</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🛠️ Browser Console Commands:</h3>";
    echo "<p>Use these commands in the browser console to test and debug:</p>";
    echo "<ul>";
    echo "<li><code>window.notificationBellFixes.updateBadges(3)</code> - Test with 3 unread</li>";
    echo "<li><code>window.notificationBellFixes.updateBadges(0)</code> - Test with 0 unread</li>";
    echo "<li><code>document.getElementById('notification-count').style.display = 'none'</code> - Try to hide badge (should be blocked)</li>";
    echo "<li><code>document.getElementById('mobile-notification-badge').style.display</code> - Should be 'none'</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Platform</th><th>Element</th><th>Before Fix</th><th>After Fix</th></tr>";
    
    echo "<tr>";
    echo "<td rowspan='2'><strong>🖥️ Desktop</strong></td>";
    echo "<td>Bell Badge</td>";
    echo "<td style='color: red;'>❌ Flickers: show→hide→show→hide</td>";
    echo "<td style='color: green;'>✅ Appears once, stays visible</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Console</td>";
    echo "<td style='color: red;'>❌ No debug info</td>";
    echo "<td style='color: green;'>✅ Shows protection messages</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td rowspan='2'><strong>📱 Mobile PWA</strong></td>";
    echo "<td>Bell Icon</td>";
    echo "<td style='color: green;'>✅ Yellow when unread</td>";
    echo "<td style='color: green;'>✅ Yellow when unread (unchanged)</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Count Badge</td>";
    echo "<td style='color: red;'>❌ Red badge with number</td>";
    echo "<td style='color: green;'>✅ No badge (clean look)</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Details</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🛡️ Desktop Protection Mechanisms:</h3>";
    echo "<ul>";
    echo "<li><strong>MutationObserver:</strong> Watches for style changes and restores visibility</li>";
    echo "<li><strong>Style Override:</strong> Blocks direct display:none assignments</li>";
    echo "<li><strong>Force Properties:</strong> Uses !important and high z-index</li>";
    echo "<li><strong>Debug Logging:</strong> Shows what's interfering with the badge</li>";
    echo "</ul>";
    
    echo "<h3>📱 Mobile Simplification:</h3>";
    echo "<ul>";
    echo "<li><strong>Badge Disabled:</strong> mobile-notification-badge always hidden</li>";
    echo "<li><strong>Bell Color Only:</strong> Simple yellow/normal color indication</li>";
    echo "<li><strong>Clean UI:</strong> No numbers cluttering the navigation</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Final Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>What to expect:</strong></p>";
        echo "<ul>";
        echo "<li>🖥️ <strong>Desktop:</strong> Badge appears immediately and stays visible (no flickering)</li>";
        echo "<li>📱 <strong>Mobile:</strong> Bell turns yellow, no count badge displayed</li>";
        echo "<li>🔍 <strong>Console:</strong> Shows protection messages if anything tries to interfere</li>";
        echo "</ul>";
        echo "<p><strong>This should finally solve both issues!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files to Copy</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated files that need to be copied to server:</strong></p>";
    echo "<ul>";
    echo "<li><code>public/js/notification-bell-fixes.js</code> - Main fix with protection</li>";
    echo "<li><code>views/includes/footer.php</code> - Loads the fix script</li>";
    echo "<li><code>views/includes/header.php</code> - Cache-busting for notification-center.js</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Final test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
