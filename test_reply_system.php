<?php
/**
 * Test Reply System
 * 
 * This script tests the new reply system with [reply] restrictions
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Reply System Test</h1>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to test the reply system.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $notificationCenterModel = new NotificationCenterModel();
    
    echo "<p><strong>Testing for User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. Database Schema Check</h2>";
    
    // Check if new columns exist
    $db = new Database();
    try {
        $db->query("SHOW COLUMNS FROM user_messages LIKE 'allows_reply'");
        $allowsReplyColumn = $db->single();
        
        $db->query("SHOW COLUMNS FROM user_messages LIKE 'reply_used'");
        $replyUsedColumn = $db->single();
        
        if ($allowsReplyColumn && $replyUsedColumn) {
            echo "<p>✅ Database schema updated with reply tracking columns</p>";
        } else {
            echo "<p>❌ Database schema missing reply tracking columns</p>";
            echo "<p><strong>Solution:</strong> Run the migration: <code>database/migrations/add_reply_tracking.sql</code></p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking database schema: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>2. User Role Check</h2>";
    
    // Check user's role using existing Auth system
    require_once 'core/Auth.php';
    $auth = new Auth();
    $hasPrivilegedRole = $auth->hasRole(['admin', 'coordinator', 'judge', 'staff']);
    
    if ($hasPrivilegedRole) {
        echo "<p>✅ You have privileged role (admin/coordinator/judge/staff) - can reply to any message</p>";
    } else {
        echo "<p>⚠️ You are a regular user - can only reply once to messages with [reply]</p>";
    }
    
    echo "<h2>3. Message Analysis</h2>";
    
    // Get recent messages for this user
    $db->query("SELECT 
                    id, 
                    from_user_id, 
                    subject, 
                    message, 
                    allows_reply, 
                    reply_used,
                    created_at
                FROM user_messages 
                WHERE to_user_id = :user_id 
                ORDER BY created_at DESC 
                LIMIT 10");
    $db->bind(':user_id', $userId);
    $messages = $db->resultSet();
    
    if (empty($messages)) {
        echo "<p>❌ No messages found for this user</p>";
        echo "<p>Try sending a message through judge management first.</p>";
    } else {
        echo "<p>✅ Found " . count($messages) . " recent messages</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Subject</th>";
        echo "<th style='padding: 8px;'>Contains [reply]</th>";
        echo "<th style='padding: 8px;'>Allows Reply</th>";
        echo "<th style='padding: 8px;'>Reply Used</th>";
        echo "<th style='padding: 8px;'>Can Reply</th>";
        echo "<th style='padding: 8px;'>Test</th>";
        echo "</tr>";
        
        foreach ($messages as $message) {
            $containsReply = strpos($message->message, '[reply]') !== false;
            $canReply = $notificationCenterModel->canUserReplyToMessage($userId, $message->id);
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$message->id}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($message->subject, 0, 30)) . "...</td>";
            echo "<td style='padding: 8px;'>" . ($containsReply ? '✅ Yes' : '❌ No') . "</td>";
            echo "<td style='padding: 8px;'>" . ($message->allows_reply ? '✅ Yes' : '❌ No') . "</td>";
            echo "<td style='padding: 8px;'>" . ($message->reply_used ? '✅ Used' : '❌ Available') . "</td>";
            echo "<td style='padding: 8px;'>" . ($canReply ? '✅ Yes' : '❌ No') . "</td>";
            
            $viewUrl = BASE_URL . "/notification_center/viewNotification/" . $message->id;
            echo "<td style='padding: 8px;'><a href='{$viewUrl}' target='_blank'>View</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>4. Reply Rules Summary</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>How the Reply System Works:</h4>";
    echo "<ol>";
    echo "<li><strong>Message with [reply]:</strong> Only messages containing <code>[reply]</code> in the text allow replies</li>";
    echo "<li><strong>One Reply Rule:</strong> Regular users can only reply once per message</li>";
    echo "<li><strong>Privileged Users:</strong> Admin/coordinator/judge/staff can reply to any message anytime</li>";
    echo "<li><strong>Reply Reset:</strong> Users get new reply ability when they receive another <code>[reply]</code> message</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>5. Test Scenarios</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>To Test the System:</h4>";
    echo "<ol>";
    echo "<li><strong>Send Message with [reply]:</strong> Use judge management to send a message containing <code>[reply]</code></li>";
    echo "<li><strong>Check Reply Button:</strong> Recipient should see reply form</li>";
    echo "<li><strong>Send Reply:</strong> Reply once - should work</li>";
    echo "<li><strong>Try Again:</strong> Try to reply again - should be blocked (unless privileged user)</li>";
    echo "<li><strong>Send New [reply] Message:</strong> Send another message with <code>[reply]</code> - should reset reply ability</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>6. Quick Actions</h2>";
    
    if ($_POST['action'] ?? '' === 'create_test_message') {
        try {
            // Create a test message with [reply]
            $testSubject = "Test Message with Reply - " . date('Y-m-d H:i:s');
            $testMessage = "This is a test message that contains [reply] to allow replies. You can reply to this message once.";
            
            $messageId = $notificationCenterModel->sendMessage(
                $userId, // from (self)
                $userId, // to (self) 
                $testSubject,
                $testMessage,
                null, // no show
                false // doesn't require reply
            );
            
            if ($messageId) {
                echo "<div class='alert alert-success'>";
                echo "<h4>Test Message Created!</h4>";
                echo "<p>Created test message with ID: {$messageId}</p>";
                echo "<p><a href='" . BASE_URL . "/notification_center/viewNotification/{$messageId}' target='_blank'>View Test Message</a></p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h4>Failed to Create Test Message</h4>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>Error Creating Test Message</h4>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<div style='margin: 20px 0;'>";
    echo "<h4>Create Test Message</h4>";
    echo "<p>Create a test message to yourself with [reply] to test the system:</p>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='create_test_message'>";
    echo "<button type='submit' class='btn btn-primary'>Create Test Message with [reply]</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<h2>7. Navigation Links</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Test the system:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Message Center</a></li>";
    echo "<li><a href='" . BASE_URL . "/judge_management' target='_blank'>Judge Management</a> (send messages with [reply])</li>";
    echo "<li><a href='" . BASE_URL . "/user/settings' target='_blank'>User Settings</a> (notification preferences)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
.btn:hover { background: #0056b3; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
