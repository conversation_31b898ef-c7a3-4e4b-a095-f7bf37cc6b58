# ContentCleaner MIME Regex Fix

## Issue
ContentCleaner was extracting MIME boundary garbage instead of actual message content when processing existing database messages with raw MIME content.

## Root Cause
The regex pattern was capturing the wrong part of the MIME content. Specifically:
- <PERSON><PERSON> was matching the MIME boundary instead of the text content
- <PERSON><PERSON> didn't account for MIME structure without blank lines after headers

## Example Problem
**Database content:**
```
------sinikael-?=_1-**************.****************
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 7bit
test message  
 asking a question
------sinikael-?=_1-**************.****************
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable
<html>...</html>
------sinikael-?=_1-**************.****************--
```

**Was extracting:** `------sinikael-?=_1-**************.****************--`
**Should extract:** `test message asking a question`

## Solution
Added specific regex pattern to handle MIME structure without blank lines after headers:

```php
// Pattern 1: Your specific MIME structure (no blank line after headers)
if (preg_match('/Content-Type:\s*text\/plain[^;]*(?:;[^=]*=[^;]+)*.*?\nContent-Transfer-Encoding:[^\n]*\n(.+?)(?=\n--)/s', $content, $matches)) {
```

## Files Modified
- `core/ContentCleaner.php` - Added specific regex pattern for MIME without blank lines
- `scripts/test_content_cleaner.php` - Created test script to verify fix
- `scripts/cleanup_mime_messages.php` - Created database cleanup script

## Testing
Run test script to verify:
```bash
cd "d:/Downloads/events and shows/scripts"
php test_content_cleaner.php
```

## Database Cleanup
For existing messages with MIME garbage:
```bash
php cleanup_mime_messages.php preview 10  # Preview
php cleanup_mime_messages.php cleanup     # Actually clean
```

## Expected Result
Messages should now display clean text instead of MIME boundaries:
- **Before:** `------sinikael-?=_1-**************.****************--`
- **After:** `test message asking a question`