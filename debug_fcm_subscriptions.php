<?php
/**
 * Debug FCM Subscriptions
 * 
 * Check if user has valid FCM subscriptions for push notifications
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'helpers/csrf_helper.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$userId = $_SESSION['user_id'];
$db = new Database();

echo "<h2>FCM Subscriptions Debug</h2>";
echo "<p><strong>User ID:</strong> $userId</p>";

try {
    // Check push subscriptions table
    echo "<h3>Push Subscriptions for User $userId</h3>";
    $db->query('SELECT * FROM push_subscriptions WHERE user_id = :user_id ORDER BY created_at DESC');
    $db->bind(':user_id', $userId);
    $subscriptions = $db->resultSet();
    
    echo "<p><strong>Total Push Subscriptions:</strong> " . count($subscriptions) . "</p>";
    
    if (!empty($subscriptions)) {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr><th>ID</th><th>Endpoint (First 30 chars)</th><th>User Agent</th><th>Active</th><th>Created</th><th>P256DH Key</th></tr>";
        foreach ($subscriptions as $sub) {
            $endpointPreview = substr($sub->endpoint ?? '', 0, 30) . '...';
            $isActive = $sub->active ? 'Yes' : 'No';
            $p256dhPreview = substr($sub->p256dh_key ?? '', 0, 20) . '...';
            echo "<tr>";
            echo "<td>{$sub->id}</td>";
            echo "<td>{$endpointPreview}</td>";
            echo "<td>" . htmlspecialchars(substr($sub->user_agent ?? 'Unknown', 0, 50)) . "...</td>";
            echo "<td style='color: " . ($sub->active ? 'green' : 'red') . ";'>{$isActive}</td>";
            echo "<td>{$sub->created_at}</td>";
            echo "<td>{$p256dhPreview}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'><strong>❌ No push subscriptions found!</strong></p>";
        echo "<p>This means your devices haven't registered for push notifications properly.</p>";
    }
    
    // Check if FCM is configured
    echo "<h3>FCM Configuration Check</h3>";
    
    $fcmConfigured = defined('FCM_SERVER_KEY') && !empty(FCM_SERVER_KEY);
    echo "<p><strong>FCM Server Key Configured:</strong> " . ($fcmConfigured ? '✅ Yes' : '❌ No') . "</p>";
    
    if ($fcmConfigured) {
        $keyPreview = substr(FCM_SERVER_KEY, 0, 20) . '...';
        echo "<p><strong>FCM Server Key Preview:</strong> {$keyPreview}</p>";
    }
    
    // Test the NotificationService method
    echo "<h3>NotificationService Test</h3>";
    require_once 'models/NotificationService.php';
    $notificationService = new NotificationService();
    
    // Use reflection to access the private method
    $reflection = new ReflectionClass($notificationService);
    $method = $reflection->getMethod('getNotificationModel');
    $method->setAccessible(true);
    $notificationModel = $method->invoke($notificationService);
    
    $userSubscriptions = $notificationModel->getUserPushSubscriptions($userId);
    
    echo "<p><strong>getUserPushSubscriptions() Result:</strong> " . count($userSubscriptions) . " subscriptions</p>";
    
    if (!empty($userSubscriptions)) {
        echo "<h4>Active Subscriptions Details</h4>";
        echo "<pre>";
        foreach ($userSubscriptions as $sub) {
            echo "ID: {$sub->id}\n";
            echo "Endpoint: " . substr($sub->endpoint ?? '', 0, 50) . "...\n";
            echo "User Agent: " . substr($sub->user_agent ?? '', 0, 50) . "...\n";
            echo "P256DH Key: " . substr($sub->p256dh_key ?? '', 0, 30) . "...\n";
            echo "Auth Key: " . substr($sub->auth_key ?? '', 0, 30) . "...\n";
            echo "Active: " . ($sub->active ? 'Yes' : 'No') . "\n";
            echo "---\n";
        }
        echo "</pre>";
    }
    
    // Check database table structure
    echo "<h3>Push Subscriptions Table Structure</h3>";
    $db->query('DESCRIBE push_subscriptions');
    $structure = $db->resultSet();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($structure as $field) {
        echo "<tr>";
        echo "<td>{$field->Field}</td>";
        echo "<td>{$field->Type}</td>";
        echo "<td>{$field->Null}</td>";
        echo "<td>{$field->Key}</td>";
        echo "<td>{$field->Default}</td>";
        echo "<td>{$field->Extra}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/test_push_fix.php'>→ Back to Push Test</a></p>";
echo "<p><a href='/admin/dashboard'>← Back to Admin Dashboard</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>FCM Subscriptions Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
</body>
</html>