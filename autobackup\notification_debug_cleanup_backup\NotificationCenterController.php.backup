<?php
/**
 * Notification Center Controller
 * 
 * This controller handles the notification center functionality including
 * viewing notifications, messages, and handling replies.
 */
class NotificationCenterController extends Controller {
    private $auth;
    private $db;
    private $notificationCenterModel;
    private $userModel;
    private $showModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Call parent constructor first
        parent::__construct();

        // Initialize core dependencies
        $this->auth = new Auth();
        $this->db = new Database();
        
        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Initialize models - use unified message system
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        $this->notificationCenterModel = new UnifiedMessageModel();
        $this->userModel = $this->model('UserModel');
        $this->showModel = $this->model('ShowModel');
    }
    
    /**
     * Default index method - shows notification center
     */
    public function index() {
        $this->center();
    }
    
    /**
     * Main notification center page - unified messages view
     */
    public function center() {
        $userId = $this->auth->getCurrentUserId();
        
        // Get status filter from URL
        $status = $_GET['status'] ?? 'all';
        $page = (int)($_GET['page'] ?? 1);
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        // Get messages for this user
        $messages = $this->notificationCenterModel->getUserMessages($userId, $status, $limit, $offset);
        
        // Group messages into conversations
        $conversations = $this->groupMessagesIntoConversations($messages);
        
        // Get counts for badges - use the reliable database counts
        $counts = $this->notificationCenterModel->getMessageCounts($userId);
        
        // Get conversation counts for tabs
        $conversationCounts = $this->notificationCenterModel->getConversationCounts($userId);
        
        // Calculate pagination
        $totalCount = $counts['total_count'];
        if ($status === 'unread') {
            $totalCount = $counts['total_unread'];
        } elseif ($status === 'archived') {
            $totalCount = $counts['archived_count'];
        }
        
        $totalPages = ceil($totalCount / $limit);
        
        // Prepare data for view
        $data = [
            'conversations' => $conversations,
            'currentStatus' => $status,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalCount' => $totalCount,
            'counts' => $counts,
            'conversationCounts' => $conversationCounts,
            'hasNextPage' => $page < $totalPages,
            'hasPrevPage' => $page > 1
        ];
        
        $this->view('notification_center/index', $data);
    }
    
    /**
     * Group individual messages into conversation threads
     */
    private function groupMessagesIntoConversations($messages) {
        $conversations = [];
        $processedMessages = [];
        
        foreach ($messages as $message) {
            // Skip if already processed as part of another conversation
            if (in_array($message->id, $processedMessages)) {
                continue;
            }
            
            // Determine the root message ID
            $rootId = $message->parent_message_id ?: $message->id;
            
            // If this is a new conversation, create it
            if (!isset($conversations[$rootId])) {
                // Find the actual root message if this message is a reply
                if ($message->parent_message_id) {
                    $rootMessage = $this->findRootMessage($messages, $message->parent_message_id);
                    if ($rootMessage) {
                        $conversations[$rootId] = [
                            'root_message' => $rootMessage,
                            'replies' => [],
                            'total_messages' => 1,
                            'last_activity' => $rootMessage->created_at,
                            'has_unread' => !$rootMessage->is_read
                        ];
                        $processedMessages[] = $rootMessage->id;
                    }
                } else {
                    // This is the root message
                    $conversations[$rootId] = [
                        'root_message' => $message,
                        'replies' => [],
                        'total_messages' => 1,
                        'last_activity' => $message->created_at,
                        'has_unread' => !$message->is_read
                    ];
                    $processedMessages[] = $message->id;
                }
            }
            
            // If this is a reply, add it to the conversation
            if ($message->parent_message_id && isset($conversations[$rootId])) {
                $conversations[$rootId]['replies'][] = $message;
                $conversations[$rootId]['total_messages']++;
                
                // Update last activity if this message is newer
                if ($message->created_at > $conversations[$rootId]['last_activity']) {
                    $conversations[$rootId]['last_activity'] = $message->created_at;
                }
                
                // Update unread status
                if (!$message->is_read) {
                    $conversations[$rootId]['has_unread'] = true;
                }
                
                $processedMessages[] = $message->id;
            }
        }
        
        // Sort conversations by last activity (newest first)
        uasort($conversations, function($a, $b) {
            return strtotime($b['last_activity']) - strtotime($a['last_activity']);
        });
        
        return array_values($conversations);
    }
    
    /**
     * Find the root message for a given message ID
     */
    private function findRootMessage($messages, $messageId) {
        foreach ($messages as $message) {
            if ($message->id == $messageId) {
                return $message;
            }
        }
        return null;
    }
    
    /**
     * View a specific message thread
     */
    public function view() {
        $userId = $this->auth->getCurrentUserId();
        $messageId = $_GET['id'] ?? null;
        
        if (!$messageId) {
            $this->redirect('notification_center');
            return;
        }
        
        // Get the message thread
        $messageThread = $this->notificationCenterModel->getMessageThread($messageId, $userId);
        
        if (empty($messageThread)) {
            $_SESSION['flash_message'] = 'Message not found or you do not have permission to view it.';
            $_SESSION['flash_type'] = 'error';
            $this->redirect('notification_center');
            return;
        }
        
        // The first message is the root message
        $message = $messageThread[0];
        
        // Prepare data for view
        $data = [
            'message' => $message,
            'messageThread' => $messageThread,
            'currentUserId' => $userId
        ];
        
        $this->view('notification_center/view', $data);
    }
    
    /**
     * Reply to a message
     */
    public function reply() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('notification_center');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $messageId = $_POST['message_id'] ?? null;
        $replyMessage = trim($_POST['reply_message'] ?? '');
        
        if (!$messageId || !$replyMessage) {
            $_SESSION['flash_message'] = 'Please provide a reply message.';
            $_SESSION['flash_type'] = 'error';
            $this->redirect('notification_center/view?id=' . $messageId);
            return;
        }
        
        // Send the reply
        $replyId = $this->notificationCenterModel->replyToMessage($messageId, $userId, $replyMessage);
        
        if ($replyId) {
            $_SESSION['flash_message'] = 'Reply sent successfully!';
            $_SESSION['flash_type'] = 'success';
        } else {
            $_SESSION['flash_message'] = 'Failed to send reply. Please try again.';
            $_SESSION['flash_type'] = 'error';
        }
        
        $this->redirect('notification_center/view?id=' . $messageId);
    }
    
    /**
     * Mark a message as read
     */
    public function markAsRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $messageId = $_POST['message_id'] ?? null;
        
        if (!$messageId) {
            $this->jsonResponse(['success' => false, 'message' => 'Message ID required']);
            return;
        }
        
        $success = $this->notificationCenterModel->markAsRead($messageId, $userId);
        
        $this->jsonResponse(['success' => $success]);
    }
    
    /**
     * Archive a message
     */
    public function archive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $messageId = $_POST['message_id'] ?? null;
        
        if (!$messageId) {
            $this->jsonResponse(['success' => false, 'message' => 'Message ID required']);
            return;
        }
        
        $success = $this->notificationCenterModel->archiveThread($messageId, $userId);
        
        if ($success) {
            $this->jsonResponse(['success' => true, 'message' => 'Conversation archived successfully']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to archive conversation']);
        }
    }
    
    /**
     * Unarchive a message
     */
    public function unarchive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $messageId = $_POST['message_id'] ?? null;
        
        if (!$messageId) {
            $this->jsonResponse(['success' => false, 'message' => 'Message ID required']);
            return;
        }
        
        $success = $this->notificationCenterModel->unarchiveThread($messageId, $userId);
        
        if ($success) {
            $this->jsonResponse(['success' => true, 'message' => 'Conversation unarchived successfully']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to unarchive conversation']);
        }
    }
    
    /**
     * Delete a message thread
     */
    public function delete() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $messageId = $_POST['message_id'] ?? null;
        
        if (!$messageId) {
            $this->jsonResponse(['success' => false, 'message' => 'Message ID required']);
            return;
        }
        
        $success = $this->notificationCenterModel->deleteThread($messageId, $userId);
        
        if ($success) {
            $this->jsonResponse(['success' => true, 'message' => 'Conversation deleted successfully']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to delete conversation']);
        }
    }
    
    /**
     * Get unread count for AJAX updates
     */
    public function getUnreadCount() {
        $userId = $this->auth->getCurrentUserId();
        
        try {
            // Use the reliable database counts
            $counts = $this->notificationCenterModel->getMessageCounts($userId);
            $conversationCounts = $this->notificationCenterModel->getConversationCounts($userId);
            
            $this->jsonResponse([
                'success' => true,
                'total_unread' => $counts['total_unread'],
                'counts' => $counts,
                'conversationCounts' => $conversationCounts
            ]);
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::getUnreadCount - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'total_unread' => 0]);
        }
    }
    
    /**
     * Get thread info for confirmation dialogs
     */
    public function getThreadInfo() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = $_POST['message_id'] ?? null;

        if (!$messageId) {
            $this->jsonResponse(['success' => false, 'message' => 'Message ID required']);
            return;
        }

        $threadInfo = $this->notificationCenterModel->getThreadInfo($messageId, $userId);

        if ($threadInfo) {
            $this->jsonResponse([
                'success' => true,
                'total_messages' => $threadInfo->total_messages,
                'self_replies' => $threadInfo->self_replies ?? 0
            ]);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'Thread not found']);
        }
    }
}
?>