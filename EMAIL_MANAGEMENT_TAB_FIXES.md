# Email Management Tab Conversation Grouping Fixes

## Issues Fixed

### 1. Statistics Count Conversations Instead of Individual Messages
**Problem**: The total emails card and folder statistics were counting individual messages instead of conversations.

**Fix**: Updated statistics queries in the manage tab to use:
```sql
COUNT(DISTINCT CASE WHEN ticket_number IS NOT NULL THEN ticket_number ELSE id END)
```

This counts:
- Messages with ticket numbers as one conversation per unique ticket number
- Messages without ticket numbers as individual conversations

**Files Modified**: 
- `controllers/NotificationCenterController.php` (lines 155-168)

### 2. Recent Emails Query Excludes Archived Messages
**Problem**: Recent emails in the manage tab dashboard were including archived messages.

**Fix**: Added `AND m.is_archived = 0` condition to both admin and coordinator recent email queries.

**Files Modified**: 
- `controllers/NotificationCenterController.php` (lines 176, 185)

### 3. Folder Email Retrieval Excludes Archived Messages
**Problem**: When clicking on folders in the manage tab, archived messages were being included, causing incorrect counts and bulk action issues.

**Fix**: Added `AND m.is_archived = 0` condition to both admin and coordinator queries in `getEmailsByFolder()` method.

**Files Modified**: 
- `controllers/NotificationCenterController.php` (lines 2351, 2362)

## Scope
All changes are specifically scoped to the email management tab (`/notification_center?status=manage`) and only affect:
- Admin and coordinator users
- Email management dashboard statistics
- Email folder filtering functionality
- Bulk actions on conversations

### 4. Conversation Badge Counts Use Actual Database Counts
**Problem**: Email tabs showed different conversation badge counts than "all messages" tab because:
- All messages tab counts ALL message types in conversation (email + direct messages)
- Email tabs only counted email messages, missing direct messages in the same conversation
- Folder filtering further reduced counts by only showing messages from specific folders

**Fix**: 
1. Modified `groupMessagesByTicketNumber()` method to accept `$useActualCounts` parameter
2. When true, queries database for actual total count of ALL messages in conversation (not just emails)
3. Enabled actual counts for both "email messages" tab and "email management" tab
4. Removed `message_type = 'email'` filter from count query to match "all messages" behavior

**Files Modified**: 
- `controllers/NotificationCenterController.php` (lines 296, 110, 330, 2389, 192)

### 5. Fixed Tab Counts to Show Conversation Counts
**Problem**: 
- Email Messages tab was counting individual messages instead of conversations
- Email Management tab had no count badge

**Fix**:
1. Created `getEmailConversationCount()` method that counts distinct conversations using ticket numbers
2. Updated Email Messages tab to use conversation count instead of message count
3. Added count badge to Email Management tab showing total email conversations

**Files Modified**: 
- `controllers/NotificationCenterController.php` (lines 119, 121, 136, 295-331)
- `views/notification_center/index.php` (line 61)

## Expected Results
1. **Total Emails Card**: Now shows conversation count instead of individual message count
2. **Folder Display Table**: Shows correct conversation counts (already working via `getFolderMessageCount`)
3. **Bulk Actions**: Now properly identifies all messages in a conversation for archive/delete operations
4. **Recent Emails**: Only shows non-archived messages in the dashboard
5. **Conversation Badges**: Show consistent counts between all tabs - always displays the actual total number of messages in each conversation
6. **Tab Counts**: Email Messages and Email Management tabs now show conversation counts instead of individual message counts

### 6. Removed Archive Folder from Email Management System
**Problem**: 
- Email management used folder-based organization (including Archive folder)
- Main system used status-based organization (Archived tab)
- Archiving from email management set `is_archived = 1` AND moved to archive folder
- This caused archived emails to disappear from email management but appear in main archived tab
- Created confusion between two different organizational systems

**Fix**:
1. Excluded Archive folder (ID 6) from email management folder lists
2. Modified archive bulk action to only set `is_archived = 1` (no folder move)
3. Prevented moving messages to Archive folder via move-to-folder actions
4. Archive folder now handled exclusively by main archived tab

**Files Modified**: 
- `models/EmailFolderModel.php` (line 23: added `WHERE f.id != 6`)
- `controllers/NotificationCenterController.php` (lines 2072-2081, 1287-1292, 1862-1867)

**Expected Results**:
- Email Management shows only active folders: Inbox, Important, Follow Up, Resolved, Spam
- Archive action sets `is_archived = 1` without moving to archive folder
- Archived emails appear in main Archived tab with all other archived messages
- Clean separation between folder-based (email management) and status-based (main tabs) organization

### 7. Fixed Message Threading for All Tabs
**Problem**: 
- `groupMessagesByTicketNumber` method only handled email messages with `ticket_number`
- Non-email messages with `parent_message_id` threading were showing as separate rows
- Other tabs (all messages, unread, archived) lost conversation grouping for non-email messages

**Fix**:
1. Enhanced `groupMessagesByTicketNumber` to handle both threading systems:
   - Email messages: Use `ticket_number` for threading (existing logic)
   - Non-email messages: Use `parent_message_id` for threading (new logic)
2. Added helper methods: `getThreadKey()`, `getThreadMessages()`, `findRootMessage()`
3. Messages now properly group into conversations regardless of message type

**Files Modified**: 
- `controllers/NotificationCenterController.php` (lines 351-388, 470-518)

**Expected Results**:
- All tabs properly group messages into conversations
- Email messages thread by `ticket_number` 
- Non-email messages thread by `parent_message_id`
- Related messages appear as single conversation rows with proper counts
- Email management bulk actions continue to work on complete conversations

### 8. Fixed Email Parser for Multipart Messages
**Problem**: 
- Email parser failed on multipart emails with custom boundaries
- Hard-coded boundary pattern (`--=-`) didn't match actual boundaries like `------sinikael-?=_1-17528869465410.7023045675010756`
- Only handled `quoted-printable` encoding, not `7bit` or other encodings
- Showed HTML parsing artifacts instead of clean text content

**Fix**:
1. **Dynamic boundary detection**: Extract actual boundary from email content
2. **Multiple encoding support**: Handle 7bit, 8bit, quoted-printable, base64 encodings
3. **Improved text extraction**: Better separation of headers from content
4. **Prefer text/plain**: Prioritize plain text parts over HTML parts
5. **Robust fallbacks**: Multiple fallback methods for edge cases
6. **Debug logging**: Added debug output when DEBUG_MODE is enabled

**Files Modified**: 
- `models/EmailRetrievalService.php` (lines 281-406: complete rewrite of multipart parsing)

**Expected Results**:
- Multipart emails display clean text content instead of HTML artifacts
- Custom boundaries are properly detected and parsed
- Different encodings (7bit, quoted-printable, base64) are handled correctly
- Email replies show actual message content like "test reply" instead of parsing errors

### 9. Fixed Email Formatting for Templates and Multiline Content
**Problem**: 
- Emails sent using templates displayed as single lines with no spacing
- Multiline content appeared without line breaks in email clients
- Plain text with `\n` line breaks sent as HTML showed no formatting

**Fix**:
1. **Added `convertPlainTextToHtml()` method**: Converts plain text to properly formatted HTML
2. **Smart HTML detection**: Checks if content is already HTML and preserves it
3. **Proper text conversion**: 
   - Escapes HTML special characters for security
   - Converts `\n` line breaks to `<br>` tags using `nl2br()`
   - Wraps content in proper HTML structure with professional CSS styling
4. **Applied to PHPMailer**: Removed useless mail() fallback since SMTP authentication is required

**Files Modified**: 
- `models/EmailService.php` (lines 299, 328, 333, 345, 445-481)

**Expected Results**:
- Template emails display with proper line breaks and spacing
- Multiline email content maintains formatting in all email clients
- Professional appearance with consistent styling
- Both incoming and outgoing emails properly formatted

## Testing
- Navigate to `/notification_center?status=manage` as admin or coordinator
- Verify total emails card shows conversation count
- Test bulk archive/delete actions on email conversations
- Verify folder counts match conversation grouping behavior