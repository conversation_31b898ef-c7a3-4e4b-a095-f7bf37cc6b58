<?php
/**
 * Test Action Button Sizing Fix
 * 
 * Tests that the View Related Show and Archive Conversation buttons are the same size
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🔘 Test Action Button Sizing Fix</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Action Button Improvements Applied</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Issues Fixed:</h3>";
    echo "<ul>";
    echo "<li><strong>Consistent sizing:</strong> Both buttons now have the same dimensions</li>";
    echo "<li><strong>Responsive layout:</strong> Stacked on mobile, side-by-side on desktop</li>";
    echo "<li><strong>Uniform styling:</strong> Same padding, font size, and spacing</li>";
    echo "<li><strong>Better alignment:</strong> Centered layout with proper gaps</li>";
    echo "<li><strong>Mobile optimization:</strong> Full-width buttons on mobile devices</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get messages with show relations
    $db->query("SELECT COUNT(*) as show_messages 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_archived = 0
                AND show_id IS NOT NULL");
    $db->bind(':user_id', $userId);
    $showResult = $db->single();
    $showMessages = $showResult ? $showResult->show_messages : 0;
    
    // Get total messages
    $db->query("SELECT COUNT(*) as total_messages 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $totalResult = $db->single();
    $totalMessages = $totalResult ? $totalResult->total_messages : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Messages with Show Relations</td>";
    echo "<td style='color: " . ($showMessages > 0 ? 'blue' : 'orange') . ";'>{$showMessages}</td>";
    echo "<td>" . ($showMessages > 0 ? '✅ Perfect - will show both buttons' : '⚠️ Will only show Archive button') . "</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Total Messages for User {$userId}</td>";
    echo "<td style='color: " . ($totalMessages > 0 ? 'green' : 'red') . ";'>{$totalMessages}</td>";
    echo "<td>" . ($totalMessages > 0 ? '✅ Good for testing' : '❌ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($totalMessages === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the button sizing...</p>";
        
        $subject = "🔘 Button Sizing Test";
        $message = "This is a test message to demonstrate the consistent action button sizing at the bottom of the message view.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $totalMessages = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Button Sizing Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Testing Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile device</strong></li>";
    echo "<li><strong>Navigate to Messages</strong> and tap on a message to view it</li>";
    echo "<li><strong>Scroll to bottom:</strong> Look for action buttons section</li>";
    echo "<li><strong>Check mobile layout:</strong> Buttons should be stacked, same width</li>";
    echo "<li><strong>Test on desktop:</strong> Buttons should be side-by-side, same size</li>";
    echo "<li><strong>Compare button sizes:</strong> Should be identical dimensions</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Device</th><th>Before</th><th>After</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Mobile</strong></td>";
    echo "<td style='color: red;'>❌ Different sizes, inconsistent spacing</td>";
    echo "<td style='color: green;'>✅ Same width (100%), stacked vertically</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Desktop</strong></td>";
    echo "<td style='color: red;'>❌ Different widths, uneven appearance</td>";
    echo "<td style='color: green;'>✅ Same width (180px min), side-by-side</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Tablet</strong></td>";
    echo "<td style='color: red;'>❌ Inconsistent sizing</td>";
    echo "<td style='color: green;'>✅ Responsive sizing, consistent appearance</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Implementation</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔘 Button Layout HTML:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Responsive button container -->
<div class=\"d-flex flex-column flex-md-row justify-content-center align-items-center gap-2\">
    <!-- View Related Show button -->
    <a href=\"/show/view/123\" class=\"btn btn-primary action-button\">
        <i class=\"fas fa-car me-1\"></i>View Related Show
    </a>
    
    <!-- Archive Conversation button -->
    <button type=\"button\" class=\"btn btn-outline-secondary action-button\">
        <i class=\"fas fa-archive me-1\"></i>Archive Conversation
    </button>
</div>";
    echo "</pre>";
    
    echo "<h3>🎨 Button Sizing CSS:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "/* Consistent sizing on all devices */
.action-button {
    min-width: 180px;
    padding: 0.75rem 1.5rem;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

/* Mobile: Full width, stacked */
@media (max-width: 768px) {
    .action-button {
        width: 100%;
        max-width: 250px;
        margin-bottom: 0.5rem;
    }
}

/* Desktop: Side-by-side, same width */
@media (min-width: 769px) {
    .action-button {
        width: auto;
        margin: 0 0.5rem;
    }
}";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>📏 Button Specifications</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📐 Consistent Dimensions:</h3>";
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Property</th><th>Mobile</th><th>Desktop</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Width</strong></td>";
    echo "<td>100% (max 250px)</td>";
    echo "<td>180px minimum</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Padding</strong></td>";
    echo "<td>0.75rem 1.5rem</td>";
    echo "<td>0.75rem 1.5rem</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Font Size</strong></td>";
    echo "<td>14px</td>";
    echo "<td>14px</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Layout</strong></td>";
    echo "<td>Stacked vertically</td>";
    echo "<td>Side-by-side</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Spacing</strong></td>";
    echo "<td>0.5rem bottom margin</td>";
    echo "<td>0.5rem side margin</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    echo "<h2>🎯 Button Scenarios</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Different Button Combinations:</h3>";
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Scenario</th><th>Buttons Shown</th><th>Layout</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Message with Show</strong></td>";
    echo "<td>View Related Show + Archive Conversation</td>";
    echo "<td>Both buttons, same size</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message without Show</strong></td>";
    echo "<td>Archive Conversation only</td>";
    echo "<td>Single button, centered</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Mobile View</strong></td>";
    echo "<td>Any combination</td>";
    echo "<td>Stacked vertically, full width</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Desktop View</strong></td>";
    echo "<td>Any combination</td>";
    echo "<td>Side-by-side, consistent width</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    if ($totalMessages > 0) {
        echo "<h2>✅ Ready for Button Sizing Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$totalMessages} message(s) to test the button sizing.</p>";
        echo "<p><strong>What you should see:</strong></p>";
        echo "<ul>";
        echo "<li>🔘 <strong>Consistent sizing:</strong> Both buttons exactly the same dimensions</li>";
        echo "<li>📱 <strong>Mobile layout:</strong> Stacked vertically, full width (max 250px)</li>";
        echo "<li>💻 <strong>Desktop layout:</strong> Side-by-side, minimum 180px width</li>";
        echo "<li>📏 <strong>Uniform styling:</strong> Same padding, font size, and spacing</li>";
        echo "<li>🎯 <strong>Professional appearance:</strong> Clean, balanced button layout</li>";
        echo "</ul>";
        echo "<p><strong>The action buttons should now look perfectly balanced on all devices!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated file:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/notification_center/view.php</code> - Fixed action button sizing</li>";
    echo "</ul>";
    echo "<p><strong>Changes made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Added responsive flexbox layout for buttons</li>";
    echo "<li>✅ Applied consistent .action-button class</li>";
    echo "<li>✅ Added CSS for uniform sizing on all devices</li>";
    echo "<li>✅ Mobile: Full-width stacked layout</li>";
    echo "<li>✅ Desktop: Side-by-side with consistent width</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Action button sizing test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
