<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-users-cog"></i> Manage Show Roles</h2>
                    <p class="text-muted mb-0">
                        <strong><?= htmlspecialchars($data['show']->name) ?></strong> - 
                        <?= date('M j, Y', strtotime($data['show']->start_date)) ?>
                        <?php if ($data['show']->start_date !== $data['show']->end_date): ?>
                            to <?= date('M j, Y', strtotime($data['show']->end_date)) ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <a href="<?= URLROOT ?>/coordinator/editShow/<?= $data['show']->id ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Show
                    </a>
                </div>
            </div>

            <?php flash('success'); ?>
            <?php flash('error'); ?>

            <!-- Role Assignment Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-plus-circle"></i> Assign New Role</h5>
                </div>
                <div class="card-body">
                    <form action="<?= URLROOT ?>/show_roles/assign" method="POST">
                        <input type="hidden" name="show_id" value="<?= $data['show']->id ?>">
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="user_search">
                                        <?php if ($data['is_admin']): ?>
                                            Search User <small class="text-muted">(name, email, or ID)</small>
                                        <?php else: ?>
                                            User ID <small class="text-muted">(user must provide their ID)</small>
                                        <?php endif; ?>
                                    </label>
                                    
                                    <?php if ($data['is_admin']): ?>
                                        <select id="user_search" name="user_id" class="form-control" required>
                                            <option value="">Search for user...</option>
                                        </select>
                                    <?php else: ?>
                                        <input type="number" id="user_id" name="user_id" class="form-control" 
                                               placeholder="Enter user ID number" required min="1">
                                        <small class="form-text text-muted">
                                            Ask the user to provide their User ID from their profile page.
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="role">Role</label>
                                    <select id="role" name="role" class="form-control" required>
                                        <option value="">Select role...</option>
                                        <option value="coordinator">Coordinator</option>
                                        <option value="judge">Judge</option>
                                        <option value="staff">Staff</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="message">Message (Optional)</label>
                                    <input type="text" id="message" name="message" class="form-control" 
                                           placeholder="Optional message to user">
                                </div>
                            </div>
                            
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus"></i> 
                                        <?= $data['is_admin'] ? 'Assign' : 'Request' ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (!$data['is_admin']): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Note:</strong> As a coordinator, role assignments require user approval. 
                                The user will receive a notification and must accept the assignment.
                            </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <!-- Current Assignments -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> Current Role Assignments</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['assignments'])): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <p>No role assignments for this show yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Role</th>
                                        <th>Assigned By</th>
                                        <th>Assigned Date</th>
                                        <th>Expires</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['assignments'] as $assignment): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($assignment->user_name) ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($assignment->user_email) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= getRoleBadgeClass($assignment->assigned_role) ?>">
                                                    <?= ucfirst($assignment->assigned_role) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($assignment->assigned_by_name) ?></td>
                                            <td><?= date('M j, Y g:i A', strtotime($assignment->assigned_at)) ?></td>
                                            <td>
                                                <?php if (strtotime($assignment->expires_at) < time()): ?>
                                                    <span class="text-danger">Expired</span>
                                                <?php else: ?>
                                                    <?= date('M j, Y', strtotime($assignment->expires_at)) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <form action="<?= URLROOT ?>/show_roles/remove" method="POST" 
                                                      style="display: inline;" 
                                                      onsubmit="return confirm('Are you sure you want to remove this role assignment?')">
                                                    <input type="hidden" name="assignment_id" value="<?= $assignment->id ?>">
                                                    <input type="hidden" name="show_id" value="<?= $data['show']->id ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-times"></i> Remove
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pending Requests (Coordinators only) -->
            <?php if (!$data['is_admin'] && !empty($data['pending_requests'])): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> Pending Approval Requests</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Role</th>
                                        <th>Requested</th>
                                        <th>Expires</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['pending_requests'] as $request): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($request->user_name) ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($request->user_email) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= getRoleBadgeClass($request->requested_role) ?>">
                                                    <?= ucfirst($request->requested_role) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M j, Y g:i A', strtotime($request->requested_at)) ?></td>
                                            <td><?= date('M j, Y g:i A', strtotime($request->expires_at)) ?></td>
                                            <td>
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-clock"></i> Pending User Response
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
function getRoleBadgeClass($role) {
    switch ($role) {
        case 'coordinator': return 'primary';
        case 'judge': return 'success';
        case 'staff': return 'info';
        default: return 'secondary';
    }
}
?>

<?php if ($data['is_admin']): ?>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<script>
$(document).ready(function() {
    $('#user_search').select2({
        placeholder: 'Search for user...',
        minimumInputLength: 2,
        ajax: {
            url: '<?= URLROOT ?>/show_roles/searchUsers',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term
                };
            },
            processResults: function (data) {
                return {
                    results: data.users
                };
            },
            cache: true
        }
    });
});
</script>
<?php endif; ?>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
