<?php
/**
 * Migration Script: Web Push API to FCM HTTP v1 API
 * 
 * This script helps migrate from the old Web Push API with VAPID
 * to Firebase Cloud Messaging (FCM) HTTP v1 API.
 * 
 * Usage: https://events.rowaneliterides.com/migrate_to_fcm.php?migrate_key=fcm_migration_2025
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Security check
if (!isset($_GET['migrate_key']) || $_GET['migrate_key'] !== 'fcm_migration_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';

echo "=== FCM MIGRATION SCRIPT ===\n\n";

try {
    $db = new Database();
    
    echo "1. CHECKING CURRENT SYSTEM STATUS\n";
    
    // Check if old push_subscriptions table exists
    $db->query("SHOW TABLES LIKE 'push_subscriptions'");
    $oldTableExists = $db->single();
    
    if ($oldTableExists) {
        $db->query("SELECT COUNT(*) as count FROM push_subscriptions WHERE active = 1");
        $db->execute();
        $oldSubscriptions = $db->single();
        echo "   Old Web Push subscriptions: " . $oldSubscriptions->count . "\n";
    } else {
        echo "   Old Web Push subscriptions: 0 (table not found)\n";
    }
    
    // Check if new fcm_tokens table exists
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    $newTableExists = $db->single();
    
    if ($newTableExists) {
        $db->query("SELECT COUNT(*) as count FROM fcm_tokens WHERE active = 1");
        $db->execute();
        $newTokens = $db->single();
        echo "   FCM tokens: " . $newTokens->count . "\n";
    } else {
        echo "   FCM tokens: 0 (table not found)\n";
    }
    
    echo "\n2. CREATING FCM TOKENS TABLE\n";
    
    if (!$newTableExists) {
        $sql = "CREATE TABLE fcm_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            token TEXT NOT NULL,
            user_agent TEXT,
            active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_active (active),
            INDEX idx_user_active (user_id, active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($sql);
        if ($db->execute()) {
            echo "   ✅ FCM tokens table created successfully\n";
        } else {
            throw new Exception("Failed to create fcm_tokens table");
        }
    } else {
        echo "   ✅ FCM tokens table already exists\n";
    }
    
    echo "\n3. CHECKING FIREBASE CONFIGURATION\n";
    
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    if (file_exists($serviceAccountPath)) {
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
        if ($serviceAccount && isset($serviceAccount['project_id'])) {
            echo "   ✅ Firebase service account file found\n";
            echo "   Project ID: " . $serviceAccount['project_id'] . "\n";
            echo "   Client Email: " . $serviceAccount['client_email'] . "\n";
        } else {
            echo "   ❌ Firebase service account file is invalid\n";
        }
    } else {
        echo "   ❌ Firebase service account file not found\n";
        echo "   Please create: /config/firebase-service-account.json\n";
    }
    
    echo "\n4. CHECKING FCM HELPER\n";
    
    $fcmHelperPath = APPROOT . '/helpers/fcm_v1_helper.php';
    if (file_exists($fcmHelperPath)) {
        echo "   ✅ FCM helper found\n";
        
        // Test FCM helper initialization
        try {
            require_once $fcmHelperPath;
            if (file_exists($serviceAccountPath)) {
                $fcm = new FCMv1Helper($serviceAccountPath);
                echo "   ✅ FCM helper can be initialized\n";
            }
        } catch (Exception $e) {
            echo "   ❌ FCM helper initialization failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ FCM helper not found\n";
    }
    
    echo "\n5. CHECKING FRONTEND INTEGRATION\n";
    
    $fcmJsPath = APPROOT . '/public/js/fcm-notifications.js';
    if (file_exists($fcmJsPath)) {
        echo "   ✅ FCM JavaScript file found\n";
    } else {
        echo "   ❌ FCM JavaScript file not found\n";
    }
    
    $firebaseSwPath = APPROOT . '/firebase-messaging-sw.js';
    if (file_exists($firebaseSwPath)) {
        echo "   ✅ Firebase service worker found\n";
    } else {
        echo "   ❌ Firebase service worker not found\n";
    }
    
    echo "\n6. MIGRATION RECOMMENDATIONS\n";
    
    if (!file_exists($serviceAccountPath)) {
        echo "   🔧 REQUIRED: Set up Firebase service account\n";
        echo "      1. Go to Firebase Console (https://console.firebase.google.com/)\n";
        echo "      2. Select your project: rowaneliterides\n";
        echo "      3. Go to Project Settings > Service Accounts\n";
        echo "      4. Click 'Generate new private key'\n";
        echo "      5. Save the JSON file as: /config/firebase-service-account.json\n\n";
    }
    
    if (!file_exists($fcmJsPath) || !file_exists($firebaseSwPath)) {
        echo "   🔧 REQUIRED: Frontend files missing\n";
        echo "      Ensure these files exist:\n";
        echo "      - /public/js/fcm-notifications.js\n";
        echo "      - /firebase-messaging-sw.js\n\n";
    }
    
    // Check if Firebase configuration is updated
    $fcmJsContent = file_get_contents($fcmJsPath);
    $firebaseSwContent = file_get_contents($firebaseSwPath);
    
    $hasRealConfig = (
        strpos($fcmJsContent, 'AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg') !== false &&
        strpos($firebaseSwContent, 'AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg') !== false
    );
    
    if ($hasRealConfig) {
        echo "   ✅ Firebase web configuration updated\n";
        echo "      Both frontend files have real Firebase config\n\n";
    } else {
        echo "   🔧 REQUIRED: Update Firebase configuration\n";
        echo "      Update these files with your Firebase config:\n";
        echo "      - /public/js/fcm-notifications.js (firebaseConfig object)\n";
        echo "      - /firebase-messaging-sw.js (firebaseConfig object)\n";
        echo "      Get config from: Firebase Console > Project Settings > General\n\n";
    }
    
    if ($oldTableExists && $oldSubscriptions->count > 0) {
        echo "   📋 OPTIONAL: Clean up old Web Push subscriptions\n";
        echo "      After FCM is working, you can:\n";
        echo "      1. Deactivate old subscriptions: UPDATE push_subscriptions SET active = 0\n";
        echo "      2. Eventually drop the table: DROP TABLE push_subscriptions\n\n";
    }
    
    echo "\n7. TESTING RECOMMENDATIONS\n";
    
    echo "   After completing the setup:\n";
    echo "   1. Test FCM token generation in browser console\n";
    echo "   2. Send a test notification using the admin panel\n";
    echo "   3. Verify notifications work in both foreground and background\n";
    echo "   4. Test on different browsers (Chrome, Firefox, Edge)\n";
    echo "   5. Test on mobile devices\n\n";
    
    echo "   Test URLs:\n";
    echo "   - Deep FCM diagnostic: /deep_fcm_diagnostic.php?diag_key=deep_fcm_2025\n";
    echo "   - Test notification: /test_fcm_v1.php?test_key=fcm_test_2025\n\n";
    
    echo "\n8. NEXT STEPS\n";
    
    echo "   1. Complete Firebase configuration (service account + web config)\n";
    echo "   2. Test FCM token generation in browser\n";
    echo "   3. Send test notifications\n";
    echo "   4. Update notification preferences UI if needed\n";
    echo "   5. Monitor FCM token storage and cleanup\n\n";
    
    echo "=== MIGRATION ANALYSIS COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>