[20-Jul-2025 16:08:46 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:46 UTC] Raw URL: notification_center/getUnreadCount
[20-Jul-2025 16:08:46 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:46 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:46 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:46 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:46 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:46 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:46 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:46 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[20-Jul-2025 16:08:46 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[20-Jul-2025 16:08:46 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[20-Jul-2025 16:08:46 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[20-Jul-2025 16:08:46 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:08:46 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:46 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:46 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:08:46 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:08:46 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:08:46 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:08:46 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:46 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:46 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:46 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:46 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:46 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:46 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:08:46 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:08:46 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:46 UTC] Raw URL: notification_center/getUnreadCount
[20-Jul-2025 16:08:46 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:46 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:46 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:46 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:46 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:46 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:46 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[20-Jul-2025 16:08:46 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[20-Jul-2025 16:08:46 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[20-Jul-2025 16:08:46 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[20-Jul-2025 16:08:47 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:47 UTC] Raw URL: notification_center/getUnreadCount
[20-Jul-2025 16:08:47 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:47 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:47 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:47 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:47 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:47 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:47 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[20-Jul-2025 16:08:47 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[20-Jul-2025 16:08:47 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[20-Jul-2025 16:08:47 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[20-Jul-2025 16:08:48 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:48 UTC] Raw URL: notification_center/getUnreadCount
[20-Jul-2025 16:08:48 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:48 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:48 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:48 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:48 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:48 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:48 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[20-Jul-2025 16:08:48 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[20-Jul-2025 16:08:48 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[20-Jul-2025 16:08:48 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Raw URL: calendar
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => calendar
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => calendar
)

[20-Jul-2025 16:08:52 UTC] CalendarController::getCurrentMethod - URL: calendar, Method: index
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] Controller: CalendarController, Method: index, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: index on controller: CalendarController
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [179] SELECT c.*, u.name as owner_name 
                    FROM calendars c 
                    LEFT JOIN users u ON c.owner_id = u.id WHERE c.owner_id = :user_id OR c.is_public = 1
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 3
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"id":"1","name":"Public Car Show Calendar NC","description":"Public Car Show Calendar NC","color":"#ff0000","is_visible":"1","is_public":"1","owner_id":"3","created_at":"2025-06-16 18:33:22","updated_at":"2025-06-16 18:33:22","owner_name":"Brian Correll"}
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [561] SELECT setting_key, setting_value FROM calendar_settings 
                             WHERE setting_key IN ('calendar_default_view', 'calendar_start_day', 
                                                 'calendar_time_format', 'calendar_date_format', 
                                                 'calendar_events_per_page', 'event_show_weekends',
                                                 'event_enable_drag_drop', 'event_show_today_line',
                                                 'event_show_event_hover', 'event_mobile_breakpoint')
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 10
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"setting_key":"calendar_default_view","setting_value":"month"}
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [31] SELECT * FROM calendar_settings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 49
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"id":"1","setting_key":"default_view","setting_value":"month","setting_group":"general","description":null,"created_at":"2025-06-16 18:06:32","updated_at":"2025-06-16 18:06:32"}
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [475] SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE m.to_user_id = ? AND m.is_archived = 0 ORDER BY m.created_at DESC LIMIT ? OFFSET ?
Params:  3
Key: Position #0:
paramno=0
name=[0] ""
is_param=1
param_type=2
Key: Position #1:
paramno=1
name=[0] ""
is_param=1
param_type=1
Key: Position #2:
paramno=2
name=[0] ""
is_param=1
param_type=1

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 5
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"id":"504","from_user_id":"3","to_user_id":"3","subject":"Re: test message from registration","message":"testing reply from admin","show_id":"9","parent_message_id":"503","message_type":"direct","priority":"normal","is_read":"1","is_archived":"0","requires_reply":"0","allows_reply":"1","reply_used":"0","created_at":"2025-07-19 01:04:47","read_at":"2025-07-19 01:04:48","replied_at":null,"ticket_number":null,"email_message_id":null,"original_sender_email":null,"folder_id":null,"owned_by_admin_id":null,"security_token":null,"from_user_name":"Brian Correll","from_user_email":"<EMAIL>","show_title":"Summer Classic Auto Show 2025","show_location":"Rowan Sheriff Dept"}
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Raw URL: api/getSiteLogo
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => getSiteLogo
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => api
    [1] => getSiteLogo
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => getSiteLogo
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] Endpoint: getSiteLogo, Action: index
[20-Jul-2025 16:08:52 UTC] [API] Calling ApiController::getSiteLogo
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Raw URL: notification_center/getUnreadCount
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[20-Jul-2025 16:08:52 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[20-Jul-2025 16:08:52 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Raw URL: calendar/getClubs
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => calendar
    [1] => getClubs
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => calendar
    [1] => getClubs
)

[20-Jul-2025 16:08:52 UTC] CalendarController::getCurrentMethod - URL: calendar/getClubs, Method: getClubs
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] App.php: Looking for method: getClubs in controller: CalendarController
[20-Jul-2025 16:08:52 UTC] App.php: Using camelCase method: getClubs for URL: getClubs
[20-Jul-2025 16:08:52 UTC] Controller: CalendarController, Method: getClubs, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: getClubs on controller: CalendarController
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [249] SELECT c.id, c.name, COUNT(ec.event_id) as event_count
                    FROM calendar_clubs c
                    LEFT JOIN calendar_event_clubs ec ON c.id = ec.club_id
                    GROUP BY c.id
                    ORDER BY c.name ASC
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 1
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"id":"1","name":"Rowan Elite Rides","event_count":"1"}
[20-Jul-2025 16:08:52 UTC] Raw URL: calendar/getStates
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => calendar
    [1] => getStates
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => calendar
    [1] => getStates
)

[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] CalendarController::getCurrentMethod - URL: calendar/getStates, Method: getStates
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] App.php: Looking for method: getStates in controller: CalendarController
[20-Jul-2025 16:08:52 UTC] App.php: Using camelCase method: getStates for URL: getStates
[20-Jul-2025 16:08:52 UTC] Controller: CalendarController, Method: getStates, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: getStates on controller: CalendarController
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [216] SELECT state, COUNT(*) as event_count
                    FROM calendar_events
                    WHERE state IS NOT NULL AND state != ''
                    GROUP BY state
                    ORDER BY state ASC
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 2
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"state":"NC","event_count":"13"}
[20-Jul-2025 16:08:52 UTC] Raw URL: calendar/getTags
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => calendar
    [1] => getTags
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => calendar
    [1] => getTags
)

[20-Jul-2025 16:08:52 UTC] CalendarController::getCurrentMethod - URL: calendar/getTags, Method: getTags
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] App.php: Looking for method: getTags in controller: CalendarController
[20-Jul-2025 16:08:52 UTC] App.php: Using camelCase method: getTags for URL: getTags
[20-Jul-2025 16:08:52 UTC] Controller: CalendarController, Method: getTags, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: getTags on controller: CalendarController
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [266] 
                SELECT t.id, t.name, COUNT(DISTINCT et.event_id) as event_count
                FROM event_tags t
                LEFT JOIN event_tag_mapping et ON t.id = et.tag_id
                GROUP BY t.id
                ORDER BY t.name ASC
            
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:52 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:52 UTC] Raw URL: calendar/getCategories
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => calendar
    [1] => getCategories
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => calendar
    [1] => getCategories
)

[20-Jul-2025 16:08:52 UTC] CalendarController::getCurrentMethod - URL: calendar/getCategories, Method: getCategories
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] App.php: Looking for method: getCategories in controller: CalendarController
[20-Jul-2025 16:08:52 UTC] App.php: Using camelCase method: getCategories for URL: getCategories
[20-Jul-2025 16:08:52 UTC] Controller: CalendarController, Method: getCategories, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: getCategories on controller: CalendarController
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [282] 
                SELECT c.id, c.name, COUNT(DISTINCT ec.event_id) as event_count
                FROM event_categories c
                LEFT JOIN event_category_mapping ec ON c.id = ec.category_id
                GROUP BY c.id
                ORDER BY c.name ASC
            
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:52 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:52 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:52 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:08:52 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:08:52 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:52 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:52 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:52 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:08:52 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:08:52 UTC] Raw URL: api/pwa/usage
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => usage
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] Endpoint: pwa, Action: usage
[20-Jul-2025 16:08:52 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[20-Jul-2025 16:08:52 UTC] [PWA] Usage data received for user 3: {"isInstalled":false,"isStandalone":false,"supportsPush":true,"timestamp":"2025-07-20T16:08:51.761Z"}
[20-Jul-2025 16:08:52 UTC] Raw URL: api/cameraBanners
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] API endpoint called
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 4
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Raw URL: calendar/getEvents
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => calendar
    [1] => getEvents
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => calendar
    [1] => getEvents
)

[20-Jul-2025 16:08:52 UTC] CalendarController::getCurrentMethod - URL: calendar/getEvents, Method: getEvents
[20-Jul-2025 16:08:52 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:08:52 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:08:52 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:08:52 UTC] App.php: Looking for method: getEvents in controller: CalendarController
[20-Jul-2025 16:08:52 UTC] App.php: Using camelCase method: getEvents for URL: getEvents
[20-Jul-2025 16:08:52 UTC] Controller: CalendarController, Method: getEvents, Params: Array
(
)

[20-Jul-2025 16:08:52 UTC] App.php: About to call method: getEvents on controller: CalendarController
[20-Jul-2025 16:08:52 UTC] === CalendarController::getEvents called at 2025-07-20 16:08:52 ===
[20-Jul-2025 16:08:52 UTC] Raw GET parameters: {"url":"calendar\/getEvents","calendar_id":"1,2,4","start":"2025-07-01","end":"2025-08-01 23:59:59","_t":"1753027731858"}
[20-Jul-2025 16:08:52 UTC] User logged in: Yes (ID: 3)
[20-Jul-2025 16:08:52 UTC] CalendarController::getEvents - Raw parameters received:
[20-Jul-2025 16:08:52 UTC]   calendar_id: 1,2,4
[20-Jul-2025 16:08:52 UTC]   keyword: null
[20-Jul-2025 16:08:52 UTC]   state: null
[20-Jul-2025 16:08:52 UTC]   city: null
[20-Jul-2025 16:08:52 UTC] CalendarController::getEvents - Applying filters: {"start_date":"2025-07-01","end_date":"2025-08-01 23:59:59","calendar_ids":["1","2","4"]}
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - End date filter applied: 2025-08-01 23:59:59
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - Privacy filter: public + draft for user 3
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - SQL Query: SELECT DISTINCT e.*, c.name as calendar_name, c.color as calendar_color, 
                           v.name as venue_name, v.address as venue_address, 
                           v.city as venue_city, v.state as venue_state, 
                           v.latitude as venue_latitude, v.longitude as venue_longitude,
                           s.name as show_name
                    FROM calendar_events e 
                    LEFT JOIN calendars c ON e.calendar_id = c.id 
                    LEFT JOIN calendar_venues v ON e.venue_id = v.id
                    LEFT JOIN shows s ON e.show_id = s.id WHERE 1=1 AND FIND_IN_SET(e.calendar_id, :calendar_ids) AND e.end_date >= :start_date AND e.start_date <= :end_date AND (e.privacy = 'public' OR (e.privacy = 'draft' AND e.created_by = :user_id)) ORDER BY e.start_date ASC
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - Params: {":calendar_ids":"1,2,4",":start_date":"2025-07-01",":end_date":"2025-08-01 23:59:59",":user_id":"3"}
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - Filters received: {"start_date":"2025-07-01","end_date":"2025-08-01 23:59:59","calendar_ids":["1","2","4"]}
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - Parameter count: 4
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [829] SELECT DISTINCT e.*, c.name as calendar_name, c.color as calendar_color, 
                           v.name as venue_name, v.address as venue_address, 
                           v.city as venue_city, v.state as venue_state, 
                           v.latitude as venue_latitude, v.longitude as venue_longitude,
                           s.name as show_name
                    FROM calendar_events e 
                    LEFT JOIN calendars c ON e.calendar_id = c.id 
                    LEFT JOIN calendar_venues v ON e.venue_id = v.id
                    LEFT JOIN shows s ON e.show_id = s.id WHERE 1=1 AND FIND_IN_SET(e.calendar_id, :calendar_ids) AND e.end_date >= :start_date AND e.start_date <= :end_date AND (e.privacy = 'public' OR (e.privacy = 'draft' AND e.created_by = :user_id)) ORDER BY e.start_date ASC
Params:  4
Key: Name: [13] :calendar_ids
paramno=0
name=[13] ":calendar_ids"
is_param=1
param_type=2
Key: Name: [11] :start_date
paramno=1
name=[11] ":start_date"
is_param=1
param_type=2
Key: Name: [9] :end_date
paramno=2
name=[9] ":end_date"
is_param=1
param_type=2
Key: Name: [8] :user_id
paramno=3
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 5
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"id":"21","calendar_id":"4","title":"Las Vegas Strip Supercar Spectacular","description":"The most glamorous supercar event in Las Vegas! Featuring the world's most exclusive and expensive vehicles. Red carpet arrivals, celebrity appearances, and VIP experiences. Night cruise down the famous Las Vegas Strip.","start_date":"2025-07-01 23:00:00","end_date":"2025-07-02 00:00:00","all_day":"0","location":"Las Vegas Convention Center, Las Vegas, NV","address1":"Las Vegas Convention Center, Las Vegas, NV","address2":"","city":"Las Vegas Convention Center, Las Vegas","state":"NV","zipcode":"","lat":"36.12860870","lng":"-115.15154260","venue_id":null,"url":"","color":"#3788d8","is_recurring":"0","recurrence_pattern":"daily","recurrence_end_date":null,"privacy":"public","show_id":null,"created_by":null,"created_at":"2025-07-09 21:17:59","updated_at":"2025-07-20 14:38:17","calendar_name":"Shows - AK","calendar_color":"#3788d8","venue_name":null,"venue_address":null,"venue_city":null,"venue_state":null,"venue_latitude":null,"venue_longitude":null,"show_name":null}
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - Found 5 events
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - First event title: Las Vegas Strip Supercar Spectacular
[20-Jul-2025 16:08:52 UTC] CalendarModel::getEvents - First event description: The most glamorous supercar event in Las Vegas! Featuring the world's most exclusive and expensive vehicles. Red carpet arrivals, celebrity appearances, and VIP experiences. Night cruise down the famous Las Vegas Strip.
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Event ID: 21
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User ID: 3
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User Timezone: America/Los_Angeles
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Original UTC: 2025-07-01 23:00:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Converted Time: 2025-07-01T16:00:00-07:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Event ID: 5
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User ID: 3
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User Timezone: America/Los_Angeles
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Original UTC: 2025-07-07 14:00:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Converted Time: 2025-07-07T07:00:00-07:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Event ID: 20
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User ID: 3
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User Timezone: America/Los_Angeles
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Original UTC: 2025-07-09 08:00:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Converted Time: 2025-07-09T01:00:00-07:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Event ID: 9
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User ID: 3
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User Timezone: America/Los_Angeles
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Original UTC: 2025-07-21 08:50:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Converted Time: 2025-07-21T01:50:00-07:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Event ID: 18
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User ID: 3
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - User Timezone: America/Los_Angeles
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Original UTC: 2025-07-30 18:49:00
[20-Jul-2025 16:08:52 UTC] CALENDAR DEBUG - Converted Time: 2025-07-30T11:49:00-07:00
[20-Jul-2025 16:08:52 UTC] Raw URL: api/cameraBanners
[20-Jul-2025 16:08:52 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:08:52 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[20-Jul-2025 16:08:52 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] API endpoint called
[20-Jul-2025 16:08:52 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[20-Jul-2025 16:08:52 UTC] Database::resultSet - Result count: 4
[20-Jul-2025 16:08:52 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[20-Jul-2025 16:08:52 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[20-Jul-2025 16:08:53 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:53 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:08:53 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:53 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:53 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:08:53 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:08:53 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:08:53 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:08:53 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:53 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:53 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:53 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:53 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:53 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:53 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:08:53 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:08:54 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:54 UTC] Raw URL: api/pwa/vapid-key
[20-Jul-2025 16:08:54 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => vapid-key
)

[20-Jul-2025 16:08:54 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => vapid-key
)

[20-Jul-2025 16:08:54 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => vapid-key
)

[20-Jul-2025 16:08:54 UTC] [API_ROUTING] Endpoint: pwa, Action: vapid-key
[20-Jul-2025 16:08:54 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[20-Jul-2025 16:08:54 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:54 UTC] Raw URL: api/pwa/fcm-verify-token
[20-Jul-2025 16:08:54 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => fcm-verify-token
)

[20-Jul-2025 16:08:54 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => fcm-verify-token
)

[20-Jul-2025 16:08:54 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => fcm-verify-token
)

[20-Jul-2025 16:08:54 UTC] [API_ROUTING] Endpoint: pwa, Action: fcm-verify-token
[20-Jul-2025 16:08:54 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[20-Jul-2025 16:08:57 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:08:57 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:08:57 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:57 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:08:57 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:08:57 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:08:57 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:08:57 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:08:57 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:57 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:57 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:57 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:08:57 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:08:57 UTC] Database::resultSet - No results found
[20-Jul-2025 16:08:57 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:08:57 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:01 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:09:01 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:09:01 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:09:01 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:01 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:01 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:01 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:09:01 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Raw URL: calendar/event/21
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => calendar
    [1] => event
    [2] => 21
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => calendar
    [1] => event
    [2] => 21
)

[20-Jul-2025 16:09:01 UTC] CalendarController::getCurrentMethod - URL: calendar/event/21, Method: event
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:09:01 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:09:01 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:09:01 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:09:01 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:09:01 UTC] App.php: Looking for method: event in controller: CalendarController
[20-Jul-2025 16:09:01 UTC] App.php: Using camelCase method: event for URL: event
[20-Jul-2025 16:09:01 UTC] Controller: CalendarController, Method: event, Params: Array
(
    [0] => 21
)

[20-Jul-2025 16:09:01 UTC] App.php: About to call method: event on controller: CalendarController
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [252] SELECT c.* 
                             FROM calendar_clubs c 
                             JOIN calendar_event_clubs ec ON c.id = ec.club_id 
                             WHERE ec.event_id = :event_id 
                             ORDER BY c.name
Params:  1
Key: Name: [9] :event_id
paramno=0
name=[9] ":event_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:01 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:01 UTC] EVENT DETAIL DEBUG - Event ID: 21
[20-Jul-2025 16:09:01 UTC] EVENT DETAIL DEBUG - User ID: 3
[20-Jul-2025 16:09:01 UTC] EVENT DETAIL DEBUG - User Timezone: America/Los_Angeles
[20-Jul-2025 16:09:01 UTC] EVENT DETAIL DEBUG - Original UTC: 2025-07-01 23:00:00
[20-Jul-2025 16:09:01 UTC] EVENT DETAIL DEBUG - Converted Time: 2025-07-01T16:00:00-07:00
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [475] SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE m.to_user_id = ? AND m.is_archived = 0 ORDER BY m.created_at DESC LIMIT ? OFFSET ?
Params:  3
Key: Position #0:
paramno=0
name=[0] ""
is_param=1
param_type=2
Key: Position #1:
paramno=1
name=[0] ""
is_param=1
param_type=1
Key: Position #2:
paramno=2
name=[0] ""
is_param=1
param_type=1

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 5
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"id":"504","from_user_id":"3","to_user_id":"3","subject":"Re: test message from registration","message":"testing reply from admin","show_id":"9","parent_message_id":"503","message_type":"direct","priority":"normal","is_read":"1","is_archived":"0","requires_reply":"0","allows_reply":"1","reply_used":"0","created_at":"2025-07-19 01:04:47","read_at":"2025-07-19 01:04:48","replied_at":null,"ticket_number":null,"email_message_id":null,"original_sender_email":null,"folder_id":null,"owned_by_admin_id":null,"security_token":null,"from_user_name":"Brian Correll","from_user_email":"<EMAIL>","show_title":"Summer Classic Auto Show 2025","show_location":"Rowan Sheriff Dept"}
[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Raw URL: api/getSiteLogo
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => getSiteLogo
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => getSiteLogo
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => getSiteLogo
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] Endpoint: getSiteLogo, Action: index
[20-Jul-2025 16:09:01 UTC] [API] Calling ApiController::getSiteLogo
[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Raw URL: notification_center/getUnreadCount
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:09:01 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:09:01 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[20-Jul-2025 16:09:01 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[20-Jul-2025 16:09:01 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[20-Jul-2025 16:09:01 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[20-Jul-2025 16:09:01 UTC] Raw URL: notification/checkSubscription
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => checkSubscription
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => notification
    [1] => checkSubscription
)

[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] App.php: Looking for method: checkSubscription in controller: NotificationController
[20-Jul-2025 16:09:01 UTC] App.php: Using camelCase method: checkSubscription for URL: checkSubscription
[20-Jul-2025 16:09:01 UTC] Controller: NotificationController, Method: checkSubscription, Params: Array
(
)

[20-Jul-2025 16:09:01 UTC] App.php: About to call method: checkSubscription on controller: NotificationController
[20-Jul-2025 16:09:01 UTC] Raw URL: calendar/getEvents
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => calendar
    [1] => getEvents
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => calendar
    [1] => getEvents
)

[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] CalendarController::getCurrentMethod - URL: calendar/getEvents, Method: getEvents
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:09:01 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:09:01 UTC] CalendarModel::ensureCalendarsTableExists - Calendars table already exists
[20-Jul-2025 16:09:01 UTC] CalendarModel::ensureCalendarsTableExists - Calendar_events table already exists
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 38
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[20-Jul-2025 16:09:01 UTC] DynamicFormFieldManager: Loaded 38 mappings from database
[20-Jul-2025 16:09:01 UTC] App.php: Looking for method: getEvents in controller: CalendarController
[20-Jul-2025 16:09:01 UTC] App.php: Using camelCase method: getEvents for URL: getEvents
[20-Jul-2025 16:09:01 UTC] Controller: CalendarController, Method: getEvents, Params: Array
(
)

[20-Jul-2025 16:09:01 UTC] App.php: About to call method: getEvents on controller: CalendarController
[20-Jul-2025 16:09:01 UTC] === CalendarController::getEvents called at 2025-07-20 16:09:01 ===
[20-Jul-2025 16:09:01 UTC] Raw GET parameters: {"url":"calendar\/getEvents","calendar_id":"4","start":"2025-06-01T23:00:00.000Z","end":"2025-08-01T23:00:00.000Z"}
[20-Jul-2025 16:09:01 UTC] User logged in: Yes (ID: 3)
[20-Jul-2025 16:09:01 UTC] CalendarController::getEvents - Raw parameters received:
[20-Jul-2025 16:09:01 UTC]   calendar_id: 4
[20-Jul-2025 16:09:01 UTC]   keyword: null
[20-Jul-2025 16:09:01 UTC]   state: null
[20-Jul-2025 16:09:01 UTC]   city: null
[20-Jul-2025 16:09:01 UTC] CalendarController::getEvents - Applying filters: {"start_date":"2025-06-01T23:00:00.000Z","end_date":"2025-08-01T23:00:00.000Z","calendar_id":"4"}
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - End date filter applied: 2025-08-01T23:00:00.000Z
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - Privacy filter: public + draft for user 3
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - SQL Query: SELECT DISTINCT e.*, c.name as calendar_name, c.color as calendar_color, 
                           v.name as venue_name, v.address as venue_address, 
                           v.city as venue_city, v.state as venue_state, 
                           v.latitude as venue_latitude, v.longitude as venue_longitude,
                           s.name as show_name
                    FROM calendar_events e 
                    LEFT JOIN calendars c ON e.calendar_id = c.id 
                    LEFT JOIN calendar_venues v ON e.venue_id = v.id
                    LEFT JOIN shows s ON e.show_id = s.id WHERE 1=1 AND e.calendar_id = :calendar_id AND e.end_date >= :start_date AND e.start_date <= :end_date AND (e.privacy = 'public' OR (e.privacy = 'draft' AND e.created_by = :user_id)) ORDER BY e.start_date ASC
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - Params: {":calendar_id":"4",":start_date":"2025-06-01T23:00:00.000Z",":end_date":"2025-08-01T23:00:00.000Z",":user_id":"3"}
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - Filters received: {"start_date":"2025-06-01T23:00:00.000Z","end_date":"2025-08-01T23:00:00.000Z","calendar_id":"4"}
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - Parameter count: 4
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [816] SELECT DISTINCT e.*, c.name as calendar_name, c.color as calendar_color, 
                           v.name as venue_name, v.address as venue_address, 
                           v.city as venue_city, v.state as venue_state, 
                           v.latitude as venue_latitude, v.longitude as venue_longitude,
                           s.name as show_name
                    FROM calendar_events e 
                    LEFT JOIN calendars c ON e.calendar_id = c.id 
                    LEFT JOIN calendar_venues v ON e.venue_id = v.id
                    LEFT JOIN shows s ON e.show_id = s.id WHERE 1=1 AND e.calendar_id = :calendar_id AND e.end_date >= :start_date AND e.start_date <= :end_date AND (e.privacy = 'public' OR (e.privacy = 'draft' AND e.created_by = :user_id)) ORDER BY e.start_date ASC
Params:  4
Key: Name: [12] :calendar_id
paramno=0
name=[12] ":calendar_id"
is_param=1
param_type=2
Key: Name: [11] :start_date
paramno=1
name=[11] ":start_date"
is_param=1
param_type=2
Key: Name: [9] :end_date
paramno=2
name=[9] ":end_date"
is_param=1
param_type=2
Key: Name: [8] :user_id
paramno=3
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 1
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"id":"21","calendar_id":"4","title":"Las Vegas Strip Supercar Spectacular","description":"The most glamorous supercar event in Las Vegas! Featuring the world's most exclusive and expensive vehicles. Red carpet arrivals, celebrity appearances, and VIP experiences. Night cruise down the famous Las Vegas Strip.","start_date":"2025-07-01 23:00:00","end_date":"2025-07-02 00:00:00","all_day":"0","location":"Las Vegas Convention Center, Las Vegas, NV","address1":"Las Vegas Convention Center, Las Vegas, NV","address2":"","city":"Las Vegas Convention Center, Las Vegas","state":"NV","zipcode":"","lat":"36.12860870","lng":"-115.15154260","venue_id":null,"url":"","color":"#3788d8","is_recurring":"0","recurrence_pattern":"daily","recurrence_end_date":null,"privacy":"public","show_id":null,"created_by":null,"created_at":"2025-07-09 21:17:59","updated_at":"2025-07-20 14:38:17","calendar_name":"Shows - AK","calendar_color":"#3788d8","venue_name":null,"venue_address":null,"venue_city":null,"venue_state":null,"venue_latitude":null,"venue_longitude":null,"show_name":null}
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - Found 1 events
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - First event title: Las Vegas Strip Supercar Spectacular
[20-Jul-2025 16:09:01 UTC] CalendarModel::getEvents - First event description: The most glamorous supercar event in Las Vegas! Featuring the world's most exclusive and expensive vehicles. Red carpet arrivals, celebrity appearances, and VIP experiences. Night cruise down the famous Las Vegas Strip.
[20-Jul-2025 16:09:01 UTC] CALENDAR DEBUG - Event ID: 21
[20-Jul-2025 16:09:01 UTC] CALENDAR DEBUG - User ID: 3
[20-Jul-2025 16:09:01 UTC] CALENDAR DEBUG - User Timezone: America/Los_Angeles
[20-Jul-2025 16:09:01 UTC] CALENDAR DEBUG - Original UTC: 2025-07-01 23:00:00
[20-Jul-2025 16:09:01 UTC] CALENDAR DEBUG - Converted Time: 2025-07-01T16:00:00-07:00
[20-Jul-2025 16:09:01 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:01 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:09:01 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:09:01 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:09:01 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:01 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:01 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:01 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:09:01 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:09:01 UTC] Raw URL: api/pwa/usage
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => usage
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] Endpoint: pwa, Action: usage
[20-Jul-2025 16:09:01 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[20-Jul-2025 16:09:01 UTC] [PWA] Usage data received for user 3: {"isInstalled":false,"isStandalone":false,"supportsPush":true,"timestamp":"2025-07-20T16:09:01.215Z"}
[20-Jul-2025 16:09:01 UTC] Raw URL: api/cameraBanners
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] API endpoint called
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 4
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[20-Jul-2025 16:09:01 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:01 UTC] Raw URL: api/cameraBanners
[20-Jul-2025 16:09:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:09:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[20-Jul-2025 16:09:01 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] API endpoint called
[20-Jul-2025 16:09:01 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[20-Jul-2025 16:09:01 UTC] Database::resultSet - Result count: 4
[20-Jul-2025 16:09:01 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[20-Jul-2025 16:09:01 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[20-Jul-2025 16:09:02 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:02 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:09:02 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:02 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:02 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:09:02 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:09:02 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:09:02 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:09:02 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:02 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:02 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:02 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:02 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:02 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:02 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:09:02 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:09:03 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:03 UTC] Raw URL: api/pwa/vapid-key
[20-Jul-2025 16:09:03 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => vapid-key
)

[20-Jul-2025 16:09:03 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => vapid-key
)

[20-Jul-2025 16:09:03 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => vapid-key
)

[20-Jul-2025 16:09:03 UTC] [API_ROUTING] Endpoint: pwa, Action: vapid-key
[20-Jul-2025 16:09:03 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[20-Jul-2025 16:09:03 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:03 UTC] Raw URL: api/pwa/fcm-verify-token
[20-Jul-2025 16:09:03 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => fcm-verify-token
)

[20-Jul-2025 16:09:03 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => fcm-verify-token
)

[20-Jul-2025 16:09:03 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => fcm-verify-token
)

[20-Jul-2025 16:09:03 UTC] [API_ROUTING] Endpoint: pwa, Action: fcm-verify-token
[20-Jul-2025 16:09:03 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[20-Jul-2025 16:09:12 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:12 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:09:12 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:12 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:12 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:09:12 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:09:12 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:09:12 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:09:12 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:12 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:12 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:12 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:12 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:12 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:12 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:09:12 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications
[20-Jul-2025 16:09:12 UTC] Session lifetime from database: 2592000 seconds
[20-Jul-2025 16:09:12 UTC] Raw URL: notification/getUnread
[20-Jul-2025 16:09:12 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:12 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[20-Jul-2025 16:09:12 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[20-Jul-2025 16:09:12 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[20-Jul-2025 16:09:12 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[20-Jul-2025 16:09:12 UTC] App.php: About to call method: getUnread on controller: NotificationController
[20-Jul-2025 16:09:12 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:12 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:12 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:12 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[20-Jul-2025 16:09:12 UTC] Database::resultSet - Result count: 0
[20-Jul-2025 16:09:12 UTC] Database::resultSet - No results found
[20-Jul-2025 16:09:12 UTC] NotificationModel::getUnreadNotifications - Found 0 push, 0 toast notifications for user 3
[20-Jul-2025 16:09:12 UTC] NotificationController::getUnread - User 3 has 0 push, 0 toast notifications