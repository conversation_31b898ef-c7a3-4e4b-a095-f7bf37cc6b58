<?php

/**
 * Email Folder Management Model
 * Handles email folder operations for admin email management
 */
class EmailFolderModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all email folders (excluding Archive folder which is handled by main archived tab)
     */
    public function getAllFolders() {
        $this->db->query("SELECT f.*, u.name as created_by_name,
                         (SELECT COUNT(DISTINCT CASE WHEN m.ticket_number IS NOT NULL THEN m.ticket_number ELSE m.id END) 
                          FROM messages m WHERE m.folder_id = f.id AND m.is_archived = 0 AND m.message_type = 'email') as message_count
                         FROM email_folders f
                         LEFT JOIN users u ON f.created_by = u.id
                         WHERE f.id != 6
                         ORDER BY f.is_system DESC, f.name ASC");
        return $this->db->resultSet();
    }
    
    /**
     * Get folder by ID
     */
    public function getFolderById($folderId) {
        $this->db->query("SELECT * FROM email_folders WHERE id = :folder_id");
        $this->db->bind(':folder_id', $folderId);
        return $this->db->single();
    }
    
    /**
     * Create new folder
     */
    public function createFolder($name, $description, $color, $icon, $createdBy) {
        // Explicitly set is_system = 0 for custom folders created by users
        $this->db->query("INSERT INTO email_folders (name, description, color, icon, created_by, is_system)
                         VALUES (:name, :description, :color, :icon, :created_by, 0)");
        $this->db->bind(':name', $name);
        $this->db->bind(':description', $description);
        $this->db->bind(':color', $color);
        $this->db->bind(':icon', $icon);
        $this->db->bind(':created_by', $createdBy);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    /**
     * Update folder
     */
    public function updateFolder($folderId, $name, $description, $color, $icon) {
        $this->db->query("UPDATE email_folders 
                         SET name = :name, description = :description, color = :color, icon = :icon
                         WHERE id = :folder_id AND is_system = 0");
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':name', $name);
        $this->db->bind(':description', $description);
        $this->db->bind(':color', $color);
        $this->db->bind(':icon', $icon);
        
        return $this->db->execute();
    }
    
    /**
     * Delete folder (only non-system folders)
     */
    public function deleteFolder($folderId) {
        // First move all messages in this folder to Inbox
        $this->db->query("UPDATE messages SET folder_id = 1 WHERE folder_id = :folder_id");
        $this->db->bind(':folder_id', $folderId);
        $this->db->execute();
        
        // Then delete the folder
        $this->db->query("DELETE FROM email_folders WHERE id = :folder_id AND is_system = 0");
        $this->db->bind(':folder_id', $folderId);
        
        return $this->db->execute();
    }
    
    /**
     * Move message to folder
     */
    public function moveMessageToFolder($messageId, $folderId) {
        $this->db->query("UPDATE messages SET folder_id = :folder_id WHERE id = :message_id");
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':message_id', $messageId);
        
        return $this->db->execute();
    }
    
    /**
     * Get messages in folder (individual messages - for backward compatibility)
     */
    public function getMessagesInFolder($folderId, $limit = 20, $offset = 0) {
        $this->db->query("SELECT m.*, u.name as from_user_name, s.name as show_title, f.name as folder_name
                         FROM messages m
                         LEFT JOIN users u ON m.from_user_id = u.id
                         LEFT JOIN shows s ON m.show_id = s.id
                         LEFT JOIN email_folders f ON m.folder_id = f.id
                         WHERE m.folder_id = :folder_id AND m.is_archived = 0
                         ORDER BY m.created_at DESC
                         LIMIT :limit OFFSET :offset");
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':limit', $limit);
        $this->db->bind(':offset', $offset);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get grouped conversations in folder (for email management tab)
     */
    public function getGroupedMessagesInFolder($folderId, $limit = 20, $offset = 0) {
        // Get more messages to allow for proper grouping, then limit conversations
        $messageLimit = $limit * 5; // Get 5x more messages to ensure we have enough for grouping
        
        $this->db->query("SELECT m.*, u.name as from_user_name, s.name as show_title, f.name as folder_name, f.color as folder_color
                         FROM messages m
                         LEFT JOIN users u ON m.from_user_id = u.id
                         LEFT JOIN shows s ON m.show_id = s.id
                         LEFT JOIN email_folders f ON m.folder_id = f.id
                         WHERE m.folder_id = :folder_id AND m.is_archived = 0 AND m.message_type = 'email'
                         ORDER BY m.created_at DESC
                         LIMIT :limit OFFSET :offset");
        
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':limit', $messageLimit);
        $this->db->bind(':offset', $offset);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get folder message count (counts conversations, not individual messages)
     */
    public function getFolderMessageCount($folderId) {
        $this->db->query("SELECT COUNT(DISTINCT CASE WHEN ticket_number IS NOT NULL THEN ticket_number ELSE id END) as count 
                         FROM messages 
                         WHERE folder_id = :folder_id AND is_archived = 0 AND message_type = 'email'");
        $this->db->bind(':folder_id', $folderId);
        $result = $this->db->single();
        
        return $result ? (int)$result->count : 0;
    }
    
    /**
     * Get folder statistics (counts conversations, not individual messages)
     */
    public function getFolderStatistics() {
        $this->db->query("SELECT f.id, f.name, f.color, f.icon, f.is_system,
                         COUNT(DISTINCT CASE WHEN m.ticket_number IS NOT NULL THEN m.ticket_number ELSE m.id END) as message_count,
                         COUNT(DISTINCT CASE WHEN m.is_read = 0 AND m.ticket_number IS NOT NULL THEN m.ticket_number 
                                            WHEN m.is_read = 0 AND m.ticket_number IS NULL THEN m.id END) as unread_count
                         FROM email_folders f
                         LEFT JOIN messages m ON f.id = m.folder_id AND m.is_archived = 0 AND m.message_type = 'email'
                         WHERE f.id != 6
                         GROUP BY f.id, f.name, f.color, f.icon, f.is_system
                         ORDER BY f.is_system DESC, f.name ASC");
        return $this->db->resultSet();
    }
    
    /**
     * Search messages across folders
     */
    public function searchMessages($query, $folderId = null, $limit = 20, $offset = 0) {
        $sql = "SELECT m.*, u.name as from_user_name, s.name as show_title, f.name as folder_name
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                LEFT JOIN email_folders f ON m.folder_id = f.id
                WHERE m.is_archived = 0 
                AND (m.subject LIKE :query OR m.message LIKE :query OR m.original_sender_email LIKE :query)";
        
        if ($folderId) {
            $sql .= " AND m.folder_id = :folder_id";
        }
        
        $sql .= " ORDER BY m.created_at DESC LIMIT :limit OFFSET :offset";
        
        $this->db->query($sql);
        $this->db->bind(':query', '%' . $query . '%');
        if ($folderId) {
            $this->db->bind(':folder_id', $folderId);
        }
        $this->db->bind(':limit', $limit);
        $this->db->bind(':offset', $offset);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get default folders
     */
    public function getDefaultFolders() {
        $this->db->query("SELECT * FROM email_folders WHERE is_system = 1 ORDER BY name ASC");
        return $this->db->resultSet();
    }
    
    /**
     * Bulk move messages to folder
     */
    public function bulkMoveMessages($messageIds, $folderId) {
        if (empty($messageIds)) {
            return false;
        }
        
        $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
        $this->db->query("UPDATE messages SET folder_id = ? WHERE id IN ($placeholders)");
        
        $this->db->bind(1, $folderId);
        foreach ($messageIds as $index => $messageId) {
            $this->db->bind($index + 2, $messageId);
        }
        
        return $this->db->execute();
    }
}
