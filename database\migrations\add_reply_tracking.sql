-- Add reply tracking to user_messages table
-- This tracks whether a user has used their reply for a specific message

ALTER TABLE user_messages 
ADD COLUMN reply_used TINYINT(1) DEFAULT 0 COMMENT 'Whether the recipient has used their reply for this message',
ADD COLUMN allows_reply TINYINT(1) DEFAULT 0 COMMENT 'Whether this message allows replies (contains [reply])';

-- Update existing messages to check for [reply] in content
UPDATE user_messages 
SET allows_reply = 1 
WHERE message LIKE '%[reply]%';

-- Add index for better performance
ALTER TABLE user_messages 
ADD INDEX idx_reply_tracking (to_user_id, allows_reply, reply_used);
