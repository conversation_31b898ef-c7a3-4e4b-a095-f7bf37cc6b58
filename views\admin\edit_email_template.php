<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-edit"></i> Edit Email Template
                </h2>
                <a href="<?= URLROOT ?>/admin/settings_email_templates" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Templates
                </a>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-<?= $_SESSION['flash_message']['type'] === 'error' ? 'danger' : $_SESSION['flash_message']['type'] ?> alert-dismissible fade show">
        <?= htmlspecialchars($_SESSION['flash_message']['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['flash_message']); endif; ?>

    <div class="row">
        <!-- Edit Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit"></i> Edit Template: <?= htmlspecialchars($data['template']->name) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= URLROOT ?>/admin/settings_email_templates/edit/<?= $data['template']->id ?>" method="POST">
                        <div class="mb-3">
                            <label for="template_name" class="form-label">Template Name</label>
                            <input type="text" class="form-control" id="template_name" name="template_name" 
                                   value="<?= htmlspecialchars($data['template']->name) ?>" required>
                            <small class="text-muted">A unique name to identify this template</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="template_subject" class="form-label">Email Subject</label>
                            <input type="text" class="form-control" id="template_subject" name="template_subject" 
                                   value="<?= htmlspecialchars($data['template']->subject) ?>" required>
                            <small class="text-muted">Use template variables like {{subject}} and {{ticket_number}}</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="template_body" class="form-label">Email Body</label>
                            <textarea class="form-control" id="template_body" name="template_body" rows="12" required><?= htmlspecialchars($data['template']->body) ?></textarea>
                            <small class="text-muted">Use template variables and plain text. HTML is supported.</small>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?= $data['template']->is_active ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">
                                    Active (available for use)
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2 mb-3">
                            <button type="button" class="btn btn-outline-info" onclick="previewTemplate()">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetTemplate()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <a href="<?= URLROOT ?>/admin/settings_email_templates" class="btn btn-secondary">
                                Cancel
                            </a>
                        </div>
                        
                        <input type="hidden" name="csrf_token" value="<?= $data['csrf_token'] ?>">
                    </form>
                </div>
            </div>
        </div>

        <!-- Template Variables Help -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle"></i> Template Variables
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Click on any variable to insert it at the cursor position. Click in the subject or body field first to choose where to insert.</p>
                    
                    <?php foreach ($data['variables'] as $category => $variables): ?>
                    <h6 class="mt-3 mb-2"><?= $category ?></h6>
                    <div class="mb-3">
                        <?php foreach ($variables as $variable => $description): ?>
                        <button type="button" class="btn btn-outline-secondary btn-sm mb-1 me-1" 
                                onclick="insertVariable('<?= $variable ?>')" 
                                title="<?= htmlspecialchars($description) ?>">
                            <?= $variable ?>
                        </button>
                        <?php endforeach; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Template Info -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Template Information
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <th>Created:</th>
                                <td><?= date('M j, Y g:i A', strtotime($data['template']->created_at)) ?></td>
                            </tr>
                            <tr>
                                <th>Updated:</th>
                                <td><?= date('M j, Y g:i A', strtotime($data['template']->updated_at)) ?></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <?php if ($data['template']->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label"><strong>Subject:</strong></label>
                    <div class="border p-2 bg-light" id="previewSubject"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>Body:</strong></label>
                    <div class="border p-3 bg-light" id="previewBody" style="white-space: pre-wrap;"></div>
                </div>
                <div class="alert alert-info">
                    <small><i class="fas fa-info-circle"></i> This preview uses sample data. Actual emails will use real values.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Define functions in global scope first
window.previewTemplate = function() {
    console.log('Preview function called'); // Debug log

    const subject = document.getElementById('template_subject').value;
    const body = document.getElementById('template_body').value;

    if (!subject || !body) {
        alert('Please enter both subject and body before previewing');
        return;
    }

    const formData = new FormData();
    formData.append('subject', subject);
    formData.append('body', body);

    fetch('<?= URLROOT ?>/admin/previewEmailTemplate', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Create preview modal if it doesn't exist
            showPreviewModal(data.preview.subject, data.preview.body);
        } else {
            alert('Failed to generate preview: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Preview error:', error);
        alert('Error generating preview: ' + error.message);
    });
};

// Function to show preview modal
function showPreviewModal(subject, body) {
    // Remove existing modal if present
    const existingModal = document.getElementById('previewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create new modal
    const modalHtml = `
        <div class="modal fade" id="previewModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Template Preview</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>Subject:</strong></label>
                            <div class="border p-2 bg-light">${subject}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>Body:</strong></label>
                            <div class="border p-3 bg-light" style="white-space: pre-wrap;">${body}</div>
                        </div>
                        <div class="alert alert-info">
                            <small><i class="fas fa-info-circle"></i> This preview uses sample data. Actual emails will use real values.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();

    // Clean up when modal is hidden
    document.getElementById('previewModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Store original values for reset functionality
const originalValues = {
    name: <?= json_encode($data['template']->name) ?>,
    subject: <?= json_encode($data['template']->subject) ?>,
    body: <?= json_encode($data['template']->body) ?>,
    active: <?= $data['template']->is_active ? 'true' : 'false' ?>
};

// Insert variable at cursor position
function insertVariable(variable) {
    // Determine which field to insert into based on focus
    const subjectField = document.getElementById('template_subject');
    const bodyField = document.getElementById('template_body');

    let targetField = bodyField; // Default to body

    // Check which field was last focused
    if (document.activeElement === subjectField) {
        targetField = subjectField;
    } else if (window.lastFocusedField === 'template_subject') {
        targetField = subjectField;
    }

    const start = targetField.selectionStart;
    const end = targetField.selectionEnd;
    const text = targetField.value;

    targetField.value = text.substring(0, start) + variable + text.substring(end);

    // Set cursor position after inserted variable
    targetField.selectionStart = targetField.selectionEnd = start + variable.length;
    targetField.focus();

    // Show feedback
    const fieldName = targetField.id === 'template_subject' ? 'subject' : 'body';
    console.log(`Inserted ${variable} into ${fieldName} field`);
}

// Track which field was last focused
document.addEventListener('DOMContentLoaded', function() {
    const subjectField = document.getElementById('template_subject');
    const bodyField = document.getElementById('template_body');

    if (subjectField) {
        subjectField.addEventListener('focus', function() {
            window.lastFocusedField = 'template_subject';
        });
    }

    if (bodyField) {
        bodyField.addEventListener('focus', function() {
            window.lastFocusedField = 'template_body';
        });
    }
});

// Reset template to original values - global function
window.resetTemplate = function() {
    if (confirm('Are you sure you want to reset all changes? This will restore the original template values.')) {
        document.getElementById('template_name').value = originalValues.name;
        document.getElementById('template_subject').value = originalValues.subject;
        document.getElementById('template_body').value = originalValues.body;
        document.getElementById('is_active').checked = originalValues.active;
    }
};

// Auto-save functionality (optional)
let autoSaveTimeout;
function autoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
        // Could implement auto-save to localStorage here
        console.log('Auto-save triggered');
    }, 5000);
}

// Add auto-save listeners
document.getElementById('template_subject').addEventListener('input', autoSave);
document.getElementById('template_body').addEventListener('input', autoSave);

// Warn about unsaved changes
let hasUnsavedChanges = false;

function checkForChanges() {
    const currentName = document.getElementById('template_name').value;
    const currentSubject = document.getElementById('template_subject').value;
    const currentBody = document.getElementById('template_body').value;
    const currentActive = document.getElementById('is_active').checked;
    
    hasUnsavedChanges = (
        currentName !== originalValues.name ||
        currentSubject !== originalValues.subject ||
        currentBody !== originalValues.body ||
        currentActive !== originalValues.active
    );
}

// Add change listeners
document.getElementById('template_name').addEventListener('input', checkForChanges);
document.getElementById('template_subject').addEventListener('input', checkForChanges);
document.getElementById('template_body').addEventListener('input', checkForChanges);
document.getElementById('is_active').addEventListener('change', checkForChanges);

// Warn before leaving page with unsaved changes
window.addEventListener('beforeunload', function(e) {
    checkForChanges();
    if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
        return '';
    }
});

// Don't warn when submitting form
document.querySelector('form').addEventListener('submit', function() {
    hasUnsavedChanges = false;
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
