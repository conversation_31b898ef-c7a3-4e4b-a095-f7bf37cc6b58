<?php
/**
 * Test Registration Messaging Functionality
 * 
 * Tests the bulk and individual messaging features for admin and coordinator registration pages
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';
require_once 'models/ShowModel.php';
require_once 'models/RegistrationModel.php';

echo "<h1>📧 Test Registration Messaging Functionality</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $showModel = new ShowModel();
    $registrationModel = new RegistrationModel();
    $db = new Database();
    
    echo "<h2>🎯 Registration Messaging Features Added</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Features Implemented:</h3>";
    echo "<ul>";
    echo "<li><strong>Bulk messaging:</strong> Message all registered users of a show at once</li>";
    echo "<li><strong>Individual messaging:</strong> Send messages to specific registered users</li>";
    echo "<li><strong>Admin access:</strong> Available on /admin/registrations/[show_id]</li>";
    echo "<li><strong>Coordinator access:</strong> Available on /coordinator/registrations/[show_id]</li>";
    echo "<li><strong>Unified messaging:</strong> Integrates with existing notification system</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current System Status</h2>";
    
    // Get shows with registrations
    $db->query("SELECT s.id, s.name, s.coordinator_id, COUNT(r.id) as registration_count 
                FROM shows s 
                LEFT JOIN registrations r ON s.id = r.show_id 
                WHERE s.status != 'cancelled' 
                GROUP BY s.id 
                HAVING registration_count > 0 
                ORDER BY s.start_date DESC 
                LIMIT 5");
    $showsWithRegistrations = $db->resultSet();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Show</th><th>Coordinator</th><th>Registrations</th><th>Messaging Available</th></tr>";
    
    foreach ($showsWithRegistrations as $show) {
        // Get coordinator name
        $db->query("SELECT name FROM users WHERE id = :id");
        $db->bind(':id', $show->coordinator_id);
        $coordinator = $db->single();
        $coordinatorName = $coordinator ? $coordinator->name : 'Unknown';
        
        echo "<tr>";
        echo "<td><strong>{$show->name}</strong> (ID: {$show->id})</td>";
        echo "<td>{$coordinatorName}</td>";
        echo "<td style='color: blue;'>{$show->registration_count} users</td>";
        echo "<td style='color: green;'>✅ Bulk + Individual messaging</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (empty($showsWithRegistrations)) {
        echo "<p style='color: orange;'>⚠️ No shows with registrations found for testing.</p>";
    }
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Admin Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Navigate to:</strong> /admin/registrations/[show_id]</li>";
    echo "<li><strong>Bulk messaging:</strong> Click 'Message All' button in header</li>";
    echo "<li><strong>Individual messaging:</strong> Click envelope icon (📧) for each user</li>";
    echo "<li><strong>Test functionality:</strong> Send test messages and verify delivery</li>";
    echo "</ol>";
    
    echo "<h3>👥 Coordinator Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Navigate to:</strong> /coordinator/registrations/[show_id]</li>";
    echo "<li><strong>Bulk messaging:</strong> Click 'Message All' button in header</li>";
    echo "<li><strong>Individual messaging:</strong> Click 'Message' button for each user</li>";
    echo "<li><strong>Test functionality:</strong> Send test messages and verify delivery</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Interface Changes</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Page</th><th>New Features</th><th>Location</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Admin Registrations</strong></td>";
    echo "<td>✅ 'Message All' button<br>✅ Individual message buttons</td>";
    echo "<td>Header + Action column</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Coordinator Registrations</strong></td>";
    echo "<td>✅ 'Message All' button<br>✅ Individual message buttons</td>";
    echo "<td>Header + Action column</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Message Modals</strong></td>";
    echo "<td>✅ Bulk message form<br>✅ Individual message form</td>";
    echo "<td>Bootstrap modals</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Implementation</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📧 Bulk Messaging:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// JavaScript AJAX call
fetch('/admin/sendBulkMessage', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        show_id: showId,
        subject: subject,
        message: message
    })
});

// PHP Controller method
public function sendBulkMessage() {
    // Get all registered users for show
    \$registrations = \$this->registrationModel->getShowRegistrations(\$showId);
    
    // Send message to each user
    foreach (\$registrations as \$registration) {
        \$messageModel->sendMessage(
            \$currentUserId,
            \$registration->user_id,
            \$subject,
            \$message,
            \$showId,
            'admin'
        );
    }
}";
    echo "</pre>";
    
    echo "<h3>👤 Individual Messaging:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// JavaScript AJAX call
fetch('/admin/sendIndividualMessage', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        to_user_id: userId,
        show_id: showId,
        subject: subject,
        message: message
    })
});

// PHP Controller method
public function sendIndividualMessage() {
    \$messageModel->sendMessage(
        \$currentUserId,
        \$toUserId,
        \$subject,
        \$message,
        \$showId,
        'admin'
    );
}";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>🎨 User Interface Features</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile-Optimized Interface:</h3>";
    echo "<ul>";
    echo "<li><strong>Responsive buttons:</strong> Text on desktop, icons on mobile</li>";
    echo "<li><strong>Modal forms:</strong> Large, easy-to-use message composition</li>";
    echo "<li><strong>Progress indicators:</strong> Loading states during message sending</li>";
    echo "<li><strong>Success feedback:</strong> Confirmation of message delivery</li>";
    echo "<li><strong>Error handling:</strong> Clear error messages for failures</li>";
    echo "</ul>";
    
    echo "<h3>🔘 Button Layout:</h3>";
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Device</th><th>Bulk Button</th><th>Individual Button</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Desktop</strong></td>";
    echo "<td>📧 'Message All'</td>";
    echo "<td>📧 'Message' (coordinator) / 📧 icon (admin)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Mobile</strong></td>";
    echo "<td>📧 'Message'</td>";
    echo "<td>📧 icon only</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    echo "<h2>🔍 Message Integration</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Unified Messaging Integration:</h3>";
    echo "<ul>";
    echo "<li><strong>Message type:</strong> 'admin' for both admin and coordinator messages</li>";
    echo "<li><strong>Show relation:</strong> Messages linked to specific show ID</li>";
    echo "<li><strong>Notification delivery:</strong> Uses recipient's notification preferences</li>";
    echo "<li><strong>Push notifications:</strong> Sent to mobile PWA users</li>";
    echo "<li><strong>Email notifications:</strong> Sent based on user settings</li>";
    echo "<li><strong>Message center:</strong> Messages appear in unified notification center</li>";
    echo "</ul>";
    
    echo "<h3>🎯 Message Features:</h3>";
    echo "<ul>";
    echo "<li><strong>Reply capability:</strong> Recipients can reply to messages</li>";
    echo "<li><strong>Read tracking:</strong> Senders can see if messages are read</li>";
    echo "<li><strong>Archive support:</strong> Messages can be archived</li>";
    echo "<li><strong>Conversation threading:</strong> Replies are threaded properly</li>";
    echo "</ul>";
    echo "</div>";
    
    if (!empty($showsWithRegistrations)) {
        echo "<h2>✅ Ready for Registration Messaging Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have " . count($showsWithRegistrations) . " show(s) with registrations for testing.</p>";
        echo "<p><strong>Test URLs:</strong></p>";
        echo "<ul>";
        foreach ($showsWithRegistrations as $show) {
            echo "<li><strong>{$show->name}:</strong>";
            echo "<ul>";
            echo "<li>Admin: <code>/admin/registrations/{$show->id}</code></li>";
            echo "<li>Coordinator: <code>/coordinator/registrations/{$show->id}</code></li>";
            echo "</ul>";
            echo "</li>";
        }
        echo "</ul>";
        echo "<p><strong>What you should see:</strong></p>";
        echo "<ul>";
        echo "<li>📧 <strong>'Message All' button:</strong> In page header for bulk messaging</li>";
        echo "<li>📧 <strong>Individual message buttons:</strong> In action column for each user</li>";
        echo "<li>📝 <strong>Message modals:</strong> Professional forms for composing messages</li>";
        echo "<li>🔔 <strong>Notification delivery:</strong> Messages sent via unified system</li>";
        echo "<li>📱 <strong>Mobile-friendly:</strong> Responsive design for all devices</li>";
        echo "</ul>";
        echo "<p><strong>The registration messaging system is ready for use!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated files:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/admin/registrations.php</code> - Added messaging interface</li>";
    echo "<li><code>views/coordinator/registrations/index.php</code> - Added messaging interface</li>";
    echo "<li><code>controllers/AdminController.php</code> - Added sendBulkMessage() and sendIndividualMessage()</li>";
    echo "<li><code>controllers/CoordinatorController.php</code> - Added sendBulkMessage() and sendIndividualMessage()</li>";
    echo "</ul>";
    echo "<p><strong>Features added:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Bulk messaging modals and JavaScript</li>";
    echo "<li>✅ Individual messaging buttons and modals</li>";
    echo "<li>✅ AJAX endpoints for message sending</li>";
    echo "<li>✅ Integration with unified messaging system</li>";
    echo "<li>✅ Mobile-responsive interface design</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Registration messaging test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
