-- Add email_message_id field to ticket_numbers table to prevent race conditions
-- This stores the email's unique message_id temporarily during processing

ALTER TABLE `ticket_numbers` 
ADD COLUMN `email_message_id` VARCHAR(255) NULL 
COMMENT 'Temporary storage of email message_id during processing to prevent race conditions'
AFTER `message_id`;

-- Add index for faster lookups during email processing
ALTER TABLE `ticket_numbers` 
ADD INDEX `idx_email_message_id` (`email_message_id`);