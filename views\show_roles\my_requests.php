<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-user-tag"></i> My Show Role Assignments</h2>
                    <p class="text-muted mb-0">Manage your show role assignments and respond to requests</p>
                </div>
                <div>
                    <a href="<?= URLROOT ?>/user/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <?php flash('success'); ?>
            <?php flash('error'); ?>

            <!-- Pending Requests -->
            <?php if (!empty($data['pending_requests'])): ?>
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-exclamation-triangle"></i> Pending Role Assignment Requests</h5>
                        <small>You have role assignment requests that require your response</small>
                    </div>
                    <div class="card-body">
                        <?php foreach ($data['pending_requests'] as $request): ?>
                            <div class="card mb-3 border-warning">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6 class="card-title">
                                                <span class="badge badge-<?= getRoleBadgeClass($request->requested_role) ?>">
                                                    <?= ucfirst($request->requested_role) ?>
                                                </span>
                                                Role Request for <strong><?= htmlspecialchars($request->show_name) ?></strong>
                                            </h6>
                                            <p class="card-text">
                                                <strong>Requested by:</strong> <?= htmlspecialchars($request->requested_by_name) ?><br>
                                                <strong>Show Date:</strong> 
                                                <?= date('M j, Y', strtotime($request->start_date)) ?>
                                                <?php if ($request->start_date !== $request->end_date): ?>
                                                    to <?= date('M j, Y', strtotime($request->end_date)) ?>
                                                <?php endif; ?><br>
                                                <strong>Requested:</strong> <?= date('M j, Y g:i A', strtotime($request->requested_at)) ?><br>
                                                <strong>Expires:</strong> 
                                                <span class="text-danger"><?= date('M j, Y g:i A', strtotime($request->expires_at)) ?></span>
                                            </p>
                                            
                                            <?php if ($request->request_message): ?>
                                                <div class="alert alert-light">
                                                    <strong>Message:</strong> <?= htmlspecialchars($request->request_message) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-right">
                                                <!-- Accept Button -->
                                                <button type="button" class="btn btn-success w-100 mb-2"
                                                        data-bs-toggle="modal" data-bs-target="#respondModal"
                                                        data-request-id="<?= $request->id ?>"
                                                        data-response="approved"
                                                        data-show-name="<?= htmlspecialchars($request->show_name) ?>"
                                                        data-role="<?= $request->requested_role ?>">
                                                    <i class="fas fa-check"></i> Accept Assignment
                                                </button>

                                                <!-- Decline Button -->
                                                <button type="button" class="btn btn-outline-danger w-100"
                                                        data-bs-toggle="modal" data-bs-target="#respondModal"
                                                        data-request-id="<?= $request->id ?>"
                                                        data-response="declined"
                                                        data-show-name="<?= htmlspecialchars($request->show_name) ?>"
                                                        data-role="<?= $request->requested_role ?>">
                                                    <i class="fas fa-times"></i> Decline Assignment
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Current Assignments -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users-cog"></i> My Current Role Assignments</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['assignments'])): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-user-tag fa-3x mb-3"></i>
                            <p>You don't have any show role assignments yet.</p>
                            <?php if (empty($data['pending_requests'])): ?>
                                <p>When coordinators assign you roles for shows, they will appear here.</p>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Show</th>
                                        <th>Role</th>
                                        <th>Assigned By</th>
                                        <th>Show Date</th>
                                        <th>Assigned Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['assignments'] as $assignment): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($assignment->show_name) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= getRoleBadgeClass($assignment->assigned_role) ?>">
                                                    <?= ucfirst($assignment->assigned_role) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($assignment->assigned_by_name) ?></td>
                                            <td>
                                                <?= date('M j, Y', strtotime($assignment->start_date)) ?>
                                                <?php if ($assignment->start_date !== $assignment->end_date): ?>
                                                    to <?= date('M j, Y', strtotime($assignment->end_date)) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($assignment->assigned_at)) ?></td>
                                            <td>
                                                <?php if (strtotime($assignment->expires_at) < time()): ?>
                                                    <span class="badge badge-secondary">Expired</span>
                                                <?php elseif (strtotime($assignment->start_date) > time()): ?>
                                                    <span class="badge badge-info">Upcoming</span>
                                                <?php elseif (strtotime($assignment->end_date) < time()): ?>
                                                    <span class="badge badge-success">Completed</span>
                                                <?php else: ?>
                                                    <span class="badge badge-primary">Active</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (strtotime($assignment->expires_at) > time()): ?>
                                                    <a href="<?= URLROOT ?>/show/view/<?= $assignment->show_id ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> View Show
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
<div class="modal fade" id="respondModal" tabindex="-1" role="dialog" aria-labelledby="respondModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="<?= URLROOT ?>/show_roles/respond" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="respondModalLabel">Respond to Role Assignment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="modal_request_id" name="request_id" value="">
                    <input type="hidden" id="modal_response" name="response" value="">
                    
                    <div class="alert" id="modal_alert">
                        <p id="modal_message"></p>
                    </div>
                    
                    <div class="form-group">
                        <label for="response_message">Optional Message</label>
                        <textarea id="response_message" name="message" class="form-control" rows="3" 
                                  placeholder="Add an optional message with your response..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="modal_submit_btn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
function getRoleBadgeClass($role) {
    switch ($role) {
        case 'coordinator': return 'primary';
        case 'judge': return 'success';
        case 'staff': return 'info';
        default: return 'secondary';
    }
}
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const respondModal = document.getElementById('respondModal');

    if (respondModal) {
        respondModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const requestId = button.getAttribute('data-request-id');
            const response = button.getAttribute('data-response');
            const showName = button.getAttribute('data-show-name');
            const role = button.getAttribute('data-role');

            // Set hidden form values
            document.getElementById('modal_request_id').value = requestId;
            document.getElementById('modal_response').value = response;

            const modalAlert = document.getElementById('modal_alert');
            const modalMessage = document.getElementById('modal_message');
            const modalSubmitBtn = document.getElementById('modal_submit_btn');

            if (response === 'approved') {
                modalAlert.className = 'alert alert-success';
                modalMessage.innerHTML = '<strong>Accept Role Assignment</strong><br>You are about to accept the role of <strong>' + role + '</strong> for the show <strong>' + showName + '</strong>.';
                modalSubmitBtn.className = 'btn btn-success';
                modalSubmitBtn.innerHTML = '<i class="fas fa-check"></i> Accept Assignment';
            } else {
                modalAlert.className = 'alert alert-danger';
                modalMessage.innerHTML = '<strong>Decline Role Assignment</strong><br>You are about to decline the role of <strong>' + role + '</strong> for the show <strong>' + showName + '</strong>.';
                modalSubmitBtn.className = 'btn btn-danger';
                modalSubmitBtn.innerHTML = '<i class="fas fa-times"></i> Decline Assignment';
            }
        });
    }
});
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
