<?php
/**
 * Test EmailService in Cron Environment
 * 
 * Tests if EmailService class can be loaded and used in cron environment
 */

// Include the application bootstrap (same as cron script)
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationModel.php';
require_once 'models/NotificationService.php';

echo "<h1>🧪 Test EmailService in Cron Environment</h1>";

echo "<h2>📋 Class Loading Test</h2>";

// Test each class loading
$classes = [
    'Database' => 'core/Database.php',
    'NotificationModel' => 'models/NotificationModel.php', 
    'NotificationService' => 'models/NotificationService.php',
    'EmailService' => 'models/EmailService.php',
    'SettingsModel' => 'models/SettingsModel.php'
];

echo "<table border='1' cellpadding='5'>";
echo "<tr style='background: #f0f0f0;'><th>Class</th><th>File</th><th>Status</th><th>Action</th></tr>";

foreach ($classes as $className => $filePath) {
    $fileExists = file_exists($filePath);
    $classExists = class_exists($className);
    
    echo "<tr>";
    echo "<td><strong>{$className}</strong></td>";
    echo "<td>{$filePath}</td>";
    
    if ($classExists) {
        echo "<td style='color: green;'>✅ Loaded</td>";
        echo "<td>Ready to use</td>";
    } elseif ($fileExists) {
        echo "<td style='color: orange;'>⚠️ File exists but class not loaded</td>";
        echo "<td>Need to include file</td>";
        
        // Try to include the file
        try {
            require_once $filePath;
            if (class_exists($className)) {
                echo "<td style='color: green;'>✅ Now loaded</td>";
            } else {
                echo "<td style='color: red;'>❌ Still not loaded</td>";
            }
        } catch (Exception $e) {
            echo "<td style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</td>";
        }
    } else {
        echo "<td style='color: red;'>❌ File not found</td>";
        echo "<td>File missing</td>";
    }
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔧 EmailService Test</h2>";

try {
    if (class_exists('EmailService')) {
        echo "<p style='color: green;'>✅ EmailService class is available</p>";
        
        $emailService = new EmailService();
        echo "<p style='color: green;'>✅ EmailService instance created successfully</p>";
        
        $isConfigured = $emailService->isConfigured();
        echo "<p><strong>EmailService configured:</strong> " . ($isConfigured ? '✅ Yes' : '❌ No') . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ EmailService class is NOT available</p>";
        echo "<p><strong>This is the exact issue causing the cron job to fail!</strong></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing EmailService: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🔄 NotificationService Test</h2>";

try {
    if (class_exists('NotificationService')) {
        echo "<p style='color: green;'>✅ NotificationService class is available</p>";
        
        $notificationService = new NotificationService();
        echo "<p style='color: green;'>✅ NotificationService instance created successfully</p>";
        
        // Test if it can process notifications (this is where it fails in cron)
        echo "<p><strong>Testing processPendingNotifications method...</strong></p>";
        
        // This should fail if EmailService is not available
        $results = $notificationService->processPendingNotifications(1);
        echo "<p style='color: green;'>✅ processPendingNotifications executed successfully</p>";
        echo "<p><strong>Results:</strong> Processed: {$results['processed']}, Sent: {$results['sent']}, Failed: {$results['failed']}</p>";
        
        if (!empty($results['errors'])) {
            echo "<p style='color: red;'><strong>Errors:</strong></p>";
            echo "<ul>";
            foreach ($results['errors'] as $error) {
                echo "<li>" . htmlspecialchars($error) . "</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ NotificationService class is NOT available</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing NotificationService: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Error details:</strong> " . htmlspecialchars($e->getTraceAsString()) . "</p>";
}

echo "<h2>📋 Cron Script Fix</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>🎯 The Issue:</h3>";
echo "<p>The cron script is missing the EmailService class include. Based on your logs, the cron script is at:</p>";
echo "<code>/home/<USER>/events.rowaneliterides.com/scripts/process_notifications.php</code>";

echo "<h3>🔧 The Fix:</h3>";
echo "<p>Add these lines to the cron script after the existing includes:</p>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo "require_once dirname(__DIR__) . '/models/EmailService.php';\n";
echo "require_once dirname(__DIR__) . '/models/SettingsModel.php';";
echo "</pre>";

echo "<h3>📝 Complete Include Section Should Look Like:</h3>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo "// Include the application bootstrap\n";
echo "require_once dirname(__DIR__) . '/config/config.php';\n";
echo "require_once dirname(__DIR__) . '/core/Database.php';\n";
echo "require_once dirname(__DIR__) . '/models/NotificationModel.php';\n";
echo "require_once dirname(__DIR__) . '/models/NotificationService.php';\n";
echo "require_once dirname(__DIR__) . '/models/EmailService.php';\n";
echo "require_once dirname(__DIR__) . '/models/SettingsModel.php';";
echo "</pre>";
echo "</div>";

echo "<h2>🧪 Manual Email Processing Test</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>📱 Test Processing Your 2 Pending Emails:</h3>";

if (class_exists('NotificationService') && class_exists('EmailService')) {
    echo "<form method='post'>";
    echo "<button type='submit' name='process_now' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Process 2 Pending Emails Now</button>";
    echo "</form>";
    
    if (isset($_POST['process_now'])) {
        echo "<h4>🔄 Processing Results:</h4>";
        try {
            $notificationService = new NotificationService();
            $results = $notificationService->processPendingNotifications(10);
            
            echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
            echo "<strong>Processed:</strong> {$results['processed']}<br>";
            echo "<strong>Sent:</strong> {$results['sent']}<br>";
            echo "<strong>Failed:</strong> {$results['failed']}<br>";
            
            if (!empty($results['errors'])) {
                echo "<strong>Errors:</strong><br>";
                foreach ($results['errors'] as $error) {
                    echo "• " . htmlspecialchars($error) . "<br>";
                }
            }
            echo "</div>";
            
            if ($results['sent'] > 0) {
                echo "<p style='color: green;'><strong>✅ Success! {$results['sent']} email(s) sent!</strong></p>";
                echo "<p>Check your email inbox for the contact form notifications.</p>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
            echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ Cannot test processing - required classes not available</p>";
    echo "<p>Fix the class loading issues first, then try manual processing.</p>";
}
echo "</div>";

echo "<h2>📋 Next Steps</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<h3>🎯 To Fix the Cron Job:</h3>";
echo "<ol>";
echo "<li><strong>Locate the actual cron script:</strong> <code>/home/<USER>/events.rowaneliterides.com/scripts/process_notifications.php</code></li>";
echo "<li><strong>Add the missing includes:</strong> EmailService.php and SettingsModel.php</li>";
echo "<li><strong>Test the fix:</strong> Run the cron script manually to verify it works</li>";
echo "<li><strong>Monitor the logs:</strong> Check if the fatal error is resolved</li>";
echo "</ol>";

echo "<h3>🔧 Alternative Quick Fix:</h3>";
echo "<p>If you can't find the scripts/process_notifications.php file, you can:</p>";
echo "<ol>";
echo "<li>Copy the working cron/process_notifications.php to scripts/process_notifications.php</li>";
echo "<li>Update your cron job to use the cron/ version instead</li>";
echo "<li>Or create a symlink from scripts/ to cron/</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><em>EmailService cron test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
