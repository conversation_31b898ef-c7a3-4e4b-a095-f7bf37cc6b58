<?php
/**
 * Simple notification test - just test storage and retrieval without controllers
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load Database class
require_once APPROOT . '/core/Database.php';

// Load notification helper
require_once APPROOT . '/helpers/notification_helper.php';

echo "<h1>🔧 Simple Notification Test</h1>";

$userId = 3; // Test user ID

try {
    echo "<h2>1. Sending Test Notifications</h2>";
    
    // Send a toast notification
    echo "<h3>📱 Sending Toast Notification...</h3>";
    $toastResult = sendToastNotification($userId, "Simple Test Toast", "This toast should appear in browser");
    echo "Toast sent: " . ($toastResult ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    // Send a push notification
    echo "<h3>🔔 Sending Push Notification...</h3>";
    $pushResult = sendPushNotification($userId, "Simple Test Push", "This push should appear in browser");
    echo "Push sent: " . ($pushResult ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    echo "<h2>2. Checking Database Storage</h2>";
    
    $db = new Database();
    
    // Check toast notifications table
    echo "<h3>🍞 Toast Notifications in Database:</h3>";
    $db->query("SELECT * FROM user_toast_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 3");
    $db->bind(':user_id', $userId);
    $toastNotifications = $db->resultSet();
    
    if (!empty($toastNotifications)) {
        echo "<p>✅ Found " . count($toastNotifications) . " toast notifications</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Title</th><th>Message</th><th>Is Read</th><th>Created</th></tr>";
        foreach ($toastNotifications as $notification) {
            $readStatus = $notification->is_read ? "✅ Yes" : "❌ No";
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->title}</td>";
            echo "<td>{$notification->message}</td>";
            echo "<td>{$readStatus}</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No toast notifications found in database</p>";
    }
    
    // Check push notifications table
    echo "<h3>🔔 Push Notifications in Database:</h3>";
    $db->query("SELECT * FROM user_push_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 3");
    $db->bind(':user_id', $userId);
    $pushNotifications = $db->resultSet();
    
    if (!empty($pushNotifications)) {
        echo "<p>✅ Found " . count($pushNotifications) . " push notifications</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Title</th><th>Message</th><th>Is Read</th><th>Created</th></tr>";
        foreach ($pushNotifications as $notification) {
            $readStatus = $notification->is_read ? "✅ Yes" : "❌ No";
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->title}</td>";
            echo "<td>{$notification->message}</td>";
            echo "<td>{$readStatus}</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No push notifications found in database</p>";
    }
    
    echo "<h2>3. Testing Retrieval with NotificationModel</h2>";
    
    require_once APPROOT . '/models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    
    $unreadNotifications = $notificationModel->getUnreadNotifications($userId);
    
    echo "<h3>📊 Unread Notifications Retrieved:</h3>";
    echo "<p>🔔 Push notifications: <strong>" . count($unreadNotifications['push']) . "</strong></p>";
    echo "<p>🍞 Toast notifications: <strong>" . count($unreadNotifications['toast']) . "</strong></p>";
    
    if (!empty($unreadNotifications['toast'])) {
        echo "<h4>Toast Notifications Details:</h4>";
        echo "<ul>";
        foreach ($unreadNotifications['toast'] as $notification) {
            echo "<li><strong>{$notification->title}</strong>: {$notification->message} <em>(ID: {$notification->id})</em></li>";
        }
        echo "</ul>";
    }
    
    if (!empty($unreadNotifications['push'])) {
        echo "<h4>Push Notifications Details:</h4>";
        echo "<ul>";
        foreach ($unreadNotifications['push'] as $notification) {
            echo "<li><strong>{$notification->title}</strong>: {$notification->message} <em>(ID: {$notification->id})</em></li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>4. Testing API Endpoint via cURL</h2>";
    
    // Test the API endpoint using cURL
    $apiUrl = BASE_URL . '/notification/getUnread';
    echo "<p>Testing: <code>$apiUrl</code></p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    // Add session cookie if we have one
    if (isset($_COOKIE[session_name()])) {
        curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . $_COOKIE[session_name()]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p>❌ cURL Error: $error</p>";
    } else {
        echo "<p>✅ HTTP Status: $httpCode</p>";
        echo "<h4>API Response:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto;'>" . htmlspecialchars($response) . "</pre>";
        
        $jsonResponse = json_decode($response, true);
        if ($jsonResponse) {
            echo "<p>✅ Valid JSON response</p>";
            if (isset($jsonResponse['push']) && isset($jsonResponse['toast'])) {
                echo "<p>🔔 API Push count: " . count($jsonResponse['push']) . "</p>";
                echo "<p>🍞 API Toast count: " . count($jsonResponse['toast']) . "</p>";
            }
        } else {
            echo "<p>❌ Invalid JSON response</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<details><summary>Stack Trace</summary><pre>" . $e->getTraceAsString() . "</pre></details>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Notification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        h1, h2, h3 { color: #333; }
        h1 { border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
        details { margin: 10px 0; }
        summary { cursor: pointer; font-weight: bold; }
    </style>
</head>
<body>
    <hr>
    <h2>🎯 What This Test Checks</h2>
    <ol>
        <li><strong>Notification Sending:</strong> Uses the helper functions to send notifications</li>
        <li><strong>Database Storage:</strong> Verifies notifications are stored in the correct tables</li>
        <li><strong>Data Retrieval:</strong> Tests the updated NotificationModel methods</li>
        <li><strong>API Endpoint:</strong> Tests the /notification/getUnread endpoint via cURL</li>
    </ol>
    
    <h2>✅ Success Criteria</h2>
    <ul>
        <li>✅ Notifications should be sent successfully</li>
        <li>✅ Data should appear in <code>user_toast_notifications</code> and <code>user_push_notifications</code> tables</li>
        <li>✅ NotificationModel should retrieve the data correctly</li>
        <li>✅ API should return valid JSON with push and toast arrays</li>
    </ul>
    
    <h2>🔄 Next Steps</h2>
    <p>If this test passes:</p>
    <ol>
        <li>Open your main site in another browser tab</li>
        <li>Wait 30 seconds for automatic notification polling, or refresh the page</li>
        <li>You should see the toast notifications appear in the browser</li>
    </ol>
    
    <p><a href="test_frontend_notifications.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔔 Test Frontend Display</a></p>
    
</body>
</html>
