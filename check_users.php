<?php
/**
 * Check Users in System
 * 
 * This script shows what users exist in the system for testing purposes
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>👥 Check Users in System</h1>";

try {
    $db = new Database();
    
    // Check if users table exists
    $db->query("SHOW TABLES LIKE 'users'");
    $tableExists = $db->single();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ Users table does not exist!</p>";
        exit;
    }
    
    echo "<h2>📊 Available Users</h2>";
    
    // Get all users
    $db->query("SELECT id, name, email, phone, role, created_at FROM users ORDER BY id");
    $users = $db->resultSet();
    
    if (empty($users)) {
        echo "<p style='color: orange;'>⚠️ No users found in the system</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($users) . " users:</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Email</th><th>Phone</th><th>Role</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td><strong>{$user->id}</strong></td>";
            echo "<td>" . htmlspecialchars($user->name ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($user->email ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($user->phone ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($user->role ?? 'user') . "</td>";
            echo "<td>" . htmlspecialchars($user->created_at ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>🧪 Testing Recommendations</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>For testing the messaging system:</strong></p>";
        echo "<ul>";
        
        // Find users with email addresses
        $usersWithEmail = array_filter($users, function($user) {
            return !empty($user->email);
        });
        
        // Find users with phone numbers
        $usersWithPhone = array_filter($users, function($user) {
            return !empty($user->phone);
        });
        
        if (!empty($usersWithEmail)) {
            $emailUser = reset($usersWithEmail);
            echo "<li>✅ <strong>User {$emailUser->id}</strong> has email ({$emailUser->email}) - good for email testing</li>";
        } else {
            echo "<li>⚠️ No users have email addresses - email notifications won't work</li>";
        }
        
        if (!empty($usersWithPhone)) {
            $phoneUser = reset($usersWithPhone);
            echo "<li>✅ <strong>User {$phoneUser->id}</strong> has phone ({$phoneUser->phone}) - good for SMS testing</li>";
        } else {
            echo "<li>⚠️ No users have phone numbers - SMS notifications won't work</li>";
        }
        
        // Recommend a good test user
        $recommendedUser = null;
        foreach ($users as $user) {
            if (!empty($user->email)) {
                $recommendedUser = $user;
                break;
            }
        }
        
        if (!$recommendedUser && !empty($users)) {
            $recommendedUser = $users[0];
        }
        
        if ($recommendedUser) {
            echo "<li>🎯 <strong>Recommended test user: User {$recommendedUser->id}</strong> ({$recommendedUser->name})</li>";
            echo "<li>📝 Update test scripts to use: <code>\$fromUserId = {$recommendedUser->id}; \$toUserId = {$recommendedUser->id};</code></li>";
        }
        
        echo "</ul>";
        echo "</div>";
        
        echo "<h3>🔔 Notification Settings Check</h3>";
        
        // Check notification preferences for users
        $db->query("SHOW TABLES LIKE 'user_notification_preferences'");
        $prefTableExists = $db->single();
        
        if ($prefTableExists) {
            $db->query("SELECT user_id, email_enabled, push_enabled, toast_enabled, sms_enabled FROM user_notification_preferences");
            $preferences = $db->resultSet();
            
            if (!empty($preferences)) {
                echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
                echo "<tr style='background: #f0f0f0;'>";
                echo "<th>User ID</th><th>Email</th><th>Push</th><th>Toast</th><th>SMS</th>";
                echo "</tr>";
                
                foreach ($preferences as $pref) {
                    echo "<tr>";
                    echo "<td>{$pref->user_id}</td>";
                    echo "<td>" . ($pref->email_enabled ? '✅' : '❌') . "</td>";
                    echo "<td>" . ($pref->push_enabled ? '✅' : '❌') . "</td>";
                    echo "<td>" . ($pref->toast_enabled ? '✅' : '❌') . "</td>";
                    echo "<td>" . ($pref->sms_enabled ? '✅' : '❌') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p style='color: orange;'>⚠️ No user notification preferences found</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ user_notification_preferences table does not exist</p>";
        }
        
        // Check global notification settings
        $db->query("SHOW TABLES LIKE 'notification_settings'");
        $globalTableExists = $db->single();
        
        if ($globalTableExists) {
            $db->query("SELECT * FROM notification_settings LIMIT 1");
            $globalSettings = $db->single();
            
            if ($globalSettings) {
                echo "<h4>🌐 Global Notification Settings</h4>";
                echo "<p>";
                echo "Email: " . ($globalSettings->email_enabled ? '✅ Enabled' : '❌ Disabled') . " | ";
                echo "Push: " . ($globalSettings->push_enabled ? '✅ Enabled' : '❌ Disabled') . " | ";
                echo "Toast: " . ($globalSettings->toast_enabled ? '✅ Enabled' : '❌ Disabled') . " | ";
                echo "SMS: " . ($globalSettings->sms_enabled ? '✅ Enabled' : '❌ Disabled');
                echo "</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ No global notification settings found</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ notification_settings table does not exist</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Check completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
