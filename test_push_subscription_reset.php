<?php
/**
 * Test Push Subscription Reset Functionality
 * 
 * This script tests the new push subscription reset functionality
 * to ensure it works correctly before deployment.
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationModel.php';

// Only run in debug mode
if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
    die('This test script can only be run in DEBUG_MODE');
}

echo "<h1>Push Subscription Reset Test</h1>\n";

try {
    $notificationModel = new NotificationModel();
    
    // Test user ID (use a test user)
    $testUserId = 3; // Adjust this to a valid test user ID
    
    echo "<h2>Testing Push Subscription Status</h2>\n";
    
    // Get current status
    $status = $notificationModel->getUserPushSubscriptionStatus($testUserId);
    echo "<pre>";
    print_r($status);
    echo "</pre>";
    
    echo "<h2>Testing Reset Functionality</h2>\n";
    
    // Test reset (only if there are subscriptions to reset)
    if ($status['has_subscriptions']) {
        echo "<p>Found {$status['active_tokens']} active tokens. Testing reset...</p>\n";
        
        $resetResult = $notificationModel->resetUserPushSubscriptions($testUserId);
        echo "<pre>";
        print_r($resetResult);
        echo "</pre>";
        
        // Check status after reset
        echo "<h2>Status After Reset</h2>\n";
        $statusAfter = $notificationModel->getUserPushSubscriptionStatus($testUserId);
        echo "<pre>";
        print_r($statusAfter);
        echo "</pre>";
        
        if ($statusAfter['active_tokens'] == 0 && !$statusAfter['push_enabled']) {
            echo "<p style='color: green;'><strong>✅ Reset test PASSED - All subscriptions cleared</strong></p>\n";
        } else {
            echo "<p style='color: red;'><strong>❌ Reset test FAILED - Subscriptions not properly cleared</strong></p>\n";
        }
    } else {
        echo "<p>No active subscriptions found for test user {$testUserId}. Reset functionality cannot be tested.</p>\n";
        echo "<p>To test properly:</p>\n";
        echo "<ol>\n";
        echo "<li>Go to /user/notifications as user {$testUserId}</li>\n";
        echo "<li>Enable push notifications</li>\n";
        echo "<li>Run this test again</li>\n";
        echo "</ol>\n";
    }
    
    echo "<h2>Database Table Check</h2>\n";
    
    // Check if required tables exist
    $db = new Database();
    
    $tables = ['fcm_tokens', 'user_notification_preferences', 'notification_queue'];
    foreach ($tables as $table) {
        $db->query("SHOW TABLES LIKE '{$table}'");
        $exists = $db->single();
        
        if ($exists) {
            echo "<p style='color: green;'>✅ Table '{$table}' exists</p>\n";
            
            // Show table structure for fcm_tokens
            if ($table === 'fcm_tokens') {
                $db->query("DESCRIBE {$table}");
                $structure = $db->resultSet();
                echo "<details><summary>Table structure for {$table}</summary><pre>";
                print_r($structure);
                echo "</pre></details>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ Table '{$table}' does not exist</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<hr>\n";
echo "<p><strong>Test completed.</strong> If all checks pass, the push subscription reset functionality is ready for use.</p>\n";
echo "<p><a href='/user/notifications'>Go to Notification Settings</a> | <a href='/'>Back to Home</a></p>\n";
?>