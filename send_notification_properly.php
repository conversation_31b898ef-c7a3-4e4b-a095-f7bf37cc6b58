<?php
/**
 * Proper way to send notifications immediately using user preferences
 */

require_once 'config/config.php';
require_once 'helpers/notification_helper.php';

/**
 * Send notification to user respecting their preferences
 * 
 * @param int $userId User ID
 * @param string $subject Notification subject/title
 * @param string $message Notification message
 * @param int $eventId Optional event ID (for event-related notifications)
 * @param string $eventType Optional event type ('calendar_event', 'car_show', 'test')
 * @param string $category Optional notification category ('event_reminder', 'registration_deadline', 'test')
 * @return array Results of sending notifications
 */
function sendNotificationToUser($userId, $subject, $message, $eventId = null, $eventType = 'test', $category = 'test') {
    $notificationModel = new NotificationModel();
    $notificationService = new NotificationService();
    
    $results = [
        'success' => false,
        'sent_types' => [],
        'failed_types' => [],
        'errors' => []
    ];
    
    try {
        // 1. Get global notification settings
        $globalSettings = $notificationModel->getNotificationSettings();
        
        // 2. Get user's notification preferences
        $userPreferences = $notificationModel->getUserPreferences($userId);
        
        if (!$userPreferences) {
            // Create default preferences if they don't exist
            $notificationModel->createDefaultPreferences($userId);
            $userPreferences = $notificationModel->getUserPreferences($userId);
        }
        
        // 3. Determine which notification types to send based on preferences
        $typesToSend = [];
        
        // Check email
        if ($globalSettings['email_enabled'] && $userPreferences->email_notifications) {
            $typesToSend[] = 'email';
        }
        
        // Check SMS
        if ($globalSettings['sms_enabled'] && $userPreferences->sms_notifications) {
            $typesToSend[] = 'sms';
        }
        
        // Check push notifications
        if ($globalSettings['push_enabled'] && $userPreferences->push_notifications) {
            $typesToSend[] = 'push';
        }
        
        // Check toast notifications
        if ($globalSettings['toast_enabled'] && $userPreferences->toast_notifications) {
            $typesToSend[] = 'toast';
        }
        
        if (empty($typesToSend)) {
            $results['errors'][] = 'No notification types enabled for this user';
            return $results;
        }
        
        // 4. Send each enabled notification type immediately
        foreach ($typesToSend as $type) {
            try {
                $sent = false;
                
                switch ($type) {
                    case 'email':
                        $sent = $notificationService->sendTestNotification($userId, 'email', $subject, $message);
                        break;
                        
                    case 'sms':
                        $sent = $notificationService->sendTestNotification($userId, 'sms', $subject, $message);
                        break;
                        
                    case 'push':
                        $sent = $notificationService->sendTestPushNotification($userId, $subject, $message);
                        break;
                        
                    case 'toast':
                        $sent = $notificationService->sendTestToastNotification($userId, $subject, $message);
                        break;
                }
                
                if ($sent) {
                    $results['sent_types'][] = $type;
                } else {
                    $results['failed_types'][] = $type;
                }
                
            } catch (Exception $e) {
                $results['failed_types'][] = $type;
                $results['errors'][] = "Failed to send $type: " . $e->getMessage();
            }
        }
        
        $results['success'] = !empty($results['sent_types']);
        
    } catch (Exception $e) {
        $results['errors'][] = 'General error: ' . $e->getMessage();
    }
    
    return $results;
}

/**
 * Alternative method: Queue notifications for immediate processing
 * This method queues notifications but processes them immediately
 */
function queueAndProcessNotification($userId, $subject, $message, $eventId = null, $eventType = 'test') {
    $notificationModel = new NotificationModel();
    $notificationService = new NotificationService();
    
    $results = [
        'success' => false,
        'queued_types' => [],
        'processed_results' => []
    ];
    
    try {
        // Get user preferences
        $userPreferences = $notificationModel->getUserPreferences($userId);
        $globalSettings = $notificationModel->getNotificationSettings();
        
        if (!$userPreferences) {
            $notificationModel->createDefaultPreferences($userId);
            $userPreferences = $notificationModel->getUserPreferences($userId);
        }
        
        // Queue each enabled notification type
        $typesToQueue = [];
        
        if ($globalSettings['email_enabled'] && $userPreferences->email_notifications) {
            $typesToQueue[] = 'email';
        }
        if ($globalSettings['sms_enabled'] && $userPreferences->sms_notifications) {
            $typesToQueue[] = 'sms';
        }
        if ($globalSettings['push_enabled'] && $userPreferences->push_notifications) {
            $typesToQueue[] = 'push';
        }
        if ($globalSettings['toast_enabled'] && $userPreferences->toast_notifications) {
            $typesToQueue[] = 'toast';
        }
        
        // Queue notifications for immediate processing (scheduled_for = NOW())
        foreach ($typesToQueue as $type) {
            $queued = $notificationModel->queueTestNotification(
                $userId,
                $type,
                $subject,
                $message,
                null // null = immediate processing
            );
            
            if ($queued) {
                $results['queued_types'][] = $type;
            }
        }
        
        // Process the queued notifications immediately
        if (!empty($results['queued_types'])) {
            $processResults = $notificationService->processPendingNotifications(50);
            $results['processed_results'] = $processResults;
            $results['success'] = $processResults['sent'] > 0;
        }
        
    } catch (Exception $e) {
        $results['error'] = $e->getMessage();
    }
    
    return $results;
}

// Example usage:
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['test'])) {
    $userId = 3; // Replace with actual user ID
    $subject = "Test Notification";
    $message = "This is a test notification sent using user preferences.";
    
    echo "<h1>Sending Notification to User $userId</h1>";
    
    // Method 1: Direct sending
    echo "<h2>Method 1: Direct Sending</h2>";
    $results1 = sendNotificationToUser($userId, $subject, $message);
    echo "<pre>";
    print_r($results1);
    echo "</pre>";
    
    // Method 2: Queue and process
    echo "<h2>Method 2: Queue and Process</h2>";
    $results2 = queueAndProcessNotification($userId, $subject . " (Queued)", $message);
    echo "<pre>";
    print_r($results2);
    echo "</pre>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Send Notification Test</title>
</head>
<body>
    <h1>Test Notification Sending</h1>
    <form method="post">
        <button type="submit">Send Test Notification</button>
    </form>
    <p><a href="?test=1">Or click here to test</a></p>
</body>
</html>
