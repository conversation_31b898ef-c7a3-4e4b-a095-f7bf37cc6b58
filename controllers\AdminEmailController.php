<?php
/**
 * Admin Email Dashboard Controller
 * 
 * Handles the comprehensive email management interface for administrators
 * Features: folders, sorting, reminders, templates, and message ownership
 */
class AdminEmailController extends Controller {
    private $db;
    private $auth;
    private $unifiedMessageModel;
    private $ticketService;
    private $currentAdminId;
    
    public function __construct() {
        $this->db = new Database();
        $this->auth = new Auth();
        
        // Check if user is admin
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->currentAdminId = $this->auth->getCurrentUserId();
        
        // Load required models
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        require_once APPROOT . '/models/TicketNumberService.php';
        $this->unifiedMessageModel = new UnifiedMessageModel();
        $this->ticketService = new TicketNumberService();
    }
    
    /**
     * Main email dashboard
     */
    public function index() {
        $folderId = $_GET['folder'] ?? null;
        $sortBy = $_GET['sort'] ?? 'created_at';
        $sortOrder = $_GET['order'] ?? 'DESC';
        $page = (int)($_GET['page'] ?? 1);
        $search = $_GET['search'] ?? '';
        
        // Get admin folders
        $folders = $this->getAdminFolders();
        
        // Get messages for current folder
        $messages = $this->getMessages($folderId, $sortBy, $sortOrder, $page, $search);
        
        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        // Get pending reminders
        $reminders = $this->getPendingReminders();
        
        $data = [
            'title' => 'Email Dashboard',
            'folders' => $folders,
            'messages' => $messages,
            'stats' => $stats,
            'reminders' => $reminders,
            'current_folder' => $folderId,
            'sort_by' => $sortBy,
            'sort_order' => $sortOrder,
            'current_page' => $page,
            'search_query' => $search,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/email_dashboard', $data);
    }
    
    /**
     * View individual message
     */
    public function viewMessage($messageId) {
        $message = $this->getMessage($messageId);
        
        if (!$message) {
            $this->setFlashMessage('error', 'Message not found');
            $this->redirect('admin/email_dashboard');
            return;
        }
        
        // Check ownership or take ownership
        $this->handleMessageOwnership($messageId);
        
        // Mark as read
        $this->markMessageAsRead($messageId);
        
        // Get conversation thread
        $thread = $this->getMessageThread($message);
        
        // Get available templates
        $templates = $this->getEmailTemplates();
        
        // Get attachments
        $attachments = $this->getMessageAttachments($messageId);
        
        $data = [
            'title' => 'View Message',
            'message' => $message,
            'thread' => $thread,
            'templates' => $templates,
            'attachments' => $attachments,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/email_message', $data);
    }
    
    /**
     * Create new folder
     */
    public function createFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/email_dashboard');
            return;
        }
        
        if (!$this->verifyCsrfToken()) {
            $this->setFlashMessage('error', 'Invalid request');
            $this->redirect('admin/email_dashboard');
            return;
        }
        
        $name = trim($_POST['folder_name'] ?? '');
        $color = $_POST['folder_color'] ?? '#007bff';
        
        if (empty($name)) {
            $this->setFlashMessage('error', 'Folder name is required');
            $this->redirect('admin/email_dashboard');
            return;
        }
        
        // Check if folder already exists
        $sql = "SELECT id FROM admin_email_folders WHERE admin_user_id = :admin_id AND name = :name";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $this->db->bind(':name', $name);
        
        if ($this->db->single()) {
            $this->setFlashMessage('error', 'Folder already exists');
            $this->redirect('admin/email_dashboard');
            return;
        }
        
        // Get next sort order
        $sql = "SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM admin_email_folders WHERE admin_user_id = :admin_id";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $result = $this->db->single();
        $sortOrder = $result->next_order ?? 1;
        
        // Create folder
        $sql = "INSERT INTO admin_email_folders (admin_user_id, name, color, sort_order) VALUES (:admin_id, :name, :color, :sort_order)";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $this->db->bind(':name', $name);
        $this->db->bind(':color', $color);
        $this->db->bind(':sort_order', $sortOrder);
        
        if ($this->db->execute()) {
            $this->setFlashMessage('success', 'Folder created successfully');
        } else {
            $this->setFlashMessage('error', 'Failed to create folder');
        }
        
        $this->redirect('admin/email_dashboard');
    }
    
    /**
     * Move message to folder
     */
    public function moveToFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $messageId = (int)($_POST['message_id'] ?? 0);
        $folderId = (int)($_POST['folder_id'] ?? 0);
        
        if (!$messageId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }
        
        // Check if message exists and get current owner
        $message = $this->getMessage($messageId);
        if (!$message) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message not found']);
            return;
        }
        
        // Check ownership
        if ($message->owned_by_admin_id && $message->owned_by_admin_id != $this->currentAdminId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message is owned by another admin']);
            return;
        }
        
        // Verify folder belongs to current admin
        if ($folderId > 0) {
            $sql = "SELECT id FROM admin_email_folders WHERE id = :folder_id AND admin_user_id = :admin_id";
            $this->db->query($sql);
            $this->db->bind(':folder_id', $folderId);
            $this->db->bind(':admin_id', $this->currentAdminId);
            
            if (!$this->db->single()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Invalid folder']);
                return;
            }
        }
        
        // Move message and take ownership
        $sql = "UPDATE messages SET folder_id = :folder_id, owned_by_admin_id = :admin_id WHERE id = :message_id";
        $this->db->query($sql);
        $this->db->bind(':folder_id', $folderId > 0 ? $folderId : null);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $this->db->bind(':message_id', $messageId);
        
        if ($this->db->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Message moved successfully']);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to move message']);
        }
    }
    
    /**
     * Set reminder for message
     */
    public function setReminder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $messageId = (int)($_POST['message_id'] ?? 0);
        $reminderTime = $_POST['reminder_time'] ?? '';
        $note = trim($_POST['note'] ?? '');
        
        if (!$messageId || !$reminderTime) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            return;
        }
        
        // Validate reminder time
        $reminderDateTime = date('Y-m-d H:i:s', strtotime($reminderTime));
        if (!$reminderDateTime || strtotime($reminderDateTime) <= time()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid reminder time']);
            return;
        }
        
        // Check if message exists
        $message = $this->getMessage($messageId);
        if (!$message) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message not found']);
            return;
        }
        
        // Create reminder
        $sql = "INSERT INTO message_reminders (message_id, admin_user_id, reminder_time, note) VALUES (:message_id, :admin_id, :reminder_time, :note)";
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $this->db->bind(':reminder_time', $reminderDateTime);
        $this->db->bind(':note', $note);
        
        if ($this->db->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Reminder set successfully']);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to set reminder']);
        }
    }
    
    /**
     * Get admin folders
     */
    private function getAdminFolders() {
        $sql = "SELECT * FROM admin_email_folders WHERE admin_user_id = :admin_id ORDER BY sort_order ASC";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        return $this->db->resultSet();
    }
    
    /**
     * Get messages for folder
     */
    private function getMessages($folderId, $sortBy, $sortOrder, $page, $search) {
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE m.to_user_id = :admin_id";
        $params = [':admin_id' => $this->currentAdminId];
        
        if ($folderId) {
            $whereClause .= " AND m.folder_id = :folder_id";
            $params[':folder_id'] = $folderId;
        } else {
            $whereClause .= " AND (m.folder_id IS NULL OR m.folder_id = 0)";
        }
        
        if ($search) {
            $whereClause .= " AND (m.subject LIKE :search OR m.message LIKE :search OR m.ticket_number LIKE :search)";
            $params[':search'] = '%' . $search . '%';
        }
        
        $allowedSortFields = ['created_at', 'subject', 'is_read', 'priority', 'ticket_number'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "SELECT m.*, u.name as sender_name, f.name as folder_name, f.color as folder_color
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN admin_email_folders f ON m.folder_id = f.id
                {$whereClause}
                ORDER BY m.{$sortBy} {$sortOrder}
                LIMIT {$limit} OFFSET {$offset}";
        
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind($key, $value);
        }
        
        return $this->db->resultSet();
    }

    /**
     * Get individual message
     */
    private function getMessage($messageId) {
        $sql = "SELECT m.*, u.name as sender_name, f.name as folder_name, f.color as folder_color,
                       owner.name as owner_name
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN admin_email_folders f ON m.folder_id = f.id
                LEFT JOIN users owner ON m.owned_by_admin_id = owner.id
                WHERE m.id = :message_id AND m.to_user_id = :admin_id";

        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':admin_id', $this->currentAdminId);
        return $this->db->single();
    }

    /**
     * Handle message ownership
     */
    private function handleMessageOwnership($messageId) {
        $sql = "UPDATE messages SET owned_by_admin_id = :admin_id WHERE id = :message_id AND (owned_by_admin_id IS NULL OR owned_by_admin_id = :admin_id)";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $this->db->bind(':message_id', $messageId);
        $this->db->execute();
    }

    /**
     * Mark message as read
     */
    private function markMessageAsRead($messageId) {
        $sql = "UPDATE messages SET is_read = 1, read_at = NOW() WHERE id = :message_id AND to_user_id = :admin_id";
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $this->db->execute();
    }

    /**
     * Get message thread
     */
    private function getMessageThread($message) {
        if (!$message->ticket_number) {
            return [$message];
        }

        return $this->ticketService->getTicketMessages($message->ticket_number);
    }

    /**
     * Get email templates
     */
    private function getEmailTemplates() {
        $sql = "SELECT * FROM email_templates WHERE is_active = 1 ORDER BY name ASC";
        $this->db->query($sql);
        return $this->db->resultSet();
    }

    /**
     * Get message attachments
     */
    private function getMessageAttachments($messageId) {
        $sql = "SELECT * FROM message_attachments WHERE message_id = :message_id ORDER BY filename ASC";
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        return $this->db->resultSet();
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats() {
        // Unread count
        $sql = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = :admin_id AND is_read = 0";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $unreadCount = $this->db->single()->count ?? 0;

        // Today's emails
        $sql = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = :admin_id AND DATE(created_at) = CURDATE()";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $todayCount = $this->db->single()->count ?? 0;

        // Pending reminders
        $sql = "SELECT COUNT(*) as count FROM message_reminders WHERE admin_user_id = :admin_id AND is_sent = 0 AND reminder_time <= NOW()";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $pendingReminders = $this->db->single()->count ?? 0;

        // Owned messages
        $sql = "SELECT COUNT(*) as count FROM messages WHERE owned_by_admin_id = :admin_id";
        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $ownedCount = $this->db->single()->count ?? 0;

        return [
            'unread' => $unreadCount,
            'today' => $todayCount,
            'pending_reminders' => $pendingReminders,
            'owned' => $ownedCount
        ];
    }

    /**
     * Get pending reminders
     */
    private function getPendingReminders() {
        $sql = "SELECT r.*, m.subject, m.ticket_number
                FROM message_reminders r
                JOIN messages m ON r.message_id = m.id
                WHERE r.admin_user_id = :admin_id AND r.is_sent = 0 AND r.reminder_time <= NOW()
                ORDER BY r.reminder_time ASC
                LIMIT 10";

        $this->db->query($sql);
        $this->db->bind(':admin_id', $this->currentAdminId);
        return $this->db->resultSet();
    }

    /**
     * Generate CSRF token
     */
    private function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Verify CSRF token
     */
    protected function verifyCsrfToken($token = null, $source = 'post') {
        return parent::verifyCsrfToken($token, $source);
    }

    /**
     * Set flash message
     */
    protected function setFlashMessage($name, $message, $type = 'success') {
        parent::setFlashMessage($name, $message, $type);
    }

    /**
     * Delete folder
     */
    public function deleteFolder() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $folderId = (int)($_POST['folder_id'] ?? 0);

        if (!$folderId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid folder ID']);
            return;
        }

        // Check if folder exists and belongs to current admin
        $sql = "SELECT id, name, is_system FROM admin_email_folders WHERE id = :folder_id AND admin_user_id = :admin_id";
        $this->db->query($sql);
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $folder = $this->db->single();

        if (!$folder) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Folder not found']);
            return;
        }

        if ($folder->is_system) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Cannot delete system folder']);
            return;
        }

        // Move messages to inbox (folder_id = NULL)
        $sql = "UPDATE messages SET folder_id = NULL WHERE folder_id = :folder_id AND owned_by_admin_id = :admin_id";
        $this->db->query($sql);
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $this->db->execute();

        // Delete folder
        $sql = "DELETE FROM admin_email_folders WHERE id = :folder_id AND admin_user_id = :admin_id";
        $this->db->query($sql);
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':admin_id', $this->currentAdminId);

        if ($this->db->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Folder deleted successfully']);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to delete folder']);
        }
    }

    /**
     * Bulk mark as read
     */
    public function bulkMarkAsRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $messageIds = $_POST['message_ids'] ?? [];

        if (empty($messageIds) || !is_array($messageIds)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'No messages selected']);
            return;
        }

        $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
        $sql = "UPDATE messages SET is_read = 1, read_at = NOW()
                WHERE id IN ($placeholders) AND to_user_id = ?";

        $this->db->query($sql);
        foreach ($messageIds as $index => $messageId) {
            $this->db->bind($index + 1, (int)$messageId);
        }
        $this->db->bind(count($messageIds) + 1, $this->currentAdminId);

        if ($this->db->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Messages marked as read']);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to mark messages as read']);
        }
    }

    /**
     * Transfer message ownership
     */
    public function transferOwnership() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $messageId = (int)($_POST['message_id'] ?? 0);
        $newOwnerId = (int)($_POST['new_owner_id'] ?? 0);

        if (!$messageId || !$newOwnerId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            return;
        }

        // Verify new owner is an admin
        $sql = "SELECT id, name FROM users WHERE id = :user_id AND role = 'admin' AND status = 'active'";
        $this->db->query($sql);
        $this->db->bind(':user_id', $newOwnerId);
        $newOwner = $this->db->single();

        if (!$newOwner) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid new owner']);
            return;
        }

        // Check current ownership
        $message = $this->getMessage($messageId);
        if (!$message) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Message not found']);
            return;
        }

        if ($message->owned_by_admin_id && $message->owned_by_admin_id != $this->currentAdminId) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'You do not own this message']);
            return;
        }

        // Transfer ownership
        $sql = "UPDATE messages SET owned_by_admin_id = :new_owner, folder_id = NULL WHERE id = :message_id";
        $this->db->query($sql);
        $this->db->bind(':new_owner', $newOwnerId);
        $this->db->bind(':message_id', $messageId);

        if ($this->db->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Ownership transferred to ' . $newOwner->name]);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to transfer ownership']);
        }
    }

    /**
     * Get admin users for ownership transfer
     */
    public function getAdminUsers() {
        $sql = "SELECT id, name FROM users WHERE role = 'admin' AND status = 'active' AND id != :current_admin ORDER BY name";
        $this->db->query($sql);
        $this->db->bind(':current_admin', $this->currentAdminId);
        $admins = $this->db->resultSet();

        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'admins' => $admins]);
    }

    /**
     * Download attachment
     */
    public function downloadAttachment($attachmentId) {
        $sql = "SELECT a.*, m.to_user_id FROM message_attachments a
                JOIN messages m ON a.message_id = m.id
                WHERE a.id = :attachment_id AND m.to_user_id = :admin_id";

        $this->db->query($sql);
        $this->db->bind(':attachment_id', $attachmentId);
        $this->db->bind(':admin_id', $this->currentAdminId);
        $attachment = $this->db->single();

        if (!$attachment) {
            $this->setFlashMessage('error', 'Attachment not found');
            $this->redirect('admin/email_dashboard');
            return;
        }

        $filePath = $attachment->file_path;

        if (!file_exists($filePath)) {
            $this->setFlashMessage('error', 'Attachment file not found on disk');
            $this->redirect('admin/email_dashboard');
            return;
        }

        // Set headers for download
        header('Content-Type: ' . $attachment->mime_type);
        header('Content-Disposition: attachment; filename="' . $attachment->filename . '"');
        header('Content-Length: ' . filesize($filePath));

        // Output file
        readfile($filePath);
        exit;
    }

    /**
     * Redirect helper
     */
    protected function redirect($url) {
        header('Location: ' . URLROOT . '/' . $url);
        exit;
    }
}
