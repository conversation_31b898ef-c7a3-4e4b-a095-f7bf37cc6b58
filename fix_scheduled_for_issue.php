<?php
/**
 * Fix Scheduled For Issue
 * 
 * Fixes the future scheduled_for times and tests the solution
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🔧 Fix Scheduled For Issue</h1>";

try {
    $db = new Database();
    
    echo "<h2>🎯 Issue Analysis and Fix</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Root Cause Identified:</h3>";
    echo "<ul>";
    echo "<li>❌ <strong>Problem:</strong> UnifiedMessageModel::addToNotificationQueue() was NOT setting scheduled_for field</li>";
    echo "<li>🔍 <strong>Result:</strong> Database default value was being used for scheduled_for</li>";
    echo "<li>⏰ <strong>Effect:</strong> Notifications scheduled for future times, preventing immediate processing</li>";
    echo "<li>✅ <strong>Solution:</strong> Explicitly set scheduled_for = NOW() in INSERT statement</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔄 Code Fix Applied</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📝 Before (Broken):</h3>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
    echo "INSERT INTO notification_queue \n";
    echo "(user_id, notification_type, subject, message, status, created_at)\n";
    echo "VALUES (:user_id, :type, :subject, :message, 'pending', NOW())\n\n";
    echo "// scheduled_for was NOT being set - used database default!";
    echo "</pre>";
    
    echo "<h3>✅ After (Fixed):</h3>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
    echo "INSERT INTO notification_queue \n";
    echo "(user_id, notification_type, subject, message, status, scheduled_for, created_at, updated_at)\n";
    echo "VALUES (:user_id, :type, :subject, :message, 'pending', NOW(), NOW(), NOW())\n\n";
    echo "// scheduled_for explicitly set to NOW() - ready for immediate processing!";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>🧹 Fix Existing Future-Scheduled Notifications</h2>";
    
    // Check for notifications scheduled in the future
    $db->query("SELECT id, user_id, subject, scheduled_for, created_at FROM notification_queue WHERE status = 'pending' AND scheduled_for > NOW()");
    $db->execute();
    $futureNotifications = $db->resultSet();
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🕐 Future-Scheduled Notifications:</h3>";
    
    if (empty($futureNotifications)) {
        echo "<p style='color: green;'>✅ No notifications scheduled in the future</p>";
    } else {
        echo "<p><strong>Found " . count($futureNotifications) . " notification(s) scheduled in the future</strong></p>";
        
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Subject</th><th>Scheduled For</th><th>Minutes in Future</th></tr>";
        
        $currentTime = time();
        foreach ($futureNotifications as $notification) {
            $scheduledTime = strtotime($notification->scheduled_for);
            $minutesInFuture = round(($scheduledTime - $currentTime) / 60);
            
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>" . htmlspecialchars(substr($notification->subject, 0, 40)) . "...</td>";
            echo "<td>{$notification->scheduled_for}</td>";
            echo "<td style='color: red;'>+{$minutesInFuture} min</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<form method='post' style='margin: 15px 0;'>";
        echo "<button type='submit' name='fix_future_notifications' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Fix All Future-Scheduled Notifications</button>";
        echo "</form>";
        
        if (isset($_POST['fix_future_notifications'])) {
            echo "<h4>🔄 Fixing Results:</h4>";
            
            $db->query("UPDATE notification_queue SET scheduled_for = NOW(), updated_at = NOW() WHERE status = 'pending' AND scheduled_for > NOW()");
            $success = $db->execute();
            $affectedRows = $db->rowCount();
            
            if ($success) {
                echo "<p style='color: green;'>✅ Successfully fixed {$affectedRows} notification(s)</p>";
                echo "<p><strong>All notifications are now scheduled for immediate processing!</strong></p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to fix notifications</p>";
            }
        }
    }
    echo "</div>";
    
    echo "<h2>🧪 Test Email Processing</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📧 Test Notification Processing:</h3>";
    
    // Check current pending notifications
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE status = 'pending' AND notification_type = 'email' AND scheduled_for <= NOW()");
    $db->execute();
    $readyEmails = $db->single();
    $readyCount = $readyEmails->count ?? 0;
    
    echo "<p><strong>Emails ready for processing:</strong> {$readyCount}</p>";
    
    if ($readyCount > 0) {
        echo "<form method='post'>";
        echo "<button type='submit' name='test_processing' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Email Processing Now</button>";
        echo "</form>";
        
        if (isset($_POST['test_processing'])) {
            echo "<h4>🔄 Processing Test Results:</h4>";
            
            try {
                // Load required classes
                require_once APPROOT . '/models/NotificationService.php';
                require_once APPROOT . '/models/NotificationModel.php';
                require_once APPROOT . '/models/EmailService.php';
                require_once APPROOT . '/models/SettingsModel.php';
                
                $notificationService = new NotificationService();
                $results = $notificationService->processPendingNotifications(5);
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Processing Results:</strong><br>";
                echo "Processed: {$results['processed']}<br>";
                echo "Sent: {$results['sent']}<br>";
                echo "Failed: {$results['failed']}<br>";
                
                if (!empty($results['errors'])) {
                    echo "<strong>Errors:</strong><br>";
                    foreach ($results['errors'] as $error) {
                        echo "• " . htmlspecialchars($error) . "<br>";
                    }
                }
                echo "</div>";
                
                if ($results['processed'] > 0) {
                    echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Notifications are now being processed correctly!</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Still no notifications processed. Check other settings.</p>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
        }
    } else {
        echo "<p>No emails ready for processing. Submit a contact form to test.</p>";
    }
    echo "</div>";
    
    echo "<h2>🎯 Verification Steps</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 How to Verify the Fix:</h3>";
    echo "<ol>";
    echo "<li><strong>Copy updated file to server:</strong> models/UnifiedMessageModel.php</li>";
    echo "<li><strong>Fix existing future notifications:</strong> Use the button above</li>";
    echo "<li><strong>Test contact form:</strong> Submit a new contact form</li>";
    echo "<li><strong>Check notification timing:</strong> New notifications should have scheduled_for = NOW()</li>";
    echo "<li><strong>Verify processing:</strong> Emails should be processed immediately by cron or manual processing</li>";
    echo "</ol>";
    
    echo "<h3>🎯 Expected Results:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>New notifications:</strong> scheduled_for set to current time</li>";
    echo "<li>✅ <strong>Immediate processing:</strong> Emails processed within 2-5 minutes by cron</li>";
    echo "<li>✅ <strong>No future scheduling:</strong> All notifications ready for immediate processing</li>";
    echo "<li>✅ <strong>Contact form emails:</strong> Delivered promptly to admins</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Status Summary</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📈 Notification Queue Status:</h3>";
    
    // Get comprehensive status
    $db->query("SELECT 
                    status,
                    notification_type,
                    COUNT(*) as count,
                    MIN(created_at) as oldest,
                    MAX(created_at) as newest
                FROM notification_queue 
                GROUP BY status, notification_type 
                ORDER BY status, notification_type");
    $db->execute();
    $statusSummary = $db->resultSet();
    
    if (!empty($statusSummary)) {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Status</th><th>Type</th><th>Count</th><th>Oldest</th><th>Newest</th></tr>";
        
        foreach ($statusSummary as $status) {
            $statusColor = $status->status === 'pending' ? 'orange' : ($status->status === 'sent' ? 'green' : 'red');
            echo "<tr>";
            echo "<td style='color: {$statusColor};'><strong>{$status->status}</strong></td>";
            echo "<td>{$status->notification_type}</td>";
            echo "<td>{$status->count}</td>";
            echo "<td>{$status->oldest}</td>";
            echo "<td>{$status->newest}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No notifications in queue</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Fix Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Scheduled for fix completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
