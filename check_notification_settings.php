<?php
/**
 * Check Notification Settings
 * 
 * This script checks if notification settings are properly configured
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationModel.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$notificationModel = new NotificationModel();
$userId = $_SESSION['user_id'];

echo "<h2>Notification Settings Check</h2>";

try {
    // Check global settings
    echo "<h3>Global Notification Settings</h3>";
    $globalSettings = $notificationModel->getNotificationSettings();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    $settings = ['email_enabled', 'sms_enabled', 'push_enabled', 'toast_enabled'];
    foreach ($settings as $setting) {
        $value = $globalSettings[$setting] ?? 'NOT SET';
        $status = ($value === '1' || $value === 1 || $value === true) ? '✅ Enabled' : '❌ Disabled';
        echo "<tr><td>$setting</td><td>$value</td><td>$status</td></tr>";
    }
    echo "</table>";
    
    // Check user preferences
    echo "<h3>User Notification Preferences (User ID: $userId)</h3>";
    $userPrefs = $notificationModel->getUserNotificationPreferences($userId);
    
    if ($userPrefs) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Preference</th><th>Value</th><th>Status</th></tr>";
        
        $prefs = ['email_notifications', 'sms_notifications', 'push_notifications', 'toast_notifications'];
        foreach ($prefs as $pref) {
            $value = $userPrefs->$pref ?? 'NOT SET';
            $status = ($value === '1' || $value === 1 || $value === true) ? '✅ Enabled' : '❌ Disabled';
            echo "<tr><td>$pref</td><td>$value</td><td>$status</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No user preferences found!</p>";
    }
    
    // Check if toast notifications will work
    echo "<h3>Toast Notification Status</h3>";
    $toastGlobalEnabled = ($globalSettings['toast_enabled'] ?? false) == 1;
    $toastUserEnabled = ($userPrefs->toast_notifications ?? false) == 1;
    
    echo "<p><strong>Global toast enabled:</strong> " . ($toastGlobalEnabled ? '✅ Yes' : '❌ No') . "</p>";
    echo "<p><strong>User toast enabled:</strong> " . ($toastUserEnabled ? '✅ Yes' : '❌ No') . "</p>";
    echo "<p><strong>Toast notifications will work:</strong> " . ($toastGlobalEnabled && $toastUserEnabled ? '✅ Yes' : '❌ No') . "</p>";
    
    if (!$toastGlobalEnabled) {
        echo "<p style='color: red;'><strong>Issue:</strong> Toast notifications are disabled globally. Enable them in Admin → Notification Settings.</p>";
    }
    
    if (!$toastUserEnabled) {
        echo "<p style='color: red;'><strong>Issue:</strong> Toast notifications are disabled for your user. Enable them in your user preferences.</p>";
    }
    
    // Fix buttons
    echo "<h3>Quick Fixes</h3>";
    
    if (isset($_POST['enable_global_toast'])) {
        try {
            $db = new Database();
            $db->query('UPDATE notification_settings SET setting_value = "1" WHERE setting_key = "toast_enabled"');
            $db->execute();
            echo "<p style='color: green;'>✅ Global toast notifications enabled!</p>";
            echo "<script>setTimeout(() => location.reload(), 1000);</script>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error enabling global toast: " . $e->getMessage() . "</p>";
        }
    }
    
    if (isset($_POST['enable_user_toast'])) {
        try {
            $db = new Database();
            $db->query('UPDATE user_notification_preferences SET toast_notifications = 1 WHERE user_id = :user_id');
            $db->bind(':user_id', $userId);
            $db->execute();
            echo "<p style='color: green;'>✅ User toast notifications enabled!</p>";
            echo "<script>setTimeout(() => location.reload(), 1000);</script>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error enabling user toast: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<form method='post' style='display: inline-block; margin-right: 10px;'>";
    if (!$toastGlobalEnabled) {
        echo "<button type='submit' name='enable_global_toast' class='btn btn-warning'>Enable Global Toast Notifications</button>";
    }
    echo "</form>";
    
    echo "<form method='post' style='display: inline-block;'>";
    if (!$toastUserEnabled) {
        echo "<button type='submit' name='enable_user_toast' class='btn btn-info'>Enable User Toast Notifications</button>";
    }
    echo "</form>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/test_toast_direct.php'>→ Test Toast Notifications</a></p>";
echo "<p><a href='/admin/dashboard'>← Back to Admin Dashboard</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Notification Settings Check</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
</body>
</html>