<?php
/**
 * Simple push notification test using Firebase Cloud Messaging API directly
 * This bypasses VAPID and uses FCM's legacy API which is simpler
 */

// Prevent direct access without security key
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'simple_push_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';

echo "=== SIMPLE PUSH NOTIFICATION TEST ===\n\n";

try {
    $userId = 3;
    
    echo "1. GETTING PUSH SUBSCRIPTIONS\n";
    
    $db = new Database();
    $db->query("SELECT * FROM push_subscriptions WHERE user_id = ? AND active = 1 ORDER BY created_at DESC LIMIT 3");
    $db->bind(1, $userId);
    $db->execute();
    $subscriptions = $db->resultSet();
    
    echo "   Found " . count($subscriptions) . " subscriptions\n";
    
    if (empty($subscriptions)) {
        echo "   No subscriptions found\n";
        exit;
    }
    
    echo "\n2. EXTRACTING FCM TOKENS\n";
    
    $fcmTokens = [];
    foreach ($subscriptions as $sub) {
        // Extract FCM token from endpoint
        if (strpos($sub->endpoint, 'fcm.googleapis.com') !== false) {
            $parts = explode('/', $sub->endpoint);
            $token = end($parts);
            $fcmTokens[] = $token;
            echo "   Token: " . substr($token, 0, 20) . "...\n";
        }
    }
    
    if (empty($fcmTokens)) {
        echo "   No FCM tokens found\n";
        exit;
    }
    
    echo "\n3. SENDING VIA FCM LEGACY API\n";
    echo "   Note: This uses FCM's legacy API which doesn't require VAPID\n";
    
    // You would need an FCM Server Key for this approach
    // For now, let's try a different method
    
    echo "\n4. ALTERNATIVE: USING BROWSER NOTIFICATION API\n";
    echo "   Creating a test that uses browser's native notification API\n";
    
    // Create a simple HTML page that tests browser notifications
    $testHtml = '<!DOCTYPE html>
<html>
<head>
    <title>Browser Notification Test</title>
</head>
<body>
    <h1>🔔 Browser Notification Test</h1>
    <button onclick="testNotification()">Test Local Notification</button>
    <button onclick="testServiceWorkerNotification()">Test Service Worker Notification</button>
    
    <script>
        function testNotification() {
            if ("Notification" in window) {
                if (Notification.permission === "granted") {
                    new Notification("Test Notification", {
                        body: "This is a local browser notification test",
                        icon: "/public/images/icons/icon-192x192.png"
                    });
                } else if (Notification.permission !== "denied") {
                    Notification.requestPermission().then(function (permission) {
                        if (permission === "granted") {
                            new Notification("Test Notification", {
                                body: "This is a local browser notification test",
                                icon: "/public/images/icons/icon-192x192.png"
                            });
                        }
                    });
                }
            }
        }
        
        function testServiceWorkerNotification() {
            if ("serviceWorker" in navigator) {
                navigator.serviceWorker.ready.then(function(registration) {
                    registration.showNotification("Service Worker Test", {
                        body: "This is a service worker notification test",
                        icon: "/public/images/icons/icon-192x192.png",
                        badge: "/public/images/icons/badge-72x72.png",
                        tag: "sw-test"
                    });
                });
            }
        }
        
        // Auto-test on load
        setTimeout(testNotification, 1000);
    </script>
</body>
</html>';
    
    file_put_contents(APPROOT . '/browser_notification_test.html', $testHtml);
    echo "   Created browser_notification_test.html\n";
    echo "   Visit: https://events.rowaneliterides.com/browser_notification_test.html\n";
    
    echo "\n5. RECOMMENDATION: USE ONESIGNAL\n";
    echo "   Since VAPID is complex, I recommend using OneSignal:\n";
    echo "   - Free push notification service\n";
    echo "   - Handles all the VAPID complexity\n";
    echo "   - Easy to integrate\n";
    echo "   - Works with your existing setup\n";
    echo "   - Visit: https://onesignal.com/\n";
    
    echo "\n6. ONESIGNAL INTEGRATION STEPS\n";
    echo "   1. Sign up at OneSignal.com\n";
    echo "   2. Create a new app\n";
    echo "   3. Get your App ID and API Key\n";
    echo "   4. Replace your current push system with OneSignal\n";
    echo "   5. OneSignal handles all VAPID/FCM complexity\n";
    
    echo "\n7. IMMEDIATE WORKAROUND\n";
    echo "   For now, you can:\n";
    echo "   - Disable push notifications temporarily\n";
    echo "   - Use only toast notifications (which work)\n";
    echo "   - Use email notifications\n";
    echo "   - Implement OneSignal for proper push notifications\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== END TEST ===\n";
?>
