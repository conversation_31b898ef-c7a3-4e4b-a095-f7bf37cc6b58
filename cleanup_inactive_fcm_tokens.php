<?php
/**
 * Cleanup Inactive FCM Tokens
 * 
 * Removes inactive FCM tokens that are no longer needed
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🧹 Cleanup Inactive FCM Tokens</h1>";

try {
    $db = new Database();
    
    // Check if fcm_tokens table exists
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    $tableExists = $db->single();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ fcm_tokens table does not exist!</p>";
        exit;
    }
    
    echo "<h2>📊 Current Status</h2>";
    
    // Get current counts
    $db->query("SELECT 
                    COUNT(*) as total,
                    SUM(active) as active,
                    COUNT(*) - SUM(active) as inactive
                FROM fcm_tokens");
    $counts = $db->single();
    
    echo "<p><strong>Total tokens:</strong> {$counts->total}</p>";
    echo "<p><strong>Active tokens:</strong> {$counts->active}</p>";
    echo "<p><strong>Inactive tokens:</strong> {$counts->inactive}</p>";
    
    if ($counts->inactive == 0) {
        echo "<p style='color: green;'>✅ No inactive tokens to clean up!</p>";
        exit;
    }
    
    echo "<h2>🔍 Inactive Tokens by User</h2>";
    
    // Show inactive tokens by user
    $db->query("SELECT user_id, COUNT(*) as inactive_count 
                FROM fcm_tokens 
                WHERE active = 0 
                GROUP BY user_id 
                ORDER BY inactive_count DESC");
    $inactiveByUser = $db->resultSet();
    
    if (!empty($inactiveByUser)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>User ID</th><th>Inactive Tokens</th></tr>";
        foreach ($inactiveByUser as $user) {
            echo "<tr><td>{$user->user_id}</td><td>{$user->inactive_count}</td></tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>🗑️ Cleanup Process</h2>";
    
    // Show what will be deleted
    $db->query("SELECT id, user_id, LEFT(token, 20) as token_preview, created_at, last_used 
                FROM fcm_tokens 
                WHERE active = 0 
                ORDER BY user_id, created_at");
    $inactiveTokens = $db->resultSet();
    
    if (!empty($inactiveTokens)) {
        echo "<p>The following inactive tokens will be deleted:</p>";
        echo "<details><summary>Show inactive tokens to be deleted</summary>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Token Preview</th><th>Created</th><th>Last Used</th></tr>";
        foreach ($inactiveTokens as $token) {
            echo "<tr>";
            echo "<td>{$token->id}</td>";
            echo "<td>{$token->user_id}</td>";
            echo "<td>{$token->token_preview}...</td>";
            echo "<td>{$token->created_at}</td>";
            echo "<td>" . ($token->last_used ?? 'Never') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</details>";
        
        echo "<h3>🚀 Performing Cleanup</h3>";
        
        // Delete inactive tokens
        $db->query("DELETE FROM fcm_tokens WHERE active = 0");
        $deletedCount = $db->rowCount();
        
        if ($db->execute()) {
            echo "<p style='color: green;'>✅ Successfully deleted {$deletedCount} inactive FCM tokens!</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to delete inactive tokens</p>";
        }
        
        echo "<h2>📊 Final Status</h2>";
        
        // Get final counts
        $db->query("SELECT 
                        COUNT(*) as total,
                        SUM(active) as active,
                        COUNT(*) - SUM(active) as inactive
                    FROM fcm_tokens");
        $finalCounts = $db->single();
        
        echo "<p><strong>Total tokens:</strong> {$finalCounts->total}</p>";
        echo "<p><strong>Active tokens:</strong> {$finalCounts->active}</p>";
        echo "<p><strong>Inactive tokens:</strong> {$finalCounts->inactive}</p>";
        
        if ($finalCounts->inactive == 0) {
            echo "<p style='color: green;'>✅ All inactive tokens have been cleaned up!</p>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ No inactive tokens found to delete</p>";
    }
    
    echo "<h2>💡 Why Clean Up Inactive Tokens?</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Benefits of removing inactive tokens:</strong></p>";
    echo "<ul>";
    echo "<li>🚀 <strong>Faster queries:</strong> Smaller fcm_tokens table = faster lookups</li>";
    echo "<li>💾 <strong>Less storage:</strong> Reduces database size</li>";
    echo "<li>🔧 <strong>Easier debugging:</strong> Only see tokens that actually matter</li>";
    echo "<li>🛡️ <strong>Security:</strong> Remove old tokens that might be compromised</li>";
    echo "<li>📊 <strong>Accurate metrics:</strong> Better understanding of active users</li>";
    echo "</ul>";
    echo "<p><strong>When tokens become inactive:</strong></p>";
    echo "<ul>";
    echo "<li>User uninstalls PWA</li>";
    echo "<li>User clears browser data</li>";
    echo "<li>Token expires or becomes invalid</li>";
    echo "<li>User gets a new device/browser</li>";
    echo "<li>Firebase marks token as invalid</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Cleanup completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
