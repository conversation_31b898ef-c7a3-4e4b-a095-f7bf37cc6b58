<?php
/**
 * Email Processing Engine
 * 
 * Handles email parsing, ticket number extraction, threading, spam filtering,
 * and integration with the unified messaging system
 */
class EmailProcessingEngine {
    private $db;
    private $settingsModel;
    private $unifiedMessageModel;
    private $ticketService;
    private $spamFilteringEnabled;
    private $maxEmailSizeMB;
    private $attachmentEnabled;
    private $maxAttachmentSizeMB;

    public function __construct() {
        $this->db = new Database();
        require_once APPROOT . '/models/SettingsModel.php';
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        require_once APPROOT . '/models/EnhancedTicketService.php';

        $this->settingsModel = new SettingsModel();
        $this->unifiedMessageModel = new UnifiedMessageModel();
        $this->ticketService = new EnhancedTicketService();
        $this->loadSettings();
    }
    
    /**
     * Load processing settings
     */
    private function loadSettings() {
        $this->spamFilteringEnabled = $this->settingsModel->getSetting('email_spam_filtering', '1') === '1';
        $this->maxEmailSizeMB = (int)$this->settingsModel->getSetting('email_max_size_mb', '10');
        $this->attachmentEnabled = $this->settingsModel->getSetting('email_attachment_enabled', '1') === '1';
        $this->maxAttachmentSizeMB = (int)$this->settingsModel->getSetting('email_attachment_max_size_mb', '5');
    }
    
    /**
     * Process a batch of emails
     */
    public function processEmails($emails) {
        $results = [
            'processed' => 0,
            'failed' => 0,
            'ignored' => 0,
            'rejected' => 0,
            'errors' => [],
            'results' => [] // Detailed results for each email
        ];

        foreach ($emails as $index => $email) {
            try {
                $result = $this->processEmail($email);

                // Store detailed result for this email
                $results['results'][$index] = $result;

                if ($result['status'] === 'processed') {
                    $results['processed']++;
                } elseif ($result['status'] === 'ignored') {
                    $results['ignored']++;
                } elseif ($result['status'] === 'rejected') {
                    $results['rejected']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = $result['message'];
                }

            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Email processing error: " . $e->getMessage();
                $results['results'][$index] = ['status' => 'failed', 'message' => $e->getMessage()];
                error_log("EmailProcessingEngine::processEmails - Error: " . $e->getMessage());
            }
        }

        return $results;
    }
    
    /**
     * Process individual email
     */
    public function processEmail($email) {
        try {
            // Log email processing attempt
            $logId = $this->logEmailProcessing($email, 'pending');
            
            // Check if already processed
            if ($this->isEmailAlreadyProcessed($email['message_id'])) {
                $this->updateProcessingLog($logId, 'ignored', 'Email already processed');
                return ['status' => 'ignored', 'message' => 'Email already processed'];
            }
            
            // Validate email size
            if ($this->maxEmailSizeMB > 0 && $email['size'] > ($this->maxEmailSizeMB * 1024 * 1024)) {
                $this->updateProcessingLog($logId, 'ignored', 'Email too large');
                return ['status' => 'ignored', 'message' => 'Email too large'];
            }
            
            // Check for auto-reply
            if ($this->isAutoReply($email)) {
                $this->updateProcessingLog($logId, 'ignored', 'Auto-reply detected');
                return ['status' => 'ignored', 'message' => 'Auto-reply ignored'];
            }
            
            // Spam filtering
            if ($this->spamFilteringEnabled) {
                $spamScore = $this->calculateSpamScore($email);
                if ($spamScore > 0.7) {
                    $this->updateProcessingLog($logId, 'ignored', 'Spam detected (score: ' . $spamScore . ')');
                    return ['status' => 'ignored', 'message' => 'Spam detected'];
                }
            }

            // SECURITY CHECK - Check original subject BEFORE any processing
            error_log("SECURITY-CHECK: Original email subject: " . $email['subject']);
            try {
                $this->performSecurityCheck($email['subject']);
                error_log("SECURITY-CHECK: Security check passed");
            } catch (Exception $e) {
                error_log("SECURITY-CHECK: Security check failed: " . $e->getMessage());
                $this->updateProcessingLog($logId, 'rejected', $e->getMessage());
                return ['status' => 'rejected', 'message' => $e->getMessage()];
            }

            // Extract or generate ticket number (may throw exception for security threats)
            file_put_contents(APPROOT . '/logs/email_debug.log', date('Y-m-d H:i:s') . " - PROCESSING EMAIL: " . $email['subject'] . "\n", FILE_APPEND);

            try {
                $ticketNumber = $this->extractOrGenerateTicketNumber($email);
                file_put_contents(APPROOT . '/logs/email_debug.log', date('Y-m-d H:i:s') . " - GOT TICKET: " . $ticketNumber . "\n", FILE_APPEND);
            } catch (Exception $e) {
                // Email rejected due to security threat
                file_put_contents(APPROOT . '/logs/email_debug.log', date('Y-m-d H:i:s') . " - REJECTED: " . $e->getMessage() . "\n", FILE_APPEND);
                $this->updateProcessingLog($logId, 'rejected', $e->getMessage());
                return ['status' => 'rejected', 'message' => $e->getMessage()];
            }

            // Find parent message for threading
            $parentMessageId = $this->findParentMessage($email, $ticketNumber);
            error_log("EmailProcessingEngine::processEmail - Parent message ID for ticket " . $ticketNumber . ": " . ($parentMessageId ?? 'NULL'));
            
            // Determine sender user
            $senderUserId = $this->findSenderUser($email['from_email']);
            
            // Get admin users for notification
            $adminUsers = $this->getAdminUsers();
            
            // Process attachments
            $attachments = [];
            if ($this->attachmentEnabled && !empty($email['attachments'])) {
                $attachments = $this->processAttachments($email, $logId);
            }
            
            // Create message for each admin
            $messageIds = [];
            foreach ($adminUsers as $admin) {
                $messageId = $this->createMessage($email, $ticketNumber, $parentMessageId, $senderUserId, $admin->id);
                if ($messageId) {
                    $messageIds[] = $messageId;
                    
                    // Link attachments to message
                    foreach ($attachments as $attachment) {
                        $this->linkAttachmentToMessage($messageId, $attachment);
                    }
                }
            }
            
            if (!empty($messageIds)) {
                $this->updateProcessingLog($logId, 'processed', 'Created ' . count($messageIds) . ' messages', $messageIds[0], $ticketNumber);
                
                // Send auto-reply if enabled
                if ($this->settingsModel->getSetting('email_auto_reply_enabled', '1') === '1') {
                    $this->sendAutoReply($email, $ticketNumber);
                }
                
                return ['status' => 'processed', 'message' => 'Email processed successfully', 'ticket' => $ticketNumber];
            } else {
                $this->updateProcessingLog($logId, 'failed', 'Failed to create messages');
                return ['status' => 'failed', 'message' => 'Failed to create messages'];
            }
            
        } catch (Exception $e) {
            if (isset($logId)) {
                $this->updateProcessingLog($logId, 'failed', $e->getMessage());
            }
            throw $e;
        }
    }
    
    /**
     * Log email processing attempt
     */
    private function logEmailProcessing($email, $status) {
        $sql = "INSERT INTO email_processing_log 
                (email_message_id, sender_email, subject, processing_status, email_size, attachment_count, is_auto_reply)
                VALUES (:message_id, :sender, :subject, :status, :size, :attachments, :auto_reply)";
        
        $this->db->query($sql);
        $this->db->bind(':message_id', $email['message_id']);
        $this->db->bind(':sender', $email['from_email']);
        $this->db->bind(':subject', $email['subject']);
        $this->db->bind(':status', $status);
        $this->db->bind(':size', $email['size']);
        $this->db->bind(':attachments', count($email['attachments']));
        $this->db->bind(':auto_reply', $this->isAutoReply($email) ? 1 : 0);
        
        $this->db->execute();
        return $this->db->lastInsertId();
    }
    
    /**
     * Update processing log
     */
    private function updateProcessingLog($logId, $status, $message = null, $messageId = null, $ticketNumber = null) {
        $sql = "UPDATE email_processing_log 
                SET processing_status = :status, error_message = :message, message_id = :msg_id, 
                    ticket_number = :ticket, processed_at = NOW()
                WHERE id = :id";
        
        $this->db->query($sql);
        $this->db->bind(':status', $status);
        $this->db->bind(':message', $message);
        $this->db->bind(':msg_id', $messageId);
        $this->db->bind(':ticket', $ticketNumber);
        $this->db->bind(':id', $logId);
        
        return $this->db->execute();
    }
    
    /**
     * Check if email already processed
     */
    private function isEmailAlreadyProcessed($messageId) {
        $sql = "SELECT id FROM email_processing_log WHERE email_message_id = :message_id AND processing_status = 'processed'";
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        return $this->db->single() !== false;
    }
    
    /**
     * Detect auto-reply emails
     */
    private function isAutoReply($email) {
        $subject = strtolower($email['subject']);
        $body = strtolower($email['body']);
        
        // Common auto-reply indicators
        $autoReplyIndicators = [
            'auto-reply', 'automatic reply', 'out of office', 'vacation', 'away message',
            'delivery status notification', 'undelivered mail', 'mail delivery failed',
            'postmaster', 'mailer-daemon', 'no-reply', 'noreply', 'do not reply'
        ];
        
        foreach ($autoReplyIndicators as $indicator) {
            if (strpos($subject, $indicator) !== false || strpos($body, $indicator) !== false) {
                return true;
            }
        }
        
        // Check for auto-reply headers (would need to be passed from email retrieval)
        if (isset($email['auto_submitted']) || isset($email['x_auto_response_suppress'])) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Calculate spam score
     */
    private function calculateSpamScore($email) {
        $score = 0.0;
        $subject = strtolower($email['subject']);
        $body = strtolower($email['body']);
        
        // Excessive caps
        if (preg_match_all('/[A-Z]/', $email['subject']) > (strlen($email['subject']) * 0.7)) {
            $score += 0.3;
        }
        
        // Spam keywords
        $spamKeywords = ['urgent!!!', 'act now', 'limited time', 'free money', 'click here', 'viagra', 'casino'];
        foreach ($spamKeywords as $keyword) {
            if (strpos($subject, $keyword) !== false || strpos($body, $keyword) !== false) {
                $score += 0.2;
            }
        }
        
        // Excessive links
        $linkCount = preg_match_all('/https?:\/\//', $email['body']);
        if ($linkCount > 5) {
            $score += 0.3;
        }
        
        // No actual content
        if (strlen(trim(strip_tags($email['body']))) < 10) {
            $score += 0.2;
        }
        
        return min(1.0, $score);
    }
    
    /**
     * Extract ticket number and security token from email or generate new ones
     */
    private function extractOrGenerateTicketNumber($email) {
        // Try to extract existing ticket number with security token from subject
        $subject = $email['subject'];

        error_log("TICKET-EXTRACT-DEBUG: Processing email subject: " . $subject);

        // Use comprehensive regex that handles all ticket formats and security validation
        $comprehensivePattern = '/^(?:(?!\[RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6})\]).)*\[(RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6}))\](?:(?!\[RER-(?:\d{4}-\d+-[A-Za-z0-9]{6}|[AC]\d{2}-\d+-\d+-[A-Za-z0-9]{6})\]).)*$|^(?!.*\[RER-(?:\d{4}-\d+|[AC]\d{2}-\d+-\d+)\])(?!.*\[RER-).*$/';

        if (preg_match($comprehensivePattern, $subject, $matches)) {
            if (isset($matches[1]) && !empty($matches[1])) {
                // Found valid ticket with security token
                $fullTicket = $matches[1];
                error_log("TICKET-EXTRACT-DEBUG: Found VALID ticket with security token: " . $fullTicket);

                // Validate the token against database
                file_put_contents(APPROOT . '/logs/email_debug.log', date('Y-m-d H:i:s') . " - VALIDATING TICKET: " . $fullTicket . "\n", FILE_APPEND);
                $validationResult = $this->validateTicketSecurity($fullTicket);
                if ($validationResult === true) {
                    file_put_contents(APPROOT . '/logs/email_debug.log', date('Y-m-d H:i:s') . " - VALIDATION SUCCESS: " . $fullTicket . "\n", FILE_APPEND);
                    return $fullTicket;
                } else {
                    file_put_contents(APPROOT . '/logs/email_debug.log', date('Y-m-d H:i:s') . " - VALIDATION FAILED: " . $fullTicket . " (result: " . $validationResult . ")\n", FILE_APPEND);
                    throw new Exception("Email rejected due to invalid security token: " . $fullTicket);
                }
            } else {
                // No ticket found - this is allowed (will generate new ticket)
                error_log("TICKET-EXTRACT-DEBUG: No ticket format found in subject - will generate new ticket");
            }
        } else {
            // Pattern didn't match - this means invalid ticket format was found
            error_log("TICKET-EXTRACT-DEBUG: Invalid ticket format detected - checking for security threats");

            // Check for tickets without security tokens (security threat)
            if (preg_match('/\[RER-(?:\d{4}-\d+|[AC]\d{2}-\d+-\d+)\]/', $subject, $matches)) {
                $invalidTicket = $matches[0];
                error_log("EmailProcessingEngine: SECURITY THREAT - Ticket format without security token: " . $invalidTicket);
                throw new Exception("Email rejected due to missing security token: " . $invalidTicket);
            }
        }

        // REGEX 3: No ticket format found in subject - check references and in-reply-to
        error_log("TICKET-EXTRACT-DEBUG: No ticket format found in subject - checking references");

        // Check references and in-reply-to for ticket numbers using same comprehensive pattern
        $references = $email['references'] . ' ' . $email['in_reply_to'];
        error_log("TICKET-EXTRACT-DEBUG: Checking references: " . $references);

        // Use the same comprehensive pattern for references
        if (preg_match($comprehensivePattern, $references, $matches)) {
            if (isset($matches[1]) && !empty($matches[1])) {
                // Found valid ticket with security token in references
                $fullTicket = $matches[1];
                error_log("TICKET-EXTRACT-DEBUG: Found VALID ticket with security token in references: " . $fullTicket);

                // Validate the token against database
                $validationResult = $this->validateTicketSecurity($fullTicket);
                if ($validationResult === true) {
                    error_log("TICKET-EXTRACT-DEBUG: Security token validated in references - ALLOWING: " . $fullTicket);
                    return $fullTicket;
                } else {
                    error_log("EmailProcessingEngine: SECURITY THREAT - Invalid security token in references: " . $fullTicket);
                    throw new Exception("Email rejected due to invalid security token in references: " . $fullTicket);
                }
            }
        } else {
            // Check for invalid ticket formats in references
            if (preg_match('/\[RER-(?:\d{4}-\d+|[AC]\d{2}-\d+-\d+)\]/', $references, $matches)) {
                $invalidTicket = $matches[0];
                error_log("EmailProcessingEngine: SECURITY THREAT - Ticket format without security token in references: " . $invalidTicket);
                throw new Exception("Email rejected due to missing security token in references: " . $invalidTicket);
            }
        }

        // Generate new ticket number using enhanced service
        error_log("TICKET-EXTRACT-DEBUG: No existing ticket found - generating new ticket");
        return $this->generateEnhancedTicketNumber($email);
    }

    /**
     * Validate ticket security token
     * Returns: true (valid), false (no token - allow as new), 'reject' (security threat)
     */
    private function validateTicketSecurity($fullTicket) {
        error_log("VALIDATE-TICKET: Starting validation for: " . $fullTicket);

        // Flexible pattern to handle any ticket format with 6-char security token at the end:
        // RER-2025-001-ZIK2WU, RER-A25-22-001-ABC123, RER-C25-22-001-XYZ789, etc.
        $pattern = '/^(RER-.+)-([A-Z0-9]{6})$/';
        error_log("VALIDATE-TICKET: Testing pattern: " . $pattern);

        if (!preg_match($pattern, $fullTicket, $matches)) {
            error_log("VALIDATE-TICKET: Pattern did NOT match - invalid format");
            error_log("VALIDATE-TICKET: Full ticket: " . $fullTicket);
            return 'reject'; // Invalid format
        }

        $ticketNumber = $matches[1];
        $securityToken = $matches[2];

        error_log("VALIDATE-TICKET: Pattern MATCHED - ticket: " . $ticketNumber . ", token: " . $securityToken);

        // Validate ticket + token against ticket_numbers table (primary)
        error_log("VALIDATE-TICKET: Checking ticket_numbers table...");
        $this->db->query("SELECT id, ticket_number, security_token FROM ticket_numbers WHERE ticket_number = :ticket_number AND security_token = :security_token");
        $this->db->bind(':ticket_number', $ticketNumber);
        $this->db->bind(':security_token', $securityToken);
        $result = $this->db->single();

        if ($result) {
            error_log("VALIDATE-TICKET: SUCCESS via ticket_numbers table - ID: " . $result->id);
            return true; // Valid ticket with correct security token
        }
        error_log("VALIDATE-TICKET: NOT FOUND in ticket_numbers table");

        // Fallback: check messages table for backward compatibility
        error_log("VALIDATE-TICKET: Checking messages table (fallback)...");
        $this->db->query("SELECT id, ticket_number, security_token FROM messages WHERE ticket_number = :ticket_number AND security_token = :security_token");
        $this->db->bind(':ticket_number', $ticketNumber);
        $this->db->bind(':security_token', $securityToken);
        $result = $this->db->single();

        if ($result) {
            error_log("VALIDATE-TICKET: SUCCESS via messages table (fallback) - ID: " . $result->id);
            return true; // Valid ticket with correct security token
        }
        error_log("VALIDATE-TICKET: NOT FOUND in messages table either");

        // Check what tickets DO exist for debugging
        error_log("VALIDATE-TICKET: Checking what tickets exist in ticket_numbers...");
        $this->db->query("SELECT ticket_number, security_token FROM ticket_numbers ORDER BY id DESC LIMIT 5");
        $existingTickets = $this->db->resultSet();
        foreach ($existingTickets as $ticket) {
            error_log("VALIDATE-TICKET: Found ticket: " . $ticket->ticket_number . " with token: " . $ticket->security_token);
        }

        error_log("VALIDATE-TICKET: FAILED - ticket/token combination not found in database");
        return 'reject'; // Invalid security token - SECURITY THREAT
    }
    
    /**
     * Generate new enhanced ticket number based on email context
     */
    private function generateEnhancedTicketNumber($email) {
        // Try to determine context from email
        $senderEmail = $email['from_email'];
        $showId = $this->extractShowIdFromEmail($email);

        // Check if sender is a known user and get their role
        $senderUserId = $this->findSenderUser($senderEmail);
        $userRole = 'system'; // Default for unknown senders

        if ($senderUserId && $senderUserId !== 1) { // Not system user
            $this->db->query("SELECT role FROM users WHERE id = :user_id");
            $this->db->bind(':user_id', $senderUserId);
            $result = $this->db->single();
            $userRole = $result ? $result->role : 'system';
        }

        // Generate ticket with security token using enhanced service
        // Pass email message_id to prevent race conditions between multiple emails
        $emailMessageId = $email['message_id'] ?? null;
        return $this->ticketService->generateTicketWithSecurityToken($userRole, $showId, $senderUserId, null, $emailMessageId);
    }

    /**
     * Perform security check on original email subject using simple 3-regex approach
     */
    private function performSecurityCheck($subject) {
        error_log("SECURITY-CHECK: Checking subject: " . $subject);

        // REGEX 1: Test for valid tickets with security tokens
        // Updated to match all three formats: [RER-2025-001-ZIK2WU], [RER-A25-01-001-ZIK2WU], [RER-C25-01-001-ZIK2WU]
        error_log("SECURITY-CHECK: Testing REGEX 1 for tickets with security tokens...");

        if (preg_match('/\[(RER-(?:[AC]?\d{2,4}-)?(?:\d+-)?(?:\d{3})-[A-Z0-9]{6})\]/', $subject, $matches)) {
            $fullTicket = $matches[1];
            error_log("SECURITY-CHECK: REGEX 1 MATCHED - Found ticket with security token: " . $fullTicket);
            error_log("SECURITY-CHECK: Full matches array: " . print_r($matches, true));

            // Now validate this ticket against the database
            error_log("SECURITY-CHECK: Validating ticket against database...");
            $validationResult = $this->validateTicketSecurity($fullTicket);
            error_log("SECURITY-CHECK: Database validation result: " . ($validationResult === true ? 'VALID' : 'INVALID'));

            if ($validationResult === true) {
                error_log("SECURITY-CHECK: ALLOWING - Valid ticket with correct security token");
                return true;
            } else {
                error_log("SECURITY-CHECK: REJECTING - Invalid security token in database");
                throw new Exception("Email rejected due to invalid security token: " . $fullTicket);
            }
        }
        error_log("SECURITY-CHECK: REGEX 1 did NOT match - no security token found");

        // REGEX 2: Test for tickets without security tokens
        // Updated to match all three formats without tokens: [RER-2025-001], [RER-A25-01-001], [RER-C25-01-001]
        error_log("SECURITY-CHECK: Testing REGEX 2 for tickets without tokens...");
        if (preg_match('/\[(RER-(?:[AC]?\d{2,4}-)?(?:\d+-)?(?:\d{3}))\]/', $subject, $matches)) {
            $fullTicket = $matches[1];
            error_log("SECURITY-CHECK: REGEX 2 MATCHED - Found INVALID ticket without security token: " . $fullTicket);
            throw new Exception("Email rejected due to missing security token: " . $fullTicket);
        }
        error_log("SECURITY-CHECK: REGEX 2 did NOT match");

        // REGEX 3: No RER ticket format found - ALLOW (will generate new ticket)
        error_log("SECURITY-CHECK: REGEX 3 - No RER ticket format found - allowing as new email");
        return true;
    }

    /**
     * Extract show ID from email content or sender
     */
    private function extractShowIdFromEmail($email) {
        // Try to extract show ID from email content
        $content = $email['subject'] . ' ' . $email['body'];

        // Look for show references in content
        if (preg_match('/show\s*(?:id|#)\s*:?\s*(\d+)/i', $content, $matches)) {
            return (int)$matches[1];
        }

        // Check if sender is a coordinator and get their current show
        $senderUserId = $this->findSenderUser($email['from_email']);
        if ($senderUserId) {
            $this->db->query("SELECT show_id FROM show_role_assignments
                             WHERE user_id = :user_id AND assigned_role = 'coordinator' AND is_active = 1
                             AND show_id IN (SELECT id FROM shows WHERE start_date >= UTC_TIMESTAMP())
                             ORDER BY show_id DESC LIMIT 1");
            $this->db->bind(':user_id', $senderUserId);
            $result = $this->db->single();

            if ($result) {
                return (int)$result->show_id;
            }
        }

        return null; // No show context found
    }
    
    /**
     * Find parent message for threading
     */
    private function findParentMessage($email, $ticketNumber) {
        // Extract base ticket number (remove security token if present)
        $baseTicketNumber = $ticketNumber;
        if (preg_match('/^(RER-.+)-([A-Z0-9]{6})$/', $ticketNumber, $matches)) {
            $baseTicketNumber = $matches[1]; // e.g., RER-2025-003
        }
        
        // Find the ROOT message (first message in conversation) for proper threading
        $sql = "SELECT id FROM messages WHERE ticket_number = :ticket AND parent_message_id IS NULL ORDER BY created_at ASC LIMIT 1";
        $this->db->query($sql);
        $this->db->bind(':ticket', $baseTicketNumber);
        $result = $this->db->single();
        
        if ($result) {
            error_log("EmailProcessingEngine::findParentMessage - Found ROOT message ID: " . $result->id . " for ticket: " . $baseTicketNumber);
            return $result->id;
        } else {
            error_log("EmailProcessingEngine::findParentMessage - No ROOT message found for ticket: " . $baseTicketNumber . " (this will be the first message)");
        }
        
        // Try by email message ID threading
        if (!empty($email['in_reply_to'])) {
            $sql = "SELECT id FROM messages WHERE email_message_id = :email_id ORDER BY created_at DESC LIMIT 1";
            $this->db->query($sql);
            $this->db->bind(':email_id', $email['in_reply_to']);
            $result = $this->db->single();
            
            if ($result) {
                return $result->id;
            }
        }
        
        return null;
    }
    
    /**
     * Find sender user by email
     */
    private function findSenderUser($email) {
        $sql = "SELECT id FROM users WHERE email = :email AND status = 'active' LIMIT 1";
        $this->db->query($sql);
        $this->db->bind(':email', $email);
        $result = $this->db->single();
        
        return $result ? $result->id : 1; // Default to system user (ID 1)
    }
    
    /**
     * Get all admin users
     */
    private function getAdminUsers() {
        $sql = "SELECT id, name, email FROM users WHERE role = 'admin' AND status = 'active'";
        $this->db->query($sql);
        return $this->db->resultSet();
    }

    /**
     * Create message in unified messaging system
     */
    private function createMessage($email, $ticketNumber, $parentMessageId, $senderUserId, $adminUserId) {
        // Prepare subject with enhanced ticket number (includes security token)
        $subject = $email['subject'];
        if (strpos($subject, '[' . $ticketNumber . ']') === false) {
            $subject = $subject . ' [' . $ticketNumber . ']';
        }

        // Prepare message body
        $message = $email['body'];

        // Add original sender info if not a registered user
        if ($senderUserId === 1 && !empty($email['from_email'])) {
            $senderInfo = "Original sender: " . $email['from_name'] . " <" . $email['from_email'] . ">\n";
            $senderInfo .= "Received: " . $email['date'] . "\n\n";
            $message = $senderInfo . $message;
        }

        // Extract show ID from ticket if available
        $showId = $this->ticketService->getShowIdFromTicket($ticketNumber);

        // Get the correct ticket and security token for this specific email
        $existingTicketNumber = null;
        $existingSecurityToken = null;
        
        // First, try to get ticket by email message ID (for new emails processed by this system)
        $emailMessageId = $email['message_id'] ?? null;
        if ($emailMessageId) {
            $ticketInfo = $this->ticketService->getTicketByEmailMessageId($emailMessageId);
            if ($ticketInfo) {
                $existingTicketNumber = $ticketInfo->ticket_number;
                $existingSecurityToken = $ticketInfo->security_token;
                error_log("EmailProcessingEngine::createMessage - Found ticket by email ID: " . $existingTicketNumber . " with token: " . $existingSecurityToken);
            }
        }
        
        // If not found by email ID, try parsing the ticket number (for reply emails)
        if (!$existingTicketNumber) {
            $pattern = '/^(RER-.+)-([A-Z0-9]{6})$/';
            if (preg_match($pattern, $ticketNumber, $matches)) {
                // This is a full ticket with security token - extract the parts
                $baseTicketNumber = $matches[1];  // e.g., RER-2025-001
                $securityToken = $matches[2];     // e.g., QRK3B1
                
                error_log("EmailProcessingEngine::createMessage - Parsed ticket from subject: base=" . $baseTicketNumber . ", token=" . $securityToken);
                
                // Check if this exact combination exists in the database
                $this->db->query("SELECT ticket_number, security_token FROM messages WHERE ticket_number = :ticket_number AND security_token = :security_token LIMIT 1");
                $this->db->bind(':ticket_number', $baseTicketNumber);
                $this->db->bind(':security_token', $securityToken);
                $existingMessage = $this->db->single();

                // Always use the parsed ticket and token values (whether it's a new or existing conversation)
                $existingTicketNumber = $baseTicketNumber;
                $existingSecurityToken = $securityToken;
                
                if ($existingMessage) {
                    error_log("EmailProcessingEngine::createMessage - Found existing message, reusing ticket: " . $existingTicketNumber . " with token: " . $existingSecurityToken);
                } else {
                    error_log("EmailProcessingEngine::createMessage - New conversation, using ticket: " . $existingTicketNumber . " with token: " . $existingSecurityToken);
                }
            } else {
                // This is just a base ticket number without security token - will generate new
                error_log("EmailProcessingEngine::createMessage - No security token in ticket: " . $ticketNumber . ", will generate new");
            }
        }

        // Create email message using enhanced unified messaging system
        file_put_contents(APPROOT . '/logs/email_debug.log', date('Y-m-d H:i:s') . " - CREATING MESSAGE: ticket='" . ($existingTicketNumber ?? 'NULL') . "', token='" . ($existingSecurityToken ?? 'NULL') . "'\n", FILE_APPEND);
        
        $messageId = $this->unifiedMessageModel->createEmailMessage(
            $senderUserId,
            $adminUserId,
            $subject,
            $message,
            $email['from_email'], // ALWAYS store original sender email for replies
            $showId,
            'normal', // priority
            $existingTicketNumber, // Pass existing ticket number if found
            $existingSecurityToken // Pass existing security token if found
        );

        if ($messageId) {
            // Update message with additional email-specific fields
            $sql = "UPDATE messages SET
                    email_message_id = :email_id,
                    parent_message_id = :parent_id
                    WHERE id = :id";

            $this->db->query($sql);
            $this->db->bind(':email_id', $email['message_id']);
            $this->db->bind(':parent_id', $parentMessageId);
            $this->db->bind(':id', $messageId);
            $this->db->execute();
        }

        return $messageId;
    }

    /**
     * Process email attachments
     */
    private function processAttachments($email, $logId) {
        $processedAttachments = [];

        if (empty($email['attachments'])) {
            return $processedAttachments;
        }

        // Create attachments directory if it doesn't exist
        $attachmentDir = APPROOT . '/uploads/email_attachments/' . date('Y/m');
        if (!is_dir($attachmentDir)) {
            mkdir($attachmentDir, 0755, true);
        }

        foreach ($email['attachments'] as $attachment) {
            try {
                // Validate attachment
                if (!$this->isAttachmentAllowed($attachment)) {
                    continue;
                }

                // Generate unique filename
                $extension = pathinfo($attachment['filename'], PATHINFO_EXTENSION);
                $storedFilename = uniqid('email_') . '_' . time() . '.' . $extension;
                $filePath = $attachmentDir . '/' . $storedFilename;

                // This would need to be implemented in EmailRetrievalService
                // For now, we'll just log the attachment info
                $processedAttachments[] = [
                    'original_filename' => $attachment['filename'],
                    'stored_filename' => $storedFilename,
                    'file_path' => $filePath,
                    'file_size' => $attachment['size'],
                    'mime_type' => $this->getMimeType($attachment),
                    'part_number' => $attachment['part_number'] ?? 0
                ];

            } catch (Exception $e) {
                error_log("EmailProcessingEngine::processAttachments - Error processing attachment: " . $e->getMessage());
                continue;
            }
        }

        return $processedAttachments;
    }

    /**
     * Check if attachment is allowed
     */
    private function isAttachmentAllowed($attachment) {
        // Check size
        if ($this->maxAttachmentSizeMB > 0 && $attachment['size'] > ($this->maxAttachmentSizeMB * 1024 * 1024)) {
            return false;
        }

        // Check file type
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'zip'];
        $extension = strtolower(pathinfo($attachment['filename'], PATHINFO_EXTENSION));

        return in_array($extension, $allowedExtensions);
    }

    /**
     * Get MIME type from attachment info
     */
    private function getMimeType($attachment) {
        $types = [
            0 => 'text',
            1 => 'multipart',
            2 => 'message',
            3 => 'application',
            4 => 'audio',
            5 => 'image',
            6 => 'video',
            7 => 'other'
        ];

        $type = $types[$attachment['type']] ?? 'application';
        $subtype = $attachment['subtype'] ?? 'octet-stream';

        return $type . '/' . strtolower($subtype);
    }

    /**
     * Link attachment to message
     */
    private function linkAttachmentToMessage($messageId, $attachment) {
        $sql = "INSERT INTO message_attachments
                (message_id, filename, stored_filename, file_path, file_size, mime_type)
                VALUES (:message_id, :filename, :stored_filename, :file_path, :file_size, :mime_type)";

        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':filename', $attachment['original_filename']);
        $this->db->bind(':stored_filename', $attachment['stored_filename']);
        $this->db->bind(':file_path', $attachment['file_path']);
        $this->db->bind(':file_size', $attachment['file_size']);
        $this->db->bind(':mime_type', $attachment['mime_type']);

        return $this->db->execute();
    }

    /**
     * Send auto-reply confirmation
     */
    private function sendAutoReply($email, $ticketNumber) {
        try {
            // Get auto-reply template
            $sql = "SELECT subject, body FROM email_templates WHERE name = 'Auto Reply Confirmation' AND is_active = 1 LIMIT 1";
            $this->db->query($sql);
            $template = $this->db->single();

            if (!$template) {
                return false;
            }

            // Replace template variables
            $subject = str_replace('{{subject}}', $email['subject'], $template->subject);
            $subject = str_replace('{{ticket_number}}', $ticketNumber, $subject);

            $body = str_replace('{{subject}}', $email['subject'], $template->body);
            $body = str_replace('{{ticket_number}}', $ticketNumber, $body);
            $body = str_replace('{{date}}', date('Y-m-d H:i:s'), $body);
            $body = str_replace('{{site_name}}', $this->settingsModel->getSetting('app_name', 'Events and Shows'), $body);

            // Send email using EmailService
            require_once APPROOT . '/models/EmailService.php';
            $emailService = new EmailService();

            if ($emailService->isConfigured()) {
                return $emailService->send($email['from_email'], $subject, nl2br($body), $body);
            }

            return false;

        } catch (Exception $e) {
            error_log("EmailProcessingEngine::sendAutoReply - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up old processing logs
     */
    public function cleanupOldLogs($daysOld = 30) {
        $sql = "DELETE FROM email_processing_log WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        $this->db->query($sql);
        $this->db->bind(':days', $daysOld);
        return $this->db->execute();
    }
}
