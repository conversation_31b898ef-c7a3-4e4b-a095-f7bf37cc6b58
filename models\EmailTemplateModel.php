<?php

/**
 * Email Template Management Model
 * Handles email template operations for admin/coordinator quick replies
 */
class EmailTemplateModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all email templates
     */
    public function getAllTemplates($activeOnly = false) {
        $sql = "SELECT t.*, u.name as created_by_name
                FROM email_templates t
                LEFT JOIN users u ON t.created_by = u.id";
        
        if ($activeOnly) {
            $sql .= " WHERE t.is_active = 1";
        }
        
        $sql .= " ORDER BY t.category ASC, t.name ASC";
        
        $this->db->query($sql);
        return $this->db->resultSet();
    }
    
    /**
     * Get templates by category
     */
    public function getTemplatesByCategory($category, $activeOnly = true) {
        $sql = "SELECT * FROM email_templates WHERE category = :category";
        
        if ($activeOnly) {
            $sql .= " AND is_active = 1";
        }
        
        $sql .= " ORDER BY name ASC";
        
        $this->db->query($sql);
        $this->db->bind(':category', $category);
        return $this->db->resultSet();
    }
    
    /**
     * Get template by ID
     */
    public function getTemplateById($templateId) {
        $this->db->query("SELECT * FROM email_templates WHERE id = :template_id");
        $this->db->bind(':template_id', $templateId);
        return $this->db->single();
    }
    
    /**
     * Create new template
     */
    public function createTemplate($name, $subject, $body, $variables, $category, $createdBy) {
        $this->db->query("INSERT INTO email_templates (name, subject, body, template_variables, category, created_by)
                         VALUES (:name, :subject, :body, :template_variables, :category, :created_by)");
        $this->db->bind(':name', $name);
        $this->db->bind(':subject', $subject);
        $this->db->bind(':body', $body);
        $this->db->bind(':template_variables', is_array($variables) ? implode(',', $variables) : $variables);
        $this->db->bind(':category', $category);
        $this->db->bind(':created_by', $createdBy);

        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    /**
     * Update template
     */
    public function updateTemplate($templateId, $name, $subject, $body, $variables, $category) {
        $this->db->query("UPDATE email_templates
                         SET name = :name, subject = :subject, body = :body,
                             template_variables = :template_variables, category = :category
                         WHERE id = :template_id");
        $this->db->bind(':template_id', $templateId);
        $this->db->bind(':name', $name);
        $this->db->bind(':subject', $subject);
        $this->db->bind(':body', $body);
        $this->db->bind(':template_variables', is_array($variables) ? implode(',', $variables) : $variables);
        $this->db->bind(':category', $category);

        return $this->db->execute();
    }
    
    /**
     * Delete template
     */
    public function deleteTemplate($templateId) {
        $this->db->query("DELETE FROM email_templates WHERE id = :template_id");
        $this->db->bind(':template_id', $templateId);
        
        return $this->db->execute();
    }
    
    /**
     * Toggle template active status
     */
    public function toggleTemplateStatus($templateId) {
        $this->db->query("UPDATE email_templates SET is_active = NOT is_active WHERE id = :template_id");
        $this->db->bind(':template_id', $templateId);
        
        return $this->db->execute();
    }
    
    /**
     * Get template categories
     */
    public function getCategories() {
        $this->db->query("SELECT DISTINCT category FROM email_templates ORDER BY category ASC");
        $results = $this->db->resultSet();
        
        $categories = [];
        foreach ($results as $result) {
            $categories[] = $result->category;
        }
        
        return $categories;
    }
    
    /**
     * Replace template variables with actual values
     */
    public function replaceTemplateVariables($template, $variables) {
        $subject = $template->subject;
        $body = $template->body;
        
        foreach ($variables as $variable => $value) {
            $subject = str_replace($variable, $value, $subject);
            $body = str_replace($variable, $value, $body);
        }
        
        return [
            'subject' => $subject,
            'body' => $body
        ];
    }
    
    /**
     * Get available template variables
     */
    public function getAvailableVariables() {
        return [
            '{{subject}}' => 'Original message subject',
            '{{ticket_number}}' => 'Ticket number for the conversation',
            '{{date}}' => 'Current date and time',
            '{{admin_name}}' => 'Name of the admin/coordinator sending the reply',
            '{{site_name}}' => 'Name of the website/platform',
            '{{sender_name}}' => 'Name of the original message sender',
            '{{sender_email}}' => 'Email of the original message sender'
        ];
    }
    
    /**
     * Search templates
     */
    public function searchTemplates($query, $category = null) {
        $sql = "SELECT t.*, u.name as created_by_name
                FROM email_templates t
                LEFT JOIN users u ON t.created_by = u.id
                WHERE (t.name LIKE :query OR t.subject LIKE :query OR t.body LIKE :query)";
        
        if ($category) {
            $sql .= " AND t.category = :category";
        }
        
        $sql .= " ORDER BY t.name ASC";
        
        $this->db->query($sql);
        $this->db->bind(':query', '%' . $query . '%');
        if ($category) {
            $this->db->bind(':category', $category);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Get template usage statistics
     */
    public function getTemplateUsageStats($templateId) {
        // This would require tracking template usage in a separate table
        // For now, return basic info
        return [
            'total_uses' => 0,
            'last_used' => null,
            'most_used_by' => null
        ];
    }
    
    /**
     * Duplicate template
     */
    public function duplicateTemplate($templateId, $newName, $createdBy) {
        $template = $this->getTemplateById($templateId);
        if (!$template) {
            return false;
        }
        
        return $this->createTemplate(
            $newName,
            $template->subject,
            $template->body,
            $template->template_variables,
            $template->category,
            $createdBy
        );
    }
}
