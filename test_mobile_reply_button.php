<?php
/**
 * Test Mobile Reply Button Fix
 * 
 * Tests that the reply button is now available on mobile
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>💬 Test Mobile Reply Button Fix</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Mobile Reply Button Added</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Issue Fixed:</h3>";
    echo "<ul>";
    echo "<li><strong>Missing reply button:</strong> Mobile layout was missing the reply functionality</li>";
    echo "<li><strong>Added reply button:</strong> Now matches desktop functionality</li>";
    echo "<li><strong>Proper conditions:</strong> Shows for direct messages and messages requiring reply</li>";
    echo "<li><strong>Consistent styling:</strong> Same size and spacing as other mobile buttons</li>";
    echo "<li><strong>Correct link:</strong> Links to message view with #reply anchor</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    // Get messages that allow replies
    $db->query("SELECT COUNT(*) as reply_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_archived = 0
                AND (requires_reply = 1 OR message_type = 'direct')");
    $db->bind(':user_id', $userId);
    $replyResult = $db->single();
    $replyCount = $replyResult ? $replyResult->reply_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Good for testing' : '⚠️ Could use more') . "</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Messages with Reply Button</td>";
    echo "<td style='color: " . ($replyCount > 0 ? 'blue' : 'orange') . ";'>{$replyCount}</td>";
    echo "<td>" . ($replyCount > 0 ? '✅ Perfect for testing' : '⚠️ Need direct message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($replyCount === 0) {
        echo "<h2>📤 Creating Direct Message for Reply Testing</h2>";
        echo "<p>Creating a direct message to demonstrate the mobile reply button...</p>";
        
        $subject = "💬 Mobile Reply Test";
        $message = "This is a direct message that should show a reply button on mobile. Please test the reply functionality!";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message, null, 'direct');
        
        if ($result) {
            echo "<p style='color: green;'>✅ Direct message created successfully! Message ID: {$result}</p>";
            $replyCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create direct message</p>";
        }
    }
    
    echo "<h2>🧪 Mobile Reply Button Testing</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile Testing Instructions:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile device</strong></li>";
    echo "<li><strong>Navigate to Messages</strong> (notification center)</li>";
    echo "<li><strong>Look for direct messages:</strong> Should show reply button</li>";
    echo "<li><strong>Check button layout:</strong> Should be horizontal with other buttons</li>";
    echo "<li><strong>Test reply button:</strong> Tap to open message with reply form</li>";
    echo "<li><strong>Verify functionality:</strong> Should scroll to #reply section</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Mobile Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Message Type</th><th>Before</th><th>After</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Direct Messages</strong></td>";
    echo "<td style='color: red;'>❌ No reply button on mobile</td>";
    echo "<td style='color: green;'>✅ Reply button (💬) available</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Messages Requiring Reply</strong></td>";
    echo "<td style='color: red;'>❌ No reply button on mobile</td>";
    echo "<td style='color: green;'>✅ Reply button (💬) available</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>System Messages</strong></td>";
    echo "<td style='color: green;'>✅ No reply button (correct)</td>";
    echo "<td style='color: green;'>✅ No reply button (correct)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Button Layout</strong></td>";
    echo "<td style='color: red;'>❌ Missing button in sequence</td>";
    echo "<td style='color: green;'>✅ 👁️ ✅ 💬 📦 horizontal layout</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Implementation</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>💬 Mobile Reply Button HTML:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "<!-- Added to mobile layout -->
<?php if (\$message->requires_reply || \$message->message_type === 'direct'): ?>
    <a href=\"<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo \$message->id; ?>#reply\" 
       class=\"btn btn-outline-info btn-sm\" 
       style=\"padding: 3px 7px; font-size: 11px; margin-bottom: 2px;\" 
       title=\"Reply\">
        <i class=\"fas fa-reply\"></i>
    </a>
<?php endif; ?>";
    echo "</pre>";
    
    echo "<h3>🎯 Reply Button Conditions:</h3>";
    echo "<ul>";
    echo "<li><strong>Direct messages:</strong> message_type === 'direct'</li>";
    echo "<li><strong>Messages requiring reply:</strong> requires_reply = 1</li>";
    echo "<li><strong>System messages:</strong> No reply button (correct behavior)</li>";
    echo "<li><strong>Archived messages:</strong> Reply button still available</li>";
    echo "</ul>";
    
    echo "<h3>📱 Mobile Button Sequence:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "1. 👁️ View button (always present)
2. ✅ Mark Read button (if unread)
3. 💬 Reply button (if direct or requires_reply)
4. 📦 Archive button (if not archived) OR 🔄 Restore button (if archived)";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>🔍 Reply Button Logic</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 When Reply Button Shows:</h3>";
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Message Type</th><th>Requires Reply</th><th>Shows Reply Button</th></tr>";
    
    echo "<tr>";
    echo "<td>direct</td>";
    echo "<td>No</td>";
    echo "<td style='color: green;'>✅ Yes</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>direct</td>";
    echo "<td>Yes</td>";
    echo "<td style='color: green;'>✅ Yes</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>system</td>";
    echo "<td>Yes</td>";
    echo "<td style='color: green;'>✅ Yes</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>system</td>";
    echo "<td>No</td>";
    echo "<td style='color: red;'>❌ No</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>judging</td>";
    echo "<td>Yes</td>";
    echo "<td style='color: green;'>✅ Yes</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>event</td>";
    echo "<td>No</td>";
    echo "<td style='color: red;'>❌ No</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    if ($replyCount > 0) {
        echo "<h2>✅ Ready for Mobile Reply Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$replyCount} message(s) with reply functionality.</p>";
        echo "<p><strong>What you should see on mobile:</strong></p>";
        echo "<ul>";
        echo "<li>💬 <strong>Reply button:</strong> Blue reply icon (💬) for direct messages</li>";
        echo "<li>🔘 <strong>Horizontal layout:</strong> 👁️ ✅ 💬 📦 in a row</li>";
        echo "<li>📏 <strong>Consistent size:</strong> 3px 7px padding like other buttons</li>";
        echo "<li>🔗 <strong>Proper link:</strong> Opens message view with #reply anchor</li>";
        echo "<li>📱 <strong>Mobile-friendly:</strong> Easy to tap and use</li>";
        echo "</ul>";
        echo "<p><strong>The mobile reply functionality should now match desktop!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated file:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/notification_center/index.php</code> - Added mobile reply button</li>";
    echo "</ul>";
    echo "<p><strong>Changes made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Added reply button to mobile layout</li>";
    echo "<li>✅ Same conditions as desktop (direct messages + requires_reply)</li>";
    echo "<li>✅ Consistent styling (3px 7px padding)</li>";
    echo "<li>✅ Proper link with #reply anchor</li>";
    echo "<li>✅ Horizontal layout with other buttons</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Mobile reply button test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
