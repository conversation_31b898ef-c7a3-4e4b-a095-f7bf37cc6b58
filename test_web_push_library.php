<?php
/**
 * Test using web-push-php library for proper VAPID authentication
 */

// Prevent direct access without security key
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'web_push_test_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';

echo "=== WEB PUSH LIBRARY TEST ===\n\n";

// Check if we can install web-push-php library
echo "1. CHECKING COMPOSER AVAILABILITY\n";

$composerExists = file_exists(APPROOT . '/composer.json');
echo "   Composer.json exists: " . ($composerExists ? "YES" : "NO") . "\n";

if (!$composerExists) {
    echo "\n2. CREATING COMPOSER.JSON\n";
    
    $composerConfig = [
        "require" => [
            "minishlink/web-push" => "^8.0"
        ]
    ];
    
    file_put_contents(APPROOT . '/composer.json', json_encode($composerConfig, JSON_PRETTY_PRINT));
    echo "   Created composer.json\n";
}

// Check if vendor directory exists
$vendorExists = file_exists(APPROOT . '/vendor/autoload.php');
echo "   Vendor directory exists: " . ($vendorExists ? "YES" : "NO") . "\n";

if (!$vendorExists) {
    echo "\n3. COMPOSER INSTALL NEEDED\n";
    echo "   Run this command on your server:\n";
    echo "   cd " . APPROOT . "\n";
    echo "   composer install\n\n";
    
    echo "   OR download web-push-php manually:\n";
    echo "   https://github.com/web-push-libs/web-push-php\n\n";
    
    echo "4. ALTERNATIVE: USE EXTERNAL SERVICE\n";
    echo "   Since your server has OpenSSL issues, you could:\n";
    echo "   - Use a cloud function (Firebase Functions, AWS Lambda)\n";
    echo "   - Use a push notification service (OneSignal, Pusher)\n";
    echo "   - Use a different server with proper OpenSSL support\n\n";
    
    echo "5. MANUAL VAPID JWT SOLUTION\n";
    echo "   I can create a solution that works without EC cryptography\n";
    echo "   by using a different approach or external JWT service.\n\n";
    
    exit;
}

// Try to use the web-push library
require_once APPROOT . '/vendor/autoload.php';

use Minishlink\WebPush\WebPush;
use Minishlink\WebPush\Subscription;

echo "\n2. TESTING WEB-PUSH LIBRARY\n";

try {
    // Initialize core classes
    require_once APPROOT . '/core/Database.php';
    require_once APPROOT . '/models/NotificationModel.php';
    
    $notificationModel = new NotificationModel();
    $userId = 3;
    
    // Get user subscriptions
    $subscriptions = $notificationModel->getUserPushSubscriptions($userId);
    echo "   Found " . count($subscriptions) . " subscriptions\n";
    
    if (empty($subscriptions)) {
        echo "   No subscriptions to test with\n";
        exit;
    }
    
    // Get VAPID keys
    $vapidPublicKey = defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : null;
    $vapidPrivateKey = defined('VAPID_PRIVATE_KEY') ? VAPID_PRIVATE_KEY : null;
    $vapidSubject = defined('VAPID_SUBJECT') ? VAPID_SUBJECT : 'https://events.rowaneliterides.com';
    
    if (!$vapidPublicKey || !$vapidPrivateKey) {
        echo "   VAPID keys not configured\n";
        exit;
    }
    
    echo "   VAPID Public Key: " . substr($vapidPublicKey, 0, 20) . "...\n";
    echo "   VAPID Private Key: " . substr($vapidPrivateKey, 0, 10) . "...\n";
    echo "   VAPID Subject: $vapidSubject\n";
    
    // Initialize WebPush
    $webPush = new WebPush([
        'VAPID' => [
            'subject' => $vapidSubject,
            'publicKey' => $vapidPublicKey,
            'privateKey' => $vapidPrivateKey,
        ]
    ]);
    
    echo "\n3. SENDING TEST NOTIFICATION\n";
    
    $payload = json_encode([
        'title' => '🎉 WEB-PUSH LIBRARY TEST',
        'body' => 'This notification was sent using the web-push-php library at ' . date('H:i:s'),
        'icon' => '/public/images/icons/icon-192x192.png',
        'badge' => '/public/images/icons/badge-72x72.png',
        'tag' => 'library-test-' . time(),
        'requireInteraction' => false,
        'data' => [
            'url' => '/',
            'event_id' => 0,
            'event_type' => 'library_test'
        ]
    ]);
    
    $successCount = 0;
    $failCount = 0;
    
    // Test with first 3 subscriptions
    $testSubscriptions = array_slice($subscriptions, 0, 3);
    
    foreach ($testSubscriptions as $sub) {
        try {
            $subscription = Subscription::create([
                'endpoint' => $sub->endpoint,
                'publicKey' => $sub->p256dh_key,
                'authToken' => $sub->auth_key,
            ]);
            
            $result = $webPush->sendOneNotification($subscription, $payload);
            
            if ($result->isSuccess()) {
                echo "   ✅ SUCCESS: " . substr($sub->endpoint, -20) . "\n";
                $successCount++;
            } else {
                echo "   ❌ FAILED: " . substr($sub->endpoint, -20) . " - " . $result->getReason() . "\n";
                $failCount++;
            }
            
        } catch (Exception $e) {
            echo "   ❌ ERROR: " . substr($sub->endpoint, -20) . " - " . $e->getMessage() . "\n";
            $failCount++;
        }
    }
    
    echo "\n4. RESULTS\n";
    echo "   Successful: $successCount\n";
    echo "   Failed: $failCount\n";
    
    if ($successCount > 0) {
        echo "\n🎉 SUCCESS! The web-push library works!\n";
        echo "   Check your browser for notifications\n";
        echo "   You can now integrate this library into your NotificationService\n";
    } else {
        echo "\n❌ All notifications failed\n";
        echo "   Check the error messages above for details\n";
    }
    
} catch (Exception $e) {
    echo "   ERROR: " . $e->getMessage() . "\n";
    echo "   Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== END TEST ===\n";
?>
