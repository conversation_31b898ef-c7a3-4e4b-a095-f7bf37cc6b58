<?php
/**
 * BACKUP: controllers/NotificationCenterController.php - Before bulk move parameter fix
 * Date: <?php echo date('Y-m-d H:i:s'); ?>
 * 
 * Changes made:
 * - Fixed bulkMoveToFolder() method to properly convert comma-separated message IDs string to array
 * - Added proper string-to-array conversion: array_map('intval', explode(',', $messageIdsString))
 * - Fixed PHP Fatal error: count(): Argument #1 ($value) must be of type Countable|array, string given
 */

// This is a backup file - original functionality preserved
// See current controllers/NotificationCenterController.php for fixed parameter handling
?>