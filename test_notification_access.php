<?php
/**
 * Test Notification Center Access
 * 
 * This script tests if the notification center can be accessed via web interface
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

echo "<h1>Notification Center Access Test</h1>";

try {
    echo "<h2>1. Testing File Structure</h2>";
    
    // Check if controller file exists
    $controllerFile = 'controllers/NotificationCenterController.php';
    if (file_exists($controllerFile)) {
        echo "<p>✅ NotificationCenterController.php exists</p>";
    } else {
        echo "<p>❌ NotificationCenterController.php not found</p>";
    }
    
    // Check if view files exist
    $viewFiles = [
        'views/notification_center/index.php',
        'views/notification_center/view.php'
    ];
    
    foreach ($viewFiles as $viewFile) {
        if (file_exists($viewFile)) {
            echo "<p>✅ {$viewFile} exists</p>";
        } else {
            echo "<p>❌ {$viewFile} not found</p>";
        }
    }
    
    // Check if model file exists
    $modelFile = 'models/NotificationCenterModel.php';
    if (file_exists($modelFile)) {
        echo "<p>✅ NotificationCenterModel.php exists</p>";
    } else {
        echo "<p>❌ NotificationCenterModel.php not found</p>";
    }
    
    echo "<h2>2. Testing Database Connection</h2>";
    
    // Test database connection
    require_once 'core/Database.php';
    $db = new Database();
    $db->query("SELECT 1 as test");
    $result = $db->single();
    if ($result && $result->test == 1) {
        echo "<p>✅ Database connection successful</p>";
    } else {
        throw new Exception("Database connection failed");
    }
    
    echo "<h2>3. Testing Database Tables</h2>";
    
    // Check if notification center tables exist
    $tables = [
        'notification_center_items',
        'user_messages'
    ];
    
    foreach ($tables as $table) {
        try {
            $db->query("SELECT COUNT(*) as count FROM {$table}");
            $result = $db->single();
            echo "<p>✅ Table '{$table}' exists with {$result->count} records</p>";
        } catch (Exception $e) {
            echo "<p>❌ Table '{$table}' error: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>4. Testing URL Access</h2>";
    
    $baseUrl = BASE_URL;
    $notificationCenterUrl = $baseUrl . '/notification_center';
    
    echo "<p>Base URL: <strong>{$baseUrl}</strong></p>";
    echo "<p>Notification Center URL: <strong>{$notificationCenterUrl}</strong></p>";
    
    // Check if user is logged in
    if (isset($_SESSION['user_id'])) {
        echo "<p>✅ User is logged in (ID: {$_SESSION['user_id']})</p>";
        echo "<p>🔗 <a href='{$notificationCenterUrl}' target='_blank'>Click here to test Notification Center</a></p>";
    } else {
        echo "<p>⚠️ User is not logged in - notification center requires authentication</p>";
        echo "<p>🔗 <a href='{$baseUrl}/auth/login' target='_blank'>Login first</a>, then test notification center</p>";
    }
    
    echo "<h2>5. Testing Core Dependencies</h2>";
    
    // Test if core classes can be loaded
    $coreClasses = [
        'core/Controller.php' => 'Controller',
        'core/Auth.php' => 'Auth'
    ];
    
    foreach ($coreClasses as $file => $className) {
        if (file_exists($file)) {
            echo "<p>✅ {$file} exists</p>";
            try {
                require_once $file;
                if (class_exists($className)) {
                    echo "<p>✅ {$className} class can be loaded</p>";
                } else {
                    echo "<p>❌ {$className} class not found after loading {$file}</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Error loading {$file}: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>❌ {$file} not found</p>";
        }
    }
    
    echo "<h2>6. Testing Helper Functions</h2>";
    
    // Test if helper files exist
    $helperFiles = [
        'helpers/url_helper.php',
        'helpers/session_helper.php',
        'helpers/csrf_helper.php'
    ];
    
    foreach ($helperFiles as $helperFile) {
        if (file_exists($helperFile)) {
            echo "<p>✅ {$helperFile} exists</p>";
            try {
                require_once $helperFile;
                echo "<p>✅ {$helperFile} loaded successfully</p>";
            } catch (Exception $e) {
                echo "<p>❌ Error loading {$helperFile}: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>❌ {$helperFile} not found</p>";
        }
    }
    
    echo "<h2>7. Testing JavaScript File</h2>";
    
    $jsFile = 'public/js/notification-center.js';
    if (file_exists($jsFile)) {
        echo "<p>✅ notification-center.js exists</p>";
        $jsSize = filesize($jsFile);
        echo "<p>File size: {$jsSize} bytes</p>";
    } else {
        echo "<p>❌ notification-center.js not found</p>";
    }
    
    echo "<h2>✅ Access Test Complete!</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>Ready to Test!</h3>";
        echo "<p>All components are in place. You can now test the notification center:</p>";
        echo "<ul>";
        echo "<li><a href='{$notificationCenterUrl}' target='_blank'>Open Notification Center</a></li>";
        echo "<li><a href='{$baseUrl}' target='_blank'>Go to Homepage</a> (check bell icon in header)</li>";
        echo "<li><a href='{$baseUrl}/judge_management' target='_blank'>Test Judge Management</a> (send a message to test notifications)</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>Login Required</h3>";
        echo "<p>To test the notification center, you need to be logged in:</p>";
        echo "<ol>";
        echo "<li><a href='{$baseUrl}/auth/login' target='_blank'>Login to your account</a></li>";
        echo "<li>Return to this page to get the test links</li>";
        echo "<li>Or directly visit <a href='{$notificationCenterUrl}' target='_blank'>{$notificationCenterUrl}</a></li>";
        echo "</ol>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
