<?php
/**
 * FCM Frontend Debug Script
 * 
 * Tests FCM JavaScript initialization and token generation
 * 
 * Usage: https://events.rowaneliterides.com/debug_fcm_frontend.php?debug_key=fcm_debug_2025
 */

// Security check
if (!isset($_GET['debug_key']) || $_GET['debug_key'] !== 'fcm_debug_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

require_once APPROOT . '/config/config.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set a test user session if not logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 999; // Test user ID
    $_SESSION['username'] = 'fcm_test_user';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FCM Frontend Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .log { height: 300px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FCM Frontend Debug</h1>
        
        <div class="info status">
            <strong>Debug Session:</strong> User ID <?php echo $_SESSION['user_id']; ?> (<?php echo $_SESSION['username']; ?>)
        </div>
        
        <h2>1. Firebase SDK Loading</h2>
        <div id="firebase-status" class="warning status">⏳ Checking Firebase SDK...</div>
        
        <h2>2. FCM Manager Initialization</h2>
        <div id="fcm-status" class="warning status">⏳ Checking FCM Manager...</div>
        
        <h2>3. Notification Permission</h2>
        <div id="permission-status" class="warning status">⏳ Checking permission...</div>
        <button onclick="requestPermission()">Request Permission</button>
        <button onclick="checkPermission()">Check Permission</button>
        
        <h2>4. FCM Token Generation</h2>
        <div id="token-status" class="warning status">⏳ Waiting for token...</div>
        <button onclick="generateToken()">Generate Token</button>
        <button onclick="sendTokenToServer()">Send Token to Server</button>
        
        <h2>5. Debug Log</h2>
        <div id="debug-log" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
        
        <h2>6. Manual Test</h2>
        <button onclick="runFullTest()">Run Full FCM Test</button>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>
    
    <!-- FCM Notifications Script -->
    <script src="<?php echo BASE_URL; ?>/public/js/fcm-notifications.js"></script>
    
    <script>
        let fcmManager = null;
        let currentToken = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            const color = type === 'error' ? '#f00' : type === 'success' ? '#0f0' : '#fff';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[FCM Debug] ${message}`);
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `${type} status`;
            element.innerHTML = message;
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        async function checkFirebaseSDK() {
            try {
                if (typeof firebase !== 'undefined') {
                    log('✅ Firebase SDK loaded successfully', 'success');
                    updateStatus('firebase-status', '✅ Firebase SDK loaded', 'success');
                    return true;
                } else {
                    log('❌ Firebase SDK not found', 'error');
                    updateStatus('firebase-status', '❌ Firebase SDK not loaded', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Firebase SDK error: ${error.message}`, 'error');
                updateStatus('firebase-status', '❌ Firebase SDK error', 'error');
                return false;
            }
        }
        
        async function checkFCMManager() {
            try {
                if (typeof FCMNotificationManager !== 'undefined') {
                    log('✅ FCMNotificationManager class found', 'success');
                    
                    // Initialize FCM Manager
                    fcmManager = new FCMNotificationManager();
                    log('✅ FCMNotificationManager initialized', 'success');
                    updateStatus('fcm-status', '✅ FCM Manager initialized', 'success');
                    return true;
                } else {
                    log('❌ FCMNotificationManager class not found', 'error');
                    updateStatus('fcm-status', '❌ FCM Manager not found', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ FCM Manager error: ${error.message}`, 'error');
                updateStatus('fcm-status', '❌ FCM Manager error', 'error');
                return false;
            }
        }
        
        async function checkPermission() {
            try {
                const permission = Notification.permission;
                log(`📋 Current permission: ${permission}`);
                
                if (permission === 'granted') {
                    updateStatus('permission-status', '✅ Notifications allowed', 'success');
                } else if (permission === 'denied') {
                    updateStatus('permission-status', '❌ Notifications denied', 'error');
                } else {
                    updateStatus('permission-status', '⚠️ Permission not requested', 'warning');
                }
                
                return permission;
            } catch (error) {
                log(`❌ Permission check error: ${error.message}`, 'error');
                updateStatus('permission-status', '❌ Permission check failed', 'error');
                return 'error';
            }
        }
        
        async function requestPermission() {
            try {
                log('🔔 Requesting notification permission...');
                const permission = await Notification.requestPermission();
                log(`📋 Permission result: ${permission}`);
                
                if (permission === 'granted') {
                    updateStatus('permission-status', '✅ Permission granted!', 'success');
                    log('✅ Notification permission granted', 'success');
                } else {
                    updateStatus('permission-status', '❌ Permission denied', 'error');
                    log('❌ Notification permission denied', 'error');
                }
                
                return permission;
            } catch (error) {
                log(`❌ Permission request error: ${error.message}`, 'error');
                updateStatus('permission-status', '❌ Permission request failed', 'error');
                return 'error';
            }
        }
        
        async function generateToken() {
            try {
                if (!fcmManager) {
                    log('❌ FCM Manager not initialized', 'error');
                    return;
                }
                
                log('🔑 Generating FCM token...');
                updateStatus('token-status', '⏳ Generating token...', 'warning');
                
                // Check if FCM manager has the method
                if (typeof fcmManager.requestPermissionAndGetToken === 'function') {
                    const token = await fcmManager.requestPermissionAndGetToken();
                    
                    if (token) {
                        currentToken = token;
                        log(`✅ FCM token generated: ${token.substring(0, 50)}...`, 'success');
                        updateStatus('token-status', '✅ Token generated successfully', 'success');
                    } else {
                        log(`❌ Token generation failed: No token returned`, 'error');
                        updateStatus('token-status', '❌ Token generation failed', 'error');
                    }
                } else {
                    log('❌ FCM Manager requestPermission method not found', 'error');
                    updateStatus('token-status', '❌ FCM method not found', 'error');
                }
            } catch (error) {
                log(`❌ Token generation error: ${error.message}`, 'error');
                updateStatus('token-status', '❌ Token generation error', 'error');
            }
        }
        
        async function sendTokenToServer() {
            try {
                if (!currentToken) {
                    log('❌ No token available to send', 'error');
                    return;
                }
                
                log('📤 Sending token to server...');
                
                const response = await fetch('/api/pwa/fcm-subscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        fcm_token: currentToken,
                        user_agent: navigator.userAgent
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Token sent to server successfully. Token ID: ${data.token_id}`, 'success');
                } else {
                    log(`❌ Server error: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Send token error: ${error.message}`, 'error');
            }
        }
        
        async function runFullTest() {
            log('🚀 Starting full FCM test...');
            clearLog();
            
            // Step 1: Check Firebase SDK
            const firebaseOk = await checkFirebaseSDK();
            if (!firebaseOk) return;
            
            // Step 2: Check FCM Manager
            const fcmOk = await checkFCMManager();
            if (!fcmOk) return;
            
            // Step 3: Check permission
            const permission = await checkPermission();
            
            // Step 4: Request permission if needed
            if (permission !== 'granted') {
                await requestPermission();
            }
            
            // Step 5: Generate token
            await generateToken();
            
            // Step 6: Send to server
            if (currentToken) {
                await sendTokenToServer();
            }
            
            log('🏁 Full test complete!');
        }
        
        // Auto-run initial checks
        document.addEventListener('DOMContentLoaded', async function() {
            log('🔧 FCM Frontend Debug started');
            
            // Wait a bit for scripts to load
            setTimeout(async () => {
                await checkFirebaseSDK();
                await checkFCMManager();
                await checkPermission();
            }, 1000);
        });
    </script>
</body>
</html>