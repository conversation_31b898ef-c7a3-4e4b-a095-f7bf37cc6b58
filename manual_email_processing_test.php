<?php
/**
 * Manual Email Processing Test
 * 
 * Manually steps through the exact same logic as NotificationService to find the issue
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🧪 Manual Email Processing Test</h1>";

try {
    $db = new Database();
    
    echo "<h2>🔍 Step-by-Step Manual Processing</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Replicating NotificationService Logic:</h3>";
    
    // Step 1: Get pending notifications (same query as NotificationService)
    echo "<h4>Step 1: Getting pending notifications</h4>";
    
    $sql = "SELECT nq.*, u.email, u.phone, u.name as user_name
            FROM notification_queue nq
            JOIN users u ON nq.user_id = u.id
            WHERE nq.status = 'pending' AND nq.scheduled_for <= NOW()
            ORDER BY nq.scheduled_for ASC
            LIMIT 10";
    
    echo "<p><strong>Query:</strong></p>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>{$sql}</pre>";
    
    $db->query($sql);
    $db->execute();
    $notifications = $db->resultSet();
    
    echo "<p><strong>Results:</strong> Found " . count($notifications) . " notifications</p>";
    
    if (empty($notifications)) {
        echo "<p style='color: orange;'>⚠️ No notifications found with the JOIN query</p>";
        
        // Try without JOIN to see if that's the issue
        echo "<h4>Alternative: Check notification_queue without JOIN</h4>";
        $db->query("SELECT * FROM notification_queue WHERE status = 'pending' AND notification_type = 'email'");
        $db->execute();
        $queueOnly = $db->resultSet();
        
        echo "<p>Notifications in queue (no JOIN): " . count($queueOnly) . "</p>";
        
        if (!empty($queueOnly)) {
            echo "<p style='color: red;'>❌ <strong>ISSUE FOUND:</strong> JOIN with users table is failing!</p>";
            
            foreach ($queueOnly as $item) {
                echo "<p>Checking user_id {$item->user_id}...</p>";
                $db->query("SELECT id, email, name FROM users WHERE id = ?");
                $db->bind(1, $item->user_id);
                $db->execute();
                $user = $db->single();
                
                if ($user) {
                    echo "<p style='color: green;'>✅ User {$item->user_id} exists: {$user->email}</p>";
                } else {
                    echo "<p style='color: red;'>❌ User {$item->user_id} NOT FOUND in users table!</p>";
                }
            }
        }
        
    } else {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Type</th><th>User</th><th>Email</th><th>Subject</th><th>Scheduled</th>";
        echo "</tr>";
        
        foreach ($notifications as $notification) {
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->notification_type}</td>";
            echo "<td>{$notification->user_name}</td>";
            echo "<td>{$notification->email}</td>";
            echo "<td>" . htmlspecialchars(substr($notification->subject, 0, 30)) . "...</td>";
            echo "<td>{$notification->scheduled_for}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Step 2: Check notification settings
        echo "<h4>Step 2: Checking notification settings</h4>";
        
        $db->query("SELECT setting_key, setting_value, setting_type FROM notification_settings");
        $db->execute();
        $settingsRows = $db->resultSet();
        
        $settings = [];
        foreach ($settingsRows as $row) {
            $value = $row->setting_value;
            if ($row->setting_type === 'boolean') {
                $value = ($value === '1' || $value === 'true');
            }
            $settings[$row->setting_key] = $value;
        }
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr style='background: #f0f0f0;'><th>Setting</th><th>Value</th><th>Status</th></tr>";
        
        $emailEnabled = $settings['email_enabled'] ?? false;
        echo "<tr>";
        echo "<td><strong>email_enabled</strong></td>";
        echo "<td>" . ($emailEnabled ? 'true' : 'false') . "</td>";
        echo "<td style='color: " . ($emailEnabled ? 'green' : 'red') . ";'>" . ($emailEnabled ? '✅ Enabled' : '❌ Disabled') . "</td>";
        echo "</tr>";
        echo "</table>";
        
        if (!$emailEnabled) {
            echo "<p style='color: red;'>❌ <strong>ISSUE FOUND:</strong> Email notifications are disabled globally!</p>";
        } else {
            // Step 3: Process each notification
            echo "<h4>Step 3: Processing each notification</h4>";
            
            foreach ($notifications as $notification) {
                echo "<h5>Processing notification ID: {$notification->id}</h5>";
                
                // Check if it's an email notification
                if ($notification->notification_type !== 'email') {
                    echo "<p>Skipping non-email notification</p>";
                    continue;
                }
                
                // Check if user has email
                if (empty($notification->email)) {
                    echo "<p style='color: red;'>❌ No email address for user {$notification->user_id}</p>";
                    continue;
                }
                
                // Check user preferences
                $db->query("SELECT * FROM user_notification_preferences WHERE user_id = ?");
                $db->bind(1, $notification->user_id);
                $db->execute();
                $userPrefs = $db->single();
                
                $userEmailEnabled = true; // Default
                if ($userPrefs) {
                    $userEmailEnabled = (bool)$userPrefs->email_notifications;
                }
                
                echo "<p><strong>User email preference:</strong> " . ($userEmailEnabled ? 'Enabled' : 'Disabled') . "</p>";
                
                if (!$userEmailEnabled) {
                    echo "<p style='color: red;'>❌ User has email notifications disabled</p>";
                    continue;
                }
                
                // This notification should be processed
                echo "<p style='color: green;'>✅ This notification should be processed!</p>";
                
                // Try to send it manually
                echo "<form method='post'>";
                echo "<input type='hidden' name='notification_id' value='{$notification->id}'>";
                echo "<button type='submit' name='send_manual' style='background: #28a745; color: white; padding: 5px 10px; border: none; border-radius: 3px;'>Send This Email Manually</button>";
                echo "</form>";
                
                if (isset($_POST['send_manual']) && $_POST['notification_id'] == $notification->id) {
                    echo "<h6>🔄 Manual Send Results:</h6>";
                    
                    try {
                        // Load EmailService
                        require_once APPROOT . '/models/EmailService.php';
                        require_once APPROOT . '/models/SettingsModel.php';
                        
                        $emailService = new EmailService();
                        
                        if (!$emailService->isConfigured()) {
                            echo "<p style='color: red;'>❌ EmailService not configured</p>";
                        } else {
                            // Try to send
                            $sent = $emailService->send(
                                $notification->email,
                                $notification->subject,
                                nl2br(htmlspecialchars($notification->message)),
                                $notification->message
                            );
                            
                            if ($sent) {
                                echo "<p style='color: green;'>✅ Email sent successfully!</p>";
                                
                                // Update status
                                $db->query("UPDATE notification_queue SET status = 'sent', sent_at = NOW() WHERE id = ?");
                                $db->bind(1, $notification->id);
                                $db->execute();
                                
                                echo "<p>Status updated to 'sent'</p>";
                            } else {
                                echo "<p style='color: red;'>❌ Email send failed</p>";
                            }
                        }
                        
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                }
            }
        }
    }
    echo "</div>";
    
    echo "<h2>🎯 Diagnosis Summary</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Possible Issues Found:</h3>";
    
    // Check scheduled_for dates
    $db->query("SELECT id, scheduled_for, created_at FROM notification_queue WHERE status = 'pending' AND notification_type = 'email'");
    $db->execute();
    $pendingEmails = $db->resultSet();
    
    if (!empty($pendingEmails)) {
        echo "<h4>🕐 Scheduled Time Analysis:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Scheduled For</th><th>Current Time</th><th>Ready?</th></tr>";
        
        $currentTime = date('Y-m-d H:i:s');
        
        foreach ($pendingEmails as $email) {
            $isReady = ($email->scheduled_for <= $currentTime) || ($email->scheduled_for === '0000-00-00 00:00:00');
            echo "<tr>";
            echo "<td>{$email->id}</td>";
            echo "<td>{$email->scheduled_for}</td>";
            echo "<td>{$currentTime}</td>";
            echo "<td style='color: " . ($isReady ? 'green' : 'red') . ";'>" . ($isReady ? '✅ Ready' : '❌ Future') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h4>🔧 Recommended Actions:</h4>";
    echo "<ol>";
    echo "<li>Check if users exist in the users table</li>";
    echo "<li>Verify notification_settings has email_enabled = 1</li>";
    echo "<li>Check user_notification_preferences for email_notifications</li>";
    echo "<li>Verify EmailService configuration</li>";
    echo "<li>Check scheduled_for dates are not in the future</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Manual email processing test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
