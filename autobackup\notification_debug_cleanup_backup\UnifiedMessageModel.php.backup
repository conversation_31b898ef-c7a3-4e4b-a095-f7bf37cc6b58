<?php
/**
 * Unified Message Model
 * 
 * This model handles all messaging functionality in a clean, unified way.
 * Replaces the complex notification system with a simple, effective solution.
 */
class UnifiedMessageModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Send a message to a user
     * This handles all delivery methods based on user preferences
     */
    public function sendMessage($fromUserId, $toUserId, $subject, $message, $showId = null, $messageType = 'direct', $requiresReply = false, $parentMessageId = null) {
        try {
            $this->db->beginTransaction();
            
            // Insert message into unified table
            $sql = "INSERT INTO messages 
                    (from_user_id, to_user_id, subject, message, show_id, message_type, requires_reply, parent_message_id, created_at)
                    VALUES (:from_user_id, :to_user_id, :subject, :message, :show_id, :message_type, :requires_reply, :parent_message_id, NOW())";
            
            $this->db->query($sql);
            $this->db->bind(':from_user_id', $fromUserId);
            $this->db->bind(':to_user_id', $toUserId);
            $this->db->bind(':subject', $subject);
            $this->db->bind(':message', $message);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':message_type', $messageType);
            $this->db->bind(':requires_reply', $requiresReply ? 1 : 0);
            $this->db->bind(':parent_message_id', $parentMessageId);
            
            if (!$this->db->execute()) {
                throw new Exception("Failed to insert message");
            }
            
            $messageId = $this->db->lastInsertId();
            
            // Send via user's preferred delivery methods
            $this->deliverMessage($messageId, $toUserId);
            
            $this->db->commit();
            return $messageId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("UnifiedMessageModel::sendMessage - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Deliver message via user's preferred methods
     */
    private function deliverMessage($messageId, $userId) {
        // Get user's notification preferences
        $preferences = $this->getUserNotificationPreferences($userId);
        
        if (!$preferences) {
            // Create default preferences if none exist
            $this->createDefaultNotificationPreferences($userId);
            $preferences = $this->getUserNotificationPreferences($userId);
        }
        
        // Get message details
        $message = $this->getMessageById($messageId);
        if (!$message) return;
        
        // Get sender name
        $sender = $this->getUserById($message->from_user_id);
        $senderName = $sender ? $sender->name : 'System';
        
        // Deliver via enabled methods
        if ($preferences->email_enabled) {
            $this->deliverViaEmail($messageId, $message, $senderName);
        }
        
        if ($preferences->push_enabled) {
            $this->deliverViaPush($messageId, $message, $senderName);
        }
        
        if ($preferences->toast_enabled) {
            $this->deliverViaToast($messageId, $message, $senderName);
        }
        
        if ($preferences->sms_enabled) {
            $this->deliverViaSMS($messageId, $message, $senderName);
        }
    }
    
    /**
     * Deliver message via email
     */
    private function deliverViaEmail($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'email', 'pending');
            
            // Get recipient email
            $recipient = $this->getUserById($message->to_user_id);
            if (!$recipient || !$recipient->email) {
                $this->trackDelivery($messageId, 'email', 'failed', 'No email address');
                return;
            }
            
            // Prepare email content
            $emailSubject = $message->subject;
            $emailBody = "Hello {$recipient->name},\n\n";
            $emailBody .= "You have received a message from {$senderName}:\n\n";
            $emailBody .= "Subject: {$message->subject}\n\n";
            $emailBody .= $message->message . "\n\n";
            $emailBody .= "You can view and reply to this message in your notification center.\n\n";
            $emailBody .= "Thank you!";
            
            // Send email using existing notification queue system
            $this->addToNotificationQueue($message->to_user_id, 'email', $emailSubject, $emailBody);
            
            $this->trackDelivery($messageId, 'email', 'sent');
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'email', 'failed', $e->getMessage());
            error_log("Email delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via push notification
     */
    private function deliverViaPush($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'push', 'pending');
            
            // Create push notification
            $pushTitle = "Message from {$senderName}";
            $pushBody = $message->subject;
            
            // Insert into push notifications table for PWA system
            $sql = "INSERT INTO user_push_notifications 
                    (user_id, title, message, event_id, event_type, created_at)
                    VALUES (:user_id, :title, :message, :event_id, 'message', NOW())";
            
            $this->db->query($sql);
            $this->db->bind(':user_id', $message->to_user_id);
            $this->db->bind(':title', $pushTitle);
            $this->db->bind(':message', $pushBody);
            $this->db->bind(':event_id', $messageId);
            
            if ($this->db->execute()) {
                $this->trackDelivery($messageId, 'push', 'sent');
            } else {
                $this->trackDelivery($messageId, 'push', 'failed', 'Database insert failed');
            }
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'push', 'failed', $e->getMessage());
            error_log("Push delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via toast notification
     */
    private function deliverViaToast($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'toast', 'pending');
            
            // Create toast notification
            $toastTitle = "New Message";
            $toastBody = "From {$senderName}: {$message->subject}";
            
            // Insert into toast notifications table
            $sql = "INSERT INTO user_toast_notifications 
                    (user_id, title, message, event_id, event_type, created_at)
                    VALUES (:user_id, :title, :message, :event_id, 'message', NOW())";
            
            $this->db->query($sql);
            $this->db->bind(':user_id', $message->to_user_id);
            $this->db->bind(':title', $toastTitle);
            $this->db->bind(':message', $toastBody);
            $this->db->bind(':event_id', $messageId);
            
            if ($this->db->execute()) {
                $this->trackDelivery($messageId, 'toast', 'sent');
            } else {
                $this->trackDelivery($messageId, 'toast', 'failed', 'Database insert failed');
            }
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'toast', 'failed', $e->getMessage());
            error_log("Toast delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via SMS
     */
    private function deliverViaSMS($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'sms', 'pending');
            
            // Get recipient phone
            $recipient = $this->getUserById($message->to_user_id);
            if (!$recipient || !$recipient->phone) {
                $this->trackDelivery($messageId, 'sms', 'failed', 'No phone number');
                return;
            }
            
            // Prepare SMS content
            $smsMessage = "Message from {$senderName}: {$message->subject}. View full message in your notification center.";
            
            // Add to notification queue for SMS processing
            $this->addToNotificationQueue($message->to_user_id, 'sms', 'New Message', $smsMessage);
            
            $this->trackDelivery($messageId, 'sms', 'sent');
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'sms', 'failed', $e->getMessage());
            error_log("SMS delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Track delivery attempt
     */
    private function trackDelivery($messageId, $method, $status, $errorMessage = null) {
        $sql = "INSERT INTO message_deliveries 
                (message_id, delivery_method, status, sent_at, error_message, attempts, last_attempt, created_at)
                VALUES (:message_id, :method, :status, :sent_at, :error_message, 1, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                sent_at = VALUES(sent_at),
                error_message = VALUES(error_message),
                attempts = attempts + 1,
                last_attempt = NOW()";
        
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':method', $method);
        $this->db->bind(':status', $status);
        $this->db->bind(':sent_at', $status === 'sent' ? gmdate('Y-m-d H:i:s') : null);
        $this->db->bind(':error_message', $errorMessage);
        
        $this->db->execute();
    }
    
    /**
     * Add to notification queue for email/SMS processing
     */
    private function addToNotificationQueue($userId, $type, $subject, $message) {
        $sql = "INSERT INTO notification_queue 
                (user_id, notification_type, subject, message, status, created_at)
                VALUES (:user_id, :type, :subject, :message, 'pending', NOW())";
        
        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':type', $type);
        $this->db->bind(':subject', $subject);
        $this->db->bind(':message', $message);
        
        $this->db->execute();
    }
    
    /**
     * Get all messages for a user (unified view)
     */
    public function getUserMessages($userId, $status = 'all', $limit = 20, $offset = 0) {
        $sql = "SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE m.to_user_id = ?";
        
        // Add status filter
        if ($status === 'unread') {
            $sql .= " AND m.is_read = 0 AND m.is_archived = 0";
        } elseif ($status === 'read') {
            $sql .= " AND m.is_read = 1 AND m.is_archived = 0";
        } elseif ($status === 'archived') {
            $sql .= " AND m.is_archived = 1";
        } else {
            $sql .= " AND m.is_archived = 0"; // Default: exclude archived
        }
        
        $sql .= " ORDER BY m.created_at DESC LIMIT ? OFFSET ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        $this->db->bind(2, $limit);
        $this->db->bind(3, $offset);
        
        $results = $this->db->resultSet();
        
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("getUserMessages - Status: $status, User: $userId, SQL: $sql");
            error_log("getUserMessages - Found: " . count($results));
            foreach ($results as $i => $msg) {
                error_log("Message $i: ID={$msg->id}, Read={$msg->is_read}, Archived={$msg->is_archived}");
            }
        }
        
        return $results;
    }
    
    /**
     * Get message counts
     */
    public function getMessageCounts($userId) {
        // Let's debug each query step by step
        
        // Count total active messages (non-archived)
        $sql1 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_archived = 0";
        $this->db->query($sql1);
        $this->db->bind(1, $userId);
        $result1 = $this->db->single();
        $totalCount = (int)$result1->count;
        
        // Count unread messages
        $sql2 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_read = 0 AND is_archived = 0";
        $this->db->query($sql2);
        $this->db->bind(1, $userId);
        $result2 = $this->db->single();
        $unreadCount = (int)$result2->count;
        
        // Count archived messages
        $sql3 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_archived = 1";
        $this->db->query($sql3);
        $this->db->bind(1, $userId);
        $result3 = $this->db->single();
        $archivedCount = (int)$result3->count;

        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("=== getMessageCounts Debug ===");
            error_log("User ID: $userId");
            error_log("SQL1: $sql1");
            error_log("Result1: " . json_encode($result1));
            error_log("Total Count: $totalCount");
            error_log("SQL2: $sql2");
            error_log("Result2: " . json_encode($result2));
            error_log("Unread Count: $unreadCount");
            error_log("SQL3: $sql3");
            error_log("Result3: " . json_encode($result3));
            error_log("Archived Count: $archivedCount");
            error_log("=== End Debug ===");
        }

        $counts = [
            'total_unread' => $unreadCount,
            'total_count' => $totalCount,
            'archived_count' => $archivedCount
        ];
        
        // Try alternative method if counts seem wrong
        if (defined('DEBUG_MODE') && DEBUG_MODE && ($totalCount != 4 || $unreadCount != 2)) {
            error_log("COUNTS SEEM WRONG - TRYING ALTERNATIVE METHOD");
            
            $altSql = "SELECT is_read, is_archived FROM messages WHERE to_user_id = ?";
            $this->db->query($altSql);
            $this->db->bind(1, $userId);
            $allMessages = $this->db->resultSet();
            
            $altTotal = 0;
            $altUnread = 0;
            $altArchived = 0;
            
            foreach ($allMessages as $msg) {
                if ($msg->is_archived == 1) {
                    $altArchived++;
                } else {
                    $altTotal++;
                    if ($msg->is_read == 0) {
                        $altUnread++;
                    }
                }
            }
            
            error_log("Alternative method - Total: $altTotal, Unread: $altUnread, Archived: $altArchived");
        }
        
        return $counts;
    }
    
    /**
     * Mark a message as read
     */
    public function markAsRead($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_read = 1, read_at = NOW() 
                WHERE id = ? AND to_user_id = ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Archive a message
     */
    public function archiveMessage($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_archived = 1 
                WHERE id = ? AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Unarchive a message
     */
    public function unarchiveMessage($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_archived = 0 
                WHERE id = ? AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Archive entire thread (parent + all replies)
     */
    public function archiveThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Archive the root message and all its replies
        $sql = "UPDATE messages 
                SET is_archived = 1 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Unarchive entire thread (parent + all replies)
     */
    public function unarchiveThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Unarchive the root message and all its replies
        $sql = "UPDATE messages 
                SET is_archived = 0 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Delete entire thread (parent + all replies)
     */
    public function deleteThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("deleteThread: Could not find root ID for message $messageId, user $userId");
            }
            return false;
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("deleteThread: Deleting thread with root ID $rootId for user $userId");
        }
        
        // Delete the root message and all its replies that the user has access to
        // First, let's see what messages we're about to delete
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            $checkSql = "SELECT id, from_user_id, to_user_id, subject 
                        FROM messages 
                        WHERE (id = ? OR parent_message_id = ?) 
                        AND (from_user_id = ? OR to_user_id = ?)";
            $this->db->query($checkSql);
            $this->db->bind(1, $rootId);
            $this->db->bind(2, $rootId);
            $this->db->bind(3, $userId);
            $this->db->bind(4, $userId);
            $messagesToDelete = $this->db->resultSet();
            error_log("deleteThread: Found " . count($messagesToDelete) . " messages to delete:");
            foreach ($messagesToDelete as $msg) {
                error_log("  - Message {$msg->id}: from {$msg->from_user_id} to {$msg->to_user_id}, subject: {$msg->subject}");
            }
        }
        
        // Delete the root message and all its replies
        $sql = "DELETE FROM messages 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        $result = $this->db->execute();
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("deleteThread: Delete result = " . ($result ? 'success' : 'failed'));
            
            // Check what messages remain for this user
            $checkSql = "SELECT id, subject, parent_message_id FROM messages WHERE to_user_id = ? ORDER BY created_at DESC";
            $this->db->query($checkSql);
            $this->db->bind(1, $userId);
            $remainingMessages = $this->db->resultSet();
            error_log("deleteThread: Remaining messages for user $userId:");
            foreach ($remainingMessages as $msg) {
                error_log("  - Message {$msg->id}: parent={$msg->parent_message_id}, subject={$msg->subject}");
            }
        }
        
        return $result;
    }
    
    /**
     * Get conversation counts (grouped by thread)
     */
    public function getConversationCounts($userId) {
        // Count all conversations (root messages only)
        $sql1 = "SELECT COUNT(*) as count FROM messages 
                WHERE to_user_id = ? AND parent_message_id IS NULL AND is_archived = 0";
        $this->db->query($sql1);
        $this->db->bind(1, $userId);
        $result1 = $this->db->single();
        $allCount = (int)$result1->count;
        
        // Count unread conversations (root messages with unread messages in thread)
        $sql2 = "SELECT COUNT(DISTINCT COALESCE(m.parent_message_id, m.id)) as count 
                FROM messages m 
                WHERE m.to_user_id = ? AND m.is_read = 0 AND m.is_archived = 0";
        $this->db->query($sql2);
        $this->db->bind(1, $userId);
        $result2 = $this->db->single();
        $unreadCount = (int)$result2->count;
        
        // Count archived conversations (root messages only)
        $sql3 = "SELECT COUNT(*) as count FROM messages 
                WHERE to_user_id = ? AND parent_message_id IS NULL AND is_archived = 1";
        $this->db->query($sql3);
        $this->db->bind(1, $userId);
        $result3 = $this->db->single();
        $archivedCount = (int)$result3->count;
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("=== getConversationCounts Debug ===");
            error_log("User ID: $userId");
            error_log("All conversations: $allCount");
            error_log("Unread conversations: $unreadCount");
            error_log("Archived conversations: $archivedCount");
            error_log("=== End Debug ===");
        }
        
        return [
            'all' => $allCount,
            'unread' => $unreadCount,
            'archived' => $archivedCount
        ];
    }
    
    /**
     * Get the root message ID for a thread
     */
    private function getThreadRootId($messageId, $userId) {
        $sql = "SELECT id, parent_message_id 
                FROM messages 
                WHERE id = ? 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        $message = $this->db->single();
        if (!$message) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("getThreadRootId: No message found for ID $messageId, user $userId");
            }
            return false;
        }
        
        $rootId = $message->parent_message_id ?: $message->id;
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("getThreadRootId: Message $messageId has parent {$message->parent_message_id}, returning root ID $rootId");
        }
        
        // If this message has a parent, return the parent ID, otherwise return this message's ID
        return $rootId;
    }
    
    /**
     * Get thread info for confirmation dialogs
     */
    public function getThreadInfo($messageId, $userId) {
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Count messages in thread
        $sql = "SELECT COUNT(*) as total_messages,
                       SUM(CASE WHEN from_user_id = to_user_id THEN 1 ELSE 0 END) as self_replies
                FROM messages 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        $this->db->bind(5, $userId);
        $this->db->bind(6, $userId);
        
        $result = $this->db->single();
        
        return $result ?: (object)['total_messages' => 1, 'self_replies' => 0];
    }
    
    /**
     * Mark all messages in a thread as read
     */
    private function markThreadAsRead($rootMessageId, $userId) {
        $sql = "UPDATE messages 
                SET is_read = 1, read_at = NOW() 
                WHERE (id = ? OR parent_message_id = ?) 
                AND to_user_id = ? 
                AND is_read = 0";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootMessageId);
        $this->db->bind(2, $rootMessageId);
        $this->db->bind(3, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get a specific message thread
     */
    public function getMessageThread($rootMessageId, $userId) {
        $sql = "SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE (m.id = ? OR m.parent_message_id = ?)
                AND (m.to_user_id = ? OR m.from_user_id = ?)
                ORDER BY m.created_at ASC";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootMessageId);
        $this->db->bind(2, $rootMessageId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        $results = $this->db->resultSet();
        
        // Mark all unread messages in this thread as read
        $this->markThreadAsRead($rootMessageId, $userId);
        
        // Organize messages into thread structure
        $thread = [];
        foreach ($results as $msg) {
            $thread[] = $msg;
        }
        
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("=== MESSAGE THREAD DEBUG ===");
            error_log("Root Message ID: $rootMessageId");
            error_log("User ID: $userId");
            error_log("Thread contains " . count($thread) . " messages");
            foreach ($thread as $index => $msg) {
                error_log("Message $index: ID={$msg->id}, Parent={$msg->parent_message_id}, From={$msg->from_user_id}, To={$msg->to_user_id}, Subject={$msg->subject}");
            }
            error_log("=== END THREAD DEBUG ===");
        }
        
        return $thread;
    }
    
    /**
     * Reply to a message
     */
    public function replyToMessage($messageId, $fromUserId, $replyMessage) {
        // Get the original message to determine the thread root
        $originalMessage = $this->getMessageById($messageId);
        if (!$originalMessage) {
            return false;
        }
        
        // Determine the root message ID and recipient
        $rootMessageId = $originalMessage->parent_message_id ?: $originalMessage->id;
        $toUserId = $originalMessage->from_user_id;
        
        // Create reply subject
        $subject = $originalMessage->subject;
        if (!str_starts_with($subject, 'Re: ')) {
            $subject = 'Re: ' . $subject;
        }
        
        // Send the reply
        return $this->sendMessage(
            $fromUserId,
            $toUserId,
            $subject,
            $replyMessage,
            $originalMessage->show_id,
            'direct',
            false,
            $rootMessageId
        );
    }
    
    /**
     * Get user notification preferences
     */
    private function getUserNotificationPreferences($userId) {
        $sql = "SELECT * FROM user_notification_preferences WHERE user_id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        return $this->db->single();
    }
    
    /**
     * Create default notification preferences for a user
     */
    private function createDefaultNotificationPreferences($userId) {
        $sql = "INSERT INTO user_notification_preferences 
                (user_id, email_enabled, push_enabled, toast_enabled, sms_enabled, created_at)
                VALUES (?, 1, 1, 1, 0, NOW())";
        
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        return $this->db->execute();
    }
    
    /**
     * Get message by ID
     */
    private function getMessageById($messageId) {
        $sql = "SELECT * FROM messages WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        return $this->db->single();
    }
    
    /**
     * Get user by ID
     */
    private function getUserById($userId) {
        $sql = "SELECT * FROM users WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        return $this->db->single();
    }
    
    /**
     * Check if user can send notifications
     */
    public function canUserSendNotifications($userId) {
        // For now, all authenticated users can send notifications
        // This can be expanded with role-based permissions later
        return $userId > 0;
    }
}
?><?php
/**
 * Unified Message Model
 * 
 * This model handles all messaging functionality in a clean, unified way.
 * Replaces the complex notification system with a simple, effective solution.
 */
class UnifiedMessageModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Send a message to a user
     * This handles all delivery methods based on user preferences
     */
    public function sendMessage($fromUserId, $toUserId, $subject, $message, $showId = null, $messageType = 'direct', $requiresReply = false, $parentMessageId = null) {
        try {
            $this->db->beginTransaction();
            
            // Insert message into unified table
            $sql = "INSERT INTO messages 
                    (from_user_id, to_user_id, subject, message, show_id, message_type, requires_reply, parent_message_id, created_at)
                    VALUES (:from_user_id, :to_user_id, :subject, :message, :show_id, :message_type, :requires_reply, :parent_message_id, NOW())";
            
            $this->db->query($sql);
            $this->db->bind(':from_user_id', $fromUserId);
            $this->db->bind(':to_user_id', $toUserId);
            $this->db->bind(':subject', $subject);
            $this->db->bind(':message', $message);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':message_type', $messageType);
            $this->db->bind(':requires_reply', $requiresReply ? 1 : 0);
            $this->db->bind(':parent_message_id', $parentMessageId);
            
            if (!$this->db->execute()) {
                throw new Exception("Failed to insert message");
            }
            
            $messageId = $this->db->lastInsertId();
            
            // Send via user's preferred delivery methods
            $this->deliverMessage($messageId, $toUserId);
            
            $this->db->commit();
            return $messageId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("UnifiedMessageModel::sendMessage - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Deliver message via user's preferred methods
     */
    private function deliverMessage($messageId, $userId) {
        // Get user's notification preferences
        $preferences = $this->getUserNotificationPreferences($userId);
        
        if (!$preferences) {
            // Create default preferences if none exist
            $this->createDefaultNotificationPreferences($userId);
            $preferences = $this->getUserNotificationPreferences($userId);
        }
        
        // Get message details
        $message = $this->getMessageById($messageId);
        if (!$message) return;
        
        // Get sender name
        $sender = $this->getUserById($message->from_user_id);
        $senderName = $sender ? $sender->name : 'System';
        
        // Deliver via enabled methods
        if ($preferences->email_enabled) {
            $this->deliverViaEmail($messageId, $message, $senderName);
        }
        
        if ($preferences->push_enabled) {
            $this->deliverViaPush($messageId, $message, $senderName);
        }
        
        if ($preferences->toast_enabled) {
            $this->deliverViaToast($messageId, $message, $senderName);
        }
        
        if ($preferences->sms_enabled) {
            $this->deliverViaSMS($messageId, $message, $senderName);
        }
    }
    
    /**
     * Deliver message via email
     */
    private function deliverViaEmail($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'email', 'pending');
            
            // Get recipient email
            $recipient = $this->getUserById($message->to_user_id);
            if (!$recipient || !$recipient->email) {
                $this->trackDelivery($messageId, 'email', 'failed', 'No email address');
                return;
            }
            
            // Prepare email content
            $emailSubject = $message->subject;
            $emailBody = "Hello {$recipient->name},\n\n";
            $emailBody .= "You have received a message from {$senderName}:\n\n";
            $emailBody .= "Subject: {$message->subject}\n\n";
            $emailBody .= $message->message . "\n\n";
            $emailBody .= "You can view and reply to this message in your notification center.\n\n";
            $emailBody .= "Thank you!";
            
            // Send email using existing notification queue system
            $this->addToNotificationQueue($message->to_user_id, 'email', $emailSubject, $emailBody);
            
            $this->trackDelivery($messageId, 'email', 'sent');
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'email', 'failed', $e->getMessage());
            error_log("Email delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via push notification
     */
    private function deliverViaPush($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'push', 'pending');
            
            // Create push notification
            $pushTitle = "Message from {$senderName}";
            $pushBody = $message->subject;
            
            // Insert into push notifications table for PWA system
            $sql = "INSERT INTO user_push_notifications 
                    (user_id, title, message, event_id, event_type, created_at)
                    VALUES (:user_id, :title, :message, :event_id, 'message', NOW())";
            
            $this->db->query($sql);
            $this->db->bind(':user_id', $message->to_user_id);
            $this->db->bind(':title', $pushTitle);
            $this->db->bind(':message', $pushBody);
            $this->db->bind(':event_id', $messageId);
            
            if ($this->db->execute()) {
                $this->trackDelivery($messageId, 'push', 'sent');
            } else {
                $this->trackDelivery($messageId, 'push', 'failed', 'Database insert failed');
            }
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'push', 'failed', $e->getMessage());
            error_log("Push delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via toast notification
     */
    private function deliverViaToast($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'toast', 'pending');
            
            // Create toast notification
            $toastTitle = "New Message";
            $toastBody = "From {$senderName}: {$message->subject}";
            
            // Insert into toast notifications table
            $sql = "INSERT INTO user_toast_notifications 
                    (user_id, title, message, event_id, event_type, created_at)
                    VALUES (:user_id, :title, :message, :event_id, 'message', NOW())";
            
            $this->db->query($sql);
            $this->db->bind(':user_id', $message->to_user_id);
            $this->db->bind(':title', $toastTitle);
            $this->db->bind(':message', $toastBody);
            $this->db->bind(':event_id', $messageId);
            
            if ($this->db->execute()) {
                $this->trackDelivery($messageId, 'toast', 'sent');
            } else {
                $this->trackDelivery($messageId, 'toast', 'failed', 'Database insert failed');
            }
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'toast', 'failed', $e->getMessage());
            error_log("Toast delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Deliver message via SMS
     */
    private function deliverViaSMS($messageId, $message, $senderName) {
        try {
            // Track delivery attempt
            $this->trackDelivery($messageId, 'sms', 'pending');
            
            // Get recipient phone
            $recipient = $this->getUserById($message->to_user_id);
            if (!$recipient || !$recipient->phone) {
                $this->trackDelivery($messageId, 'sms', 'failed', 'No phone number');
                return;
            }
            
            // Prepare SMS content
            $smsMessage = "Message from {$senderName}: {$message->subject}. View full message in your notification center.";
            
            // Add to notification queue for SMS processing
            $this->addToNotificationQueue($message->to_user_id, 'sms', 'New Message', $smsMessage);
            
            $this->trackDelivery($messageId, 'sms', 'sent');
            
        } catch (Exception $e) {
            $this->trackDelivery($messageId, 'sms', 'failed', $e->getMessage());
            error_log("SMS delivery failed: " . $e->getMessage());
        }
    }
    
    /**
     * Track delivery attempt
     */
    private function trackDelivery($messageId, $method, $status, $errorMessage = null) {
        $sql = "INSERT INTO message_deliveries 
                (message_id, delivery_method, status, sent_at, error_message, attempts, last_attempt, created_at)
                VALUES (:message_id, :method, :status, :sent_at, :error_message, 1, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                sent_at = VALUES(sent_at),
                error_message = VALUES(error_message),
                attempts = attempts + 1,
                last_attempt = NOW()";
        
        $this->db->query($sql);
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':method', $method);
        $this->db->bind(':status', $status);
        $this->db->bind(':sent_at', $status === 'sent' ? gmdate('Y-m-d H:i:s') : null);
        $this->db->bind(':error_message', $errorMessage);
        
        $this->db->execute();
    }
    
    /**
     * Add to notification queue for email/SMS processing
     */
    private function addToNotificationQueue($userId, $type, $subject, $message) {
        $sql = "INSERT INTO notification_queue 
                (user_id, notification_type, subject, message, status, created_at)
                VALUES (:user_id, :type, :subject, :message, 'pending', NOW())";
        
        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':type', $type);
        $this->db->bind(':subject', $subject);
        $this->db->bind(':message', $message);
        
        $this->db->execute();
    }
    
    /**
     * Get all messages for a user (unified view)
     */
    public function getUserMessages($userId, $status = 'all', $limit = 20, $offset = 0) {
        $sql = "SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE m.to_user_id = ?";
        
        // Add status filter
        if ($status === 'unread') {
            $sql .= " AND m.is_read = 0 AND m.is_archived = 0";
        } elseif ($status === 'read') {
            $sql .= " AND m.is_read = 1 AND m.is_archived = 0";
        } elseif ($status === 'archived') {
            $sql .= " AND m.is_archived = 1";
        } else {
            $sql .= " AND m.is_archived = 0"; // Default: exclude archived
        }
        
        $sql .= " ORDER BY m.created_at DESC LIMIT ? OFFSET ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        $this->db->bind(2, $limit);
        $this->db->bind(3, $offset);
        
        $results = $this->db->resultSet();
        
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("getUserMessages - Status: $status, User: $userId, SQL: $sql");
            error_log("getUserMessages - Found: " . count($results));
            foreach ($results as $i => $msg) {
                error_log("Message $i: ID={$msg->id}, Read={$msg->is_read}, Archived={$msg->is_archived}");
            }
        }
        
        return $results;
    }
    
    /**
     * Get message counts
     */
    public function getMessageCounts($userId) {
        // Let's debug each query step by step
        
        // Count total active messages (non-archived)
        $sql1 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_archived = 0";
        $this->db->query($sql1);
        $this->db->bind(1, $userId);
        $result1 = $this->db->single();
        $totalCount = (int)$result1->count;
        
        // Count unread messages
        $sql2 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_read = 0 AND is_archived = 0";
        $this->db->query($sql2);
        $this->db->bind(1, $userId);
        $result2 = $this->db->single();
        $unreadCount = (int)$result2->count;
        
        // Count archived messages
        $sql3 = "SELECT COUNT(*) as count FROM messages WHERE to_user_id = ? AND is_archived = 1";
        $this->db->query($sql3);
        $this->db->bind(1, $userId);
        $result3 = $this->db->single();
        $archivedCount = (int)$result3->count;

        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("=== getMessageCounts Debug ===");
            error_log("User ID: $userId");
            error_log("SQL1: $sql1");
            error_log("Result1: " . json_encode($result1));
            error_log("Total Count: $totalCount");
            error_log("SQL2: $sql2");
            error_log("Result2: " . json_encode($result2));
            error_log("Unread Count: $unreadCount");
            error_log("SQL3: $sql3");
            error_log("Result3: " . json_encode($result3));
            error_log("Archived Count: $archivedCount");
            error_log("=== End Debug ===");
        }

        $counts = [
            'total_unread' => $unreadCount,
            'total_count' => $totalCount,
            'archived_count' => $archivedCount
        ];
        
        // Try alternative method if counts seem wrong
        if (defined('DEBUG_MODE') && DEBUG_MODE && ($totalCount != 4 || $unreadCount != 2)) {
            error_log("COUNTS SEEM WRONG - TRYING ALTERNATIVE METHOD");
            
            $altSql = "SELECT is_read, is_archived FROM messages WHERE to_user_id = ?";
            $this->db->query($altSql);
            $this->db->bind(1, $userId);
            $allMessages = $this->db->resultSet();
            
            $altTotal = 0;
            $altUnread = 0;
            $altArchived = 0;
            
            foreach ($allMessages as $msg) {
                if ($msg->is_archived == 1) {
                    $altArchived++;
                } else {
                    $altTotal++;
                    if ($msg->is_read == 0) {
                        $altUnread++;
                    }
                }
            }
            
            error_log("Alternative method - Total: $altTotal, Unread: $altUnread, Archived: $altArchived");
        }
        
        return $counts;
    }
    
    /**
     * Mark a message as read
     */
    public function markAsRead($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_read = 1, read_at = NOW() 
                WHERE id = ? AND to_user_id = ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Archive a message
     */
    public function archiveMessage($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_archived = 1 
                WHERE id = ? AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Unarchive a message
     */
    public function unarchiveMessage($messageId, $userId) {
        $sql = "UPDATE messages 
                SET is_archived = 0 
                WHERE id = ? AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Archive entire thread (parent + all replies)
     */
    public function archiveThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Archive the root message and all its replies
        $sql = "UPDATE messages 
                SET is_archived = 1 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Unarchive entire thread (parent + all replies)
     */
    public function unarchiveThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Unarchive the root message and all its replies
        $sql = "UPDATE messages 
                SET is_archived = 0 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Delete entire thread (parent + all replies)
     */
    public function deleteThread($messageId, $userId) {
        // First get the root message ID
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("deleteThread: Could not find root ID for message $messageId, user $userId");
            }
            return false;
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("deleteThread: Deleting thread with root ID $rootId for user $userId");
        }
        
        // Delete the root message and all its replies that the user has access to
        // First, let's see what messages we're about to delete
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            $checkSql = "SELECT id, from_user_id, to_user_id, subject 
                        FROM messages 
                        WHERE (id = ? OR parent_message_id = ?) 
                        AND (from_user_id = ? OR to_user_id = ?)";
            $this->db->query($checkSql);
            $this->db->bind(1, $rootId);
            $this->db->bind(2, $rootId);
            $this->db->bind(3, $userId);
            $this->db->bind(4, $userId);
            $messagesToDelete = $this->db->resultSet();
            error_log("deleteThread: Found " . count($messagesToDelete) . " messages to delete:");
            foreach ($messagesToDelete as $msg) {
                error_log("  - Message {$msg->id}: from {$msg->from_user_id} to {$msg->to_user_id}, subject: {$msg->subject}");
            }
        }
        
        // Delete the root message and all its replies
        $sql = "DELETE FROM messages 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        $result = $this->db->execute();
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("deleteThread: Delete result = " . ($result ? 'success' : 'failed'));
            
            // Check what messages remain for this user
            $checkSql = "SELECT id, subject, parent_message_id FROM messages WHERE to_user_id = ? ORDER BY created_at DESC";
            $this->db->query($checkSql);
            $this->db->bind(1, $userId);
            $remainingMessages = $this->db->resultSet();
            error_log("deleteThread: Remaining messages for user $userId:");
            foreach ($remainingMessages as $msg) {
                error_log("  - Message {$msg->id}: parent={$msg->parent_message_id}, subject={$msg->subject}");
            }
        }
        
        return $result;
    }
    
    /**
     * Get conversation counts (grouped by thread)
     */
    public function getConversationCounts($userId) {
        // Count all conversations (root messages only)
        $sql1 = "SELECT COUNT(*) as count FROM messages 
                WHERE to_user_id = ? AND parent_message_id IS NULL AND is_archived = 0";
        $this->db->query($sql1);
        $this->db->bind(1, $userId);
        $result1 = $this->db->single();
        $allCount = (int)$result1->count;
        
        // Count unread conversations (root messages with unread messages in thread)
        $sql2 = "SELECT COUNT(DISTINCT COALESCE(m.parent_message_id, m.id)) as count 
                FROM messages m 
                WHERE m.to_user_id = ? AND m.is_read = 0 AND m.is_archived = 0";
        $this->db->query($sql2);
        $this->db->bind(1, $userId);
        $result2 = $this->db->single();
        $unreadCount = (int)$result2->count;
        
        // Count archived conversations (root messages only)
        $sql3 = "SELECT COUNT(*) as count FROM messages 
                WHERE to_user_id = ? AND parent_message_id IS NULL AND is_archived = 1";
        $this->db->query($sql3);
        $this->db->bind(1, $userId);
        $result3 = $this->db->single();
        $archivedCount = (int)$result3->count;
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("=== getConversationCounts Debug ===");
            error_log("User ID: $userId");
            error_log("All conversations: $allCount");
            error_log("Unread conversations: $unreadCount");
            error_log("Archived conversations: $archivedCount");
            error_log("=== End Debug ===");
        }
        
        return [
            'all' => $allCount,
            'unread' => $unreadCount,
            'archived' => $archivedCount
        ];
    }
    
    /**
     * Get the root message ID for a thread
     */
    private function getThreadRootId($messageId, $userId) {
        $sql = "SELECT id, parent_message_id 
                FROM messages 
                WHERE id = ? 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        $this->db->bind(2, $userId);
        $this->db->bind(3, $userId);
        
        $message = $this->db->single();
        if (!$message) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("getThreadRootId: No message found for ID $messageId, user $userId");
            }
            return false;
        }
        
        $rootId = $message->parent_message_id ?: $message->id;
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("getThreadRootId: Message $messageId has parent {$message->parent_message_id}, returning root ID $rootId");
        }
        
        // If this message has a parent, return the parent ID, otherwise return this message's ID
        return $rootId;
    }
    
    /**
     * Get thread info for confirmation dialogs
     */
    public function getThreadInfo($messageId, $userId) {
        $rootId = $this->getThreadRootId($messageId, $userId);
        if (!$rootId) {
            return false;
        }
        
        // Count messages in thread
        $sql = "SELECT COUNT(*) as total_messages,
                       SUM(CASE WHEN from_user_id = to_user_id THEN 1 ELSE 0 END) as self_replies
                FROM messages 
                WHERE (id = ? OR parent_message_id = ?) 
                AND (from_user_id = ? OR to_user_id = ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootId);
        $this->db->bind(2, $rootId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        $this->db->bind(5, $userId);
        $this->db->bind(6, $userId);
        
        $result = $this->db->single();
        
        return $result ?: (object)['total_messages' => 1, 'self_replies' => 0];
    }
    
    /**
     * Mark all messages in a thread as read
     */
    private function markThreadAsRead($rootMessageId, $userId) {
        $sql = "UPDATE messages 
                SET is_read = 1, read_at = NOW() 
                WHERE (id = ? OR parent_message_id = ?) 
                AND to_user_id = ? 
                AND is_read = 0";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootMessageId);
        $this->db->bind(2, $rootMessageId);
        $this->db->bind(3, $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get a specific message thread
     */
    public function getMessageThread($rootMessageId, $userId) {
        $sql = "SELECT 
                    m.*,
                    u.name as from_user_name,
                    u.email as from_user_email,
                    s.name as show_title,
                    s.location as show_location
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                WHERE (m.id = ? OR m.parent_message_id = ?)
                AND (m.to_user_id = ? OR m.from_user_id = ?)
                ORDER BY m.created_at ASC";
        
        $this->db->query($sql);
        $this->db->bind(1, $rootMessageId);
        $this->db->bind(2, $rootMessageId);
        $this->db->bind(3, $userId);
        $this->db->bind(4, $userId);
        
        $results = $this->db->resultSet();
        
        // Mark all unread messages in this thread as read
        $this->markThreadAsRead($rootMessageId, $userId);
        
        // Organize messages into thread structure
        $thread = [];
        foreach ($results as $msg) {
            $thread[] = $msg;
        }
        
        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("=== MESSAGE THREAD DEBUG ===");
            error_log("Root Message ID: $rootMessageId");
            error_log("User ID: $userId");
            error_log("Thread contains " . count($thread) . " messages");
            foreach ($thread as $index => $msg) {
                error_log("Message $index: ID={$msg->id}, Parent={$msg->parent_message_id}, From={$msg->from_user_id}, To={$msg->to_user_id}, Subject={$msg->subject}");
            }
            error_log("=== END THREAD DEBUG ===");
        }
        
        return $thread;
    }
    
    /**
     * Reply to a message
     */
    public function replyToMessage($messageId, $fromUserId, $replyMessage) {
        // Get the original message to determine the thread root
        $originalMessage = $this->getMessageById($messageId);
        if (!$originalMessage) {
            return false;
        }
        
        // Determine the root message ID and recipient
        $rootMessageId = $originalMessage->parent_message_id ?: $originalMessage->id;
        $toUserId = $originalMessage->from_user_id;
        
        // Create reply subject
        $subject = $originalMessage->subject;
        if (!str_starts_with($subject, 'Re: ')) {
            $subject = 'Re: ' . $subject;
        }
        
        // Send the reply
        return $this->sendMessage(
            $fromUserId,
            $toUserId,
            $subject,
            $replyMessage,
            $originalMessage->show_id,
            'direct',
            false,
            $rootMessageId
        );
    }
    
    /**
     * Get user notification preferences
     */
    private function getUserNotificationPreferences($userId) {
        $sql = "SELECT * FROM user_notification_preferences WHERE user_id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        return $this->db->single();
    }
    
    /**
     * Create default notification preferences for a user
     */
    private function createDefaultNotificationPreferences($userId) {
        $sql = "INSERT INTO user_notification_preferences 
                (user_id, email_enabled, push_enabled, toast_enabled, sms_enabled, created_at)
                VALUES (?, 1, 1, 1, 0, NOW())";
        
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        return $this->db->execute();
    }
    
    /**
     * Get message by ID
     */
    private function getMessageById($messageId) {
        $sql = "SELECT * FROM messages WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $messageId);
        return $this->db->single();
    }
    
    /**
     * Get user by ID
     */
    private function getUserById($userId) {
        $sql = "SELECT * FROM users WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $userId);
        return $this->db->single();
    }
    
    /**
     * Check if user can send notifications
     */
    public function canUserSendNotifications($userId) {
        // For now, all authenticated users can send notifications
        // This can be expanded with role-based permissions later
        return $userId > 0;
    }
}
?>