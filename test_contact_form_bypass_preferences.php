<?php
/**
 * Test Contact Form Bypass User Preferences
 * 
 * Tests that contact forms are delivered even when admin users have email notifications disabled
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>📧 Test Contact Form Bypass User Preferences</h1>";

try {
    $db = new Database();
    
    echo "<h2>🎯 Contact Form Priority System</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Critical Communication Override:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Contact forms are critical business communications</strong></li>";
    echo "<li>✅ <strong>Should bypass individual user notification preferences</strong></li>";
    echo "<li>✅ <strong>Only respect global system-wide email settings</strong></li>";
    echo "<li>✅ <strong>Ensure admins always receive contact form messages</strong></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔧 Implementation Details</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📝 How Contact Form Override Works:</h3>";
    
    echo "<h4>1. Critical Notification Marking:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// In UnifiedMessageModel::deliverViaEmail()\n";
    echo "\$isContactForm = strpos(\$emailSubject, 'Contact Form:') !== false;\n";
    echo "\$this->addToNotificationQueue(\$userId, 'email', \$subject, \$body, \$isContactForm);\n\n";
    echo "// Sets notification_category = 'critical' for contact forms";
    echo "</pre>";
    
    echo "<h4>2. User Preference Bypass:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// In NotificationService::sendEmailNotification()\n";
    echo "\$isCritical = isset(\$notification->notification_category) && \$notification->notification_category === 'critical';\n\n";
    echo "if (!\$isCritical) {\n";
    echo "    // Check user preferences for normal notifications\n";
    echo "    \$userPrefs = \$this->getUserNotificationPreferences(\$userId);\n";
    echo "    if (\$userPrefs && !\$userPrefs->email_notifications) {\n";
    echo "        return false; // Skip if user disabled emails\n";
    echo "    }\n";
    echo "} else {\n";
    echo "    // Critical notifications bypass user preferences\n";
    echo "    // Contact forms are always delivered!\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>👤 Admin User Notification Settings</h2>";
    
    // Check admin users and their notification preferences
    $db->query("SELECT u.id, u.name, u.email, u.role, 
                       unp.email_notifications, unp.push_notifications, unp.toast_notifications
                FROM users u 
                LEFT JOIN user_notification_preferences unp ON u.id = unp.user_id 
                WHERE u.role = 'admin' AND u.status = 'active'
                ORDER BY u.name");
    $db->execute();
    $adminUsers = $db->resultSet();
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Admin User Email Preferences:</h3>";
    
    if (!empty($adminUsers)) {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Admin</th><th>Email</th><th>Email Notifications</th><th>Contact Form Delivery</th></tr>";
        
        foreach ($adminUsers as $admin) {
            $emailPref = $admin->email_notifications ? 'Enabled' : 'Disabled';
            $emailColor = $admin->email_notifications ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($admin->name) . "</strong></td>";
            echo "<td>" . htmlspecialchars($admin->email) . "</td>";
            echo "<td style='color: {$emailColor};'>{$emailPref}</td>";
            echo "<td style='color: green;'><strong>✅ Will Receive</strong> (Critical Override)</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $disabledCount = 0;
        foreach ($adminUsers as $admin) {
            if (!$admin->email_notifications) {
                $disabledCount++;
            }
        }
        
        if ($disabledCount > 0) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
            echo "<h4>✅ Critical Override Active:</h4>";
            echo "<p><strong>{$disabledCount} admin(s) have email notifications disabled</strong>, but they will still receive contact form messages because:</p>";
            echo "<ul>";
            echo "<li>Contact forms are marked as <strong>critical communications</strong></li>";
            echo "<li>Critical notifications <strong>bypass user preferences</strong></li>";
            echo "<li>Only global email settings can disable contact form delivery</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<p style='color: green;'>✅ All admins have email notifications enabled</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No active admin users found</p>";
    }
    echo "</div>";
    
    echo "<h2>🌐 Global Email Settings</h2>";
    
    // Check global notification settings
    $db->query("SELECT setting_key, setting_value FROM notification_settings WHERE setting_key = 'email_enabled'");
    $db->execute();
    $globalEmailSetting = $db->single();
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📊 System-Wide Email Status:</h3>";
    
    if ($globalEmailSetting) {
        $emailEnabled = $globalEmailSetting->setting_value === '1' || $globalEmailSetting->setting_value === 'true';
        $statusColor = $emailEnabled ? 'green' : 'red';
        $statusText = $emailEnabled ? '✅ Enabled' : '❌ Disabled';
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr style='background: #f0f0f0;'><th>Setting</th><th>Value</th><th>Status</th><th>Effect on Contact Forms</th></tr>";
        echo "<tr>";
        echo "<td><strong>Global Email Notifications</strong></td>";
        echo "<td>{$globalEmailSetting->setting_value}</td>";
        echo "<td style='color: {$statusColor};'>{$statusText}</td>";
        
        if ($emailEnabled) {
            echo "<td style='color: green;'>✅ Contact forms will be delivered</td>";
        } else {
            echo "<td style='color: red;'>❌ Contact forms will NOT be delivered</td>";
        }
        echo "</tr>";
        echo "</table>";
        
        if (!$emailEnabled) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
            echo "<h4>⚠️ Critical Issue:</h4>";
            echo "<p><strong>Global email notifications are disabled!</strong></p>";
            echo "<p>Even critical contact forms will not be delivered when global email is disabled.</p>";
            echo "<p><strong>Recommendation:</strong> Enable global email notifications to ensure contact form delivery.</p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Global email setting not found in database</p>";
    }
    echo "</div>";
    
    echo "<h2>🧪 Test Contact Form Processing</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Test the Override System:</h3>";
    
    // Check for pending contact form notifications
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE status = 'pending' AND subject LIKE 'Contact Form:%'");
    $db->execute();
    $pendingContactForms = $db->single();
    $pendingCount = $pendingContactForms->count ?? 0;
    
    echo "<p><strong>Pending contact form notifications:</strong> {$pendingCount}</p>";
    
    if ($pendingCount > 0) {
        echo "<form method='post'>";
        echo "<button type='submit' name='test_processing' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Contact Form Processing</button>";
        echo "</form>";
        
        if (isset($_POST['test_processing'])) {
            echo "<h4>🔄 Processing Test Results:</h4>";
            
            try {
                // Load required classes
                require_once APPROOT . '/models/NotificationService.php';
                require_once APPROOT . '/models/NotificationModel.php';
                require_once APPROOT . '/models/EmailService.php';
                require_once APPROOT . '/models/SettingsModel.php';
                
                $notificationService = new NotificationService();
                $results = $notificationService->processPendingNotifications(5);
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Processing Results:</strong><br>";
                echo "Processed: {$results['processed']}<br>";
                echo "Sent: {$results['sent']}<br>";
                echo "Failed: {$results['failed']}<br>";
                
                if (!empty($results['errors'])) {
                    echo "<strong>Errors:</strong><br>";
                    foreach ($results['errors'] as $error) {
                        echo "• " . htmlspecialchars($error) . "<br>";
                    }
                }
                echo "</div>";
                
                if ($results['sent'] > 0) {
                    echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Contact form emails processed despite user preferences!</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ No emails sent. Check global email settings or SMTP configuration.</p>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
        }
    } else {
        echo "<p>No pending contact form notifications. Submit a contact form to test.</p>";
    }
    
    echo "<h4>🎯 Expected Behavior:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Contact forms processed</strong> even if admin users have email notifications disabled</li>";
    echo "<li>✅ <strong>Critical override working</strong> - user preferences bypassed for contact forms</li>";
    echo "<li>✅ <strong>Only global settings matter</strong> - individual preferences ignored for critical communications</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📋 Summary</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 Contact Form Delivery Logic:</h3>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Notification Type</th><th>Respects User Preferences</th><th>Delivery Condition</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Contact Forms</strong></td>";
    echo "<td style='color: red;'>❌ No (Critical Override)</td>";
    echo "<td>Only global email settings</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Regular Notifications</strong></td>";
    echo "<td style='color: green;'>✅ Yes</td>";
    echo "<td>Global settings AND user preferences</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>System Messages</strong></td>";
    echo "<td style='color: green;'>✅ Yes</td>";
    echo "<td>Global settings AND user preferences</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h3>✅ Files Updated:</h3>";
    echo "<ul>";
    echo "<li><code>models/UnifiedMessageModel.php</code> - Marks contact forms as critical</li>";
    echo "<li><code>models/NotificationService.php</code> - Bypasses user preferences for critical notifications</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Contact form bypass test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
