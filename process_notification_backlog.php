<?php
/**
 * Process the backlog of pending notifications
 * This script will process all pending notifications in batches
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load core classes
require_once APPROOT . '/core/Database.php';

echo "<h1>🔄 Processing Notification Backlog</h1>";

try {
    require_once APPROOT . '/models/NotificationService.php';
    require_once APPROOT . '/models/NotificationModel.php';
    
    $notificationService = new NotificationService();
    $notificationModel = new NotificationModel();
    $db = new Database();
    
    echo "<h2>1. Checking Notification Queue Status</h2>";
    
    // Get pending notification count
    $db->query("SELECT COUNT(*) as total FROM notification_queue WHERE status = 'pending'");
    $result = $db->single();
    $totalPending = $result->total;
    
    echo "<p><strong>Total pending notifications:</strong> $totalPending</p>";
    
    if ($totalPending == 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ No Pending Notifications</h4>";
        echo "<p>All notifications have been processed!</p>";
        echo "</div>";
    } else {
        echo "<h2>2. Processing Notifications in Batches</h2>";
        
        $batchSize = 50;
        $totalProcessed = 0;
        $totalSent = 0;
        $totalFailed = 0;
        $batchNumber = 1;
        
        while ($totalPending > 0) {
            echo "<h3>📦 Batch $batchNumber (Processing $batchSize notifications)</h3>";
            
            $results = $notificationService->processPendingNotifications($batchSize);
            
            echo "<p>Processed: {$results['processed']}, Sent: {$results['sent']}, Failed: {$results['failed']}</p>";
            
            $totalProcessed += $results['processed'];
            $totalSent += $results['sent'];
            $totalFailed += $results['failed'];
            
            if (!empty($results['errors'])) {
                echo "<details><summary>Errors in this batch</summary>";
                echo "<ul>";
                foreach ($results['errors'] as $error) {
                    echo "<li>" . htmlspecialchars($error) . "</li>";
                }
                echo "</ul></details>";
            }
            
            // Check remaining pending notifications
            $db->query("SELECT COUNT(*) as total FROM notification_queue WHERE status = 'pending'");
            $result = $db->single();
            $totalPending = $result->total;
            
            echo "<p>Remaining pending: $totalPending</p>";
            
            $batchNumber++;
            
            // Safety break to prevent infinite loops
            if ($batchNumber > 20) {
                echo "<p>⚠️ Stopped after 20 batches for safety. Remaining notifications: $totalPending</p>";
                break;
            }
            
            // Small delay between batches
            if ($totalPending > 0) {
                sleep(1);
            }
        }
        
        echo "<h2>3. Processing Summary</h2>";
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📊 Final Results</h4>";
        echo "<p><strong>Total Processed:</strong> $totalProcessed</p>";
        echo "<p><strong>Total Sent:</strong> $totalSent</p>";
        echo "<p><strong>Total Failed:</strong> $totalFailed</p>";
        echo "<p><strong>Batches Processed:</strong> " . ($batchNumber - 1) . "</p>";
        echo "</div>";
        
        if ($totalSent > 0) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>✅ Success!</h4>";
            echo "<p>Successfully sent $totalSent notifications!</p>";
            echo "<p>Users should now receive their pending notifications.</p>";
            echo "</div>";
        }
        
        if ($totalFailed > 0) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>⚠️ Some Failures</h4>";
            echo "<p>$totalFailed notifications failed to send.</p>";
            echo "<p>Check the error logs for details.</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>4. Queue Status After Processing</h2>";
    
    // Get final status counts
    $db->query("SELECT status, COUNT(*) as count FROM notification_queue GROUP BY status");
    $statusCounts = $db->resultSet();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f2f2f2;'><th>Status</th><th>Count</th></tr>";
    foreach ($statusCounts as $status) {
        echo "<tr>";
        echo "<td>{$status->status}</td>";
        echo "<td>{$status->count}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>5. Recent Notifications</h2>";
    
    // Show recent notifications
    $db->query("SELECT id, user_id, notification_type, subject, status, created_at, sent_at 
                FROM notification_queue 
                ORDER BY created_at DESC 
                LIMIT 10");
    $recentNotifications = $db->resultSet();
    
    if (!empty($recentNotifications)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>User</th><th>Type</th><th>Subject</th><th>Status</th><th>Created</th><th>Sent</th></tr>";
        foreach ($recentNotifications as $notification) {
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->user_id}</td>";
            echo "<td>{$notification->notification_type}</td>";
            echo "<td>" . substr($notification->subject, 0, 30) . "...</td>";
            echo "<td>{$notification->status}</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "<td>" . ($notification->sent_at ?: 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>6. Next Steps</h2>";
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔧 Recommended Actions</h4>";
    echo "<ol>";
    echo "<li><strong>Set up cron job:</strong> Ensure the cron job runs every 5 minutes to prevent future backlogs</li>";
    echo "<li><strong>Test notifications:</strong> Send test notifications to verify the system is working</li>";
    echo "<li><strong>Check user preferences:</strong> Ensure users have enabled the notification types they want</li>";
    echo "<li><strong>Monitor queue:</strong> Regularly check the notification queue for any issues</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<details><summary>Stack Trace</summary><pre>" . $e->getTraceAsString() . "</pre></details>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Process Notification Backlog</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        h1, h2, h3 { color: #333; }
        h1 { border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
        details { margin: 10px 0; }
        summary { cursor: pointer; font-weight: bold; }
    </style>
</head>
<body>
    <hr>
    <h2>🎯 What This Script Does</h2>
    <ol>
        <li><strong>Checks the notification queue</strong> for pending notifications</li>
        <li><strong>Processes notifications in batches</strong> to avoid timeouts</li>
        <li><strong>Sends all pending notifications</strong> to users</li>
        <li><strong>Reports on success/failure rates</strong> for monitoring</li>
    </ol>
    
    <p><strong>Note:</strong> This script processes the backlog. Set up the cron job to prevent future backlogs!</p>
    
</body>
</html>
