<?php 
// Ensure APPROOT is defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(dirname(__FILE__))));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

require APPROOT . '/views/includes/header.php'; 

// Debug mode check
$debug = defined('DEBUG_MODE') && DEBUG_MODE;
?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="mb-3">Car Shows</h1>
            
            <!-- Search and Filter Section -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Search & Filter Shows</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/show" method="GET" id="showFilterForm">
                        <!-- Hidden field to preserve show_from_date filter -->
                        <?php if (!empty($data['show_from_date'])): ?>
                            <input type="hidden" name="show_from_date" value="<?php echo htmlspecialchars($data['show_from_date']); ?>">
                        <?php endif; ?>
                        <div class="row g-3">
                            <!-- Search Box -->
                            <div class="col-md-12 mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Search shows..." name="search" value="<?php echo htmlspecialchars($data['search'] ?? ''); ?>">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Filters -->
                            <div class="col-md-3">
                                <label for="state" class="form-label">State</label>
                                <select class="form-select" id="state" name="state">
                                    <option value="">All States</option>
                                    <?php if (!empty($data['states'])): ?>
                                        <?php foreach ($data['states'] as $stateObj): ?>
                                            <option value="<?php echo htmlspecialchars($stateObj->state); ?>" <?php echo ($data['state'] == $stateObj->state) ? 'selected' : ''; ?>>
                                                <?php echo isset($stateObj->display_name) ? htmlspecialchars($stateObj->display_name) : htmlspecialchars($stateObj->state); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="city" class="form-label">City</label>
                                <select class="form-select" id="city" name="city">
                                    <option value="">All Cities</option>
                                    <?php if (!empty($data['cities'])): ?>
                                        <?php foreach ($data['cities'] as $cityObj): ?>
                                            <option value="<?php echo htmlspecialchars($cityObj->city); ?>" <?php echo ($data['city'] == $cityObj->city) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($cityObj->city); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="show_date" class="form-label">Show Date</label>
                                <input type="date" class="form-control" id="show_date" name="show_date" value="<?php echo htmlspecialchars($data['show_date'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label for="fan_voting" class="form-label">Fan Voting</label>
                                <select class="form-select" id="fan_voting" name="fan_voting">
                                    <option value="-1" <?php echo ($data['fan_voting'] == -1) ? 'selected' : ''; ?>>All Shows</option>
                                    <option value="1" <?php echo ($data['fan_voting'] == 1) ? 'selected' : ''; ?>>Fan Voting Enabled</option>
                                    <option value="0" <?php echo ($data['fan_voting'] == 0) ? 'selected' : ''; ?>>Fan Voting Disabled</option>
                                </select>
                            </div>
                            
                            <div class="col-12 mt-3">
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Apply Filters
                                    </button>
                                    <a href="<?php echo BASE_URL; ?>/show" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </a>
                                    <?php if (!empty($data['show_from_date']) && $data['show_from_date'] != date('Y-m-d')): ?>
                                        <a href="<?php echo BASE_URL; ?>/show?show_from_date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-info">
                                            <i class="fas fa-calendar-day"></i> Show Upcoming Only
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Results Per Page Selector -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <span class="text-muted">Showing <?php echo count($data['shows']); ?> of <?php echo $data['total_shows']; ?> shows</span>
                    <?php if (empty($data['show_date']) && !empty($data['show_from_date']) && $data['show_from_date'] == date('Y-m-d')): ?>
                        <br><small class="text-info"><i class="fas fa-info-circle"></i> Showing upcoming shows from today forward</small>
                    <?php endif; ?>
                </div>
                <div class="d-flex align-items-center">
                    <label for="per_page" class="form-label me-2 mb-0">Shows per page:</label>
                    <select class="form-select form-select-sm" id="per_page" name="per_page" style="width: auto;">
                        <option value="20" <?php echo ($data['per_page'] == 20) ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo ($data['per_page'] == 50) ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo ($data['per_page'] == 100) ? 'selected' : ''; ?>>100</option>
                    </select>
                </div>
            </div>
            
            <!-- Show Cards -->
            <?php if (empty($data['shows'])): ?>
                <div class="alert alert-info">
                    <p>No shows found matching your criteria. Please try different filters or check back later.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($data['shows'] as $show): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                <?php if (!empty($show->banner_image)): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/shows/thumbnails/<?php echo $show->banner_image; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($show->name); ?>" style="height: 160px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 160px;">
                                        <i class="fas fa-car fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body">
                                    <h5 class="card-title fw-bold mb-2"><?php echo htmlspecialchars($show->name); ?></h5>
                                    <p class="card-text small text-muted mb-4"><?php echo substr(htmlspecialchars($show->description), 0, 100); ?>...</p>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-map-marker-alt"></i></div>
                                            <div>
                                                <strong>Location:</strong><br>
                                                <div class="mt-1"><?php echo htmlspecialchars($show->location); ?></div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-calendar-alt"></i></div>
                                            <div>
                                                <strong>Show Dates:</strong><br>
                                                <div class="mt-1">
                                                    <span class="text-nowrap"><?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></span>
                                                    <?php if ($show->start_date != $show->end_date): ?>
                                                        <span class="mx-1">-</span>
                                                        <span class="text-nowrap"><?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="small text-muted mt-1">
                                                    <i class="far fa-clock me-1"></i>
                                                    <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'g:i A'); ?> - 
                                                    <?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'g:i A'); ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-clipboard-list"></i></div>
                                            <div>
                                                <strong>Registration Period:</strong><br>
                                                <div class="mt-1">
                                                    <span class="text-nowrap"><?php echo formatDateTimeForUser($show->registration_start, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></span>
                                                    <span class="mx-1">-</span>
                                                    <span class="text-nowrap"><?php echo formatDateTimeForUser($show->registration_end, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></span>
                                                </div>
                                                <div class="small text-muted mt-1">
                                                    <i class="far fa-clock me-1"></i>
                                                    <?php echo formatDateTimeForUser($show->registration_start, $_SESSION['user_id'] ?? null, 'g:i A'); ?> - 
                                                    <?php echo formatDateTimeForUser($show->registration_end, $_SESSION['user_id'] ?? null, 'g:i A'); ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if (isset($show->registration_fee)): ?>
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-dollar-sign"></i></div>
                                            <div>
                                                <strong>Registration Fee:</strong><br>
                                                <div class="mt-1">
                                                    <?php if ($show->is_free): ?>
                                                        <span class="badge bg-success">Free Entry</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">$<?php echo number_format($show->registration_fee, 2); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="d-grid mt-4">
                                        <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $show->id; ?>" class="btn btn-primary btn-lg">
                                            <i class="fas fa-info-circle me-1"></i> View Details
                                        </a>
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-white">
                                    <div class="d-flex flex-wrap gap-2 justify-content-between align-items-center">
                                        <?php 
                                        // Use current time for accurate comparisons (all in UTC)
                                        $now = new DateTime('now', new DateTimeZone('UTC'));
                                        $regStart = new DateTime($show->registration_start, new DateTimeZone('UTC'));
                                        $regEnd = new DateTime($show->registration_end, new DateTimeZone('UTC'));
                                        $showStart = new DateTime($show->start_date, new DateTimeZone('UTC'));
                                        $showEnd = new DateTime($show->end_date, new DateTimeZone('UTC'));
                                        
                                        // Show status badges
                                        if ($now > $showEnd): ?>
                                            <span class="badge rounded-pill bg-secondary">
                                                <i class="fas fa-calendar-times me-1"></i> Show Ended
                                            </span>
                                        <?php elseif ($now >= $showStart): ?>
                                            <span class="badge rounded-pill bg-success">
                                                <i class="fas fa-calendar-check me-1"></i> Show Active
                                            </span>
                                        <?php elseif ($now >= $regStart && $now <= $regEnd): ?>
                                            <span class="badge rounded-pill bg-primary">
                                                <i class="fas fa-clipboard-check me-1"></i> Registration Open
                                            </span>
                                        <?php elseif ($now < $regStart): ?>
                                            <span class="badge rounded-pill bg-warning text-dark">
                                                <i class="fas fa-clock me-1"></i> Registration Soon
                                            </span>
                                        <?php else: ?>
                                            <span class="badge rounded-pill bg-secondary">
                                                <i class="fas fa-clipboard-list me-1"></i> Registration Closed
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($show->fan_voting_enabled): ?>
                                            <span class="badge rounded-pill bg-info">
                                                <i class="fas fa-vote-yea me-1"></i> Fan Voting
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($data['total_pages'] > 1): ?>
                <nav aria-label="Show listing pagination">
                    <ul class="pagination justify-content-center">
                        <!-- Previous page link -->
                        <li class="page-item <?php echo ($data['current_page'] <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $data['current_page'] - 1; ?>&per_page=<?php echo $data['per_page']; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?><?php echo !empty($data['show_from_date']) ? '&show_from_date=' . urlencode($data['show_from_date']) : ''; ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                        
                        <!-- Page number links -->
                        <?php 
                        $startPage = max(1, $data['current_page'] - 2);
                        $endPage = min($data['total_pages'], $data['current_page'] + 2);
                        
                        // Always show first page
                        if ($startPage > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=1&per_page=<?php echo $data['per_page']; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?><?php echo !empty($data['show_from_date']) ? '&show_from_date=' . urlencode($data['show_from_date']) : ''; ?>">1</a>
                            </li>
                            <?php if ($startPage > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <!-- Page numbers -->
                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                            <li class="page-item <?php echo ($i == $data['current_page']) ? 'active' : ''; ?>">
                                <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $i; ?>&per_page=<?php echo $data['per_page']; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?><?php echo !empty($data['show_from_date']) ? '&show_from_date=' . urlencode($data['show_from_date']) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <!-- Always show last page -->
                        <?php if ($endPage < $data['total_pages']): ?>
                            <?php if ($endPage < $data['total_pages'] - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $data['total_pages']; ?>&per_page=<?php echo $data['per_page']; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?><?php echo !empty($data['show_from_date']) ? '&show_from_date=' . urlencode($data['show_from_date']) : ''; ?>">
                                    <?php echo $data['total_pages']; ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <!-- Next page link -->
                        <li class="page-item <?php echo ($data['current_page'] >= $data['total_pages']) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $data['current_page'] + 1; ?>&per_page=<?php echo $data['per_page']; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?><?php echo !empty($data['show_from_date']) ? '&show_from_date=' . urlencode($data['show_from_date']) : ''; ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- JavaScript for dynamic filtering -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle per page change
    const perPageSelect = document.getElementById('per_page');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            
            // Update per_page parameter
            urlParams.set('per_page', this.value);
            
            // Reset to page 1 when changing items per page
            urlParams.set('page', '1');
            
            // Redirect to the new URL
            window.location.href = '<?php echo BASE_URL; ?>/show?' + urlParams.toString();
        });
    }
    
    // Handle state change to update city dropdown
    const stateSelect = document.getElementById('state');
    const citySelect = document.getElementById('city');
    
    if (stateSelect && citySelect) {
        stateSelect.addEventListener('change', function() {
            // Clear city selection when state changes
            citySelect.value = '';
            
            // Submit the form to reload with the new state filter
            // This will ensure the city dropdown is populated with cities from the selected state
            document.getElementById('showFilterForm').submit();
        });
    }
    
    <?php if ($debug): ?>
    console.log('Debug mode enabled');
    console.log('Current filters:', {
        search: '<?php echo addslashes($data['search'] ?? ''); ?>',
        state: '<?php echo addslashes($data['state'] ?? ''); ?>',
        city: '<?php echo addslashes($data['city'] ?? ''); ?>',
        show_date: '<?php echo addslashes($data['show_date'] ?? ''); ?>',
        fan_voting: <?php echo $data['fan_voting'] ?? -1; ?>,
        per_page: <?php echo $data['per_page'] ?? 20; ?>,
        current_page: <?php echo $data['current_page'] ?? 1; ?>,
        total_pages: <?php echo $data['total_pages'] ?? 1; ?>,
        total_shows: <?php echo $data['total_shows'] ?? 0; ?>
    });
    <?php endif; ?>
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>