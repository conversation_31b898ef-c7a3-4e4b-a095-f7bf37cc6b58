# Email Parser Multipart Fix

## Issue
Email parser was not correctly handling multipart emails with custom boundaries like:
`------sinikael-?=_1-17528869465410.7023045675010756`

The parser was:
1. Looking for hard-coded `--=-` boundary pattern
2. Assuming `quoted-printable` encoding
3. Not properly extracting text from `7bit` encoded parts
4. Showing HTML parsing artifacts instead of clean text content

## Sample Problematic Email
```
------sinikael-?=_1-17528869465410.7023045675010756
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 7bit
test reply  
 Hello,
Thank you for your inquiry about our events.
Here is the information you requested:
[Please add specific event details here]
If you have any other questions, please do not hesitate to ask.
Best regards,
Admin
Events and Shows Platform
------sinikael-?=_1-17528869465410.7023045675010756
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable
<html>...</html>
------sinikael-?=_1-17528869465410.7023045675010756--
```

## Fix Applied
1. **Dynamic boundary detection**: Extract actual boundary from email content
2. **Multiple encoding support**: Handle 7bit, 8bit, quoted-printable, base64
3. **Improved text extraction**: Better header/content separation
4. **Prefer text/plain**: Prioritize plain text over HTML content
5. **Robust fallbacks**: Multiple fallback methods for edge cases

## Files Modified
- `models/EmailRetrievalService.php` (lines 281-406)

## Expected Result
Email should now display: "test reply Hello, Thank you for your inquiry..." instead of HTML artifacts.

Date: <?php echo date('Y-m-d H:i:s'); ?>