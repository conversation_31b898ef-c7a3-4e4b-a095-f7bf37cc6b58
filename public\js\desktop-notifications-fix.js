/**
 * Desktop Notification Fix v1.0.0
 * 
 * This file provides desktop-specific enhancements for the notification system
 * to ensure proper functionality on desktop browsers.
 * 
 * Location: /public/js/desktop-notifications-fix.js
 * Dependencies: notifications.js, Bootstrap 5
 */

(function() {
    'use strict';
    
    // Detect desktop device (opposite of mobile detection)
    const isDesktop = !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) &&
                      window.innerWidth > 768 &&
                      !('ontouchstart' in window);
    
    if (!isDesktop) {
        console.log('🖥️ Desktop notification fix: Not a desktop device, skipping desktop enhancements');
        return;
    }
    
    console.log('🖥️ Desktop notification fix: Initializing desktop enhancements');
    
    // Wait for DOM and notification manager to be ready
    function initDesktopFix() {
        // Add desktop-specific CSS class to body
        document.body.classList.add('desktop-notifications-enhanced');
        
        // Ensure proper toast container positioning for desktop
        ensureDesktopToastContainer();
        
        // Add desktop-specific keyboard shortcuts
        setupDesktopKeyboardShortcuts();
        
        // Monitor for notification manager and enhance it
        waitForNotificationManager();
    }
    
    function ensureDesktopToastContainer() {
        // Create or enhance the toast container for desktop
        let container = document.getElementById('toast-container');
        
        if (!container) {
            // Create container if it doesn't exist
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
            console.log('🖥️ Desktop: Created toast container');
        }
        
        // Ensure proper desktop styling
        container.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 99999 !important;
            pointer-events: none !important;
            max-width: 350px !important;
        `;
        
        console.log('🖥️ Desktop: Enhanced toast container positioning');
    }
    
    function setupDesktopKeyboardShortcuts() {
        // Desktop-specific keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+N to force reload notifications
            if (e.ctrlKey && e.shiftKey && e.key === 'N') {
                e.preventDefault();
                console.log('🖥️ Desktop: Keyboard shortcut triggered - reloading notifications');
                if (window.notificationManager) {
                    window.notificationManager.loadUnreadNotifications();
                }
            }
            
            // Ctrl+Shift+T to create test toast (for debugging)
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                console.log('🖥️ Desktop: Creating test toast notification');
                createTestToast();
            }
        });
    }
    
    function createTestToast() {
        if (window.notificationManager) {
            const testNotification = {
                id: Date.now(),
                title: 'Desktop Test Toast',
                message: 'This is a test toast notification for desktop - ' + new Date().toLocaleTimeString(),
                created_at: new Date().toISOString(),
                event_type: 'test'
            };
            
            window.notificationManager.showToastNotifications([testNotification]);
            console.log('🖥️ Desktop: Test toast created');
        } else {
            console.log('🖥️ Desktop: NotificationManager not available for test toast');
        }
    }
    
    function waitForNotificationManager() {
        let attempts = 0;
        const maxAttempts = 20; // 10 seconds max wait
        
        const checkForManager = () => {
            attempts++;
            
            if (window.notificationManager) {
                console.log('🖥️ Desktop: NotificationManager found, enhancing...');
                enhanceNotificationManager();
            } else if (attempts < maxAttempts) {
                setTimeout(checkForManager, 500);
            } else {
                console.log('🖥️ Desktop: NotificationManager not found after waiting');
            }
        };
        
        checkForManager();
    }
    
    function enhanceNotificationManager() {
        const manager = window.notificationManager;
        
        // Store original methods
        const originalShowToastNotifications = manager.showToastNotifications;
        const originalLoadUnreadNotifications = manager.loadUnreadNotifications;
        
        // Enhance showToastNotifications for desktop
        manager.showToastNotifications = function(notifications) {
            console.log('🖥️ Desktop: Enhanced showToastNotifications called with', notifications.length, 'notifications');
            
            // Ensure container exists before showing toasts
            ensureDesktopToastContainer();
            
            // Call original method
            const result = originalShowToastNotifications.call(this, notifications);
            
            // Desktop-specific enhancements after showing toasts
            setTimeout(() => {
                const toasts = document.querySelectorAll('.toast-notification');
                toasts.forEach(toast => {
                    // Ensure proper pointer events for desktop
                    toast.style.pointerEvents = 'auto';
                    
                    // Add desktop-specific hover effects
                    toast.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateX(-5px)';
                        this.style.transition = 'transform 0.2s ease';
                    });
                    
                    toast.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateX(0)';
                    });
                });
            }, 100);
            
            return result;
        };
        
        // Enhance loadUnreadNotifications for desktop debugging
        manager.loadUnreadNotifications = function() {
            console.log('🖥️ Desktop: Enhanced loadUnreadNotifications called');
            return originalLoadUnreadNotifications.call(this);
        };
        
        // Force an immediate notification check
        setTimeout(() => {
            console.log('🖥️ Desktop: Forcing initial notification check...');
            manager.loadUnreadNotifications();
        }, 1000);
        
        console.log('🖥️ Desktop: NotificationManager enhanced successfully');
    }
    
    // Add desktop-specific CSS
    function addDesktopCSS() {
        const style = document.createElement('style');
        style.textContent = `
            .desktop-notifications-enhanced .toast-notification {
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                border: 1px solid rgba(0,0,0,0.1) !important;
            }
            
            .desktop-notifications-enhanced .toast-notification:hover {
                box-shadow: 0 6px 20px rgba(0,0,0,0.2) !important;
            }
            
            .desktop-notifications-enhanced #toast-container {
                max-width: 350px !important;
            }
            
            @media (min-width: 1200px) {
                .desktop-notifications-enhanced #toast-container {
                    max-width: 400px !important;
                }
            }
        `;
        document.head.appendChild(style);
        console.log('🖥️ Desktop: Added desktop-specific CSS');
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            initDesktopFix();
            addDesktopCSS();
        });
    } else {
        initDesktopFix();
        addDesktopCSS();
    }
    
    console.log('🖥️ Desktop notification fix: Loaded successfully');
})();