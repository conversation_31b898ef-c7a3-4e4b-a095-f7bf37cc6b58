<?php
/**
 * Fix User Timezone Script
 * This script checks and fixes timezone issues for users
 */

// Include the application bootstrap
require_once 'config/config.php';
require_once 'core/Database.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>User Timezone Fix Script</h1>";

// Create database connection
$db = new Database();

// Check if timezone column exists
echo "<h2>1. Database Schema Check</h2>";

try {
    $db->query("DESCRIBE users");
    $columns = $db->resultSet();
    
    $timezoneColumnExists = false;
    foreach ($columns as $column) {
        if ($column->Field === 'timezone') {
            $timezoneColumnExists = true;
            echo "<p style='color: green;'>✓ Timezone column exists</p>";
            echo "<p><strong>Type:</strong> {$column->Type}</p>";
            echo "<p><strong>Default:</strong> " . ($column->Default ?? 'NULL') . "</p>";
            echo "<p><strong>Null:</strong> {$column->Null}</p>";
            break;
        }
    }
    
    if (!$timezoneColumnExists) {
        echo "<p style='color: red;'>✗ Timezone column does not exist!</p>";
        echo "<p>You need to run the timezone migration script.</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking database schema: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>2. Current User Data</h2>";

// Get all users and their timezone data
try {
    $db->query("SELECT id, name, email, timezone FROM users ORDER BY id");
    $users = $db->resultSet();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Timezone</th><th>Status</th></tr>";
    
    $usersWithoutTimezone = [];
    
    foreach ($users as $user) {
        $timezoneStatus = empty($user->timezone) ? 'NOT SET' : 'SET';
        $statusColor = empty($user->timezone) ? 'red' : 'green';
        
        echo "<tr>";
        echo "<td>{$user->id}</td>";
        echo "<td>{$user->name}</td>";
        echo "<td>{$user->email}</td>";
        echo "<td>" . ($user->timezone ?? 'NULL') . "</td>";
        echo "<td style='color: {$statusColor};'>{$timezoneStatus}</td>";
        echo "</tr>";
        
        if (empty($user->timezone)) {
            $usersWithoutTimezone[] = $user;
        }
    }
    
    echo "</table>";
    
    echo "<p><strong>Total users:</strong> " . count($users) . "</p>";
    echo "<p><strong>Users without timezone:</strong> " . count($usersWithoutTimezone) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting user data: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>3. Fix Users Without Timezone</h2>";

if (count($usersWithoutTimezone) > 0) {
    echo "<p>The following users don't have a timezone set. Setting default to 'America/New_York':</p>";
    
    try {
        $db->query("UPDATE users SET timezone = 'America/New_York' WHERE timezone IS NULL OR timezone = ''");
        $affectedRows = $db->execute();
        
        if ($affectedRows) {
            echo "<p style='color: green;'>✓ Updated {$affectedRows} users with default timezone</p>";
        } else {
            echo "<p style='color: orange;'>No users were updated (they may already have timezones set)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error updating users: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ All users already have timezones set</p>";
}

echo "<h2>4. Verification</h2>";

// Verify the fix
try {
    $db->query("SELECT id, name, timezone FROM users WHERE timezone IS NULL OR timezone = ''");
    $usersStillWithoutTimezone = $db->resultSet();
    
    if (count($usersStillWithoutTimezone) === 0) {
        echo "<p style='color: green;'>✓ All users now have timezones set!</p>";
    } else {
        echo "<p style='color: red;'>✗ " . count($usersStillWithoutTimezone) . " users still don't have timezones:</p>";
        foreach ($usersStillWithoutTimezone as $user) {
            echo "<p>- User ID {$user->id}: {$user->name}</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error verifying fix: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Instructions</h2>";
echo "<p>1. Run this script to fix any users without timezones</p>";
echo "<p>2. Go back to your profile page and try updating your timezone again</p>";
echo "<p>3. The timezone should now save correctly</p>";

?>
