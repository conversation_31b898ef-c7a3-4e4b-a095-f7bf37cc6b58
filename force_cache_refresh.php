<?php
/**
 * Force Cache Refresh for PWA
 * 
 * This script helps force refresh cached JavaScript files in the PWA
 */

require_once 'config/config.php';

echo "<h1>🔄 Force PWA Cache Refresh</h1>";

echo "<h2>📱 PWA Cache Issue</h2>";
echo "<p>The PWA is likely using a cached version of <code>notifications.js</code> that still has the old <code>formatTimeAgo</code> function.</p>";

echo "<h2>🛠️ Solutions</h2>";

echo "<h3>1. 🔄 Force Refresh in PWA</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>On your phone:</strong></p>";
echo "<ol>";
echo "<li>Open the PWA app</li>";
echo "<li>Pull down to refresh (or use refresh button if available)</li>";
echo "<li>If that doesn't work, close the PWA completely and reopen it</li>";
echo "<li>If still not working, try the manual cache clear below</li>";
echo "</ol>";
echo "</div>";

echo "<h3>2. 🧹 Manual Cache Clear</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<p><strong>If refresh doesn't work, try this:</strong></p>";
echo "<ol>";
echo "<li>Open your browser settings</li>";
echo "<li>Go to 'Site Settings' or 'Storage'</li>";
echo "<li>Find your site (events.rowaneliterides.com)</li>";
echo "<li>Clear 'Cached data' or 'Storage'</li>";
echo "<li>Reload the PWA</li>";
echo "</ol>";
echo "</div>";

echo "<h3>3. 🔧 Developer Cache Clear</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; border-left: 4px solid #bee5eb;'>";
echo "<p><strong>If you can access browser dev tools:</strong></p>";
echo "<ol>";
echo "<li>Open Developer Tools (F12)</li>";
echo "<li>Go to 'Application' or 'Storage' tab</li>";
echo "<li>Find 'Service Workers' section</li>";
echo "<li>Click 'Unregister' or 'Update'</li>";
echo "<li>Go to 'Cache Storage' and delete all caches</li>";
echo "<li>Reload the page</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🧪 Test the Fix</h2>";
echo "<p>After clearing the cache, test a toast notification to see if it shows proper timestamps instead of 'NaN days ago'.</p>";

echo "<h3>📋 Current JavaScript File Info</h3>";
$jsFile = 'public/js/notifications.js';
if (file_exists($jsFile)) {
    $fileSize = filesize($jsFile);
    $lastModified = date('Y-m-d H:i:s', filemtime($jsFile));
    echo "<p><strong>File:</strong> {$jsFile}</p>";
    echo "<p><strong>Size:</strong> " . number_format($fileSize) . " bytes</p>";
    echo "<p><strong>Last Modified:</strong> {$lastModified}</p>";
    
    // Check if the file contains the fix
    $content = file_get_contents($jsFile);
    $hasNullCheck = strpos($content, 'if (!dateString || dateString === null') !== false;
    $hasErrorHandling = strpos($content, 'console.warn(\'Invalid date in formatTimeAgo\'') !== false;
    
    echo "<h4>🔍 Fix Status in File:</h4>";
    echo "<p><strong>Null check present:</strong> " . ($hasNullCheck ? '✅ YES' : '❌ NO') . "</p>";
    echo "<p><strong>Error handling present:</strong> " . ($hasErrorHandling ? '✅ YES' : '❌ NO') . "</p>";
    
    if ($hasNullCheck && $hasErrorHandling) {
        echo "<p style='color: green;'>✅ The JavaScript file contains the NaN fix. The issue is likely PWA caching.</p>";
    } else {
        echo "<p style='color: red;'>❌ The JavaScript file doesn't contain the fix. The file may not have been updated properly.</p>";
    }
} else {
    echo "<p style='color: red;'>❌ notifications.js file not found!</p>";
}

echo "<h2>🎯 Alternative: Add Version Parameter</h2>";
echo "<p>To force cache refresh, we can add a version parameter to the JavaScript file:</p>";
echo "<code>&lt;script src='/public/js/notifications.js?v=" . time() . "'&gt;&lt;/script&gt;</code>";

echo "<h2>🔄 Service Worker Cache Management</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>The service worker should automatically update cached files, but sometimes needs manual intervention:</strong></p>";
echo "<ul>";
echo "<li>Service workers cache JavaScript files for performance</li>";
echo "<li>Updates should happen automatically when files change</li>";
echo "<li>Sometimes the cache gets 'stuck' and needs manual clearing</li>";
echo "<li>PWA apps are more aggressive about caching than regular websites</li>";
echo "</ul>";
echo "</div>";

echo "<h2>✅ Expected Result</h2>";
echo "<p>After clearing the cache and reloading:</p>";
echo "<ul>";
echo "<li>✅ Toast notifications should show proper timestamps (e.g., '2 minutes ago')</li>";
echo "<li>✅ No more 'NaN days ago' text</li>";
echo "<li>✅ Push notifications should work with only 1 notification (no duplicates)</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Cache refresh guide generated at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
