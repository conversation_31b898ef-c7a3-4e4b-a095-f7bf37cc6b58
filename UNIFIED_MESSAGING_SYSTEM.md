# Unified Messaging System Documentation

## 🎯 Overview

The Unified Messaging System is a comprehensive communication platform that consolidates all messaging functionality into a single, clean interface. It replaces the complex, duplicated notification system with a simple, effective solution that handles direct messages, system notifications, and all delivery methods.

## 🏗️ Architecture

### Core Components

#### **1. Database Tables**

**Primary Tables:**
- `messages` - Core message storage with threading support
- `message_deliveries` - Tracks delivery attempts and status
- `notification_settings` - Global system settings
- `user_notification_preferences` - Individual user preferences

**Supporting Tables:**
- `notification_queue` - Email/SMS delivery queue
- `fcm_tokens` - Firebase Cloud Messaging tokens
- `users` - User accounts (includes system user ID 1)

#### **2. Models**

**UnifiedMessageModel.php** - Main messaging logic
- Message creation and delivery
- User preference handling
- Conversation threading
- Delivery tracking

**NotificationModel.php** - Legacy notification support
- Global settings management
- Queue processing
- Backward compatibility

#### **3. Controllers**

**NotificationCenterController.php** - Web interface
- Message viewing and management
- AJAX endpoints for real-time updates
- User interaction handling

**NotificationController.php** - Legacy notification processing
- Cron job endpoints
- Queue processing
- Admin management

#### **4. Views**

**notification_center/index.php** - Main message interface
- Unified message display
- Conversation threading
- Filter and search functionality

**notification_center/view.php** - Individual message view
- Message details
- Reply functionality
- Thread navigation

## 🚀 Features

### **Message Types**
- `direct` - User-to-user messages
- `system` - System notifications
- `judging` - Judge-related communications
- `event` - Event notifications
- `admin` - Administrative messages
- `notification` - General notifications

### **Delivery Methods**
- **Email** - Rich HTML emails via notification queue
- **Push** - Browser push notifications via FCM
- **Toast** - In-app popup notifications
- **SMS** - Text messages via multiple providers
- **In-App** - Direct database notifications

### **User Features**
- **Unified Inbox** - All messages in one place
- **Conversation Threading** - Related messages grouped
- **Reply System** - Controlled reply functionality
- **Read/Unread Tracking** - Message status management
- **Archive System** - Message organization
- **Real-time Updates** - Live notification counts

### **Admin Features**
- **Global Settings** - Enable/disable delivery methods
- **User Management** - View user preferences
- **Queue Monitoring** - Track delivery status
- **Delivery Analytics** - Success/failure tracking

## 📊 Database Schema

### Messages Table
```sql
CREATE TABLE `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_user_id` int(10) UNSIGNED NOT NULL,
  `to_user_id` int(10) UNSIGNED NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `show_id` int(11) DEFAULT NULL,
  `parent_message_id` int(11) DEFAULT NULL,
  `message_type` enum('direct','system','judging','event','admin','notification') DEFAULT 'direct',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `requires_reply` tinyint(1) DEFAULT 0,
  `allows_reply` tinyint(1) DEFAULT 1,
  `reply_used` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  `replied_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_to_user` (`to_user_id`),
  KEY `idx_from_user` (`from_user_id`),
  KEY `idx_unread` (`to_user_id`, `is_read`)
);
```

### Message Deliveries Table
```sql
CREATE TABLE `message_deliveries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `delivery_method` enum('email','sms','push','toast','in_app') NOT NULL,
  `status` enum('pending','sent','failed','cancelled') DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `attempts` int(11) DEFAULT 0,
  `last_attempt` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message` (`message_id`),
  KEY `idx_status` (`status`),
  KEY `idx_method` (`delivery_method`)
);
```

## 🔧 How to Send Messages

### Basic Message Sending

```php
<?php
// Initialize the model
require_once APPROOT . '/models/UnifiedMessageModel.php';
$messageModel = new UnifiedMessageModel();

// Send a simple message
$messageId = $messageModel->sendMessage(
    $fromUserId,    // Sender user ID
    $toUserId,      // Recipient user ID
    $subject,       // Message subject
    $message,       // Message content
    $showId,        // Optional: Related show ID
    $messageType,   // Optional: Message type (default: 'direct')
    $requiresReply, // Optional: Whether reply is expected (default: false)
    $parentMessageId // Optional: For threading (default: null)
);

if ($messageId) {
    echo "Message sent successfully! ID: $messageId";
} else {
    echo "Failed to send message";
}
?>
```

### Advanced Message Sending

```php
<?php
// Send a judge message with reply requirement
$messageId = $messageModel->sendMessage(
    $_SESSION['user_id'],  // From current user
    $judgeUserId,          // To judge
    "Judging Assignment",  // Subject
    "Please review category X. [reply] required.", // Message with reply marker
    $showId,               // Related show
    'judging',             // Message type
    true,                  // Requires reply
    null                   // Not a reply
);

// Send a system notification
$messageId = $messageModel->sendMessage(
    1,                     // From system user (ID 1)
    $userId,               // To user
    "Event Reminder",      // Subject
    "Your event starts in 1 hour!", // Message
    $eventId,              // Related event
    'system',              // System message
    false,                 // No reply needed
    null                   // Not a reply
);

// Send a reply to an existing message
$messageId = $messageModel->sendMessage(
    $userId,               // From user
    $originalSenderId,     // To original sender
    "Re: " . $originalSubject, // Reply subject
    "Thank you for the message!", // Reply content
    null,                  // No show relation
    'direct',              // Direct message
    false,                 // No reply needed
    $originalMessageId     // Parent message for threading
);
?>
```

### Message Types and Use Cases

```php
<?php
// Direct user-to-user message
$messageModel->sendMessage($fromUser, $toUser, $subject, $message, null, 'direct');

// System notification
$messageModel->sendMessage(1, $userId, $subject, $message, null, 'system');

// Judge communication
$messageModel->sendMessage($adminId, $judgeId, $subject, $message, $showId, 'judging');

// Event notification
$messageModel->sendMessage(1, $userId, $subject, $message, $eventId, 'event');

// Admin announcement
$messageModel->sendMessage($adminId, $userId, $subject, $message, null, 'admin');
?>
```

## 📁 File Structure

```
/
├── models/
│   ├── UnifiedMessageModel.php          # Main messaging logic
│   └── NotificationModel.php            # Legacy notification support
├── controllers/
│   ├── NotificationCenterController.php # Web interface
│   └── NotificationController.php       # Legacy processing
├── views/
│   ├── notification_center/
│   │   ├── index.php                    # Main message interface
│   │   └── view.php                     # Individual message view
│   └── shared/
│       └── notification_button.php      # Notification subscription
├── public/js/
│   ├── notification-center.js           # Real-time updates
│   ├── notifications.js                 # Legacy notification handling
│   └── fcm-notifications.js             # Firebase messaging
├── database/migrations/
│   ├── create_unified_message_system.sql # Main migration
│   └── add_reply_tracking.sql           # Reply system
├── cron/
│   └── process_notifications.php        # Background processing
└── api/
    ├── notifications/
    │   └── vapid-key.php                # Push notification keys
    └── pwa/
        └── fcm-subscribe.php            # FCM subscription
```

## 🌐 API Endpoints

### Message Management
- `GET /notification_center` - Main message interface
- `GET /notification_center/view/{id}` - View individual message
- `POST /notification_center/markRead` - Mark messages as read
- `POST /notification_center/archive` - Archive messages
- `POST /notification_center/unarchive` - Restore archived messages
- `POST /notification_center/delete` - Delete messages permanently
- `POST /notification_center/reply` - Send reply to message
- `GET /notification_center/getUnreadCount` - Get unread message count

### Push Notifications
- `GET /api/notifications/vapid-key` - Get VAPID public key
- `POST /api/pwa/fcm-subscribe` - Subscribe to FCM notifications
- `POST /api/pwa/fcm-unsubscribe` - Unsubscribe from FCM notifications

### Background Processing
- `GET /notification/process?key=DAILY_KEY` - Process notification queue (cron)

## ⚙️ Configuration

### Environment Variables
```php
// Firebase Cloud Messaging
define('FCM_PROJECT_ID', 'your-project-id');
define('FCM_PRIVATE_KEY_ID', 'your-private-key-id');
define('FCM_PRIVATE_KEY', 'your-private-key');
define('FCM_CLIENT_EMAIL', 'your-client-email');
define('FCM_CLIENT_ID', 'your-client-id');

// VAPID Keys for Web Push
define('VAPID_PUBLIC_KEY', 'your-vapid-public-key');
define('VAPID_PRIVATE_KEY', 'your-vapid-private-key');

// Email Configuration
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', 'your-smtp-username');
define('SMTP_PASSWORD', 'your-smtp-password');

// SMS Configuration (choose one provider)
define('SMS_PROVIDER', 'twilio'); // twilio, textmagic, nexmo, clicksend, plivo
define('TWILIO_SID', 'your-twilio-sid');
define('TWILIO_TOKEN', 'your-twilio-token');
define('TWILIO_FROM', 'your-twilio-phone');
```

### Global Settings
```php
// Enable/disable delivery methods globally
$globalSettings = [
    'email_enabled' => true,
    'sms_enabled' => true,
    'push_enabled' => true,
    'toast_enabled' => true
];
```

## 🔄 Background Processing

### Cron Job Setup
Add to your server's crontab:
```bash
# Process notifications every 2 minutes
*/2 * * * * curl -s "https://yoursite.com/notification/process?key=$(date +%Y-%m-%d | sha256sum | cut -d' ' -f1)"

# Or using PHP directly
*/2 * * * * /usr/bin/php /path/to/your/site/cron/process_notifications.php
```

### Queue Processing
The system automatically:
- Processes pending email/SMS notifications
- Retries failed deliveries (up to 3 attempts)
- Cleans up old read notifications
- Updates delivery status tracking

## 🎨 User Interface

### Desktop Features
- **Bell Icon**: Changes to bright yellow when unread messages exist
- **Count Badge**: Shows unread count positioned near bell icon
- **Real-time Updates**: Badge updates every 30 seconds
- **No Flashing**: Stable display without interference

### Mobile PWA Features
- **Bell Color**: Changes to bright yellow for unread messages
- **No Count Badge**: Clean interface with just color indication
- **Touch Optimized**: Mobile-friendly navigation
- **Offline Support**: Messages cached for offline viewing

## 🔧 Troubleshooting

### Common Issues

**Badge Not Updating:**
- Check notification-center.js is loaded
- Verify API endpoint `/notification_center/getUnreadCount` works
- Ensure user is logged in

**Push Notifications Not Working:**
- Verify FCM configuration in config.php
- Check VAPID keys are set correctly
- Ensure service worker is registered

**Email Delivery Issues:**
- Check SMTP configuration
- Verify notification queue is being processed
- Check cron job is running

**Message Not Sending:**
- Verify user IDs exist in database
- Check user notification preferences
- Ensure global settings allow delivery method

### Debug Mode
Enable debug logging:
```php
define('DEBUG_MODE', true);
```

This will log detailed information about:
- Message sending attempts
- Delivery method selection
- API endpoint calls
- Background processing

## 📈 Performance Optimization

### Database Indexes
The system includes optimized indexes for:
- User message lookups (`idx_to_user`, `idx_from_user`)
- Unread message queries (`idx_unread`)
- Delivery status tracking (`idx_status`, `idx_method`)
- Threading support (`idx_parent`)

### Caching Strategy
- Real-time badge updates every 30 seconds
- Message list pagination (20 messages per page)
- Conversation threading for better organization
- Automatic cleanup of old read notifications

### Scalability Features
- Delivery tracking prevents duplicate sends
- Queue-based processing for email/SMS
- Batch processing limits (100 notifications per cron run)
- Automatic retry logic with exponential backoff
