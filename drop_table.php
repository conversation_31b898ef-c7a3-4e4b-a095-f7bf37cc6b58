<?php
require_once 'config/config.php';
require_once 'core/Database.php';

try {
    $db = new Database();
    
    // Check if table exists
    $db->query('SHOW TABLES LIKE "notification_preferences"');
    $tableExists = $db->single();
    
    if ($tableExists) {
        echo "📋 Table notification_preferences exists, dropping it...\n";
        $db->query('DROP TABLE notification_preferences');
        $db->execute();
        echo "✅ Successfully dropped notification_preferences table\n";
    } else {
        echo "⚠️ Table notification_preferences does not exist\n";
    }
    
    // Verify it's gone
    $db->query('SHOW TABLES LIKE "notification_preferences"');
    $stillExists = $db->single();
    
    if (!$stillExists) {
        echo "✅ Verified: notification_preferences table is completely removed\n";
    } else {
        echo "❌ Error: Table still exists after drop attempt\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
