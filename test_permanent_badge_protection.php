<?php
/**
 * Test Permanent Badge Protection
 * 
 * Tests the permanent protection system that fights back against badge hiding
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🛡️ Test Permanent Badge Protection</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Permanent Protection System</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔄 The Problem:</h3>";
    echo "<ul>";
    echo "<li>Badge gets created successfully ✅</li>";
    echo "<li>Something periodically sets <code>display: none</code> ❌</li>";
    echo "<li>Badge disappears and reappears (flashing) ❌</li>";
    echo "</ul>";
    
    echo "<h3>🛡️ The Solution:</h3>";
    echo "<ul>";
    echo "<li><strong>Permanent Protection:</strong> Checks every 1 second</li>";
    echo "<li><strong>Fights Back:</strong> Immediately restores visibility when hidden</li>";
    echo "<li><strong>DOM Protection:</strong> Re-attaches badge if removed</li>";
    echo "<li><strong>Aggressive Styling:</strong> Uses !important to override</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the permanent protection...</p>";
        
        $subject = "🛡️ Protection Test";
        $message = "This tests the permanent badge protection system that fights back against hiding attempts.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🖥️ Desktop Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Open browser dev tools</strong> (F12) → Console tab</li>";
    echo "<li><strong>Refresh the page</strong> and watch console messages</li>";
    echo "<li><strong>Look for these messages:</strong>";
    echo "<ul>";
    echo "<li>'[BellFixes] New badge created successfully - starting permanent protection!'</li>";
    echo "<li>'[BellFixes] Starting permanent badge protection...'</li>";
    echo "<li>'[BellFixes] Permanent protection active - checking every 1 second'</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>If something tries to hide the badge:</strong>";
    echo "<ul>";
    echo "<li>'[BellFixes] FIGHTING BACK! Something hid the badge again. Restoring...'</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Expected result:</strong> Badge stays visible permanently</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🔍 Manual Testing</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🛠️ Try to Break It:</h3>";
    echo "<p>Use these console commands to test the protection:</p>";
    echo "<ul>";
    echo "<li><code>document.getElementById('notification-count').style.display = 'none'</code></li>";
    echo "<li><code>document.getElementById('notification-count').style.visibility = 'hidden'</code></li>";
    echo "<li><code>document.getElementById('notification-count').remove()</code></li>";
    echo "</ul>";
    echo "<p><strong>Expected:</strong> Badge should be restored within 1 second each time!</p>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Console Flow</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    echo "<p><strong>Initial Load:</strong></p>";
    echo "<pre>";
    echo "[BellFixes] Initializing...
[BellFixes] Initial count detected: 1
[BellFixes] BADGE IS NOT VISIBLE! Something is setting display: none
[BellFixes] Creating new badge to bypass CSS conflicts...
[BellFixes] New badge created successfully - starting permanent protection!
[BellFixes] Starting permanent badge protection...
[BellFixes] Permanent protection active - checking every 1 second";
    echo "</pre>";
    
    echo "<p><strong>If Something Tries to Hide Badge:</strong></p>";
    echo "<pre>";
    echo "[BellFixes] FIGHTING BACK! Something hid the badge again. Restoring...";
    echo "</pre>";
    
    echo "<p><strong>Result:</strong> Badge stays visible permanently!</p>";
    echo "</div>";
    
    echo "<h2>🔧 Technical Details</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🛡️ Protection Mechanisms:</h3>";
    echo "<ul>";
    echo "<li><strong>Interval Check:</strong> Every 1 second, checks if badge is hidden</li>";
    echo "<li><strong>Aggressive Restore:</strong> Uses <code>setProperty('display', 'flex', 'important')</code></li>";
    echo "<li><strong>DOM Re-attachment:</strong> Re-adds badge to DOM if removed</li>";
    echo "<li><strong>Content Restore:</strong> Ensures text content is correct</li>";
    echo "</ul>";
    
    echo "<h3>🎯 What This Solves:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>No more flashing</strong> - Badge stays visible</li>";
    echo "<li>✅ <strong>Fights interference</strong> - Overcomes other scripts</li>";
    echo "<li>✅ <strong>Persistent visibility</strong> - Can't be permanently hidden</li>";
    echo "<li>✅ <strong>Debug friendly</strong> - Shows what's trying to interfere</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Permanent Protection Test!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>What should happen:</strong></p>";
        echo "<ol>";
        echo "<li>🔄 Page loads, badge gets created</li>";
        echo "<li>🛡️ Permanent protection starts</li>";
        echo "<li>✅ Badge stays visible permanently</li>";
        echo "<li>🔍 Console shows protection messages</li>";
        echo "</ol>";
        echo "<p><strong>This should finally end the flashing badge issue!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files to Copy</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated file:</strong></p>";
    echo "<ul>";
    echo "<li><code>public/js/notification-bell-fixes.js</code> - Now with permanent protection</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Permanent protection test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
