<?php
/**
 * Test the actual cron endpoint for processing notifications
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

echo "<h1>🔄 Testing Cron Endpoint</h1>";

try {
    echo "<h2>1. Cron Endpoint Information</h2>";
    
    // The actual cron file path
    $cronPath = APPROOT . '/cron/process_notifications.php';
    $cronUrl = BASE_URL . '/cron/process_notifications.php';
    
    echo "<p><strong>Cron file path:</strong> $cronPath</p>";
    echo "<p><strong>Cron URL:</strong> $cronUrl</p>";
    echo "<p><strong>File exists:</strong> " . (file_exists($cronPath) ? "✅ Yes" : "❌ No") . "</p>";
    
    echo "<h2>2. Testing Direct File Execution</h2>";
    
    if (file_exists($cronPath)) {
        echo "<h3>📄 Direct PHP Execution</h3>";
        
        // Capture output from direct execution
        ob_start();
        
        // Set up environment for cron execution
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['HTTP_HOST'] = parse_url(BASE_URL, PHP_URL_HOST);
        
        try {
            include $cronPath;
        } catch (Exception $e) {
            echo "Error during execution: " . $e->getMessage();
        }
        
        $cronOutput = ob_get_clean();
        
        echo "<h4>Cron Output:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
        echo htmlspecialchars($cronOutput);
        echo "</pre>";
    }
    
    echo "<h2>3. Testing HTTP Request</h2>";
    
    echo "<h3>🌐 HTTP GET Request</h3>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $cronUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $httpResponse = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $httpError = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Status:</strong> $httpCode</p>";
    
    if ($httpError) {
        echo "<p><strong>cURL Error:</strong> $httpError</p>";
    }
    
    echo "<h4>HTTP Response:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars($httpResponse);
    echo "</pre>";
    
    echo "<h2>4. Checking Notification Queue Before/After</h2>";
    
    require_once APPROOT . '/core/Database.php';
    $db = new Database();
    
    // Check pending notifications
    $db->query("SELECT COUNT(*) as total FROM notification_queue WHERE status = 'pending'");
    $result = $db->single();
    $pendingBefore = $result->total;
    
    echo "<p><strong>Pending notifications before cron:</strong> $pendingBefore</p>";
    
    // Run cron again to see if it processes more
    if ($pendingBefore > 0) {
        echo "<h3>🔄 Running Cron Again</h3>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $cronUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        
        $secondResponse = curl_exec($ch);
        $secondHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>Second run HTTP Status:</strong> $secondHttpCode</p>";
        echo "<details><summary>Second run response</summary>";
        echo "<pre>" . htmlspecialchars($secondResponse) . "</pre>";
        echo "</details>";
        
        // Check pending after second run
        $db->query("SELECT COUNT(*) as total FROM notification_queue WHERE status = 'pending'");
        $result = $db->single();
        $pendingAfter = $result->total;
        
        echo "<p><strong>Pending notifications after second cron:</strong> $pendingAfter</p>";
        
        if ($pendingAfter < $pendingBefore) {
            echo "<p>✅ Cron is processing notifications! Processed: " . ($pendingBefore - $pendingAfter) . "</p>";
        } else {
            echo "<p>⚠️ Cron doesn't seem to be processing notifications</p>";
        }
    }
    
    echo "<h2>5. Queue Status Analysis</h2>";
    
    // Get detailed queue status
    $db->query("SELECT status, COUNT(*) as count FROM notification_queue GROUP BY status");
    $statusCounts = $db->resultSet();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f2f2f2;'><th>Status</th><th>Count</th></tr>";
    foreach ($statusCounts as $status) {
        echo "<tr><td>{$status->status}</td><td>{$status->count}</td></tr>";
    }
    echo "</table>";
    
    // Check recent notifications
    $db->query("SELECT id, user_id, notification_type, subject, status, attempts, error_message, created_at, last_attempt 
                FROM notification_queue 
                ORDER BY created_at DESC 
                LIMIT 10");
    $recentNotifications = $db->resultSet();
    
    echo "<h3>Recent Notifications</h3>";
    if (!empty($recentNotifications)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 12px;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>User</th><th>Type</th><th>Subject</th><th>Status</th><th>Attempts</th><th>Error</th><th>Created</th><th>Last Attempt</th></tr>";
        foreach ($recentNotifications as $notification) {
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->user_id}</td>";
            echo "<td>{$notification->notification_type}</td>";
            echo "<td>" . substr($notification->subject, 0, 20) . "...</td>";
            echo "<td>{$notification->status}</td>";
            echo "<td>{$notification->attempts}</td>";
            echo "<td>" . substr($notification->error_message ?: 'None', 0, 20) . "</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "<td>" . ($notification->last_attempt ?: 'Never') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>6. Recommendations</h2>";
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔧 Cron Job Setup</h4>";
    echo "<p>To set up the cron job properly:</p>";
    echo "<ol>";
    echo "<li><strong>cPanel Cron Jobs:</strong></li>";
    echo "<ul>";
    echo "<li>Frequency: <code>*/5 * * * *</code> (every 5 minutes)</li>";
    echo "<li>Command: <code>php " . $cronPath . "</code></li>";
    echo "<li>Or: <code>curl -s \"" . $cronUrl . "\"</code></li>";
    echo "</ul>";
    echo "<li><strong>Server Cron (SSH):</strong></li>";
    echo "<ul>";
    echo "<li>Add to crontab: <code>*/5 * * * * php " . $cronPath . " > /dev/null 2>&1</code></li>";
    echo "</ul>";
    echo "</ol>";
    echo "</div>";
    
    if ($pendingBefore > 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Action Required</h4>";
        echo "<p>There are $pendingBefore pending notifications that need to be processed.</p>";
        echo "<p>Run the comprehensive fix: <a href='fix_notification_system.php'>fix_notification_system.php</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ All Good!</h4>";
        echo "<p>No pending notifications. The system appears to be working correctly.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<details><summary>Stack Trace</summary><pre>" . $e->getTraceAsString() . "</pre></details>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Cron Endpoint</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        h1, h2, h3 { color: #333; }
        h1 { border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
        details { margin: 10px 0; }
        summary { cursor: pointer; font-weight: bold; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <p><strong>This script tests the actual cron endpoint</strong> to ensure it's working properly and processing notifications.</p>
</body>
</html>
