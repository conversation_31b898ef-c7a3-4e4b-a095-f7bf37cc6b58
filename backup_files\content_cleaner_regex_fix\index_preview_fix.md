# Index.php Message Preview Fix

## Issue
The notification center index page was showing MIME garbage in message previews even though view.php was fixed.

## Root Cause
The index.php was directly displaying raw database content without using ContentCleaner:

**Mobile layout (line 521):**
```php
<?php echo htmlspecialchars(substr($message->message, 0, 60)); ?>
```

**Desktop layout (line 657):**
```php
<?php echo htmlspecialchars(substr($message->message, 0, 100)); ?>
```

## Solution
Added ContentCleaner processing to both preview locations:

1. **Added ContentCleaner import** at top of file
2. **Mobile preview:** Clean content before truncating to 60 chars
3. **Desktop preview:** Clean content before truncating to 100 chars

## Code Changes

### Mobile Layout Fix:
```php
<?php 
$cleanPreview = ContentCleaner::cleanMessageContent($message->message);
echo htmlspecialchars(substr($cleanPreview, 0, 60)); 
?>
<?php if (strlen($cleanPreview) > 60): ?>...<?php endif; ?>
```

### Desktop Layout Fix:
```php
<?php 
$cleanPreview = ContentCleaner::cleanMessageContent($message->message);
echo htmlspecialchars(substr($cleanPreview, 0, 100)); 
?>
<?php if (strlen($cleanPreview) > 100): ?>...<?php endif; ?>
```

## Expected Result
Message previews in the notification center list should now show:
- **Before:** `------sinikael-?=_1-17528868592260.1560288133272248...`
- **After:** `test message asking a question...`

## Files Modified
- `views/notification_center/index.php` - Added ContentCleaner processing to message previews