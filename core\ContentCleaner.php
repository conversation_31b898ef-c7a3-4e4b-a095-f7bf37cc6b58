<?php
/**
 * Content Cleaner Utility
 * 
 * Handles cleaning and decoding of message content for display and notifications.
 * Removes MIME encoding artifacts, quoted-printable encoding, and other garbage.
 */

class ContentCleaner {
    
    /**
     * Clean message content for display or notifications
     * Handles MIME encoding, quoted-printable, and other artifacts
     */
    public static function cleanMessageContent($content) {
        if (empty($content)) {
            return $content;
        }
        
        // Check if content is already clean (no MIME headers or encoding artifacts)
        if (!self::containsMimeContent($content)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ContentCleaner: Content appears already clean, skipping MIME processing");
            }
            return $content;
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("ContentCleaner: Content contains MIME artifacts, processing...");
            error_log("ContentCleaner: Raw content preview: " . substr($content, 0, 200) . "...");
        }
        
        // First try to extract plain text from MIME content
        $cleanContent = self::extractPlainTextFromMime($content);
        
        // If that didn't work, try basic cleanup
        if ($cleanContent === $content) {
            $cleanContent = self::basicContentCleanup($content);
        }
        
        return $cleanContent;
    }
    
    /**
     * Extract plain text from MIME encoded content
     */
    private static function extractPlainTextFromMime($content) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("CONTENT-CLEANUP: Attempting MIME extraction from content: " . substr($content, 0, 200) . "...");
        }
        
        // Pattern 1: Your specific MIME structure (extract text between plain text headers and next boundary)
        if (preg_match('/Content-Type:\s*text\/plain.*?\nContent-Transfer-Encoding:.*?\n(.*?)(?=\n------)/s', $content, $matches)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CONTENT-CLEANUP: Found plain text section with specific regex");
            }
            $plainText = $matches[1];
            
            // Handle different encodings
            if (strpos($content, 'quoted-printable') !== false) {
                $plainText = quoted_printable_decode($plainText);
            }
            
            $cleanText = trim(preg_replace('/\n{3,}/', "\n\n", $plainText));
            
            if (!empty($cleanText) && strlen($cleanText) > 3) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CONTENT-CLEANUP: Extracted clean text: " . substr($cleanText, 0, 100) . "...");
                }
                return $cleanText;
            }
        }
        
        // Pattern 2: Standard MIME with blank line after headers
        if (preg_match('/Content-Type:\s*text\/plain[^;]*(?:;[^=]*=[^;]+)*.*?\n(?:Content-Transfer-Encoding:[^\n]*\n)?\n(.+?)(?=\n--|\nContent-Type:|$)/s', $content, $matches)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CONTENT-CLEANUP: Found plain text section with standard regex");
            }
            $plainText = $matches[1];
            
            // Handle different encodings
            if (strpos($content, 'quoted-printable') !== false) {
                $plainText = quoted_printable_decode($plainText);
            }
            
            $cleanText = trim(preg_replace('/\n{3,}/', "\n\n", $plainText));
            
            if (!empty($cleanText) && strlen($cleanText) > 3) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CONTENT-CLEANUP: Extracted clean text: " . substr($cleanText, 0, 100) . "...");
                }
                return $cleanText;
            }
        }
        
        // Pattern 3: Alternative MIME structure
        if (preg_match('/text\/plain[^-]*?\n(?:[^\n]*\n)*?\s*(.+?)(?=\n--|\z)/s', $content, $matches)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CONTENT-CLEANUP: Found plain text section with alternative regex");
            }
            $plainText = $matches[1];
            
            if (strpos($content, 'quoted-printable') !== false) {
                $plainText = quoted_printable_decode($plainText);
            }
            
            $cleanText = trim(preg_replace('/\n{3,}/', "\n\n", $plainText));
            
            if (!empty($cleanText) && strlen($cleanText) > 3) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CONTENT-CLEANUP: Extracted clean text (alt): " . substr($cleanText, 0, 100) . "...");
                }
                return $cleanText;
            }
        }
        
        // Pattern 4: Try to extract HTML and convert to text
        if (preg_match('/Content-Type:\s*text\/html[^;]*(?:;[^=]*=[^;]+)*.*?\n(?:Content-Transfer-Encoding:[^\n]*\n)?\s*(.+?)(?=\n------|\nContent-Type:|$)/s', $content, $matches)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CONTENT-CLEANUP: Found HTML section, converting to text");
            }
            $htmlContent = $matches[1];
            
            if (strpos($content, 'quoted-printable') !== false) {
                $htmlContent = quoted_printable_decode($htmlContent);
            }
            
            // Convert HTML to plain text
            $plainText = strip_tags($htmlContent);
            $plainText = html_entity_decode($plainText, ENT_QUOTES, 'UTF-8');
            $cleanText = trim(preg_replace('/\n{3,}/', "\n\n", $plainText));
            $cleanText = preg_replace('/\s+/', ' ', $cleanText);
            
            if (!empty($cleanText) && strlen($cleanText) > 3) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CONTENT-CLEANUP: Converted HTML to text: " . substr($cleanText, 0, 100) . "...");
                }
                return $cleanText;
            }
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("CONTENT-CLEANUP: No MIME patterns matched, returning original content");
        }
        
        return $content; // Return original if no MIME patterns found
    }
    
    /**
     * Basic content cleanup for non-MIME content
     */
    private static function basicContentCleanup($content) {
        // Remove quoted-printable encoding
        $content = quoted_printable_decode($content);
        
        // Remove excessive line breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        
        // Remove MIME headers if present
        $content = preg_replace('/^Content-Type:.*?\n\n/s', '', $content);
        $content = preg_replace('/^Content-Transfer-Encoding:.*?\n/m', '', $content);
        
        // Remove email signature separators
        $content = preg_replace('/\n--\s*\n.*$/s', '', $content);
        
        // Clean up whitespace
        $content = trim($content);
        
        return $content;
    }
    
    /**
     * Clean content specifically for SMS (shorter, no HTML)
     */
    public static function cleanForSms($content, $maxLength = 160) {
        $cleaned = self::cleanMessageContent($content);
        
        // Remove HTML tags
        $cleaned = strip_tags($cleaned);
        
        // Convert HTML entities
        $cleaned = html_entity_decode($cleaned, ENT_QUOTES, 'UTF-8');
        
        // Remove excessive whitespace
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = trim($cleaned);
        
        // Truncate if too long
        if (strlen($cleaned) > $maxLength) {
            $cleaned = substr($cleaned, 0, $maxLength - 3) . '...';
        }
        
        return $cleaned;
    }
    
    /**
     * Clean content for push notifications (medium length, no HTML)
     */
    public static function cleanForPush($content, $maxLength = 500) {
        $cleaned = self::cleanMessageContent($content);
        
        // Remove HTML tags
        $cleaned = strip_tags($cleaned);
        
        // Convert HTML entities
        $cleaned = html_entity_decode($cleaned, ENT_QUOTES, 'UTF-8');
        
        // Remove excessive whitespace
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = trim($cleaned);
        
        // Truncate if too long
        if (strlen($cleaned) > $maxLength) {
            $cleaned = substr($cleaned, 0, $maxLength - 3) . '...';
        }
        
        return $cleaned;
    }
    
    /**
     * Clean content for email notifications (preserve some formatting)
     */
    public static function cleanForEmail($content) {
        $cleaned = self::cleanMessageContent($content);
        
        // Convert to HTML if it's plain text
        if (strip_tags($cleaned) === $cleaned) {
            $cleaned = nl2br(htmlspecialchars($cleaned));
        }
        
        return $cleaned;
    }
    
    /**
     * Clean content for web display (full HTML support)
     */
    public static function cleanForWeb($content) {
        $cleaned = self::cleanMessageContent($content);
        
        // Convert to HTML if it's plain text
        if (strip_tags($cleaned) === $cleaned) {
            $cleaned = nl2br(htmlspecialchars($cleaned));
        }
        
        return $cleaned;
    }
    
    /**
     * Check if content contains MIME headers or encoding artifacts
     */
    private static function containsMimeContent($content) {
        // Check for MIME headers
        if (strpos($content, 'Content-Type:') !== false) {
            return true;
        }
        
        // Check for MIME boundaries
        if (strpos($content, 'boundary=') !== false || strpos($content, '--=-') !== false) {
            return true;
        }
        
        // Check for quoted-printable encoding patterns
        if (preg_match('/=[0-9A-F]{2}/', $content)) {
            return true;
        }
        
        // Check for base64 encoding patterns (long lines of base64 characters)
        if (preg_match('/^[A-Za-z0-9+\/]{50,}={0,2}$/m', $content)) {
            return true;
        }
        
        // Check for Content-Transfer-Encoding headers
        if (strpos($content, 'Content-Transfer-Encoding:') !== false) {
            return true;
        }
        
        return false;
    }
}
