<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Service Worker Conflict</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 Fix Service Worker Conflict</h2>
        
        <div class="alert alert-warning">
            <h5>Problem Detected:</h5>
            <p>You have TWO service workers running simultaneously:</p>
            <ul>
                <li><strong>firebase-messaging-sw.js</strong> - Handles FCM notifications ✅</li>
                <li><strong>sw.js</strong> - PWA service worker (conflicting) ❌</li>
            </ul>
            <p>This prevents FCM notifications from displaying properly.</p>
        </div>
        
        <div class="step">
            <h3>Step 1: Check Current Status</h3>
            <div id="status-check">Checking...</div>
            <button id="check-status" class="btn btn-info mt-2">Refresh Status</button>
        </div>
        
        <div class="step">
            <h3>Step 2: Unregister PWA Service Worker</h3>
            <div id="unregister-status">Ready to unregister...</div>
            <button id="unregister-pwa" class="btn btn-warning mt-2">Unregister sw.js</button>
        </div>
        
        <div class="step">
            <h3>Step 3: Keep Only FCM Service Worker</h3>
            <div id="fcm-status">Ready to ensure FCM is active...</div>
            <button id="ensure-fcm" class="btn btn-success mt-2">Ensure FCM Active</button>
        </div>
        
        <div class="step">
            <h3>Step 4: Test Notifications</h3>
            <div id="test-status">Ready to test...</div>
            <button id="test-notification" class="btn btn-primary mt-2">Test FCM Notification</button>
        </div>
        
        <div class="step">
            <h3>Console Output</h3>
            <div id="console-output" style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        const logs = [];
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            document.getElementById('console-output').innerHTML = logs.slice(-20).join('<br>');
            console.log(message);
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="${type}">${message}</span>`;
        }
        
        // Step 1: Check current status
        async function checkCurrentStatus() {
            try {
                addLog('Checking current service worker status...');
                
                const registrations = await navigator.serviceWorker.getRegistrations();
                addLog(`Found ${registrations.length} service worker registrations`);
                
                let statusHtml = `<strong>Active Service Workers (${registrations.length}):</strong><br>`;
                let hasPWA = false;
                let hasFCM = false;
                
                for (const reg of registrations) {
                    const scriptURL = reg.active?.scriptURL || reg.installing?.scriptURL || reg.waiting?.scriptURL || 'Unknown';
                    const state = reg.active?.state || reg.installing?.state || reg.waiting?.state || 'Unknown';
                    
                    statusHtml += `<br>• ${scriptURL.split('/').pop()} - ${state}`;
                    addLog(`Service Worker: ${scriptURL} - State: ${state}`);
                    
                    if (scriptURL.includes('sw.js')) hasPWA = true;
                    if (scriptURL.includes('firebase-messaging-sw.js')) hasFCM = true;
                }
                
                if (hasPWA && hasFCM) {
                    statusHtml += '<br><br><span class="error">⚠️ CONFLICT: Both PWA and FCM service workers active!</span>';
                } else if (hasFCM) {
                    statusHtml += '<br><br><span class="success">✅ Only FCM service worker active (good!)</span>';
                } else if (hasPWA) {
                    statusHtml += '<br><br><span class="warning">⚠️ Only PWA service worker active (FCM missing!)</span>';
                } else {
                    statusHtml += '<br><br><span class="error">❌ No service workers active!</span>';
                }
                
                updateStatus('status-check', statusHtml, hasFCM && !hasPWA ? 'success' : 'warning');
                
            } catch (error) {
                addLog('Error checking status: ' + error.message);
                updateStatus('status-check', '❌ Error: ' + error.message, 'error');
            }
        }
        
        // Step 2: Unregister PWA service worker
        document.getElementById('unregister-pwa').addEventListener('click', async () => {
            try {
                updateStatus('unregister-status', '🔄 Unregistering PWA service worker...', 'warning');
                addLog('Starting PWA service worker unregistration...');
                
                const registrations = await navigator.serviceWorker.getRegistrations();
                let unregistered = false;
                
                for (const registration of registrations) {
                    const scriptURL = registration.active?.scriptURL || registration.installing?.scriptURL || registration.waiting?.scriptURL;
                    
                    if (scriptURL && scriptURL.includes('sw.js')) {
                        addLog(`Unregistering PWA service worker: ${scriptURL}`);
                        await registration.unregister();
                        unregistered = true;
                        addLog('PWA service worker unregistered successfully');
                    }
                }
                
                if (unregistered) {
                    updateStatus('unregister-status', '✅ PWA service worker unregistered', 'success');
                } else {
                    updateStatus('unregister-status', '⚠️ No PWA service worker found to unregister', 'warning');
                }
                
                // Refresh status
                setTimeout(checkCurrentStatus, 1000);
                
            } catch (error) {
                addLog('Error unregistering PWA service worker: ' + error.message);
                updateStatus('unregister-status', '❌ Error: ' + error.message, 'error');
            }
        });
        
        // Step 3: Ensure FCM service worker is active
        document.getElementById('ensure-fcm').addEventListener('click', async () => {
            try {
                updateStatus('fcm-status', '🔄 Ensuring FCM service worker is active...', 'warning');
                addLog('Checking FCM service worker status...');
                
                // Register FCM service worker if not present
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                addLog('FCM service worker registration: ' + registration.scope);
                
                // Wait for it to be ready
                await navigator.serviceWorker.ready;
                addLog('FCM service worker is ready');
                
                updateStatus('fcm-status', '✅ FCM service worker is active and ready', 'success');
                
                // Refresh status
                setTimeout(checkCurrentStatus, 1000);
                
            } catch (error) {
                addLog('Error with FCM service worker: ' + error.message);
                updateStatus('fcm-status', '❌ Error: ' + error.message, 'error');
            }
        });
        
        // Step 4: Test notification
        document.getElementById('test-notification').addEventListener('click', async () => {
            try {
                updateStatus('test-status', '🔄 Sending test notification...', 'warning');
                addLog('Sending test FCM notification...');
                
                const response = await fetch('/test_fcm_now_working.php');
                const text = await response.text();
                
                if (text.includes('SUCCESS! Notification sent!')) {
                    updateStatus('test-status', '✅ Test notification sent! Check your device.', 'success');
                    addLog('Test notification sent successfully');
                } else {
                    updateStatus('test-status', '⚠️ Server response unclear', 'warning');
                    addLog('Server response: ' + text.substring(0, 100) + '...');
                }
                
            } catch (error) {
                addLog('Error sending test notification: ' + error.message);
                updateStatus('test-status', '❌ Error: ' + error.message, 'error');
            }
        });
        
        // Auto-refresh status button
        document.getElementById('check-status').addEventListener('click', checkCurrentStatus);
        
        // Initialize
        window.addEventListener('load', () => {
            addLog('Service worker conflict fix tool loaded');
            checkCurrentStatus();
        });
    </script>
</body>
</html>