<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug PushManager Error</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>PushManager Error Debug Tool</h1>
    
    <div id="logs"></div>
    
    <button onclick="testServiceWorkerRegistration()">Test Service Worker Registration</button>
    <button onclick="testPushManagerAccess()">Test PushManager Access</button>
    <button onclick="testNotificationPermission()">Test Notification Permission</button>
    <button onclick="testSecureContext()">Test Secure Context</button>
    <button onclick="testFirebaseGetToken()">Test Firebase getToken</button>
    <button onclick="runFullDiagnostic()">Run Full Diagnostic</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <script>
        function log(message, type = 'log') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        async function testServiceWorkerRegistration() {
            log('Testing Service Worker Registration...', 'log');
            
            try {
                if (!('serviceWorker' in navigator)) {
                    throw new Error('Service Worker not supported');
                }
                log('✅ Service Worker API supported', 'success');
                
                // Check existing registration
                let registration = await navigator.serviceWorker.getRegistration('/');
                if (registration) {
                    log(`✅ Existing registration found: ${registration.scope}`, 'success');
                    log(`Registration state: ${registration.active ? 'active' : 'not active'}`, registration.active ? 'success' : 'warning');
                } else {
                    log('ℹ️ No existing registration found', 'warning');
                    
                    // Try to register
                    registration = await navigator.serviceWorker.register('/sw.js', { scope: '/' });
                    log(`✅ Service worker registered: ${registration.scope}`, 'success');
                }
                
                // Wait for ready
                const readyRegistration = await navigator.serviceWorker.ready;
                log(`✅ Service worker ready: ${readyRegistration.scope}`, 'success');
                
                // Test pushManager
                if (readyRegistration.pushManager) {
                    log('✅ PushManager available on registration', 'success');
                    log(`PushManager type: ${typeof readyRegistration.pushManager}`, 'log');
                } else {
                    log('❌ PushManager NOT available on registration', 'error');
                }
                
            } catch (error) {
                log(`❌ Service Worker Registration Error: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }
        
        async function testPushManagerAccess() {
            log('Testing PushManager Access...', 'log');
            
            try {
                if (!('PushManager' in window)) {
                    throw new Error('PushManager not supported');
                }
                log('✅ PushManager API supported', 'success');
                
                const registration = await navigator.serviceWorker.ready;
                log(`✅ Service worker ready: ${registration.scope}`, 'success');
                
                if (!registration) {
                    throw new Error('No service worker registration');
                }
                
                if (!registration.pushManager) {
                    throw new Error('PushManager not available on registration');
                }
                log('✅ PushManager available', 'success');
                
                // Test getSubscription
                const subscription = await registration.pushManager.getSubscription();
                if (subscription) {
                    log('✅ Existing push subscription found', 'success');
                    log(`Endpoint: ${subscription.endpoint.substring(0, 50)}...`, 'log');
                } else {
                    log('ℹ️ No existing push subscription', 'warning');
                }
                
            } catch (error) {
                log(`❌ PushManager Access Error: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }
        
        async function testNotificationPermission() {
            log('Testing Notification Permission...', 'log');
            
            try {
                if (!('Notification' in window)) {
                    throw new Error('Notifications not supported');
                }
                log('✅ Notification API supported', 'success');
                
                log(`Current permission: ${Notification.permission}`, 'log');
                
                if (Notification.permission === 'default') {
                    log('Requesting notification permission...', 'log');
                    const permission = await Notification.requestPermission();
                    log(`Permission result: ${permission}`, permission === 'granted' ? 'success' : 'warning');
                    
                    if (permission === 'granted') {
                        // Now test pushManager after permission granted
                        await testPushManagerAccess();
                    }
                }
                
            } catch (error) {
                log(`❌ Notification Permission Error: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }
        
        // Global error handlers
        window.addEventListener('error', (event) => {
            log(`❌ Global Error: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ Unhandled Promise Rejection: ${event.reason}`, 'error');
            if (event.reason && event.reason.stack) {
                log(`Stack: ${event.reason.stack}`, 'error');
            }
        });
        
        function testSecureContext() {
            log('Testing Secure Context...', 'log');
            
            if (window.isSecureContext) {
                log('✅ Running in secure context (HTTPS)', 'success');
            } else {
                log('❌ NOT running in secure context - Push notifications require HTTPS', 'error');
            }
            
            log(`Protocol: ${window.location.protocol}`, 'log');
            log(`Host: ${window.location.host}`, 'log');
        }
        
        function runFullDiagnostic() {
            log('Running Full Diagnostic...', 'log');
            
            testSecureContext();
            setTimeout(() => testServiceWorkerRegistration(), 500);
            setTimeout(() => testPushManagerAccess(), 1000);
            setTimeout(() => testNotificationPermission(), 1500);
            
            // If FCM diagnostic is available, run it too
            setTimeout(() => {
                if (typeof window.diagnosePushNotifications === 'function') {
                    log('Running FCM diagnostic (check console for detailed output)...', 'log');
                    window.diagnosePushNotifications();
                } else {
                    log('ℹ️ FCM diagnostic function not available', 'warning');
                }
            }, 2000);
        }
        
        function testFirebaseGetToken() {
            log('Testing Firebase getToken (check console for detailed output)...', 'log');
            if (typeof window.testFirebaseGetToken === 'function') {
                window.testFirebaseGetToken();
            } else {
                log('❌ testFirebaseGetToken function not available', 'error');
            }
        }
        
        // Auto-run basic tests on load
        document.addEventListener('DOMContentLoaded', () => {
            log('Debug tool loaded', 'success');
            testSecureContext();
            setTimeout(() => testServiceWorkerRegistration(), 500);
        });
    </script>
</body>
</html>