<?php
/**
 * Cron Directory Cleanup Analysis
 * 
 * Analyzes and provides recommendations for organizing cron scripts properly
 */

echo "<h1>🧹 Cron Directory Cleanup Analysis</h1>";

echo "<h2>📊 Current Situation Analysis</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>🔍 Files Found in Wrong Locations:</h3>";

echo "<table border='1' cellpadding='5' style='width: 100%;'>";
echo "<tr style='background: #f0f0f0;'><th>File</th><th>Current Location</th><th>Should Be In</th><th>Status</th><th>Action Needed</th></tr>";

$cronFiles = [
    [
        'file' => 'process_notifications.php',
        'current' => 'scripts/',
        'should_be' => 'cron/',
        'status' => '❌ Duplicate',
        'action' => 'Use cron/ version, delete scripts/ version'
    ],
    [
        'file' => 'auto_reset_tasks.php', 
        'current' => 'scripts/',
        'should_be' => 'cron/',
        'status' => '⚠️ Wrong location',
        'action' => 'Move to cron/ directory'
    ],
    [
        'file' => 'cleanup_fcm_tokens.php',
        'current' => 'cron/',
        'should_be' => 'cron/',
        'status' => '✅ Correct',
        'action' => 'No action needed'
    ],
    [
        'file' => 'cleanup_notifications.php',
        'current' => 'cron/',
        'should_be' => 'cron/',
        'status' => '✅ Correct',
        'action' => 'No action needed'
    ],
    [
        'file' => 'cleanup_show_roles.php',
        'current' => 'cron/',
        'should_be' => 'cron/',
        'status' => '✅ Correct',
        'action' => 'No action needed'
    ]
];

foreach ($cronFiles as $file) {
    $statusColor = $file['status'] === '✅ Correct' ? 'green' : ($file['status'] === '❌ Duplicate' ? 'red' : 'orange');
    echo "<tr>";
    echo "<td><strong>{$file['file']}</strong></td>";
    echo "<td>{$file['current']}</td>";
    echo "<td>{$file['should_be']}</td>";
    echo "<td style='color: {$statusColor};'>{$file['status']}</td>";
    echo "<td>{$file['action']}</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

echo "<h2>🎯 Recommended Directory Structure</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>📁 Proper Organization:</h3>";

echo "<table border='1' cellpadding='5' style='width: 100%;'>";
echo "<tr style='background: #f0f0f0;'><th>Directory</th><th>Purpose</th><th>Files</th></tr>";

echo "<tr>";
echo "<td><strong>/cron/</strong></td>";
echo "<td>Automated background tasks that run on schedule</td>";
echo "<td>";
echo "• process_notifications.php<br>";
echo "• auto_reset_tasks.php<br>";
echo "• cleanup_fcm_tokens.php<br>";
echo "• cleanup_notifications.php<br>";
echo "• cleanup_show_roles.php";
echo "</td>";
echo "</tr>";

echo "<tr>";
echo "<td><strong>/scripts/</strong></td>";
echo "<td>Manual maintenance and utility scripts</td>";
echo "<td>";
echo "• Database maintenance scripts<br>";
echo "• One-time migration scripts<br>";
echo "• Debug and testing scripts<br>";
echo "• Setup and configuration scripts";
echo "</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<h2>🔧 Step-by-Step Cleanup Plan</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<h3>📋 Action Items:</h3>";

echo "<h4>🎯 Step 1: Update Cron Jobs</h4>";
echo "<p><strong>Current cron job (WRONG):</strong></p>";
echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
echo "*/2 * * * * /usr/bin/php /home/<USER>/events.rowaneliterides.com/scripts/process_notifications.php";
echo "</pre>";

echo "<p><strong>Updated cron jobs (CORRECT):</strong></p>";
echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
echo "# Process notifications every 2 minutes\n";
echo "*/2 * * * * /usr/bin/php /home/<USER>/events.rowaneliterides.com/cron/process_notifications.php\n\n";
echo "# Reset stuck tasks every 15 minutes\n";
echo "*/15 * * * * /usr/bin/php /home/<USER>/events.rowaneliterides.com/cron/auto_reset_tasks.php\n\n";
echo "# Cleanup FCM tokens daily at 2 AM\n";
echo "0 2 * * * /usr/bin/php /home/<USER>/events.rowaneliterides.com/cron/cleanup_fcm_tokens.php\n\n";
echo "# Cleanup old notifications daily at 3 AM\n";
echo "0 3 * * * /usr/bin/php /home/<USER>/events.rowaneliterides.com/cron/cleanup_notifications.php\n\n";
echo "# Cleanup show roles weekly on Sunday at 4 AM\n";
echo "0 4 * * 0 /usr/bin/php /home/<USER>/events.rowaneliterides.com/cron/cleanup_show_roles.php";
echo "</pre>";

echo "<h4>📁 Step 2: File Operations</h4>";
echo "<ol>";
echo "<li><strong>Copy to server:</strong>";
echo "<ul>";
echo "<li>cron/process_notifications.php (updated with EmailService)</li>";
echo "<li>cron/auto_reset_tasks.php (new, improved version)</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Delete from server:</strong>";
echo "<ul>";
echo "<li>scripts/process_notifications.php (duplicate)</li>";
echo "<li>scripts/auto_reset_tasks.php (moved to cron/)</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";

echo "<h4>🧪 Step 3: Test the Changes</h4>";
echo "<ol>";
echo "<li>Update your crontab with the new paths</li>";
echo "<li>Wait 2 minutes and check if notifications are processed</li>";
echo "<li>Check logs in /logs/ directory for any errors</li>";
echo "<li>Verify your 2 pending emails are sent</li>";
echo "</ol>";
echo "</div>";

echo "<h2>✅ Improvements Made</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h3>🎯 Enhanced auto_reset_tasks.php:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Proper path structure:</strong> Uses dirname(__DIR__) for correct paths</li>";
echo "<li>✅ <strong>Better error handling:</strong> Comprehensive try/catch with logging</li>";
echo "<li>✅ <strong>Enhanced logging:</strong> Timestamps, log levels, and detailed messages</li>";
echo "<li>✅ <strong>Web access protection:</strong> Prevents direct web access</li>";
echo "<li>✅ <strong>Heartbeat monitoring:</strong> Creates status file for monitoring</li>";
echo "<li>✅ <strong>Table existence check:</strong> Safely handles missing tables</li>";
echo "<li>✅ <strong>Additional checks:</strong> Also monitors old pending tasks</li>";
echo "<li>✅ <strong>Statistics reporting:</strong> Provides task status overview</li>";
echo "</ul>";

echo "<h3>🎯 Enhanced process_notifications.php:</h3>";
echo "<ul>";
echo "<li>✅ <strong>EmailService integration:</strong> Now includes required EmailService class</li>";
echo "<li>✅ <strong>SettingsModel integration:</strong> Includes SettingsModel for SMTP settings</li>";
echo "<li>✅ <strong>Comprehensive logging:</strong> Detailed execution tracking</li>";
echo "<li>✅ <strong>Better error handling:</strong> Graceful failure handling</li>";
echo "<li>✅ <strong>Multiple cleanup operations:</strong> Cleans queue, push, and toast notifications</li>";
echo "</ul>";
echo "</div>";

echo "<h2>📊 Expected Results</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
echo "<h3>🎯 After Implementing Changes:</h3>";

echo "<table border='1' cellpadding='5' style='width: 100%;'>";
echo "<tr style='background: #f0f0f0;'><th>Feature</th><th>Before</th><th>After</th></tr>";

echo "<tr>";
echo "<td><strong>Email Notifications</strong></td>";
echo "<td style='color: red;'>❌ Failing (EmailService not found)</td>";
echo "<td style='color: green;'>✅ Working (EmailService included)</td>";
echo "</tr>";

echo "<tr>";
echo "<td><strong>Cron Organization</strong></td>";
echo "<td style='color: orange;'>⚠️ Scattered across directories</td>";
echo "<td style='color: green;'>✅ Properly organized in /cron/</td>";
echo "</tr>";

echo "<tr>";
echo "<td><strong>Task Management</strong></td>";
echo "<td style='color: orange;'>⚠️ Basic stuck task detection</td>";
echo "<td style='color: green;'>✅ Comprehensive task monitoring</td>";
echo "</tr>";

echo "<tr>";
echo "<td><strong>Monitoring</strong></td>";
echo "<td style='color: orange;'>⚠️ Limited heartbeat files</td>";
echo "<td style='color: green;'>✅ Detailed heartbeat monitoring</td>";
echo "</tr>";

echo "<tr>";
echo "<td><strong>Error Handling</strong></td>";
echo "<td style='color: orange;'>⚠️ Basic error logging</td>";
echo "<td style='color: green;'>✅ Comprehensive error tracking</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<h2>🚀 Immediate Benefits</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>📧 Email System:</h3>";
echo "<ul>";
echo "<li>✅ Your 2 pending contact form emails will be sent</li>";
echo "<li>✅ Future contact forms will automatically send email notifications</li>";
echo "<li>✅ Professional SMTP delivery using admin settings</li>";
echo "</ul>";

echo "<h3>🔧 System Maintenance:</h3>";
echo "<ul>";
echo "<li>✅ Stuck tasks will be automatically reset every 15 minutes</li>";
echo "<li>✅ Old notifications will be cleaned up automatically</li>";
echo "<li>✅ FCM tokens will be maintained properly</li>";
echo "<li>✅ Show roles will be cleaned up after events</li>";
echo "</ul>";

echo "<h3>📊 Monitoring:</h3>";
echo "<ul>";
echo "<li>✅ Heartbeat files for all cron jobs</li>";
echo "<li>✅ Detailed logging for troubleshooting</li>";
echo "<li>✅ Statistics and performance tracking</li>";
echo "<li>✅ Error detection and reporting</li>";
echo "</ul>";
echo "</div>";

echo "<h2>📋 Files to Copy to Server</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>🎯 Required File Operations:</h3>";

echo "<h4>📁 Copy These Files:</h4>";
echo "<ul>";
echo "<li><code>cron/process_notifications.php</code> - Updated with EmailService integration</li>";
echo "<li><code>cron/auto_reset_tasks.php</code> - New improved version with proper paths</li>";
echo "</ul>";

echo "<h4>🗑️ Delete These Files from Server:</h4>";
echo "<ul>";
echo "<li><code>scripts/process_notifications.php</code> - Duplicate, use cron/ version</li>";
echo "<li><code>scripts/auto_reset_tasks.php</code> - Moved to cron/ directory</li>";
echo "</ul>";

echo "<h4>⚙️ Update Crontab:</h4>";
echo "<p>Change the paths in your cron jobs to point to the /cron/ directory instead of /scripts/</p>";
echo "</div>";

echo "<hr>";
echo "<p><em>Cron directory cleanup analysis completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
