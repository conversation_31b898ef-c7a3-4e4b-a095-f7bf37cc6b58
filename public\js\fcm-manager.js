class FCMManager {
    constructor() {
        this.app = null;
        this.messaging = null;
        this.currentToken = null;
        this.tokenKey = 'fcm_token';
        this.tokenTimestampKey = 'fcm_token_timestamp';
        this.tokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    }

    async init() {
        try {
            // Get VAPID key from server using controller route
            const vapidResponse = await fetch('/api/pwa/vapid-key');
            const vapidData = await vapidResponse.json();
            
            if (!vapidData.success) {
                throw new Error('Failed to get VAPID key');
            }
            
            this.vapidKey = vapidData.publicKey;

            // Initialize Firebase
            if (!firebase.apps.length) {
                this.app = firebase.initializeApp({
                    apiKey: "AIzaSyBvOieE2oOEPinadxYTI00sNH_MG_-M-tw",
                    authDomain: "events-and-shows-management.firebaseapp.com",
                    projectId: "events-and-shows-management",
                    storageBucket: "events-and-shows-management.appspot.com",
                    messagingSenderId: "1008734635821",
                    appId: "1:1008734635821:web:abc123def456"
                });
            } else {
                this.app = firebase.app();
            }

            this.messaging = firebase.messaging();

            // Set up message handler
            this.messaging.onMessage((payload) => {
                console.log('Message received:', payload);
                this.showNotification(payload);
            });

            console.log('FCM Manager initialized successfully');
        } catch (error) {
            console.error('FCM initialization error:', error);
            throw error;
        }
    }

    async requestPermissionAndGetToken() {
        try {
            // Check if we have a valid cached token first
            const cachedToken = await this.getCachedToken();
            if (cachedToken) {
                console.log('Using cached FCM token');
                this.currentToken = cachedToken;
                return cachedToken;
            }

            console.log('No valid cached token, requesting new one...');
            
            // Request permission
            const permission = await Notification.requestPermission();
            if (permission !== 'granted') {
                throw new Error('Notification permission denied');
            }

            // Get new token
            const token = await this.messaging.getToken({
                vapidKey: this.vapidKey
            });

            if (token) {
                console.log('New FCM token generated');
                this.currentToken = token;
                this.cacheToken(token);
                await this.sendTokenToServer(token);
                return token;
            } else {
                throw new Error('No registration token available');
            }
        } catch (error) {
            console.error('Error getting FCM token:', error);
            throw error;
        }
    }

    async getCachedToken() {
        const token = localStorage.getItem(this.tokenKey);
        const timestamp = localStorage.getItem(this.tokenTimestampKey);

        if (!token || !timestamp) {
            return null;
        }

        // Check if token is expired by time
        const tokenAge = Date.now() - parseInt(timestamp);
        if (tokenAge > this.tokenMaxAge) {
            console.log('[FCM] Cached token expired by time, will generate new one');
            this.clearCachedToken();
            return null;
        }

        // Verify token exists in database (with minimal performance impact)
        try {
            const isValid = await this.verifyTokenInDatabase(token);
            if (!isValid) {
                console.log('[FCM] Token not found in database, will generate new one');
                this.clearCachedToken();
                return null;
            }

            console.log('[FCM] Cached token is valid');
            return token;
        } catch (error) {
            console.warn('[FCM] Database verification failed, using cached token anyway:', error);
            // If database check fails, still use cached token to avoid breaking functionality
            return token;
        }
    }

    cacheToken(token) {
        localStorage.setItem(this.tokenKey, token);
        localStorage.setItem(this.tokenTimestampKey, Date.now().toString());
    }

    clearCachedToken() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.tokenTimestampKey);
    }

    async verifyTokenInDatabase(token) {
        try {
            const response = await fetch('/api/pwa/fcm-verify-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    fcm_token: token
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            return data.success && data.valid;
        } catch (error) {
            console.error('[FCM] Token verification error:', error);
            throw error;
        }
    }

    async sendTokenToServer(token) {
        // Only send if we don't have this token on server
        const lastSentToken = localStorage.getItem('fcm_last_sent_token');
        if (lastSentToken === token) {
            console.log('Token already sent to server');
            return;
        }

        try {
            const response = await fetch('/api/pwa/fcm-subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    fcm_token: token,
                    user_agent: navigator.userAgent
                })
            });

            const data = await response.json();
            if (data.success) {
                localStorage.setItem('fcm_last_sent_token', token);
                console.log('Token sent to server successfully');
            }
        } catch (error) {
            console.error('Failed to send token to server:', error);
        }
    }

    showNotification(payload) {
        const notificationTitle = payload.notification?.title || 'New Notification';
        const notificationOptions = {
            body: payload.notification?.body || 'You have a new notification',
            icon: '/public/images/icons/icon-192x192.png',
            badge: '/public/images/icons/badge-72x72.png',
            tag: 'fcm-' + Date.now(),
            requireInteraction: false,
            data: payload.data || {}
        };

        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                registration.showNotification(notificationTitle, notificationOptions);
            });
        }
    }
}
