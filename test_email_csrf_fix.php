<?php
/**
 * Test Email CSRF Fix
 * 
 * This file tests the specific CSRF fix for the email test functionality.
 */

// Start session and include necessary files
session_start();

// Define constants
define('APPROOT', dirname(__FILE__));
define('BASE_URL', 'https://events.rowaneliterides.com');
define('DEBUG_MODE', true);

// Include required files
require_once APPROOT . '/helpers/csrf_helper.php';

echo "<h1>📧 Email Test CSRF Fix Verification</h1>";

echo "<h2>🔧 Testing the Exact Scenario</h2>";

// Simulate the exact request that the email test makes
$token = generateCsrfToken();

echo "<p><strong>Generated CSRF Token:</strong> " . htmlspecialchars($token) . "</p>";

// Simulate the AJAX request headers and data
$_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
$_SERVER['HTTP_X_CSRF_TOKEN'] = $token;
$_POST['csrf_token'] = $token;
$_POST['email'] = '<EMAIL>';

echo "<h3>Simulated Request:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<strong>Headers:</strong><br>";
echo "X-Requested-With: XMLHttpRequest<br>";
echo "X-CSRF-Token: " . htmlspecialchars($token) . "<br><br>";
echo "<strong>POST Data:</strong><br>";
echo "csrf_token: " . htmlspecialchars($token) . "<br>";
echo "email: <EMAIL><br>";
echo "</div>";

echo "<h3>Validation Tests:</h3>";

// Test the validateCsrfToken function (what the fix uses)
$result1 = validateCsrfToken();
echo "<p><strong>validateCsrfToken():</strong> " . ($result1 ? '✅ PASS' : '❌ FAIL') . "</p>";

// Test the original verifyCsrfToken function
$result2 = verifyCsrfToken();
echo "<p><strong>verifyCsrfToken() (POST):</strong> " . ($result2 ? '✅ PASS' : '❌ FAIL') . "</p>";

// Test header verification
$result3 = verifyCsrfToken(null, 'header');
echo "<p><strong>verifyCsrfToken() (HEADER):</strong> " . ($result3 ? '✅ PASS' : '❌ FAIL') . "</p>";

echo "<h2>🧪 Testing with Wrong Token</h2>";

// Test with wrong token
$_SERVER['HTTP_X_CSRF_TOKEN'] = 'wrong_token';
$_POST['csrf_token'] = 'wrong_token';

$result4 = validateCsrfToken();
echo "<p><strong>validateCsrfToken() (wrong token):</strong> " . ($result4 ? '❌ Should FAIL' : '✅ Correctly FAILED') . "</p>";

// Reset to correct token
$_SERVER['HTTP_X_CSRF_TOKEN'] = $token;
$_POST['csrf_token'] = $token;

echo "<h2>🎯 Summary</h2>";

if ($result1 && $result2 && $result3 && !$result4) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<h3>✅ All Tests PASSED!</h3>";
    echo "<p>The CSRF token fix should work correctly. The email test functionality should now work.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Some Tests FAILED!</h3>";
    echo "<p>There may still be an issue with the CSRF token validation.</p>";
    echo "</div>";
}

echo "<h2>📋 What Was Fixed</h2>";
echo "<ol>";
echo "<li><strong>Added X-Requested-With header:</strong> The JavaScript now sends 'X-Requested-With: XMLHttpRequest' to properly identify the request as AJAX</li>";
echo "<li><strong>Added CSRF token to POST body:</strong> The token is now sent both in the header and POST data as a fallback</li>";
echo "<li><strong>Used validateCsrfToken():</strong> The AdminController now uses the AJAX-aware validation function</li>";
echo "<li><strong>Added debugging:</strong> Debug logging was added to help troubleshoot any remaining issues</li>";
echo "</ol>";

echo "<h2>🚀 Next Steps</h2>";
echo "<ol>";
echo "<li>Copy the updated files to your server:
    <ul>
        <li><code>views/admin/settings_email.php</code></li>
        <li><code>controllers/AdminController.php</code></li>
    </ul>
</li>";
echo "<li>Test the email functionality at <code>/admin/settings_email</code></li>";
echo "<li>If it still fails, check the server error logs for the debug messages</li>";
echo "<li>Delete this test file after verification</li>";
echo "</ol>";

// Clean up test data
unset($_POST['csrf_token']);
unset($_POST['email']);
unset($_SERVER['HTTP_X_CSRF_TOKEN']);
unset($_SERVER['HTTP_X_REQUESTED_WITH']);

// Self-delete after 5 minutes
if (file_exists(__FILE__) && (time() - filemtime(__FILE__)) > 300) {
    unlink(__FILE__);
}
?>
