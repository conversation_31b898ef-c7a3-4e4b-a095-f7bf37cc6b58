<?php
/**
 * Email Integration System Setup Script
 * 
 * This script sets up the complete email integration system including:
 * - Database migrations
 * - Default settings
 * - Default templates
 * - Default folders for existing admins
 * - Cron job setup instructions
 */

// Include the application configuration
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/SettingsModel.php';

echo "=== Email Integration System Setup ===\n\n";

try {
    $db = new Database();
    $settingsModel = new SettingsModel();
    
    echo "1. Running database migrations...\n";
    
    // Run the unified message system migration first
    echo "   - Creating unified message system tables...\n";
    $unifiedMessageSql = file_get_contents('database/migrations/create_unified_message_system_fixed.sql');
    if ($unifiedMessageSql) {
        $statements = explode(';', $unifiedMessageSql);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                try {
                    $db->query($statement);
                    $db->execute();
                } catch (Exception $e) {
                    // Ignore table already exists errors
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "     Warning: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
    }
    
    // Run the email integration migration
    echo "   - Creating email integration tables...\n";
    $emailIntegrationSql = file_get_contents('database/migrations/add_email_integration_system.sql');
    if ($emailIntegrationSql) {
        $statements = explode(';', $emailIntegrationSql);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                try {
                    $db->query($statement);
                    $db->execute();
                } catch (Exception $e) {
                    // Ignore duplicate entry errors
                    if (strpos($e->getMessage(), 'Duplicate entry') === false && 
                        strpos($e->getMessage(), 'already exists') === false) {
                        echo "     Warning: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
    }
    
    echo "   ✓ Database migrations completed\n\n";
    
    echo "2. Setting up default configuration...\n";
    
    // Default email settings
    $defaultSettings = [
        'email_processing_enabled' => '0',
        'email_server_protocol' => 'imap',
        'email_server_host' => '',
        'email_server_port' => '993',
        'email_server_username' => '',
        'email_server_password' => '',
        'email_server_encryption' => 'ssl',
        'email_delete_after_processing' => '1',
        'email_spam_filtering' => '1',
        'email_max_size_mb' => '10',
        'email_fetch_limit' => '50',
        'email_attachment_enabled' => '1',
        'email_attachment_max_size_mb' => '5',
        'email_auto_reply_enabled' => '1',
        'ticket_number_prefix' => 'RER',
        'email_log_retention_days' => '30'
    ];
    
    foreach ($defaultSettings as $key => $value) {
        try {
            $existing = $settingsModel->getSetting($key);
            if ($existing === null) {
                $settingsModel->setSetting($key, $value);
                echo "   - Set $key = $value\n";
            } else {
                echo "   - $key already configured\n";
            }
        } catch (Exception $e) {
            echo "   Warning: Could not set $key: " . $e->getMessage() . "\n";
        }
    }
    
    echo "   ✓ Default settings configured\n\n";
    
    echo "3. Creating default folders for existing admins...\n";
    
    // Get all admin users
    $db->query("SELECT id, name FROM users WHERE role = 'admin' AND status = 'active'");
    $admins = $db->resultSet();
    
    $defaultFolders = [
        ['name' => 'Inbox', 'color' => '#007bff', 'sort_order' => 1, 'is_system' => 1],
        ['name' => 'Sent', 'color' => '#28a745', 'sort_order' => 2, 'is_system' => 1],
        ['name' => 'Archive', 'color' => '#6c757d', 'sort_order' => 3, 'is_system' => 1],
        ['name' => 'Trash', 'color' => '#dc3545', 'sort_order' => 4, 'is_system' => 1]
    ];
    
    foreach ($admins as $admin) {
        echo "   - Creating folders for admin: {$admin->name}\n";
        
        foreach ($defaultFolders as $folder) {
            try {
                $db->query("INSERT IGNORE INTO admin_email_folders (admin_user_id, name, color, sort_order, is_system) 
                           VALUES (:admin_id, :name, :color, :sort_order, :is_system)");
                $db->bind(':admin_id', $admin->id);
                $db->bind(':name', $folder['name']);
                $db->bind(':color', $folder['color']);
                $db->bind(':sort_order', $folder['sort_order']);
                $db->bind(':is_system', $folder['is_system']);
                $db->execute();
            } catch (Exception $e) {
                // Ignore duplicate errors
                if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "     Warning: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "   ✓ Default folders created\n\n";
    
    echo "4. Verifying email templates...\n";
    
    // Check if default templates exist
    $db->query("SELECT COUNT(*) as count FROM email_templates");
    $templateCount = $db->single()->count;
    
    if ($templateCount > 0) {
        echo "   ✓ Email templates already exist ($templateCount templates)\n\n";
    } else {
        echo "   - No templates found, they should be created automatically\n\n";
    }
    
    echo "5. Creating necessary directories...\n";
    
    $directories = [
        'logs',
        'uploads/email_attachments',
        'uploads/email_attachments/' . date('Y'),
        'uploads/email_attachments/' . date('Y') . '/' . date('m')
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "   ✓ Created directory: $dir\n";
            } else {
                echo "   ✗ Failed to create directory: $dir\n";
            }
        } else {
            echo "   - Directory already exists: $dir\n";
        }
    }
    
    echo "\n6. System verification...\n";
    
    // Check PHP extensions
    $requiredExtensions = ['imap', 'openssl', 'json', 'mbstring'];
    foreach ($requiredExtensions as $ext) {
        if (extension_loaded($ext)) {
            echo "   ✓ PHP extension '$ext' is loaded\n";
        } else {
            echo "   ✗ PHP extension '$ext' is NOT loaded (required for email processing)\n";
        }
    }
    
    // Check file permissions
    $writableDirectories = ['logs', 'uploads'];
    foreach ($writableDirectories as $dir) {
        if (is_writable($dir)) {
            echo "   ✓ Directory '$dir' is writable\n";
        } else {
            echo "   ✗ Directory '$dir' is NOT writable\n";
        }
    }
    
    echo "\n=== Setup Complete! ===\n\n";
    
    echo "Next Steps:\n";
    echo "1. Configure email server settings in Admin > Settings > Email Server\n";
    echo "2. Test the email server connection\n";
    echo "3. Set up cron jobs (see instructions below)\n";
    echo "4. Enable email processing in the settings\n\n";
    
    echo "Cron Job Setup:\n";
    echo "Add these lines to your crontab (crontab -e):\n\n";
    echo "# Process incoming emails every 5 minutes\n";
    echo "*/5 * * * * /usr/bin/php " . realpath('.') . "/cron/process_incoming_emails.php\n\n";
    echo "# Process reminders every 2 minutes\n";
    echo "*/2 * * * * /usr/bin/php " . realpath('.') . "/cron/process_reminders.php\n\n";
    
    echo "Access Points:\n";
    echo "- Email Dashboard: /admin/email_dashboard\n";
    echo "- Email Server Settings: /admin/settings_email_server\n";
    echo "- Email Templates: /admin/settings_email_templates\n";
    echo "- Main Admin Settings: /admin/settings\n\n";
    
    echo "Features Available:\n";
    echo "✓ POP3/IMAP email retrieval\n";
    echo "✓ Automatic ticket number generation (RER-2025-001 format)\n";
    echo "✓ Email threading and conversation tracking\n";
    echo "✓ Spam filtering and auto-reply detection\n";
    echo "✓ Attachment handling and download\n";
    echo "✓ Admin folder system with ownership tracking\n";
    echo "✓ Reminder system with notifications\n";
    echo "✓ Email templates for quick responses\n";
    echo "✓ Auto-reply confirmation emails\n";
    echo "✓ Comprehensive admin dashboard\n";
    echo "✓ Mobile-responsive interface\n";
    echo "✓ Integration with existing unified messaging system\n\n";
    
    echo "The email integration system is now ready to use!\n";
    
} catch (Exception $e) {
    echo "✗ Setup failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
