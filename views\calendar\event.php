<?php
// Prepare Open Graph meta data for social sharing
$eventTitle = htmlspecialchars($data['event']->title, ENT_QUOTES, 'UTF-8');
$eventDescription = !empty($data['event']->description) ? 
    htmlspecialchars(substr(strip_tags($data['event']->description), 0, 300), ENT_QUOTES, 'UTF-8') : 
    'Check out this event!';
$eventUrl = URLROOT . '/calendar/event/' . $data['event']->id;
// Use timezone-aware date formatting
$eventDate = formatDateTimeForUser($data['event']->start_date, $_SESSION['user_id'] ?? null, 'F j, Y');
$eventLocation = !empty($data['event']->location) ? htmlspecialchars($data['event']->location, ENT_QUOTES, 'UTF-8') : '';

// Set page-specific meta data for header
$pageTitle = $eventTitle . ' - ' . APP_NAME;
$pageDescription = $eventDescription;
$pageUrl = $eventUrl;
$pageImage = URLROOT . '/assets/images/logo.png'; // Default image, can be enhanced later

// Add Open Graph and Twitter Card meta tags
$additionalMetaTags = '
<!-- Open Graph / Facebook -->
<meta property="og:type" content="event">
<meta property="og:url" content="' . $eventUrl . '">
<meta property="og:title" content="' . $eventTitle . '">
<meta property="og:description" content="' . $eventDescription . '">
<meta property="og:image" content="' . $pageImage . '">
<meta property="og:site_name" content="' . APP_NAME . '">
<meta property="event:start_time" content="' . gmdate('c', strtotime($data['event']->start_date . ' ' . ($data['event']->start_time ?? '00:00:00'))) . '">
<meta property="event:end_time" content="' . gmdate('c', strtotime($data['event']->end_date . ' ' . ($data['event']->end_time ?? '23:59:59'))) . '">
' . (!empty($eventLocation) ? '<meta property="event:location" content="' . $eventLocation . '">' : '') . '

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:url" content="' . $eventUrl . '">
<meta name="twitter:title" content="' . $eventTitle . '">
<meta name="twitter:description" content="' . $eventDescription . '">
<meta name="twitter:image" content="' . $pageImage . '">

<!-- LinkedIn -->
<meta property="og:locale" content="en_US">
<meta property="article:author" content="' . APP_NAME . '">
';

require APPROOT . '/views/includes/header.php'; 

// Debug: Show Open Graph tags in debug mode
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    echo '<!-- DEBUG: Open Graph Meta Tags -->';
    echo '<!-- Event Title: ' . $eventTitle . ' -->';
    echo '<!-- Event Description: ' . $eventDescription . ' -->';
    echo '<!-- Event URL: ' . $eventUrl . ' -->';
    echo '<!-- Event Date: ' . $eventDate . ' -->';
    echo '<!-- Event Location: ' . $eventLocation . ' -->';
    echo '<!-- Facebook Debugger: https://developers.facebook.com/tools/debug/?q=' . urlencode($eventUrl) . ' -->';
}
?>

<div class="container">
    <!-- Mobile-First Responsive Header -->
    <div class="row mb-4">
        <!-- Title Section -->
        <div class="col-12 col-lg-6 mb-3 mb-lg-0">
            <h1 class="mb-0">Event Details</h1>
        </div>
        
        <!-- Action Buttons Section -->
        <div class="col-12 col-lg-6">
            <div class="header-nav-buttons d-flex flex-column flex-sm-row gap-2 justify-content-lg-end">
                <!-- Back Button -->
                <div class="d-flex gap-2">
                    <button onclick="goBack()" class="btn btn-secondary flex-fill flex-sm-auto d-flex align-items-center justify-content-center">
                        <i class="fas fa-arrow-left me-1 me-sm-2"></i>
                        Back
                    </button>
                </div>
                
                <!-- Action Buttons -->
                <?php if (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && empty($data['event']->show_id)): ?>
                <div class="d-flex gap-2">
                    <a href="<?php echo URLROOT; ?>/calendar/editEvent/<?php echo $data['event']->id; ?>" class="btn btn-primary flex-fill flex-sm-auto d-flex align-items-center justify-content-center">
                        <i class="fas fa-edit me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Edit </span>Event
                    </a>
                    <button type="button" class="btn btn-danger flex-fill flex-sm-auto d-flex align-items-center justify-content-center" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                        <i class="fas fa-trash-alt me-1 me-sm-2"></i>
                        <span class="d-sm-none">Del</span>
                        <span class="d-none d-sm-inline">Delete</span>
                    </button>
                </div>
                <?php elseif (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && !empty($data['event']->show_id)): ?>
                <div class="d-flex gap-2">
                    <a href="<?php echo URLROOT; ?>/admin/editShow/<?php echo $data['event']->show_id; ?>" class="btn btn-primary flex-fill flex-sm-auto d-flex align-items-center justify-content-center">
                        <i class="fas fa-edit me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Edit </span>Show
                    </a>
                    <button type="button" class="btn btn-danger flex-fill flex-sm-auto d-flex align-items-center justify-content-center" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                        <i class="fas fa-trash-alt me-1 me-sm-2"></i>
                        <span class="d-sm-none">Del</span>
                        <span class="d-none d-sm-inline">Delete</span>
                    </button>
                </div>
                <!-- Show info notice -->
                <div class="col-12 mt-2 d-lg-none">
                    <small class="text-muted d-flex align-items-center">
                        <i class="fas fa-info-circle me-1"></i>
                        This event is linked to a show. Edit the show to update this event.
                    </small>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Show info notice for desktop -->
            <?php if (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && !empty($data['event']->show_id)): ?>
            <div class="d-none d-lg-block mt-2">
                <small class="text-muted d-flex align-items-center justify-content-end">
                    <i class="fas fa-info-circle me-1"></i>
                    This event is linked to a show. Edit the show to update this event.
                </small>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1"><?php echo $data['event']->title; ?></h5>

                        <!-- Notification Button (only for logged-in users and non-draft events) -->
                        <?php if (isLoggedIn() && $data['event']->privacy !== 'draft'): ?>
                        <div class="ms-2">
                            <?php
                            // Set notification button variables
                            $event_id = $data['event']->id;
                            $event_type = 'calendar_event';
                            $button_class = 'btn-primary btn-sm notification-btn-header';
                            $show_text = true; // Show text for proper functionality

                            // Include the notification button
                            include APPROOT . '/views/shared/notification_button.php';
                            ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <div class="calendar-color-dot me-2" style="background-color: <?php echo $data['event']->color ?: $data['event']->calendar_color ?: '#3788d8'; ?>"></div>
                            <h6 class="mb-0"><?php echo $data['event']->calendar_name; ?></h6>
                        </div>
                        
                        <div class="mb-3">
                            <i class="far fa-calendar-alt me-2"></i>
                            <?php
                            // Format date and time using user's timezone
                            $userId = isLoggedIn() ? $_SESSION['user_id'] : null;
                            
                            if ($data['event']->all_day) {
                                // For all-day events, just show the date without timezone conversion
                                $startDate = new DateTime($data['event']->start_date);
                                $endDate = new DateTime($data['event']->end_date);
                                
                                echo $startDate->format('l, F j, Y');
                                
                                // Check if multi-day event
                                if ($startDate->format('Y-m-d') != $endDate->format('Y-m-d')) {
                                    echo ' to ' . $endDate->format('l, F j, Y');
                                }
                                
                                echo ' (All day)';
                            } else {
                                // For timed events - convert database time to user's timezone
                                if ($userId && function_exists('formatDateTimeForUser')) {
                                    // Use timezone conversion for logged-in users
                                    try {
                                        $startFormatted = formatDateTimeForUser($data['event']->start_date, $userId, 'l, F j, Y \a\t g:i A');
                                        $endFormatted = formatDateTimeForUser($data['event']->end_date, $userId, 'l, F j, Y \a\t g:i A');
                                        
                                        echo $startFormatted;
                                        
                                        // Check if same day by comparing dates
                                        $startDateOnly = formatDateTimeForUser($data['event']->start_date, $userId, 'Y-m-d');
                                        $endDateOnly = formatDateTimeForUser($data['event']->end_date, $userId, 'Y-m-d');
                                        
                                        if ($startDateOnly == $endDateOnly) {
                                            // Same day - just show end time
                                            $endTimeOnly = formatDateTimeForUser($data['event']->end_date, $userId, 'g:i A');
                                            echo ' - ' . $endTimeOnly;
                                        } else {
                                            // Different days
                                            echo ' to ' . $endFormatted;
                                        }
                                        
                                        // Show user's timezone
                                        if (function_exists('getUserTimezone')) {
                                            $userTimezone = getUserTimezone($userId);
                                            if ($userTimezone && $userTimezone !== 'UTC') {
                                                echo ' <small class="text-muted">(' . $userTimezone . ')</small>';
                                            }
                                        }
                                        
                                    } catch (Exception $e) {
                                        // Fallback to database time if conversion fails
                                        $startDate = new DateTime($data['event']->start_date);
                                        $endDate = new DateTime($data['event']->end_date);
                                        
                                        echo $startDate->format('l, F j, Y \a\t g:i A');
                                        if ($startDate->format('Y-m-d') == $endDate->format('Y-m-d')) {
                                            echo ' - ' . $endDate->format('g:i A');
                                        } else {
                                            echo ' to ' . $endDate->format('l, F j, Y \a\t g:i A');
                                        }
                                        echo ' <small class="text-muted">(Server Time)</small>';
                                    }
                                } else {
                                    // For guests - convert to Eastern time (same as calendar)
                                    try {
                                        $startDate = new DateTime($data['event']->start_date, new DateTimeZone('UTC'));
                                        $startDate->setTimezone(new DateTimeZone('America/New_York'));

                                        $endDate = new DateTime($data['event']->end_date, new DateTimeZone('UTC'));
                                        $endDate->setTimezone(new DateTimeZone('America/New_York'));

                                        echo $startDate->format('l, F j, Y \a\t g:i A');

                                        // Check if same day
                                        if ($startDate->format('Y-m-d') == $endDate->format('Y-m-d')) {
                                            // Same day - just show end time
                                            echo ' - ' . $endDate->format('g:i A');
                                        } else {
                                            // Different days
                                            echo ' to ' . $endDate->format('l, F j, Y \a\t g:i A');
                                        }

                                        echo ' <small class="text-muted">(Eastern Time)</small>';

                                    } catch (Exception $e) {
                                        // Fallback to database time if conversion fails
                                        $startDate = new DateTime($data['event']->start_date);
                                        $endDate = new DateTime($data['event']->end_date);

                                        echo $startDate->format('l, F j, Y \a\t g:i A');
                                        if ($startDate->format('Y-m-d') == $endDate->format('Y-m-d')) {
                                            echo ' - ' . $endDate->format('g:i A');
                                        } else {
                                            echo ' to ' . $endDate->format('l, F j, Y \a\t g:i A');
                                        }
                                        echo ' <small class="text-muted">(Server Time)</small>';
                                    }
                                }
                            }
                            ?>
                        </div>

                        <!-- Timezone Warning for Guests -->
                        <?php if (!isLoggedIn()): ?>
                        <div class="alert alert-info py-2 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock me-2"></i>
                                <small class="mb-0">
                                    <strong>Note:</strong> Event time is displayed in Eastern Time (New York).
                                    <a href="<?php echo URLROOT; ?>/auth/register" class="alert-link">Register</a> or
                                    <a href="<?php echo URLROOT; ?>/auth/login" class="alert-link">login</a> to see times in your local timezone.
                                </small>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($data['event']->location)): ?>
                        <div class="mb-3">
                            <div class="d-flex align-items-start justify-content-between">
                                <div class="flex-grow-1">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    <?php echo $data['event']->location; ?>
                                </div>

                                <?php if (isLoggedIn() && ((!empty($data['event']->lat) && !empty($data['event']->lng)) || (!empty($data['venue']->latitude) && !empty($data['venue']->longitude)))): ?>
                                <div class="ms-2">
                                    <a href="<?php echo URLROOT; ?>/calendar/map/event/<?php echo $data['event']->id; ?>"
                                       class="btn btn-sm btn-outline-primary"
                                       title="View this event on the map">
                                        <i class="fas fa-map me-1"></i>
                                        <span class="d-none d-sm-inline">View on Map</span>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>

                            <?php if (!empty($data['venue'])): ?>
                            <div class="ms-4 mt-2">
                                <?php if (!empty($data['venue']->address)): ?>
                                <div><?php echo $data['venue']->address; ?></div>
                                <?php endif; ?>

                                <?php if (!empty($data['venue']->city) || !empty($data['venue']->state) || !empty($data['venue']->zip)): ?>
                                <div>
                                    <?php echo $data['venue']->city; ?>
                                    <?php if (!empty($data['venue']->city) && !empty($data['venue']->state)): ?>, <?php endif; ?>
                                    <?php echo $data['venue']->state; ?>
                                    <?php if (!empty($data['venue']->zip)): ?> <?php echo $data['venue']->zip; ?><?php endif; ?>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($data['venue']->latitude) && !empty($data['venue']->longitude)): ?>
                                <div class="mt-2">
                                    <a href="https://maps.google.com/?q=<?php echo $data['venue']->latitude; ?>,<?php echo $data['venue']->longitude; ?>" target="_blank" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-external-link-alt me-1"></i> Google Maps
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->url)): ?>
                        <div class="mb-3">
                            <i class="fas fa-link me-2"></i>
                            <a href="<?php echo $data['event']->url; ?>" target="_blank"><?php echo $data['event']->url; ?></a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->show_id)): ?>
                        <div class="mb-3">
                            <i class="fas fa-car me-2"></i>
                            <a href="<?php echo URLROOT; ?>/show/view/<?php echo $data['event']->show_id; ?>"><?php echo $data['event']->show_name; ?></a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->clubs)): ?>
                        <div class="mb-3">
                            <i class="fas fa-users me-2"></i>
                            <strong>Clubs:</strong>
                            <div class="ms-4 mt-1">
                                <?php foreach ($data['event']->clubs as $club): ?>
                                <div class="mb-1"><?php echo $club->name; ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($data['event']->is_recurring): ?>
                        <div class="mb-3">
                            <i class="fas fa-sync-alt me-2"></i>
                            <strong>Recurring Event:</strong>
                            <div class="ms-4 mt-1">
                                <?php echo $data['event']->recurrence_pattern; ?>
                                <?php if (!empty($data['event']->recurrence_end_date)): ?>
                                <div>Until <?php echo $userId && function_exists('formatDateTimeForUser') ? formatDateTimeForUser($data['event']->recurrence_end_date, $userId, 'F j, Y') : formatDateTimeForUser($data['event']->recurrence_end_date, $userId ?? null, 'F j, Y'); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Privacy:</strong>
                            <?php
                            switch ($data['event']->privacy) {
                                case 'public':
                                    echo 'Public - Visible to everyone';
                                    break;
                                case 'private':
                                    echo 'Private - Visible only to you';
                                    break;
                                case 'members':
                                    echo 'Members - Visible to registered users';
                                    break;
                                default:
                                    echo 'Unknown';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($data['event']->description)): ?>
                    <div class="mb-3">
                        <h5>Description</h5>
                        <div class="event-description">
                            <?php 
                            // Check if description contains HTML tags (from WYSIWYG editor)
                            if (strip_tags($data['event']->description) !== $data['event']->description) {
                                // Contains HTML - render it directly with basic security
                                // Use a more sophisticated approach that preserves style attributes
                                $description = $data['event']->description;
                                
                                // Remove potentially dangerous tags and scripts
                                $description = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $description);
                                $description = preg_replace('/<iframe\b[^>]*>.*?<\/iframe>/mi', '', $description);
                                $description = preg_replace('/<object\b[^>]*>.*?<\/object>/mi', '', $description);
                                $description = preg_replace('/<embed\b[^>]*>/mi', '', $description);
                                $description = preg_replace('/<form\b[^>]*>.*?<\/form>/mi', '', $description);
                                
                                // Remove dangerous attributes but preserve style, class, and other formatting attributes
                                $description = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $description);
                                $description = preg_replace('/\s*javascript\s*:/i', '', $description);
                                
                                echo $description;
                            } else {
                                // Plain text - escape and add line breaks
                                echo nl2br(htmlspecialchars($data['event']->description));
                            }
                            ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Social Sharing Section -->
                    <?php if ($data['event']->privacy == 'public'): ?>
                    <div class="mt-4 pt-3 border-top">
                        <div class="row align-items-center">
                            <div class="col-12 col-md-4 mb-3 mb-md-0">
                                <h6 class="mb-0 text-muted">
                                    <i class="fas fa-share-alt me-2"></i>Share this event
                                </h6>
                            </div>
                            <div class="col-12 col-md-8">
                                <div class="social-sharing-buttons d-flex flex-wrap gap-2 justify-content-md-end">
                                    <?php
                                    // Prepare sharing data
                                    $eventUrl = urlencode(URLROOT . '/calendar/event/' . $data['event']->id);
                                    $eventTitle = urlencode($data['event']->title);
                                    $eventDescription = urlencode(!empty($data['event']->description) ? substr(strip_tags($data['event']->description), 0, 200) : 'Check out this event!');
                                    $eventDate = formatDateTimeForUser($data['event']->start_date, $_SESSION['user_id'] ?? null, 'F j, Y');
                                    $eventLocation = !empty($data['event']->location) ? urlencode($data['event']->location) : '';
                                    
                                    // Create sharing text
                                    $shareText = urlencode("Join us for {$data['event']->title} on {$eventDate}" . (!empty($data['event']->location) ? " at {$data['event']->location}" : ''));
                                    ?>

                                    <!-- Facebook Share -->
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo $eventUrl; ?>"
                                       target="_blank"
                                       class="btn btn-outline-primary social-btn facebook-btn"
                                       title="Share on Facebook"
                                       data-bs-toggle="tooltip"
                                       onclick="return openFacebookShare(this.href);">
                                        <i class="fab fa-facebook-f"></i>
                                        <span class="d-none d-sm-inline ms-1">Facebook</span>
                                    </a>

                                    <!-- Twitter Share -->
                                    <a href="https://twitter.com/intent/tweet?url=<?php echo $eventUrl; ?>&text=<?php echo $shareText; ?>&hashtags=events,<?php echo urlencode(str_replace(' ', '', $data['event']->calendar_name)); ?>"
                                       target="_blank"
                                       class="btn btn-outline-info social-btn twitter-btn"
                                       title="Share on Twitter"
                                       data-bs-toggle="tooltip">
                                        <i class="fab fa-twitter"></i>
                                        <span class="d-none d-sm-inline ms-1">Twitter</span>
                                    </a>

                                    <!-- LinkedIn Share -->
                                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo $eventUrl; ?>&title=<?php echo $eventTitle; ?>&summary=<?php echo $eventDescription; ?>"
                                       target="_blank"
                                       class="btn btn-outline-primary social-btn linkedin-btn"
                                       title="Share on LinkedIn"
                                       data-bs-toggle="tooltip">
                                        <i class="fab fa-linkedin-in"></i>
                                        <span class="d-none d-sm-inline ms-1">LinkedIn</span>
                                    </a>

                                    <!-- Email Share -->
                                    <a href="mailto:?subject=<?php echo $eventTitle; ?>&body=<?php echo $shareText; ?>%0A%0AView event details: <?php echo $eventUrl; ?>"
                                       class="btn btn-outline-secondary social-btn email-btn"
                                       title="Share via Email"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-envelope"></i>
                                        <span class="d-none d-sm-inline ms-1">Email</span>
                                    </a>

                                    <!-- Copy Link Button -->
                                    <button type="button"
                                            class="btn btn-outline-dark social-btn copy-link-btn"
                                            title="Copy Link"
                                            data-bs-toggle="tooltip"
                                            data-url="<?php echo URLROOT . '/calendar/event/' . $data['event']->id; ?>"
                                            onclick="copyEventLink(this)">
                                        <i class="fas fa-link"></i>
                                        <span class="d-none d-sm-inline ms-1">Copy Link</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Action Buttons Section -->
                    <div class="mt-4">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <div class="mb-3 mb-md-0">
                                <small class="text-muted">
                                    <?php if (isLoggedIn()): ?>
                                        Created by: <?php echo $data['event']->created_by == $_SESSION['user_id'] ? 'You' : 'Another user'; ?>
                                    <?php else: ?>
                                        <?php if ($data['event']->privacy == 'public'): ?>
                                            Public Event
                                        <?php else: ?>
                                            Member Event
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                            <div class="d-flex flex-column flex-sm-row gap-2 align-items-start align-items-sm-center">

                                <!-- Export Button (for logged-in users) -->
                                <?php if (isLoggedIn()): ?>
                                <div>
                                    <a href="<?php echo URLROOT; ?>/calendar/export/<?php echo $data['event']->calendar_id; ?>" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-download me-1"></i> Export Calendar
                                    </a>
                                </div>
                                <?php else: ?>
                                <!-- Login prompt for guests -->
                                <div>
                                    <a href="<?php echo URLROOT; ?>/auth/login" class="btn btn-sm btn-primary">
                                        <i class="fas fa-sign-in-alt me-1"></i> Login to Access More Features
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <?php if (!empty($data['show'])): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Show Details</h5>
                </div>
                <div class="card-body">
                    <h6><?php echo $data['show']->name; ?></h6>
                    
                    <?php if (!empty($data['show']->description)): ?>
                    <div class="mb-3">
                        <?php echo substr($data['show']->description, 0, 150); ?>
                        <?php if (strlen($data['show']->description) > 150): ?>...<?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?php
                        switch ($data['show']->status) {
                            case 'published':
                                echo 'success';
                                break;
                            case 'draft':
                                echo 'secondary';
                                break;
                            case 'cancelled':
                                echo 'danger';
                                break;
                            case 'completed':
                                echo 'info';
                                break;
                            default:
                                echo 'primary';
                        }
                        ?>">
                            <?php echo ucfirst($data['show']->status); ?>
                        </span>
                    </div>
                    
                    <a href="<?php echo URLROOT; ?>/show/view/<?php echo $data['show']->id; ?>" class="btn btn-primary">
                        View Show Details
                    </a>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($data['venue'])): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Venue Information</h5>
                </div>
                <div class="card-body">
                    <h6><?php echo $data['venue']->name; ?></h6>
                    
                    <?php if (!empty($data['venue']->address) || !empty($data['venue']->city) || !empty($data['venue']->state)): ?>
                    <div class="mb-3">
                        <?php if (!empty($data['venue']->address)): ?>
                        <div><?php echo $data['venue']->address; ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->city) || !empty($data['venue']->state) || !empty($data['venue']->zip)): ?>
                        <div>
                            <?php echo $data['venue']->city; ?>
                            <?php if (!empty($data['venue']->city) && !empty($data['venue']->state)): ?>, <?php endif; ?>
                            <?php echo $data['venue']->state; ?>
                            <?php if (!empty($data['venue']->zip)): ?> <?php echo $data['venue']->zip; ?><?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->country)): ?>
                        <div><?php echo $data['venue']->country; ?></div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->phone) || !empty($data['venue']->email) || !empty($data['venue']->website)): ?>
                    <div class="mb-3">
                        <?php if (!empty($data['venue']->phone)): ?>
                        <div><i class="fas fa-phone me-2"></i> <?php echo $data['venue']->phone; ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->email)): ?>
                        <div><i class="fas fa-envelope me-2"></i> <a href="mailto:<?php echo $data['venue']->email; ?>"><?php echo $data['venue']->email; ?></a></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->website)): ?>
                        <div><i class="fas fa-globe me-2"></i> <a href="<?php echo $data['venue']->website; ?>" target="_blank"><?php echo $data['venue']->website; ?></a></div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->capacity)): ?>
                    <div class="mb-3">
                        <i class="fas fa-users me-2"></i> Capacity: <?php echo number_format($data['venue']->capacity); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->notes)): ?>
                    <div class="mb-3">
                        <strong>Notes:</strong>
                        <div><?php echo nl2br(htmlspecialchars($data['venue']->notes)); ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->latitude) && !empty($data['venue']->longitude)): ?>
                    <div class="mb-3">
                        <a href="https://maps.google.com/?q=<?php echo $data['venue']->latitude; ?>,<?php echo $data['venue']->longitude; ?>" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-map-marker-alt me-1"></i> View on Map
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Related Events</h5>
                </div>
                <div class="card-body p-0">
                    <div id="related-events" class="list-group list-group-flush">
                        <!-- Related events will be loaded here via JavaScript -->
                        <div class="list-group-item text-center">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Loading events...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" id="notificationModalContent">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

<!-- Delete Event Modal -->
<?php if (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin())): ?>
<div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEventModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this event?</p>
                <p><strong><?php echo $data['event']->title; ?></strong></p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo URLROOT; ?>/calendar/deleteEvent/<?php echo $data['event']->id; ?>" method="post">
                    <?php echo csrfTokenField(); ?>
                    <button type="submit" class="btn btn-danger">Delete Event</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Custom CSS -->
<style>
    /* Mobile-First Responsive Header Styles */
    .header-nav-buttons {
        min-height: 44px; /* Minimum touch target size for mobile */
    }

    .header-nav-buttons .btn {
        min-height: 44px;
        font-size: 0.9rem;
        font-weight: 500;
        border-radius: 0.375rem;
        transition: all 0.2s ease-in-out;
    }

    .header-nav-buttons .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-nav-buttons .btn:active {
        transform: translateY(0);
    }

    /* Mobile button adjustments */
    @media (max-width: 575.98px) {
        .header-nav-buttons .btn {
            font-size: 0.85rem;
            padding: 0.5rem 0.75rem;
        }
        
        .header-nav-buttons .btn-group {
            width: 100%;
        }
        
        .header-nav-buttons .btn-group .btn {
            flex: 1;
        }
        
        h1 {
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
    }

    /* Tablet adjustments */
    @media (min-width: 576px) and (max-width: 991.98px) {
        .header-nav-buttons .btn {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
    }

    /* Desktop adjustments */
    @media (min-width: 992px) {
        .header-nav-buttons .btn {
            font-size: 0.95rem;
            padding: 0.5rem 1.25rem;
        }
    }

    /* Button group improvements */
    .header-nav-buttons .btn-group .btn:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .header-nav-buttons .btn-group .btn:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .header-nav-buttons .btn-group .btn:not(:first-child):not(:last-child) {
        border-radius: 0;
    }

    /* Icon spacing improvements */
    .header-nav-buttons .btn i {
        font-size: 0.9em;
    }

    /* Active state improvements */
    .header-nav-buttons .btn.active {
        box-shadow: 0 2px 4px rgba(0,0,0,0.15);
    }

    /* Focus states for accessibility */
    .header-nav-buttons .btn:focus {
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        outline: none;
    }

    .calendar-color-dot {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
    }
    
    .event-description {
        white-space: pre-line;
        line-height: 1.6;
        word-wrap: break-word;
    }
    
    /* WYSIWYG Content Styles */
    .event-description p {
        margin: 0 0 10px 0;
    }
    
    .event-description p:last-child {
        margin-bottom: 0;
    }
    
    .event-description ul,
    .event-description ol {
        margin: 10px 0;
        padding-left: 30px;
    }
    
    .event-description li {
        margin-bottom: 5px;
    }
    
    .event-description blockquote {
        margin: 15px 0;
        padding: 10px 20px;
        border-left: 4px solid #ddd;
        background: #f8f9fa;
        font-style: italic;
    }
    
    .event-description h1,
    .event-description h2,
    .event-description h3,
    .event-description h4,
    .event-description h5,
    .event-description h6 {
        margin: 15px 0 10px 0;
        font-weight: bold;
        line-height: 1.2;
    }
    
    .event-description h1 { font-size: 1.8em; }
    .event-description h2 { font-size: 1.5em; }
    .event-description h3 { font-size: 1.3em; }
    .event-description h4 { font-size: 1.1em; }
    .event-description h5 { font-size: 1em; }
    .event-description h6 { font-size: 0.9em; }
    
    .event-description img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin: 10px 0;
        display: block;
    }
    
    .event-description a {
        color: #007bff;
        text-decoration: none;
    }
    
    .event-description a:hover {
        color: #0056b3;
        text-decoration: underline;
    }
    
    .event-description strong,
    .event-description b {
        font-weight: bold;
    }
    
    .event-description em,
    .event-description i {
        font-style: italic;
    }
    
    .event-description u {
        text-decoration: underline;
    }
    
    .event-description pre {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 10px;
        overflow-x: auto;
        font-family: 'Courier New', Courier, monospace;
        font-size: 0.9em;
        margin: 10px 0;
    }
    
    .event-description code {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 3px;
        padding: 2px 4px;
        font-family: 'Courier New', Courier, monospace;
        font-size: 0.9em;
    }
    
    .related-event {
        border-left: 4px solid #3788d8;
    }
    
    .related-event-title {
        font-weight: 600;
    }
    
    .related-event-time {
        font-size: 0.85rem;
    }
    
    /* Social Sharing Section Styles */
    .social-sharing-buttons {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .social-btn {
        min-width: 44px;
        height: 38px;
        transition: all 0.3s ease;
        border-width: 1px;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem;
    }

    .social-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .social-btn:active {
        transform: translateY(0);
    }
    
    .facebook-btn:hover {
        background-color: #1877f2;
        border-color: #1877f2;
        color: white;
    }
    
    .twitter-btn:hover {
        background-color: #1da1f2;
        border-color: #1da1f2;
        color: white;
    }
    
    .linkedin-btn:hover {
        background-color: #0077b5;
        border-color: #0077b5;
        color: white;
    }
    
    .email-btn:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }
    
    .copy-link-btn:hover {
        background-color: #212529;
        border-color: #212529;
        color: white;
    }
    
    .copy-link-btn.copied {
        background-color: #198754;
        border-color: #198754;
        color: white;
    }
    
    /* Mobile responsiveness */
    @media (max-width: 576px) {
        .social-sharing-buttons {
            justify-content: flex-start;
            width: 100%;
        }

        .social-btn {
            min-width: 44px;
            padding: 0.5rem;
        }

        .social-btn span {
            display: none !important;
        }
    }

    @media (min-width: 577px) and (max-width: 768px) {
        .social-sharing-buttons {
            justify-content: flex-end;
        }

        .social-btn span {
            display: none !important;
        }
    }

    /* View on Map button styles */
    .btn-outline-primary.btn-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }

    /* Notification button in header styles */
    .notification-btn-header {
        font-size: 0.75rem !important;
        padding: 0.25rem 0.5rem !important;
        border-radius: 0.25rem !important;
        background-color: #007bff !important;
        border-color: #007bff !important;
        color: white !important;
        min-height: 28px !important;
        white-space: nowrap !important;
        transition: all 0.2s ease !important;
    }

    .notification-btn-header:hover {
        background-color: #0056b3 !important;
        border-color: #0056b3 !important;
        color: white !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,123,255,0.3);
    }

    .notification-btn-header:active {
        background-color: #004085 !important;
        border-color: #004085 !important;
        color: white !important;
        transform: translateY(0);
    }

    .notification-btn-header:focus {
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25) !important;
        color: white !important;
    }

    /* Handle different notification states */
    .notification-btn-header.subscribed {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
    }

    .notification-btn-header.subscribed:hover {
        background-color: #c82333 !important;
        border-color: #bd2130 !important;
    }

    /* Ensure the header doesn't get too tall */
    .card-header .d-flex {
        min-height: 40px;
        align-items: center;
    }

    @media (max-width: 576px) {
        .btn-outline-primary.btn-sm span {
            display: none !important;
        }

        .notification-btn-header {
            font-size: 0.7rem !important;
            padding: 0.2rem 0.4rem !important;
            min-height: 26px !important;
        }

        /* On very small screens, show shorter text */
        .notification-btn-header .btn-text {
            display: none;
        }

        .notification-btn-header .btn-text-short {
            display: inline;
        }
    }

    @media (min-width: 577px) {
        .notification-btn-header .btn-text {
            display: inline;
        }

        .notification-btn-header .btn-text-short {
            display: none;
        }
    }
    
    /* Debug mode indicator */
    <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
    .social-sharing-buttons::before {
        content: "DEBUG: Social Sharing Active";
        position: absolute;
        top: -20px;
        left: 0;
        font-size: 10px;
        color: #dc3545;
        font-weight: bold;
    }
    <?php endif; ?>
</style>

<!-- JavaScript for related events and social sharing -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Initialize notification button handlers
        initNotificationHandlers();
        
        // Load related events
        loadRelatedEvents();
        
        function initNotificationHandlers() {
            // Handle notification button clicks
            const notificationBtn = document.querySelector('[data-notification-btn]');
            console.log('Calendar event: Looking for notification button:', notificationBtn);
            
            if (notificationBtn) {
                notificationBtn.addEventListener('click', function() {
                    const eventId = this.getAttribute('data-event-id');
                    const eventType = this.getAttribute('data-event-type');
                    
                    console.log('Calendar event: Notification button clicked - Event ID:', eventId, 'Event Type:', eventType);
                    
                    // Load notification modal content
                    const url = '<?php echo BASE_URL; ?>/notification/subscriptionModal?event_id=' + eventId + '&event_type=' + eventType;
                    console.log('Calendar event: Fetching URL:', url);
                    
                    fetch(url)
                        .then(response => {
                            console.log('Calendar event: Response received:', response);
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar event: JSON data:', data);
                            if (data.success) {
                                document.getElementById('notificationModalContent').innerHTML = data.html;
                                const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
                                modal.show();
                            } else {
                                console.error('Calendar event: Server error:', data.message);
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Calendar event: Fetch error:', error);
                            alert('Error loading notification settings. Please try again.');
                        });
                });
            }
        }
        
        function loadRelatedEvents() {
            const relatedEventsContainer = document.getElementById('related-events');
            
            // Get current event details
            const eventId = <?php echo $data['event']->id; ?>;
            const calendarId = <?php echo $data['event']->calendar_id; ?>;
            // Parse database date (stored in UTC) for range calculation
            const eventDateStr = '<?php echo $data['event']->start_date; ?>';
            console.log('Event date string from database (UTC):', eventDateStr);
            
            // Parse database datetime (stored in UTC) for range calculation
            let eventDateUTC;
            if (eventDateStr.includes('T') && (eventDateStr.includes('Z') || eventDateStr.includes('+') || eventDateStr.includes('-'))) {
                // ISO format with timezone info - convert to UTC for range calculation
                eventDateUTC = new Date(eventDateStr);
            } else if (eventDateStr.includes('T')) {
                // ISO format without timezone - assume UTC
                eventDateUTC = new Date(eventDateStr + 'Z');
            } else {
                // MySQL datetime format: YYYY-MM-DD HH:MM:SS, treat as UTC
                const isoString = eventDateStr.replace(' ', 'T') + 'Z';
                eventDateUTC = new Date(isoString);
            }
            
            console.log('Parsed event date (UTC):', eventDateUTC);
            
            // Validate the parsed date
            if (!eventDateUTC || isNaN(eventDateUTC.getTime())) {
                console.error('Invalid event date, using current date as fallback');
                eventDateUTC = new Date();
            }
            
            // Calculate date range (1 month before and after) in UTC
            const startDateUTC = new Date(eventDateUTC);
            startDateUTC.setUTCMonth(startDateUTC.getUTCMonth() - 1);
            
            const endDateUTC = new Date(eventDateUTC);
            endDateUTC.setUTCMonth(endDateUTC.getUTCMonth() + 1);
            
            console.log('Date range for related events (UTC):', startDateUTC.toISOString(), 'to', endDateUTC.toISOString());
            
            // Fetch related events - for guests, don't filter by calendar_id to get all public events
            <?php if (isLoggedIn()): ?>
            const fetchUrl = `<?php echo URLROOT; ?>/calendar/getEvents?calendar_id=${calendarId}&start=${startDateUTC.toISOString()}&end=${endDateUTC.toISOString()}`;
            <?php else: ?>
            // For guests, get all public events in the date range (don't filter by calendar)
            const fetchUrl = `<?php echo URLROOT; ?>/calendar/getEvents?start=${startDateUTC.toISOString()}&end=${endDateUTC.toISOString()}`;
            <?php endif; ?>
            console.log('Fetching related events from:', fetchUrl);
            
            fetch(fetchUrl, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Related events response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(events => {
                console.log('Related events received:', events);
                
                // Check if events is an array
                if (!Array.isArray(events)) {
                    console.error('Expected array but got:', typeof events, events);
                    throw new Error('Invalid response format');
                }
                
                // Filter out current event and sort by date (convert UTC dates for sorting)
                const relatedEvents = events
                    .filter(event => event.id != eventId)
                    .sort((a, b) => {
                        let aStart, bStart;
                        if (window.TimezoneHelper && window.TimezoneHelper.parseMySQLDateTime) {
                            aStart = window.TimezoneHelper.parseMySQLDateTime(a.start);
                            bStart = window.TimezoneHelper.parseMySQLDateTime(b.start);
                        } else {
                            // Parse ISO datetime with timezone (from API) and convert to UTC for sorting
                            aStart = new Date(a.start);
                            bStart = new Date(b.start);
                        }
                        
                        // Validate dates before comparison
                        if (!aStart || isNaN(aStart.getTime())) aStart = new Date(0);
                        if (!bStart || isNaN(bStart.getTime())) bStart = new Date(0);
                        
                        return aStart.getTime() - bStart.getTime();
                    });
                
                // Limit to 5 events
                const displayEvents = relatedEvents.slice(0, 5);
                
                // Clear container
                relatedEventsContainer.innerHTML = '';
                
                if (displayEvents.length === 0) {
                    relatedEventsContainer.innerHTML = '<div class="list-group-item text-center">No related events found</div>';
                    return;
                }
                
                // Add events to container
                displayEvents.forEach(event => {
                    // Convert UTC database date to local timezone for display
                    let startDate;
                    if (window.TimezoneHelper && window.TimezoneHelper.parseMySQLDateTime) {
                        startDate = window.TimezoneHelper.parseMySQLDateTime(event.start);
                    } else {
                        // Parse ISO datetime with timezone (from API) - already in user's timezone
                        startDate = new Date(event.start);
                    }
                    
                    // Validate the parsed date
                    if (!startDate || isNaN(startDate.getTime())) {
                        console.error('Invalid start date for event:', event);
                        startDate = new Date(); // Use current date as fallback
                    }
                    
                    const dateOptions = { month: 'short', day: 'numeric' };
                    const timeOptions = { hour: 'numeric', minute: '2-digit' };
                    
                    let dateDisplay = startDate.toLocaleDateString(undefined, dateOptions);
                    let timeDisplay = event.allDay ? 'All day' : startDate.toLocaleTimeString(undefined, timeOptions);
                    
                    const eventItem = document.createElement('a');
                    eventItem.href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
                    eventItem.className = 'list-group-item list-group-item-action related-event';
                    eventItem.style.borderLeftColor = event.backgroundColor || event.borderColor || '#3788d8';
                    
                    eventItem.innerHTML = `
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 related-event-title">${event.title}</h6>
                            <small>${dateDisplay}</small>
                        </div>
                        <div class="d-flex w-100 justify-content-between">
                            <small class="text-muted">${event.extendedProps.location || ''}</small>
                            <small class="related-event-time">${timeDisplay}</small>
                        </div>
                    `;
                    
                    relatedEventsContainer.appendChild(eventItem);
                });
            })
            .catch(error => {
                console.error('Error loading related events:', error);
                relatedEventsContainer.innerHTML = `<div class="list-group-item text-center text-danger">Error loading related events: ${error.message}</div>`;
            });
        }
    });
    
    // Copy event link function
    function copyEventLink(button) {
        const url = button.getAttribute('data-url');
        
        // Try to use the modern clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(url).then(function() {
                showCopySuccess(button);
            }).catch(function(err) {
                // Fallback to older method
                fallbackCopyTextToClipboard(url, button);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyTextToClipboard(url, button);
        }
    }
    
    function fallbackCopyTextToClipboard(text, button) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        
        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess(button);
            } else {
                showCopyError(button);
            }
        } catch (err) {
            showCopyError(button);
        }
        
        document.body.removeChild(textArea);
    }
    
    function showCopySuccess(button) {
        const originalText = button.innerHTML;
        const originalClass = button.className;
        
        button.classList.add('copied');
        button.innerHTML = '<i class="fas fa-check"></i><span class="d-none d-lg-inline ms-1">Copied!</span>';
        
        setTimeout(function() {
            button.className = originalClass;
            button.innerHTML = originalText;
        }, 2000);
        
        <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
        console.log('Event link copied to clipboard');
        <?php endif; ?>
    }
    
    function showCopyError(button) {
        const originalText = button.innerHTML;
        const originalClass = button.className;
        
        button.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span class="d-none d-lg-inline ms-1">Error</span>';
        
        setTimeout(function() {
            button.className = originalClass;
            button.innerHTML = originalText;
        }, 2000);
        
        <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
        console.error('Failed to copy event link to clipboard');
        <?php endif; ?>
    }
    
    // Facebook share popup function
    function openFacebookShare(url) {
        const popup = window.open(
            url,
            'facebook-share-dialog',
            'width=626,height=436,resizable=yes,scrollbars=yes'
        );
        
        // Focus the popup window
        if (popup) {
            popup.focus();
        }
        
        <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
        console.log('Facebook share popup opened for URL:', url);
        <?php endif; ?>
        
        return false; // Prevent default link behavior
    }
    
    // Go back to previous page function
    function goBack() {
        // Check if there's a previous page in history
        if (window.history.length > 1) {
            window.history.back();
        } else {
            // Fallback: redirect to calendar if logged in, home if not
            <?php if (isLoggedIn()): ?>
            window.location.href = '<?php echo URLROOT; ?>/calendar';
            <?php else: ?>
            window.location.href = '<?php echo URLROOT; ?>/';
            <?php endif; ?>
        }
        
        <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
        console.log('Back button clicked - navigating to previous page');
        <?php endif; ?>
    }

</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>