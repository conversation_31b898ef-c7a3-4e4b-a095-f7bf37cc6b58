<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-envelope-open-text"></i> Email Templates
                </h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                        <i class="fas fa-plus"></i> New Template
                    </button>
                    <a href="<?= URLROOT ?>/admin/settings" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Settings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-<?= $_SESSION['flash_message']['type'] === 'error' ? 'danger' : $_SESSION['flash_message']['type'] ?> alert-dismissible fade show">
        <?= htmlspecialchars($_SESSION['flash_message']['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['flash_message']); endif; ?>

    <div class="row">
        <!-- Templates List -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Email Templates
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($data['templates'])): ?>
                    <div class="text-center p-4">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No templates found</h5>
                        <p class="text-muted">Create your first email template to get started.</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['templates'] as $template): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($template->name) ?></strong>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?= htmlspecialchars(substr($template->subject, 0, 50)) ?><?= strlen($template->subject) > 50 ? '...' : '' ?></span>
                                    </td>
                                    <td>
                                        <?php if ($template->is_active): ?>
                                        <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?= htmlspecialchars($template->created_by_name ?? 'System') ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info"
                                                    onclick="previewTemplate(<?= $template->id ?>)"
                                                    title="Preview">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="<?= URLROOT ?>/admin/editEmailTemplate/<?= $template->id ?>"
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php
                                            $defaultTemplates = ['Auto Reply Confirmation', 'Welcome Message', 'General Response'];
                                            if (!in_array($template->name, $defaultTemplates)):
                                            ?>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteTemplate(<?= $template->id ?>, '<?= htmlspecialchars($template->name) ?>')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Template Information
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Email templates allow you to create reusable content for quick responses and auto-replies.</p>

                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                            <i class="fas fa-plus"></i> Create New Template
                        </button>
                        <button class="btn btn-outline-info" onclick="showVariablesHelp()">
                            <i class="fas fa-question-circle"></i> View Template Variables
                        </button>
                    </div>

                    <hr>

                    <h6 class="mb-2">Quick Tips:</h6>
                    <ul class="small text-muted">
                        <li>Use variables like {{name}} and {{ticket_number}} for dynamic content</li>
                        <li>HTML formatting is supported in template bodies</li>
                        <li>Preview templates before saving to see how they'll look</li>
                        <li>Default templates cannot be deleted but can be edited</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Template Modal -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form action="<?= URLROOT ?>/admin/createEmailTemplate" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Create Email Template</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Template Form -->
                        <div class="col-lg-8">
                            <div class="mb-3">
                                <label for="template_name" class="form-label">Template Name</label>
                                <input type="text" class="form-control" id="template_name" name="template_name" required>
                                <small class="text-muted">A unique name to identify this template</small>
                            </div>

                            <div class="mb-3">
                                <label for="template_subject" class="form-label">Email Subject</label>
                                <input type="text" class="form-control" id="template_subject" name="template_subject" required>
                                <small class="text-muted">Use template variables like {{subject}} and {{ticket_number}}</small>
                            </div>

                            <div class="mb-3">
                                <label for="template_body" class="form-label">Email Body</label>
                                <textarea class="form-control" id="template_body" name="template_body" rows="12" required></textarea>
                                <small class="text-muted">Use template variables and plain text. HTML is supported.</small>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        Active (available for use)
                                    </label>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="previewNewTemplate()">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                            </div>
                        </div>

                        <!-- Template Variables -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-code"></i> Template Variables
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted small">Click any variable to insert it at the cursor position.</p>

                                    <?php foreach ($data['variables'] as $category => $variables): ?>
                                    <h6 class="mt-3 mb-2 text-primary"><?= $category ?></h6>
                                    <div class="mb-3">
                                        <?php foreach ($variables as $variable => $description): ?>
                                        <button type="button" class="btn btn-outline-secondary btn-sm mb-1 me-1"
                                                onclick="insertVariableInModal('template_body', '<?= $variable ?>')"
                                                title="<?= htmlspecialchars($description) ?>">
                                            <?= $variable ?>
                                        </button>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endforeach; ?>

                                    <div class="alert alert-info alert-sm">
                                        <small><i class="fas fa-lightbulb"></i> <strong>Tip:</strong> You can also insert variables into the subject field by clicking on it first, then clicking a variable.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" name="csrf_token" value="<?= $data['csrf_token'] ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Template</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label"><strong>Subject:</strong></label>
                    <div class="border p-2 bg-light" id="previewSubject"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>Body:</strong></label>
                    <div class="border p-3 bg-light" id="previewBody" style="white-space: pre-wrap;"></div>
                </div>
                <div class="alert alert-info">
                    <small><i class="fas fa-info-circle"></i> This preview uses sample data. Actual emails will use real values.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Template Variables Help Modal -->
<div class="modal fade" id="variablesHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Variables Reference</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Use these variables in your email templates. They will be automatically replaced with actual values when emails are sent.</p>

                <?php foreach ($data['variables'] as $category => $variables): ?>
                <h6 class="mt-4 mb-3 text-primary border-bottom pb-2"><?= $category ?></h6>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 30%;">Variable</th>
                                <th>Description</th>
                                <th style="width: 15%;">Copy</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($variables as $variable => $description): ?>
                            <tr>
                                <td><code class="text-primary"><?= $variable ?></code></td>
                                <td class="text-muted"><?= $description ?></td>
                                <td>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                            onclick="copyToClipboard('<?= $variable ?>')"
                                            title="Copy to clipboard">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endforeach; ?>

                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-lightbulb"></i> Usage Tips:</h6>
                    <ul class="mb-0">
                        <li>Variables are case-sensitive and must include the double curly braces</li>
                        <li>You can use multiple variables in the same template</li>
                        <li>Variables work in both subject lines and email bodies</li>
                        <li>If a variable has no value, it will be replaced with an empty string</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Preview template
function previewTemplate(templateId) {
    const formData = new FormData();
    formData.append('template_id', templateId);

    fetch('<?= URLROOT ?>/admin/previewEmailTemplate', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('previewSubject').textContent = data.preview.subject;
            document.getElementById('previewBody').textContent = data.preview.body;

            const modal = new bootstrap.Modal(document.getElementById('previewModal'));
            modal.show();
        } else {
            alert('Failed to generate preview: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error generating preview: ' + error.message);
    });
}

// Preview template with custom content
function previewTemplateContent(subject, body) {
    const formData = new FormData();
    formData.append('subject', subject);
    formData.append('body', body);

    fetch('<?= URLROOT ?>/admin/previewEmailTemplate', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('previewSubject').textContent = data.preview.subject;
            document.getElementById('previewBody').textContent = data.preview.body;

            const modal = new bootstrap.Modal(document.getElementById('previewModal'));
            modal.show();
        } else {
            alert('Failed to generate preview: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error generating preview: ' + error.message);
    });
}

// Preview new template being created
function previewNewTemplate() {
    const subject = document.getElementById('template_subject').value;
    const body = document.getElementById('template_body').value;

    if (!subject || !body) {
        alert('Please enter both subject and body before previewing');
        return;
    }

    previewTemplateContent(subject, body);
}

// Delete template
function deleteTemplate(templateId, templateName) {
    if (confirm(`Are you sure you want to delete the template "${templateName}"? This action cannot be undone.`)) {
        const formData = new FormData();
        formData.append('template_id', templateId);
        
        fetch('<?= URLROOT ?>/admin/deleteEmailTemplate', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showToast('error', data.message);
            }
        })
        .catch(error => {
            showToast('error', 'Error deleting template: ' + error.message);
        });
    }
}

// Toast notifications
function showToast(type, message) {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(container);
    }
    
    container.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Show variables help modal
function showVariablesHelp() {
    const modal = new bootstrap.Modal(document.getElementById('variablesHelpModal'));
    modal.show();
}

// Insert variable into textarea at cursor position
function insertVariableInModal(textareaId, variable) {
    const textarea = document.getElementById(textareaId);
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;

    // Insert the variable at cursor position
    textarea.value = text.substring(0, start) + variable + text.substring(end);

    // Set cursor position after inserted variable
    textarea.selectionStart = textarea.selectionEnd = start + variable.length;
    textarea.focus();

    // Show brief feedback
    showToast('success', `Inserted ${variable}`);
}

// Copy variable to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(text).then(() => {
            showToast('success', `Copied ${text} to clipboard`);
        }).catch(err => {
            console.error('Failed to copy: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(text);
    }
}

// Fallback copy method for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('success', `Copied ${text} to clipboard`);
        } else {
            showToast('error', 'Failed to copy to clipboard');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showToast('error', 'Failed to copy to clipboard');
    }

    document.body.removeChild(textArea);
}

// Make subject field clickable for variable insertion
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to make subject field active for variable insertion
    const subjectField = document.getElementById('template_subject');
    if (subjectField) {
        subjectField.addEventListener('focus', function() {
            // Store the currently focused field for variable insertion
            window.currentFocusedField = 'template_subject';
        });
    }

    const bodyField = document.getElementById('template_body');
    if (bodyField) {
        bodyField.addEventListener('focus', function() {
            window.currentFocusedField = 'template_body';
        });
    }
});

// Enhanced variable insertion that works with currently focused field
function insertVariableInModal(defaultField, variable) {
    const targetField = window.currentFocusedField || defaultField;
    const textarea = document.getElementById(targetField);

    if (!textarea) {
        showToast('error', 'Please click in a text field first');
        return;
    }

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;

    // Insert the variable at cursor position
    textarea.value = text.substring(0, start) + variable + text.substring(end);

    // Set cursor position after inserted variable
    textarea.selectionStart = textarea.selectionEnd = start + variable.length;
    textarea.focus();

    // Show brief feedback
    showToast('success', `Inserted ${variable} into ${targetField === 'template_subject' ? 'subject' : 'body'}`);
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
