<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force PWA Update Banner Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/pwa-features.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Force PWA Update Banner Test</h1>
        <p>This will force show the PWA update banner to test visibility.</p>
        
        <div class="row">
            <div class="col-md-6">
                <button id="force-banner" class="btn btn-primary mb-3">Force Show Update Banner</button>
                <button id="clear-all" class="btn btn-warning mb-3">Clear All Storage</button>
                <button id="check-banner" class="btn btn-info mb-3">Check Banner Status</button>
            </div>
        </div>
        
        <div class="alert alert-info">
            <strong>Instructions:</strong>
            <ol>
                <li>Click "Clear All Storage" first</li>
                <li>Click "Force Show Update Banner"</li>
                <li>Look for the banner at the top center of the page</li>
                <li>Check browser console for debug messages</li>
            </ol>
        </div>
        
        <div class="mt-3">
            <h3>Debug Output:</h3>
            <div id="debug-output" class="border p-3" style="height: 300px; overflow-y: auto; background: #f8f9fa;"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function forceBanner() {
            log('🔄 Forcing PWA update banner...');
            
            // Clear any dismissal flags
            sessionStorage.removeItem('pwa_update_dismissed');
            localStorage.removeItem('pwa_last_update_shown');
            
            // Remove existing banners
            const existing = document.querySelectorAll('.update-banner');
            existing.forEach(banner => banner.remove());
            log(`🗑️ Removed ${existing.length} existing banners`);
            
            // Create new banner
            const banner = document.createElement('div');
            banner.className = 'update-banner alert alert-info alert-dismissible fade show position-fixed';
            banner.style.cssText = `
                top: 20px !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                z-index: 9999 !important;
                max-width: 90% !important;
                width: 400px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                border: none !important;
                background: linear-gradient(135deg, #1338BE 0%, #0056b3 100%) !important;
                color: white !important;
                border-radius: 8px !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: fixed !important;
            `;
            
            banner.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-sync-alt me-2"></i>
                    <div class="flex-grow-1">
                        <strong>App Update Available</strong>
                        <div class="small">A new version is ready to install</div>
                    </div>
                    <div class="ms-2">
                        <button type="button" class="btn btn-light btn-sm me-2" onclick="log('✅ Update button clicked')">
                            <i class="fas fa-download me-1"></i>Update
                        </button>
                        <button type="button" class="btn-close btn-close-white" aria-label="Close" onclick="document.querySelector('.update-banner').remove(); log('❌ Banner dismissed')"></button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(banner);
            log('✅ Banner created and added to DOM');
            
            // Check computed styles
            setTimeout(() => {
                const addedBanner = document.querySelector('.update-banner');
                if (addedBanner) {
                    const style = window.getComputedStyle(addedBanner);
                    log(`📊 Banner styles - Display: ${style.display}, Visibility: ${style.visibility}, Opacity: ${style.opacity}`);
                    log(`📍 Banner position - Position: ${style.position}, Top: ${style.top}, Left: ${style.left}, Z-index: ${style.zIndex}`);
                    log(`📐 Banner size - Width: ${style.width}, Height: ${style.height}`);
                    
                    // Check if it's actually visible
                    const rect = addedBanner.getBoundingClientRect();
                    log(`📏 Banner rect - Top: ${rect.top}, Left: ${rect.left}, Width: ${rect.width}, Height: ${rect.height}`);
                    
                    if (rect.width > 0 && rect.height > 0) {
                        log('✅ Banner appears to be visible!');
                    } else {
                        log('❌ Banner has zero dimensions - likely hidden');
                    }
                } else {
                    log('❌ ERROR: Banner not found in DOM');
                }
            }, 100);
        }

        function clearAll() {
            sessionStorage.clear();
            localStorage.clear();
            const banners = document.querySelectorAll('.update-banner');
            banners.forEach(banner => banner.remove());
            log('🧹 Cleared all storage and removed all banners');
        }

        function checkBanner() {
            const banner = document.querySelector('.update-banner');
            if (banner) {
                const style = window.getComputedStyle(banner);
                log(`🔍 Found banner - Display: ${style.display}, Visibility: ${style.visibility}`);
            } else {
                log('🔍 No banner found in DOM');
            }
            
            const dismissed = sessionStorage.getItem('pwa_update_dismissed');
            const lastShown = localStorage.getItem('pwa_last_update_shown');
            log(`💾 Storage - Dismissed: ${dismissed}, Last shown: ${lastShown}`);
        }

        // Event listeners
        document.getElementById('force-banner').addEventListener('click', forceBanner);
        document.getElementById('clear-all').addEventListener('click', clearAll);
        document.getElementById('check-banner').addEventListener('click', checkBanner);

        // Initial log
        log('🚀 PWA Update Banner Test initialized');
    </script>
</body>
</html>