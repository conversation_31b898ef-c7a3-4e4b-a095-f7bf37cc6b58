/**
 * Custom Calendar JavaScript
 * 
 * A fully responsive, custom-built calendar system for Events and Shows Management System
 * 
 * Version 3.37.0 - Timezone Implementation
 * 
 * CHANGELOG v3.37.0:
 * - IMPLEMENTED: Proper timezone handling for database datetime parsing
 * - Database datetime strings now parsed as UTC instead of local time
 * - Fixed Date.UTC() usage for MySQL datetime format (YYYY-MM-DD HH:MM:SS)
 * - Enhanced debug logging to show UTC vs Local representations
 * - Ensures compatibility with server-side timezone helper functions
 * 
 * CHANGELOG v3.35.63:
 * - FIXED: Layout conflicts caused by direct grid positioning
 * - Uses dedicated overlay container with matching grid structure
 * - Events positioned in overlay grid, not main calendar grid
 * - Eliminates calendar layout distortion and white box issues
 * - Proper event positioning without affecting day cell layout
 * - Top margin adjustment to clear day numbers
 * - Maintains calendar structure integrity
 * 
 * CHANGELOG v3.35.62:
 * - IMPLEMENTED: CSS Grid-based spanning events (industry standard approach)
 * - Uses grid-column and grid-row properties for precise positioning
 * - Based on FullCalendar and other popular calendar libraries
 * - Fixed null reference errors with proper validation
 * - Eliminates getBoundingClientRect() positioning issues
 * - Events span using CSS Grid native capabilities
 * - Proper segment styling for multi-row events
 * 
 * CHANGELOG v3.35.61:
 * - IMPLEMENTED: True visual spanning bars across multiple calendar cells
 * - Uses absolute positioning with getBoundingClientRect() for accurate placement
 * - Creates spanning container overlay for multi-day events
 * - Supports multi-row spanning with proper segment styling
 * - Events now visually span from start date to end date
 * - Proper event stacking and layering for overlapping events
 * 
 * CHANGELOG v3.35.60:
 * - SIMPLIFIED: Multi-day events now show on start date with duration indicator
 * - Added data attributes to day cells for accurate date matching
 * - Enhanced debugging to show actual cell-to-date mapping
 * - Added visual indicators for multi-day events (arrow + duration)
 * - Focused on getting events on correct dates first, then enhance visuals
 * 
 * CHANGELOG v3.35.59:
 * - HOTFIX: Rewrote positioning logic using actual day cell mapping
 * - Maps event dates to actual calendar cell positions
 * - Simplified grid positioning calculation
 * - Enhanced debugging with cell index mapping
 * 
 * CHANGELOG v3.35.58:
 * - HOTFIX: Fixed grid positioning calculation for spanning events
 * - Enhanced date normalization for accurate day index calculation
 * - Added detailed debugging for event positioning
 * 
 * CHANGELOG v3.35.57:
 * - HOTFIX: Added missing getEventsInRange() method
 * 
 * CHANGELOG v3.35.56:
 * - Implemented proper multi-day event spanning across calendar cells
 * - Events now visually span across multiple days instead of repeating
 * - Added CSS Grid positioning for spanning event elements
 * - Separated single-day and multi-day event rendering logic
 * - Enhanced visual continuity for multi-row spanning events
 * 
 * CHANGELOG v3.35.55:
 * - Fixed calendar day cell proportions for multi-day events
 * - Improved multi-day event rendering with visual indicators
 * - Enhanced event content truncation to prevent cell expansion
 * - Added better overflow handling for event-heavy days
 * 
 * CHANGELOG v3.35.54:
 * - Enhanced timezone handling in date comparisons
 * - Added detailed debugging for event date matching
 * - Fixed isSameDay function to handle timezone issues
 * - Added comprehensive event rendering debugging
 */

// Version check - this will show in console to confirm new version is loaded
console.log('=== CUSTOM-CALENDAR.JS LOADED - VERSION 3.37.0 - TIMEZONE IMPLEMENTATION ===');

class CustomCalendar {
    /**
     * Initialize the calendar
     * 
     * @param {string} containerId - The ID of the container element
     * @param {Object} options - Calendar options
     */
    constructor(containerId, options = {}) {
        // Store container element
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`Container element with ID "${containerId}" not found.`);
            return;
        }

        // Default options
        this.options = {
            defaultView: 'month',
            firstDayOfWeek: 0, // 0 = Sunday, 1 = Monday, etc.
            timeFormat: '12', // '12' or '24'
            showWeekends: true,
            businessHoursStart: '09:00',
            businessHoursEnd: '17:00',
            defaultEventDuration: 60, // in minutes
            enableDragDrop: true,
            enableResize: true,
            maxEventsPerDay: 5,
            eventSources: [],
            onEventClick: null,
            onDateClick: null,
            onEventDrop: null,
            onEventResize: null,
            onViewChange: null,
            ...options
        };

        // Set current date and view
        this.currentDate = new Date();
        this.currentView = this.options.defaultView;
        
        // Event data
        this.events = [];
        
        // Calendar elements
        this.calendarEl = null;
        this.headerEl = null;
        this.viewSelectorEl = null;
        this.contentEl = null;
        
        // Initialize calendar
        this.init();
    }

    /**
     * Initialize the calendar
     */
    init() {
        console.log('Initializing calendar');
        console.log(`Default view: ${this.options.defaultView}`);
        console.log(`Current date: ${this.currentDate.toDateString()}`);
        
        // Create calendar structure
        this.createCalendarStructure();
        
        // Load events
        this.loadEvents();
        
        // Render initial view
        this.renderView();
        
        // Add event listeners
        this.addEventListeners();
        
        console.log('Calendar initialization complete');
    }

    /**
     * Create the calendar structure
     */
    createCalendarStructure() {
        // Clear container
        this.container.innerHTML = '';
        
        // Create calendar element
        this.calendarEl = document.createElement('div');
        this.calendarEl.className = 'custom-calendar';
        
        // Create header
        this.headerEl = document.createElement('div');
        this.headerEl.className = 'calendar-header';
        
        // Create title
        const titleEl = document.createElement('h2');
        titleEl.className = 'calendar-title';
        this.headerEl.appendChild(titleEl);
        
        // Create navigation
        const navEl = document.createElement('div');
        navEl.className = 'calendar-nav';
        
        // Previous button
        const prevBtn = document.createElement('button');
        prevBtn.className = 'calendar-nav-btn prev';
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.addEventListener('click', () => this.navigatePrevious());
        navEl.appendChild(prevBtn);
        
        // Today button
        const todayBtn = document.createElement('button');
        todayBtn.className = 'calendar-nav-btn today';
        todayBtn.textContent = 'Today';
        todayBtn.addEventListener('click', () => this.navigateToday());
        navEl.appendChild(todayBtn);
        
        // Next button
        const nextBtn = document.createElement('button');
        nextBtn.className = 'calendar-nav-btn next';
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.addEventListener('click', () => this.navigateNext());
        navEl.appendChild(nextBtn);
        
        this.headerEl.appendChild(navEl);
        this.calendarEl.appendChild(this.headerEl);
        
        // Create view selector
        this.viewSelectorEl = document.createElement('div');
        this.viewSelectorEl.className = 'calendar-view-selector';
        
        // Month view button
        const monthBtn = document.createElement('button');
        monthBtn.className = 'calendar-view-btn month';
        monthBtn.textContent = 'Month';
        monthBtn.addEventListener('click', () => this.changeView('month'));
        this.viewSelectorEl.appendChild(monthBtn);
        
        // Week view button
        const weekBtn = document.createElement('button');
        weekBtn.className = 'calendar-view-btn week';
        weekBtn.textContent = 'Week';
        weekBtn.addEventListener('click', () => this.changeView('week'));
        this.viewSelectorEl.appendChild(weekBtn);
        
        // Day view button
        const dayBtn = document.createElement('button');
        dayBtn.className = 'calendar-view-btn day';
        dayBtn.textContent = 'Day';
        dayBtn.addEventListener('click', () => this.changeView('day'));
        this.viewSelectorEl.appendChild(dayBtn);
        
        // List view button
        const listBtn = document.createElement('button');
        listBtn.className = 'calendar-view-btn list';
        listBtn.textContent = 'List';
        listBtn.addEventListener('click', () => this.changeView('list'));
        this.viewSelectorEl.appendChild(listBtn);
        
        this.calendarEl.appendChild(this.viewSelectorEl);
        
        // Create content container
        this.contentEl = document.createElement('div');
        this.contentEl.className = 'calendar-content';
        this.calendarEl.appendChild(this.contentEl);
        
        // Add calendar to container
        this.container.appendChild(this.calendarEl);
    }

    /**
     * Add event listeners
     */
    addEventListeners() {
        // Window resize event
        window.addEventListener('resize', () => {
            this.renderView();
        });
    }

    /**
     * Load events from all sources
     */
    loadEvents() {
        console.log(`Loading events for view: ${this.currentView}`);
        console.log(`Current date: ${this.currentDate.toDateString()}`);
        console.log(`Events count before clearing: ${this.events.length}`);
        
        // Clear existing events completely (both data and DOM)
        this.clearEvents();
        
        // If no event sources, return
        if (!this.options.eventSources || this.options.eventSources.length === 0) {
            console.log('No event sources configured');
            this.renderView();
            return;
        }
        
        // Get date range for current view
        const { startDate, endDate } = this.getViewDateRange();
        console.log(`Date range for ${this.currentView} view: ${startDate.toDateString()} to ${endDate.toDateString()}`);
        console.log(`Event sources count: ${this.options.eventSources.length}`);
        
        // Track loading progress
        let loadedSources = 0;
        
        // Load events from each source
        this.options.eventSources.forEach(source => {
            if (typeof source === 'function') {
                // Function source with callbacks
                try {
                    // Create info object similar to FullCalendar
                    const fetchInfo = {
                        start: startDate,
                        end: endDate,
                        startStr: startDate.toISOString(),
                        endStr: endDate.toISOString()
                    };
                    
                    // Define success callback
                    const successCallback = (events) => {
                        if (events) {
                            if (Array.isArray(events)) {
                                console.log(`Received ${events.length} events from function source`);
                                this.addEvents(events);
                            } else {
                                console.error('Invalid events data (not an array):', events);
                            }
                        } else {
                            console.log('No events returned from function source');
                        }
                        
                        loadedSources++;
                        if (loadedSources === this.options.eventSources.length) {
                            this.renderView();
                        }
                    };
                    
                    // Define failure callback
                    const failureCallback = (error) => {
                        console.error('Error loading events from function source:', error);
                        loadedSources++;
                        if (loadedSources === this.options.eventSources.length) {
                            this.renderView();
                        }
                    };
                    
                    // Call the function source with proper parameters
                    source(fetchInfo, successCallback, failureCallback);
                } catch (error) {
                    console.error('Error calling function event source:', error);
                    loadedSources++;
                    if (loadedSources === this.options.eventSources.length) {
                        this.renderView();
                    }
                }
            } else if (typeof source === 'string') {
                // URL source
                fetch(source + `?start=${startDate.toISOString()}&end=${endDate.toISOString()}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(events => {
                        // Check if the response contains an error message
                        if (events && events.error) {
                            console.error('Server error:', events.error);
                            throw new Error(events.error);
                        }
                        
                        // Make sure events is an array
                        if (!Array.isArray(events)) {
                            console.error('Invalid events data:', events);
                            throw new Error('Invalid events data received from server');
                        }
                        
                        // Log the number of events received
                        console.log(`Received ${events.length} events from server`);
                        
                        // Add events if there are any
                        this.addEvents(events);
                        loadedSources++;
                        
                        if (loadedSources === this.options.eventSources.length) {
                            this.renderView();
                        }
                    })
                    .catch(error => {
                        console.error('Error loading events:', error);
                        loadedSources++;
                        
                        if (loadedSources === this.options.eventSources.length) {
                            this.renderView();
                        }
                    });
            } else if (Array.isArray(source)) {
                // Array source
                this.addEvents(source);
                loadedSources++;
                
                if (loadedSources === this.options.eventSources.length) {
                    this.renderView();
                }
            }
        });
    }

    /**
     * Add events to the calendar
     * 
     * @param {Array} events - Array of event objects
     */
    addEvents(events) {
        // Handle null, undefined or non-array values
        if (!events) {
            console.log('addEvents received null or undefined, skipping');
            return;
        }
        
        if (!Array.isArray(events)) {
            console.error('addEvents received non-array:', events);
            return;
        }
        
        if (events.length === 0) {
            console.log('addEvents received empty array, nothing to add');
            return;
        }
        
        console.log(`Adding ${events.length} events to calendar. Current events count: ${this.events.length}`);
        
        events.forEach(event => {
            console.log(`Processing event: ${JSON.stringify(event)}`);
            
            // Ensure start and end are Date objects
            try {
                if (typeof event.start === 'string') {
                    const originalStart = event.start;
                    // Controller handles timezone conversion - just parse the date
                    event.start = new Date(event.start);

                    // Debug: Show conversion
                    if (DEBUG_MODE) {
                        console.log(`Converted start date from ${originalStart} to Date object: ${event.start.toString()}`);
                    }
                } else if (!(event.start instanceof Date)) {
                    console.error(`Invalid start date for event: ${event.title}`);
                    event.start = new Date(); // Use current date as fallback
                }
                
                if (typeof event.end === 'string') {
                    const originalEnd = event.end;
                    // Controller handles timezone conversion - just parse the date
                    event.end = new Date(event.end);

                    // Debug: Show conversion
                    if (DEBUG_MODE) {
                        console.log(`Converted end date from ${originalEnd} to Date object: ${event.end.toString()}`);
                    }
                    console.log(`Converted end date from ${originalEnd} to ${event.end.toISOString()}`);
                } else if (!event.end) {
                    // If no end date, use start date + default duration
                    event.end = new Date(event.start.getTime() + (this.options.defaultEventDuration * 60 * 1000));
                    console.log(`Created end date: ${event.end.toISOString()} (default duration: ${this.options.defaultEventDuration} minutes)`);
                } else if (!(event.end instanceof Date)) {
                    console.error(`Invalid end date for event: ${event.title}`);
                    event.end = new Date(event.start.getTime() + (this.options.defaultEventDuration * 60 * 1000));
                }
            } catch (error) {
                console.error(`Error processing dates for event: ${event.title}`, error);
                // Use current date as fallback
                event.start = new Date();
                event.end = new Date(event.start.getTime() + (this.options.defaultEventDuration * 60 * 1000));
            }
            
            // Validate dates
            if (isNaN(event.start.getTime())) {
                console.error(`Invalid start date for event: ${event.title}`);
                event.start = new Date(); // Use current date as fallback
            }
            
            if (isNaN(event.end.getTime())) {
                console.error(`Invalid end date for event: ${event.title}`);
                event.end = new Date(event.start.getTime() + (this.options.defaultEventDuration * 60 * 1000));
            }
            
            // Ensure end date is after start date
            if (event.end < event.start) {
                console.error(`End date is before start date for event: ${event.title}`);
                event.end = new Date(event.start.getTime() + (this.options.defaultEventDuration * 60 * 1000));
            }
            
            // For debugging, add a property to track if this is a multi-day event
            event.isMultiDay = event.start.toDateString() !== event.end.toDateString();
            
            // Add a unique identifier for debugging duplicates
            const eventKey = `${event.id}_${event.title}_${event.start.toISOString()}`;
            
            // Check if this event already exists
            const existingEvent = this.events.find(e => {
                const existingKey = `${e.id}_${e.title}_${e.start.toISOString()}`;
                return existingKey === eventKey;
            });
            
            if (existingEvent) {
                console.warn(`DUPLICATE EVENT DETECTED: ${eventKey}`);
                console.warn('Existing event:', existingEvent);
                console.warn('New event:', event);
                // Don't add the duplicate
                return;
            }
            
            console.log(`Final event: ${event.title}, Start: ${event.start.toISOString()}, End: ${event.end.toISOString()}, AllDay: ${event.allDay}`);
            this.events.push(event);
        });
        
        console.log(`Total events in calendar: ${this.events.length}`);
        
        // Check for duplicates in the final events array
        this.checkForDuplicates();
    }
    
    /**
     * Check for duplicate events in the events array
     */
    checkForDuplicates() {
        const eventKeys = new Set();
        const duplicates = [];
        
        this.events.forEach((event, index) => {
            const eventKey = `${event.id}_${event.title}_${event.start.toISOString()}`;
            if (eventKeys.has(eventKey)) {
                duplicates.push({ index, event, key: eventKey });
            } else {
                eventKeys.add(eventKey);
            }
        });
        
        if (duplicates.length > 0) {
            console.error(`FOUND ${duplicates.length} DUPLICATE EVENTS:`, duplicates);
        } else {
            console.log('No duplicate events found');
        }
    }

    /**
     * Clear all events from the calendar
     */
    clearEvents() {
        console.log('Clearing all events from calendar');
        
        // Clear the events array
        this.events = [];
        
        // Clear rendered events from DOM
        if (this.container) {
            // Remove all event elements with various possible selectors
            const eventSelectors = [
                '.calendar-event',
                '.event',
                '.fc-event',
                '.custom-event',
                '[data-event-id]'
            ];
            
            eventSelectors.forEach(selector => {
                const elements = this.container.querySelectorAll(selector);
                elements.forEach(el => el.remove());
            });
            
            // Also clear any event-related classes or attributes
            const eventCells = this.container.querySelectorAll('.has-events, .event-cell, [data-events]');
            eventCells.forEach(cell => {
                cell.classList.remove('has-events', 'event-cell');
                cell.removeAttribute('data-events');
                cell.removeAttribute('data-event-count');
            });
            
            // Clear any event containers or wrappers
            const eventContainers = this.container.querySelectorAll('.events-container, .event-wrapper');
            eventContainers.forEach(container => {
                container.innerHTML = '';
            });
        }
        
        console.log('All events cleared from calendar');
    }

    /**
     * Get date range for current view
     * 
     * @returns {Object} Object with startDate and endDate
     */
    getViewDateRange() {
        const startDate = new Date(this.currentDate);
        const endDate = new Date(this.currentDate);
        
        switch (this.currentView) {
            case 'month':
                // Start from first day of month
                startDate.setDate(1);
                
                // Start from first day of week containing first day of month
                const firstDayOfWeek = (startDate.getDay() - this.options.firstDayOfWeek + 7) % 7;
                startDate.setDate(startDate.getDate() - firstDayOfWeek);
                
                // End on last day of month
                endDate.setMonth(endDate.getMonth() + 1);
                endDate.setDate(0);
                
                // End on last day of week containing last day of month
                const lastDayOfWeek = (6 - endDate.getDay() + this.options.firstDayOfWeek) % 7;
                endDate.setDate(endDate.getDate() + lastDayOfWeek);
                break;
                
            case 'week':
                // Start from first day of week
                // Create a fresh copy to avoid reference issues
                const weekStartDate = new Date(startDate.getTime());
                const dayOfWeek = (weekStartDate.getDay() - this.options.firstDayOfWeek + 7) % 7;
                weekStartDate.setDate(weekStartDate.getDate() - dayOfWeek);
                
                // Set the start date to the beginning of the week
                startDate.setFullYear(weekStartDate.getFullYear());
                startDate.setMonth(weekStartDate.getMonth());
                startDate.setDate(weekStartDate.getDate());
                
                // Create the end date as exactly 6 days after the start date
                endDate.setFullYear(startDate.getFullYear());
                endDate.setMonth(startDate.getMonth());
                endDate.setDate(startDate.getDate() + 6);
                
                console.log(`Week view range calculation: Current date: ${this.currentDate.toDateString()}`);
                console.log(`Week start: ${startDate.toDateString()}, Week end: ${endDate.toDateString()}`);
                break;
                
            case 'day':
                // Just one day - make sure start and end are the same day
                // Create fresh copies to avoid reference issues
                const dayDate = new Date(this.currentDate.getTime());
                
                // Set the start date to the beginning of the day
                startDate.setFullYear(dayDate.getFullYear());
                startDate.setMonth(dayDate.getMonth());
                startDate.setDate(dayDate.getDate());
                
                // Set the end date to the same day
                endDate.setFullYear(dayDate.getFullYear());
                endDate.setMonth(dayDate.getMonth());
                endDate.setDate(dayDate.getDate());
                
                console.log(`Day view range: ${startDate.toDateString()} to ${endDate.toDateString()}`);
                break;
                
            case 'list':
                // Default to 30 days
                endDate.setDate(endDate.getDate() + 30);
                break;
        }
        
        // Set time to start and end of day
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        
        return { startDate, endDate };
    }

    /**
     * Render the current view
     */
    renderView() {
        console.log(`Rendering view: ${this.currentView}`);
        console.log(`Total events to render: ${this.events.length}`);
        
        // Trigger event table update
        this.triggerEventTableUpdate(this.events);
        
        // Update title
        this.updateTitle();
        
        // Update view selector
        this.updateViewSelector();
        
        // Each view method now clears the content itself
        // to prevent any potential issues with multiple calls
        
        // Render appropriate view
        switch (this.currentView) {
            case 'month':
                this.renderMonthView();
                break;
                
            case 'week':
                this.renderWeekView();
                break;
                
            case 'day':
                this.renderDayView();
                break;
                
            case 'list':
                this.renderListView();
                break;
                
            default:
                console.error(`Unknown view: ${this.currentView}`);
                // Fallback to month view
                this.currentView = 'month';
                this.renderMonthView();
        }
    }

    /**
     * Update the calendar title
     */
    updateTitle() {
        const titleEl = this.headerEl.querySelector('.calendar-title');
        
        switch (this.currentView) {
            case 'month':
                titleEl.textContent = this.formatMonthYear(this.currentDate);
                break;
                
            case 'week':
                const weekStart = new Date(this.currentDate);
                const dayOfWeek = (weekStart.getDay() - this.options.firstDayOfWeek + 7) % 7;
                weekStart.setDate(weekStart.getDate() - dayOfWeek);
                
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekStart.getDate() + 6);
                
                if (weekStart.getMonth() === weekEnd.getMonth() && weekStart.getFullYear() === weekEnd.getFullYear()) {
                    titleEl.textContent = `${this.formatDate(weekStart)} - ${this.formatDate(weekEnd)}, ${weekEnd.getFullYear()}`;
                } else if (weekStart.getFullYear() === weekEnd.getFullYear()) {
                    titleEl.textContent = `${this.formatDate(weekStart)} - ${this.formatDate(weekEnd)}, ${weekEnd.getFullYear()}`;
                } else {
                    titleEl.textContent = `${this.formatDate(weekStart)}, ${weekStart.getFullYear()} - ${this.formatDate(weekEnd)}, ${weekEnd.getFullYear()}`;
                }
                break;
                
            case 'day':
                titleEl.textContent = this.formatDateFull(this.currentDate);
                break;
                
            case 'list':
                const listEnd = new Date(this.currentDate);
                listEnd.setDate(listEnd.getDate() + 30);
                
                if (this.currentDate.getMonth() === listEnd.getMonth() && this.currentDate.getFullYear() === listEnd.getFullYear()) {
                    titleEl.textContent = `${this.formatDate(this.currentDate)} - ${this.formatDate(listEnd)}, ${listEnd.getFullYear()}`;
                } else if (this.currentDate.getFullYear() === listEnd.getFullYear()) {
                    titleEl.textContent = `${this.formatDate(this.currentDate)} - ${this.formatDate(listEnd)}, ${listEnd.getFullYear()}`;
                } else {
                    titleEl.textContent = `${this.formatDate(this.currentDate)}, ${this.currentDate.getFullYear()} - ${this.formatDate(listEnd)}, ${listEnd.getFullYear()}`;
                }
                break;
        }
    }

    /**
     * Update the view selector
     */
    updateViewSelector() {
        // Remove active class from all buttons
        const buttons = this.viewSelectorEl.querySelectorAll('.calendar-view-btn');
        buttons.forEach(button => {
            button.classList.remove('active');
        });
        
        // Add active class to current view button
        const activeButton = this.viewSelectorEl.querySelector(`.calendar-view-btn.${this.currentView}`);
        if (activeButton) {
            activeButton.classList.add('active');
        }
    }

    /**
     * Render the month view
     */
    renderMonthView() {
        console.log('Rendering month view');
        
        // Clear any existing content first to prevent overlapping
        this.contentEl.innerHTML = '';
        
        // Create month container
        const monthEl = document.createElement('div');
        monthEl.className = 'calendar-month';
        
        // Add weekday headers
        const weekdays = this.getWeekdayNames();
        weekdays.forEach(weekday => {
            const weekdayEl = document.createElement('div');
            weekdayEl.className = 'calendar-weekday';
            weekdayEl.textContent = weekday;
            monthEl.appendChild(weekdayEl);
        });
        
        // Get date range for month view
        const { startDate, endDate } = this.getViewDateRange();
        
        // Get all events for the month and separate single-day from multi-day
        const allEvents = this.getEventsInRange(startDate, endDate);
        const multiDayEvents = [];
        const singleDayEvents = [];
        
        // Separate events by type
        allEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            if (this.isMultiDayEvent(eventStart, eventEnd)) {
                multiDayEvents.push(event);
            } else {
                singleDayEvents.push(event);
            }
        });
        
        console.log(`Found ${multiDayEvents.length} multi-day events and ${singleDayEvents.length} single-day events`);
        
        // Current month
        const currentMonth = this.currentDate.getMonth();
        
        // Create day cells
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            const dayEl = document.createElement('div');
            dayEl.className = 'calendar-day';
            
            // Add classes for styling
            if (currentDate.getMonth() !== currentMonth) {
                dayEl.classList.add('other-month');
            }
            
            if (this.isToday(currentDate)) {
                dayEl.classList.add('today');
            }
            
            if (this.isWeekend(currentDate) && !this.options.showWeekends) {
                dayEl.classList.add('weekend');
            }
            
            // Create day header
            const dayHeaderEl = document.createElement('div');
            dayHeaderEl.className = 'calendar-day-header';
            
            // Day number
            const dayNumberEl = document.createElement('div');
            dayNumberEl.className = 'calendar-day-number';
            dayNumberEl.textContent = currentDate.getDate();
            dayHeaderEl.appendChild(dayNumberEl);
            
            dayEl.appendChild(dayHeaderEl);
            
            // Create events container with separate sections
            const eventsEl = document.createElement('div');
            eventsEl.className = 'calendar-day-events';
            
            // Create all-day events section
            const allDayEventsEl = document.createElement('div');
            allDayEventsEl.className = 'calendar-all-day-events';
            
            // Create timed events section
            const timedEventsEl = document.createElement('div');
            timedEventsEl.className = 'calendar-timed-events';
            
            // Get only single-day events for this day
            const dayEvents = singleDayEvents.filter(event => {
                return this.getEventsForDay(currentDate).some(dayEvent => dayEvent.id === event.id);
            });
            
            if (DEBUG_MODE && dayEvents.length > 0) {
                console.log(`=== RENDERING SINGLE-DAY EVENTS FOR ${currentDate.toDateString()} ===`);
                console.log(`Found ${dayEvents.length} single-day events for this day:`);
                dayEvents.forEach((event, index) => {
                    console.log(`  ${index + 1}. ${event.title} (${event.start.toString()})`);
                });
            }
            
            // Separate all-day and timed events
            const allDayEvents = dayEvents.filter(event => event.allDay);
            const timedEvents = dayEvents.filter(event => !event.allDay);
            
            // Sort all-day events by title
            allDayEvents.sort((a, b) => a.title.localeCompare(b.title));
            
            // Sort timed events by start time
            timedEvents.sort((a, b) => a.start.getTime() - b.start.getTime());
            
            // Add all-day events to their section
            allDayEvents.forEach((event, index) => {
                if (index < 3) { // Limit all-day events to prevent overflow
                    const eventEl = this.createSingleDayEventElement(event);
                    eventEl.classList.add('all-day');
                    allDayEventsEl.appendChild(eventEl);
                    
                    if (DEBUG_MODE) {
                        console.log(`✓ RENDERED all-day event: ${event.title} on ${currentDate.toDateString()}`);
                    }
                }
            });
            
            // Add timed events with positioning
            timedEvents.forEach((event, index) => {
                if (index < 5) { // Limit timed events
                    const eventEl = this.createSingleDayEventElement(event);
                    eventEl.classList.add('timed');
                    
                    // Calculate position based on time (simplified for month view)
                    const eventStart = new Date(event.start);
                    const hour = eventStart.getHours();
                    const minute = eventStart.getMinutes();
                    const totalMinutes = hour * 60 + minute;
                    const dayMinutes = 24 * 60;
                    const position = (totalMinutes / dayMinutes) * 100; // Percentage of day
                    
                    eventEl.style.top = `${Math.min(position, 80)}%`; // Max 80% to stay in cell
                    timedEventsEl.appendChild(eventEl);
                    
                    if (DEBUG_MODE) {
                        console.log(`✓ RENDERED timed event: ${event.title} at ${hour}:${minute.toString().padStart(2, '0')} (${position.toFixed(1)}%)`);
                    }
                }
            });
            
            // Add sections to events container
            if (allDayEvents.length > 0) {
                eventsEl.appendChild(allDayEventsEl);
            }
            if (timedEvents.length > 0) {
                eventsEl.appendChild(timedEventsEl);
            }
            
            // Add "more" indicator if needed
            const totalEvents = dayEvents.length;
            const visibleEvents = Math.min(3, allDayEvents.length) + Math.min(5, timedEvents.length);
            if (totalEvents > visibleEvents) {
                const moreEl = document.createElement('div');
                moreEl.className = 'calendar-more-events';
                moreEl.textContent = `+ ${totalEvents - visibleEvents} more`;
                moreEl.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showMoreEvents(dayEvents, currentDate);
                });
                eventsEl.appendChild(moreEl);
            }
            
            dayEl.appendChild(eventsEl);
            
            // Add click event to day cell
            dayEl.addEventListener('click', () => {
                if (typeof this.options.onDateClick === 'function') {
                    const clickDate = new Date(currentDate);
                    this.options.onDateClick(clickDate);
                }
            });
            
            monthEl.appendChild(dayEl);
            
            // Move to next day
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        this.contentEl.appendChild(monthEl);
        
        // Add data attributes to day cells for debugging
        const allDayCells = monthEl.querySelectorAll('.calendar-day');
        const debugCurrentDate = new Date(startDate);
        console.log(`\n=== DAY CELL MAPPING DEBUG ===`);
        console.log(`Calendar view starts: ${startDate.toDateString()}`);
        allDayCells.forEach((cell, index) => {
            const dateStr = debugCurrentDate.toISOString().split('T')[0];
            cell.setAttribute('data-date', dateStr);
            cell.setAttribute('data-index', index);
            
            // Calculate which row this cell should be in
            const expectedRow = Math.floor(index / 7) + 2; // +2 for header row
            const expectedCol = (index % 7) + 1;
            
            console.log(`Cell ${index}: ${debugCurrentDate.toDateString()} (${dateStr}) -> Grid Row ${expectedRow}, Col ${expectedCol}`);
            debugCurrentDate.setDate(debugCurrentDate.getDate() + 1);
        });
        console.log(`=== END DAY CELL MAPPING ===\n`);
        
        // Now render multi-day events as spanning elements
        this.renderMultiDayEvents(monthEl, multiDayEvents, startDate, endDate);
    }

    /**
     * Render the week view
     */
    renderWeekView() {
        console.log('Rendering week view');
        
        // Clear any existing content first to prevent overlapping
        this.contentEl.innerHTML = '';
        
        // Create week container
        const weekEl = document.createElement('div');
        weekEl.className = 'calendar-week';
        
        // Create week header
        const headerEl = document.createElement('div');
        headerEl.className = 'calendar-week-header';
        
        // Add empty cell for time column
        const emptyHeaderCell = document.createElement('div');
        emptyHeaderCell.className = 'calendar-week-header-cell';
        headerEl.appendChild(emptyHeaderCell);
        
        // Get date range for week view
        const { startDate, endDate } = this.getViewDateRange();
        console.log(`Week view date range: ${startDate.toDateString()} to ${endDate.toDateString()}`);
        console.log(`Current date: ${this.currentDate.toDateString()}`);
        console.log(`Total events: ${this.events.length}`);
        
        // Add day headers
        // Check if we're on a mobile device by window width
        const isMobile = window.innerWidth <= 576;
        
        // Use ultra-short names on mobile
        const weekdays = this.getWeekdayNames(true, isMobile);
        
        // Use a for loop with fixed number of days (7) to avoid any issues
        for (let i = 0; i < 7; i++) {
            // Create a new date object for each day to avoid reference issues
            const currentDate = new Date(startDate);
            currentDate.setDate(startDate.getDate() + i);
            
            const headerCellEl = document.createElement('div');
            headerCellEl.className = 'calendar-week-header-cell';
            headerCellEl.dataset.date = currentDate.toISOString().split('T')[0]; // Store the date for debugging
            
            if (this.isToday(currentDate)) {
                headerCellEl.classList.add('today');
            }
            
            if (this.isWeekend(currentDate) && !this.options.showWeekends) {
                headerCellEl.classList.add('weekend');
            }
            
            // Get the day of week (0-6) and use it to get the correct weekday name
            const dayOfWeek = currentDate.getDay();
            
            // On mobile, make the layout more compact
            if (isMobile) {
                headerCellEl.innerHTML = `
                    <div>${weekdays[dayOfWeek]}</div>
                    <div>${currentDate.getDate()}</div>
                `;
            } else {
                headerCellEl.innerHTML = `
                    <div>${weekdays[dayOfWeek]}</div>
                    <div>${currentDate.getDate()}</div>
                `;
            }
            
            headerEl.appendChild(headerCellEl);
        }
        
        weekEl.appendChild(headerEl);
        
        // Create week body
        const bodyEl = document.createElement('div');
        bodyEl.className = 'calendar-week-body';
        
        // Add time column
        const timesEl = document.createElement('div');
        timesEl.className = 'calendar-week-times';
        
        // Add time slots
        // Check if we're on a mobile device by window width
        const isMobileWeekView = window.innerWidth <= 576;
        
        for (let hour = 0; hour < 24; hour++) {
            const timeEl = document.createElement('div');
            timeEl.className = 'calendar-week-time';
            
            // On mobile, show only the hour number without AM/PM
            if (isMobileWeekView) {
                timeEl.textContent = hour;
            } else {
                timeEl.textContent = this.formatTime(hour, 0);
            }
            
            timesEl.appendChild(timeEl);
        }
        
        bodyEl.appendChild(timesEl);
        
        // Reset date to start of week
        // Create a fresh copy of the start date
        const weekStartDate = new Date(startDate.getTime());
        
        // Add day columns - use a for loop with fixed number of days (7) to avoid any issues
        for (let i = 0; i < 7; i++) {
            // Create a new date object for each day to avoid reference issues
            const currentDate = new Date(weekStartDate);
            currentDate.setDate(weekStartDate.getDate() + i);
            
            // Create the day column
            const dayEl = document.createElement('div');
            dayEl.className = 'calendar-week-day';
            dayEl.dataset.date = currentDate.toISOString().split('T')[0]; // Store the date for debugging
            
            // Highlight today
            if (this.isToday(currentDate)) {
                dayEl.classList.add('today');
            }
            
            // Handle weekends
            if (this.isWeekend(currentDate) && !this.options.showWeekends) {
                dayEl.classList.add('weekend');
            }
            
            // Add hour slots
            for (let hour = 0; hour < 24; hour++) {
                const hourEl = document.createElement('div');
                hourEl.className = 'calendar-week-hour';
                hourEl.dataset.hour = hour;
                dayEl.appendChild(hourEl);
                
                // Add half-hour slot
                const halfHourEl = document.createElement('div');
                halfHourEl.className = 'calendar-week-hour half-hour';
                halfHourEl.dataset.hour = hour;
                halfHourEl.dataset.minute = 30;
                dayEl.appendChild(halfHourEl);
            }
            
            // Get events for this day
            console.log(`Getting events for day in week view: ${currentDate.toDateString()} (${currentDate.toISOString()})`);
            
            // Get events for this specific day
            const dayEvents = this.getEventsForDay(currentDate);
            console.log(`Found ${dayEvents.length} events for ${currentDate.toDateString()}`);
            
            // Add events to day column
            dayEvents.forEach(event => {
                if (!event.allDay) {
                    console.log(`Adding event to week view: ${event.title} on ${currentDate.toDateString()}`);
                    const eventEl = this.createTimeEventElement(event, currentDate);
                    dayEl.appendChild(eventEl);
                    
                    // Debug info
                    console.log(`Event element created for ${event.title}:`, {
                        top: eventEl.style.top,
                        height: eventEl.style.height,
                        width: eventEl.style.width,
                        left: eventEl.style.left,
                        right: eventEl.style.right
                    });
                }
            });
            
            // Add the day column to the body
            bodyEl.appendChild(dayEl);
        }
        
        weekEl.appendChild(bodyEl);
        
        // Add all-day events row if needed
        const allDayEvents = this.getAllDayEventsForRange(startDate, endDate);
        if (allDayEvents.length > 0) {
            const allDayEl = document.createElement('div');
            allDayEl.className = 'calendar-week-all-day';
            
            // TODO: Implement all-day events row
            
            weekEl.insertBefore(allDayEl, bodyEl);
        }
        
        this.contentEl.appendChild(weekEl);
    }

    /**
     * Render the day view
     */
    renderDayView() {
        console.log('Rendering day view');
        console.log(`Current date: ${this.currentDate.toDateString()}`);
        console.log(`Total events: ${this.events.length}`);
        
        // Clear any existing content first to prevent overlapping
        this.contentEl.innerHTML = '';
        
        // Create day container
        const dayViewEl = document.createElement('div');
        dayViewEl.className = 'calendar-day-view';
        
        // Add time column
        const timesEl = document.createElement('div');
        timesEl.className = 'calendar-day-times';
        
        // Add time slots
        // Check if we're on a mobile device by window width
        const isMobileDayView = window.innerWidth <= 576;
        
        for (let hour = 0; hour < 24; hour++) {
            const timeEl = document.createElement('div');
            timeEl.className = 'calendar-day-time';
            
            // On mobile, show only the hour number without AM/PM
            if (isMobileDayView) {
                timeEl.textContent = hour;
            } else {
                timeEl.textContent = this.formatTime(hour, 0);
            }
            
            timesEl.appendChild(timeEl);
        }
        
        dayViewEl.appendChild(timesEl);
        
        // Add day content
        const contentEl = document.createElement('div');
        contentEl.className = 'calendar-day-content';
        
        // Add hour slots
        for (let hour = 0; hour < 24; hour++) {
            const hourEl = document.createElement('div');
            hourEl.className = 'calendar-day-hour';
            hourEl.dataset.hour = hour;
            contentEl.appendChild(hourEl);
            
            // Add half-hour slot
            const halfHourEl = document.createElement('div');
            halfHourEl.className = 'calendar-day-hour half-hour';
            halfHourEl.dataset.hour = hour;
            halfHourEl.dataset.minute = 30;
            contentEl.appendChild(halfHourEl);
        }
        
        // Get events for this day
        // Create a new date object to avoid reference issues
        const dayDate = new Date(this.currentDate.getTime());
        console.log(`Getting events for day view: ${dayDate.toDateString()} (${dayDate.toISOString()})`);
        
        // Force the time to be at the start of the day to ensure consistent date comparison
        dayDate.setHours(0, 0, 0, 0);
        
        // Get events for this specific day
        const dayEvents = this.getEventsForDay(dayDate);
        console.log(`Found ${dayEvents.length} events for day view on ${dayDate.toDateString()}`);
        
        // Add events to day content
        dayEvents.forEach(event => {
            if (!event.allDay) {
                console.log(`Adding event to day view: ${event.title} on ${dayDate.toDateString()}`);
                const eventEl = this.createTimeEventElement(event, dayDate);
                contentEl.appendChild(eventEl);
                
                // Debug info
                console.log(`Event element created for ${event.title} in day view:`, {
                    top: eventEl.style.top,
                    height: eventEl.style.height,
                    width: eventEl.style.width,
                    left: eventEl.style.left,
                    right: eventEl.style.right
                });
            } else {
                console.log(`Skipping all-day event in day view: ${event.title}`);
            }
        });
        
        dayViewEl.appendChild(contentEl);
        
        // Add all-day events if needed
        const allDayEvents = dayEvents.filter(event => event.allDay);
        if (allDayEvents.length > 0) {
            const allDayEl = document.createElement('div');
            allDayEl.className = 'calendar-day-all-day';
            
            // TODO: Implement all-day events section
            
            dayViewEl.insertBefore(allDayEl, contentEl);
        }
        
        this.contentEl.appendChild(dayViewEl);
    }

    /**
     * Render the list view
     */
    renderListView() {
        console.log('Rendering list view');
        
        // Clear any existing content first to prevent overlapping
        this.contentEl.innerHTML = '';
        
        // Create list container
        const listEl = document.createElement('div');
        listEl.className = 'calendar-list-view';
        
        // Create list header
        const headerEl = document.createElement('div');
        headerEl.className = 'calendar-list-header';
        headerEl.innerHTML = `
            <div>Date & Time</div>
            <div>Event</div>
            <div>Location</div>
        `;
        listEl.appendChild(headerEl);
        
        // Get date range for list view
        const { startDate, endDate } = this.getViewDateRange();
        
        // Get events for date range
        const events = this.getEventsForRange(startDate, endDate);
        
        // Sort events by date
        events.sort((a, b) => a.start.getTime() - b.start.getTime());
        
        // Group events by day
        const eventsByDay = {};
        events.forEach(event => {
            const dateKey = this.formatDateKey(event.start);
            if (!eventsByDay[dateKey]) {
                eventsByDay[dateKey] = [];
            }
            eventsByDay[dateKey].push(event);
        });
        
        // Add events to list
        Object.keys(eventsByDay).sort().forEach(dateKey => {
            const dayEvents = eventsByDay[dateKey];
            
            // Create day header
            const dayHeaderEl = document.createElement('div');
            dayHeaderEl.className = 'calendar-list-day-header';
            
            // Parse date from key
            const [year, month, day] = dateKey.split('-').map(Number);
            const date = new Date(year, month - 1, day);
            
            // Check if this date is today
            const isToday = this.isToday(date);
            if (isToday) {
                dayHeaderEl.classList.add('today');
            }
            
            // Format the date with more details
            const formattedDate = this.formatDateFull(date);
            const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const dayOfWeek = weekdays[date.getDay()];
            
            dayHeaderEl.innerHTML = `
                ${isToday ? '<span class="today-badge">Today</span>' : ''}
                <span class="day-name">${dayOfWeek}</span>
                <span class="day-date">${formattedDate}</span>
            `;
            listEl.appendChild(dayHeaderEl);
            
            // Create day events container
            const dayEventsEl = document.createElement('div');
            dayEventsEl.className = 'calendar-list-events';
            
            // Add events
            dayEvents.forEach(event => {
                const eventEl = document.createElement('div');
                eventEl.className = 'calendar-list-event';
                
                // Format time
                let timeText;
                if (event.allDay) {
                    timeText = 'All day';
                } else {
                    timeText = `${this.formatTime(event.start.getHours(), event.start.getMinutes())} - ${this.formatTime(event.end.getHours(), event.end.getMinutes())}`;
                }
                
                // Set background color if provided
                if (event.backgroundColor) {
                    eventEl.style.setProperty('--event-color', event.backgroundColor);
                } else if (event.color) {
                    eventEl.style.setProperty('--event-color', event.color);
                }
                
                // Format the date for display - no locale conversion
                const eventDate = new Date(event.start);
                const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                const formattedDate = weekdays[eventDate.getDay()] + ', ' + months[eventDate.getMonth()] + ' ' + eventDate.getDate();
                
                eventEl.innerHTML = `
                    <div class="calendar-list-event-time">${timeText}</div>
                    <div class="calendar-list-event-title">
                        ${event.title}
                        ${event.description ? `<div class="calendar-list-event-description">${event.description}</div>` : ''}
                    </div>
                    <div class="calendar-list-event-location">${event.location || 'No location'}</div>
                `;
                
                // Add click event
                eventEl.addEventListener('click', () => {
                    if (typeof this.options.onEventClick === 'function') {
                        this.options.onEventClick(event);
                    }
                });
                
                dayEventsEl.appendChild(eventEl);
            });
            
            listEl.appendChild(dayEventsEl);
        });
        
        // If no events, show message
        if (events.length === 0) {
            const noEventsEl = document.createElement('div');
            noEventsEl.className = 'calendar-list-no-events';
            noEventsEl.innerHTML = `
                <div class="no-events-icon">📅</div>
                <h3>No Events to Display</h3>
                <p>There are no events scheduled for this time period.</p>
            `;
            listEl.appendChild(noEventsEl);
        }
        
        this.contentEl.appendChild(listEl);
    }

    /**
     * Create a single-day event element for month view
     * 
     * @param {Object} event - Event object
     * @returns {HTMLElement} Event element
     */
    createSingleDayEventElement(event) {
        const eventEl = document.createElement('div');
        eventEl.className = 'calendar-event';
        
        // Set background color
        if (event.backgroundColor) {
            eventEl.style.backgroundColor = event.backgroundColor;
        } else if (event.color) {
            eventEl.style.backgroundColor = event.color;
        }
        
        // Add all-day class if applicable
        if (event.allDay) {
            eventEl.classList.add('all-day');
        }
        
        // Add event content
        let eventContent = '';
        
        if (!event.allDay) {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            const startTime = this.formatTime(eventStart.getHours(), eventStart.getMinutes());
            const endTime = this.formatTime(eventEnd.getHours(), eventEnd.getMinutes());
            eventContent += `<span class="calendar-event-time">${startTime} - ${endTime}</span>&nbsp;&nbsp;`;
        }
        
        // Show full title - scrolling animation will handle overflow
        eventContent += `<span class="calendar-event-title">${event.title}</span>`;
        
        eventEl.innerHTML = eventContent;
        
        // Add click event
        eventEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof this.options.onEventClick === 'function') {
                this.options.onEventClick(event);
            }
        });
        
        // Add hover handlers
        this.addHoverHandlers(eventEl, event);
        
        return eventEl;
    }

    /**
     * Create an event element for month view (legacy method for compatibility)
     * 
     * @param {Object} event - Event object
     * @param {Date} currentDay - The current day being rendered (optional, for multi-day logic)
     * @returns {HTMLElement} Event element
     */
    createEventElement(event, currentDay = null) {
        const eventEl = document.createElement('div');
        eventEl.className = 'calendar-event';
        
        // Set background color
        if (event.backgroundColor) {
            eventEl.style.backgroundColor = event.backgroundColor;
        } else if (event.color) {
            eventEl.style.backgroundColor = event.color;
        }
        
        // Add all-day class if applicable
        if (event.allDay) {
            eventEl.classList.add('all-day');
        }
        
        // Check if this is a multi-day event and add appropriate classes
        const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        const isMultiDay = this.isMultiDayEvent(eventStart, eventEnd);
        
        if (isMultiDay && currentDay) {
            eventEl.classList.add('multi-day');
            
            // Determine position in multi-day sequence
            const dayStart = new Date(currentDay);
            dayStart.setHours(0, 0, 0, 0);
            const dayEnd = new Date(currentDay);
            dayEnd.setHours(23, 59, 59, 999);
            
            const eventStartDay = new Date(eventStart);
            eventStartDay.setHours(0, 0, 0, 0);
            const eventEndDay = new Date(eventEnd);
            eventEndDay.setHours(0, 0, 0, 0);
            
            if (dayStart.getTime() === eventStartDay.getTime()) {
                eventEl.classList.add('start');
            } else if (dayStart.getTime() === eventEndDay.getTime()) {
                eventEl.classList.add('end');
            } else {
                eventEl.classList.add('middle');
            }
        }
        
        // Add event content with better truncation for multi-day events
        let eventContent = '';
        
        if (!event.allDay && (!isMultiDay || (currentDay && this.isSameDay(eventStart, currentDay)))) {
            // Only show time on the start day for multi-day events
            const eventEnd = new Date(event.end);
            const startTime = this.formatTime(eventStart.getHours(), eventStart.getMinutes());
            const endTime = this.formatTime(eventEnd.getHours(), eventEnd.getMinutes());
            eventContent += `<span class="calendar-event-time">${startTime} - ${endTime}</span>&nbsp;&nbsp;`;
        }
        
        // Show full title - scrolling animation will handle overflow
        eventContent += `<span class="calendar-event-title">${event.title}</span>`;
        
        eventEl.innerHTML = eventContent;
        
        // Add click event
        eventEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof this.options.onEventClick === 'function') {
                this.options.onEventClick(event);
            }
        });
        
        // Add hover handlers
        this.addHoverHandlers(eventEl, event);
        
        return eventEl;
    }
    
    /**
     * Check if an event spans multiple days
     * 
     * @param {Date} startDate - Event start date
     * @param {Date} endDate - Event end date
     * @returns {boolean} True if event spans multiple days
     */
    isMultiDayEvent(startDate, endDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        const end = new Date(endDate);
        end.setHours(0, 0, 0, 0);
        
        return end.getTime() > start.getTime();
    }
    
    /**
     * Check if two dates are the same day (ignoring time)
     * Uses local time components to avoid timezone issues
     * 
     * @param {Date} d1 - First date
     * @param {Date} d2 - Second date
     * @returns {boolean} True if dates are the same day
     */
    isSameDay(d1, d2) {
        const year1 = d1.getFullYear();
        const month1 = d1.getMonth();
        const date1 = d1.getDate();
        
        const year2 = d2.getFullYear();
        const month2 = d2.getMonth();
        const date2 = d2.getDate();
        
        const isSame = year1 === year2 && month1 === month2 && date1 === date2;
        
        if (DEBUG_MODE) {
            console.log(`isSameDay comparison: ${d1.toString()} vs ${d2.toString()} = ${isSame}`);
            console.log(`  Date1: ${year1}-${month1+1}-${date1}, Date2: ${year2}-${month2+1}-${date2}`);
        }
        
        return isSame;
    }
    
    /**
     * Get all events within a date range
     * 
     * @param {Date} startDate - Start date of range
     * @param {Date} endDate - End date of range
     * @returns {Array} Array of events within the range
     */
    getEventsInRange(startDate, endDate) {
        const eventsInRange = [];
        
        // Check each event to see if it falls within or overlaps the range
        this.events.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            // Check if event overlaps with the date range
            if (eventStart <= endDate && eventEnd >= startDate) {
                eventsInRange.push(event);
            }
        });
        
        if (DEBUG_MODE) {
            console.log(`getEventsInRange: Found ${eventsInRange.length} events between ${startDate.toDateString()} and ${endDate.toDateString()}`);
        }
        
        return eventsInRange;
    }
    
    /**
     * Render multi-day events using overlay grid approach
     * 
     * @param {HTMLElement} monthEl - The month container element
     * @param {Array} multiDayEvents - Array of multi-day events
     * @param {Date} startDate - Start date of the calendar view
     * @param {Date} endDate - End date of the calendar view
     */
    renderMultiDayEvents(monthEl, multiDayEvents, startDate, endDate) {
        if (multiDayEvents.length === 0) return;
        
        console.log(`\n=== RENDERING ${multiDayEvents.length} MULTI-DAY EVENTS WITH OVERLAY GRID ===`);
        
        // Create or get the multi-day events overlay container
        let overlayContainer = monthEl.querySelector('.calendar-multi-day-events');
        if (!overlayContainer) {
            overlayContainer = document.createElement('div');
            overlayContainer.className = 'calendar-multi-day-events';
            monthEl.appendChild(overlayContainer);
        }
        
        // Clear existing multi-day events
        overlayContainer.innerHTML = '';
        
        multiDayEvents.forEach((event, eventIndex) => {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            console.log(`\n--- Processing event: ${event.title} ---`);
            console.log(`Event start: ${eventStart.toDateString()}`);
            console.log(`Event end: ${eventEnd.toDateString()}`);
            
            // Find start and end positions in the grid
            const dayCells = monthEl.querySelectorAll('.calendar-day');
            let startIndex = -1;
            let endIndex = -1;
            
            const eventStartKey = eventStart.toISOString().split('T')[0];
            const eventEndKey = eventEnd.toISOString().split('T')[0];
            
            dayCells.forEach((cell, index) => {
                const cellDate = cell.getAttribute('data-date');
                if (cellDate === eventStartKey) {
                    startIndex = index;
                    console.log(`✓ Found START date match: Cell ${index} has date ${cellDate}`);
                }
                if (cellDate === eventEndKey) {
                    endIndex = index;
                    console.log(`✓ Found END date match: Cell ${index} has date ${cellDate}`);
                }
            });
            
            console.log(`Looking for event dates: START=${eventStartKey}, END=${eventEndKey}`);
            
            console.log(`Start index: ${startIndex}, End index: ${endIndex}`);
            
            if (startIndex >= 0 && endIndex >= 0) {
                // Calculate grid positions (1-based for CSS Grid)
                const startCol = (startIndex % 7) + 1;
                const endCol = (endIndex % 7) + 1;
                // DEBUGGING: Try different row calculations to fix "one row above" issue
                const originalRow = Math.floor(startIndex / 7) + 2;
                const adjustedRow = Math.floor(startIndex / 7) + 3; // Try adding one more
                
                // Use the adjusted calculation
                const startRow = adjustedRow;
                const endRow = Math.floor(endIndex / 7) + 3;
                
                console.log(`ROW CALCULATION DEBUG:`);
                console.log(`  startIndex: ${startIndex}, Math.floor(${startIndex} / 7) = ${Math.floor(startIndex / 7)}`);
                console.log(`  Original calculation: ${originalRow} (was appearing one row above)`);
                console.log(`  Adjusted calculation: ${adjustedRow} (trying to fix position)`);
                console.log(`  Using startRow: ${startRow}, endRow: ${endRow}`);
                
                console.log(`Grid positions - Start: Row ${startRow}, Col ${startCol} | End: Row ${endRow}, Col ${endCol}`);
                
                if (startRow === endRow) {
                    // Single row spanning
                    this.createOverlaySpanningEvent(event, startRow, startCol, endCol, eventIndex, overlayContainer);
                } else {
                    // Multi-row spanning - create segments
                    // First row
                    this.createOverlaySpanningEvent(event, startRow, startCol, 7, eventIndex, overlayContainer, 'start');
                    
                    // Middle rows
                    for (let row = startRow + 1; row < endRow; row++) {
                        this.createOverlaySpanningEvent(event, row, 1, 7, eventIndex, overlayContainer, 'middle');
                    }
                    
                    // Last row
                    if (endRow > startRow) {
                        this.createOverlaySpanningEvent(event, endRow, 1, endCol, eventIndex, overlayContainer, 'end');
                    }
                }
            } else {
                console.log(`Could not find grid positions for event ${event.title}`);
            }
        });
    }
    
    /**
     * Create a spanning event in the overlay grid container
     * 
     * @param {Object} event - Event object
     * @param {number} row - Grid row position
     * @param {number} startCol - Starting column
     * @param {number} endCol - Ending column
     * @param {number} eventIndex - Event index for layering
     * @param {HTMLElement} overlayContainer - Overlay container element
     * @param {string} segment - Segment type: 'start', 'middle', 'end', or null
     */
    createOverlaySpanningEvent(event, row, startCol, endCol, eventIndex, overlayContainer, segment = null) {
        const eventEl = document.createElement('div');
        eventEl.className = 'calendar-event calendar-overlay-spanning-event';
        
        // Set background color
        if (event.backgroundColor) {
            eventEl.style.backgroundColor = event.backgroundColor;
        } else if (event.color) {
            eventEl.style.backgroundColor = event.color;
        } else {
            eventEl.style.backgroundColor = '#3788d8'; // Default color
        }
        
        // CSS Grid positioning within the overlay
        eventEl.style.gridRow = row;
        eventEl.style.gridColumn = `${startCol} / ${endCol + 1}`;
        eventEl.style.zIndex = 10 + eventIndex;
        
        // Styling for the spanning event - let CSS handle most styling
        eventEl.style.margin = '25px 2px 2px 2px'; // Top margin to clear day numbers
        eventEl.style.cursor = 'pointer';
        eventEl.style.pointerEvents = 'auto'; // Enable clicks on this element
        
        // Add segment styling - use CSS classes instead of inline styles
        if (segment === 'start') {
            eventEl.classList.add('segment-start');
            eventEl.style.marginRight = '0';
        } else if (segment === 'middle') {
            eventEl.classList.add('segment-middle');
            eventEl.style.marginLeft = '0';
            eventEl.style.marginRight = '0';
        } else if (segment === 'end') {
            eventEl.classList.add('segment-end');
            eventEl.style.marginLeft = '0';
        }
        
        // Add event content
        let eventContent = '';
        
        // Show time only on start segment
        if (!event.allDay && (!segment || segment === 'start')) {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            eventContent += `<span class="calendar-event-time">${this.formatTimeRange(eventStart, eventEnd)}</span> `;
        }
        
        // Show title
        const spanWidth = endCol - startCol + 1;
        const maxTitleLength = Math.max(5, spanWidth * 10); // Roughly 10 chars per day
        const truncatedTitle = event.title.length > maxTitleLength 
            ? event.title.substring(0, maxTitleLength) + '...' 
            : event.title;
        
        eventContent += `<span class="calendar-event-title">${event.title}</span>`;
        
        eventEl.innerHTML = eventContent;
        
        // Add click event
        eventEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof this.options.onEventClick === 'function') {
                this.options.onEventClick(event);
            }
        });
        
        overlayContainer.appendChild(eventEl);
        
        console.log(`Created overlay spanning event "${event.title}" at row ${row}, columns ${startCol}-${endCol}`);
    }

    /**
     * Create a CSS Grid spanning event element (DEPRECATED - caused layout issues)
     * 
     * @param {Object} event - Event object
     * @param {number} row - Grid row position
     * @param {number} startCol - Starting column
     * @param {number} endCol - Ending column
     * @param {number} eventIndex - Event index for layering
     * @param {HTMLElement} monthEl - Month container element
     * @param {string} segment - Segment type: 'start', 'middle', 'end', or null
     */
    createGridSpanningEvent(event, row, startCol, endCol, eventIndex, monthEl, segment = null) {
        const eventEl = document.createElement('div');
        eventEl.className = 'calendar-event calendar-grid-spanning-event';
        
        // Set background color
        if (event.backgroundColor) {
            eventEl.style.backgroundColor = event.backgroundColor;
        } else if (event.color) {
            eventEl.style.backgroundColor = event.color;
        } else {
            eventEl.style.backgroundColor = '#3788d8'; // Default color
        }
        
        // CSS Grid positioning
        eventEl.style.gridRow = row;
        eventEl.style.gridColumn = `${startCol} / ${endCol + 1}`;
        eventEl.style.zIndex = 10 + eventIndex;
        
        // Styling
        eventEl.style.margin = '2px 1px';
        eventEl.style.padding = '2px 6px';
        eventEl.style.borderRadius = '3px';
        eventEl.style.fontSize = '0.8rem';
        eventEl.style.color = 'white';
        eventEl.style.overflow = 'hidden';
        eventEl.style.whiteSpace = 'nowrap';
        eventEl.style.textOverflow = 'ellipsis';
        eventEl.style.display = 'flex';
        eventEl.style.alignItems = 'center';
        eventEl.style.height = '20px';
        eventEl.style.boxShadow = '0 1px 2px rgba(0,0,0,0.1)';
        eventEl.style.cursor = 'pointer';
        
        // Add segment styling - use CSS classes instead of inline styles
        if (segment === 'start') {
            eventEl.classList.add('segment-start');
            eventEl.style.marginRight = '0';
        } else if (segment === 'middle') {
            eventEl.classList.add('segment-middle');
            eventEl.style.marginLeft = '0';
            eventEl.style.marginRight = '0';
        } else if (segment === 'end') {
            eventEl.classList.add('segment-end');
            eventEl.style.marginLeft = '0';
        }
        
        // Add event content
        let eventContent = '';
        
        // Show time only on start segment
        if (!event.allDay && (!segment || segment === 'start')) {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            eventContent += `<span class="calendar-event-time">${this.formatTimeRange(eventStart, eventEnd)}</span> `;
        }
        
        // Show title
        const spanWidth = endCol - startCol + 1;
        const maxTitleLength = Math.max(5, spanWidth * 10); // Roughly 10 chars per day
        const truncatedTitle = event.title.length > maxTitleLength 
            ? event.title.substring(0, maxTitleLength) + '...' 
            : event.title;
        
        eventContent += `<span class="calendar-event-title">${event.title}</span>`;
        
        eventEl.innerHTML = eventContent;
        
        // Add click event
        eventEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof this.options.onEventClick === 'function') {
                this.options.onEventClick(event);
            }
        });
        
        monthEl.appendChild(eventEl);
        
        console.log(`Created CSS Grid spanning event "${event.title}" at row ${row}, columns ${startCol}-${endCol}`);
    }

    /**
     * Create a spanning bar element that visually spans between two cells (DEPRECATED)
     * 
     * @param {Object} event - Event object
     * @param {HTMLElement} startCell - Starting day cell
     * @param {HTMLElement} endCell - Ending day cell
     * @param {HTMLElement} container - Container for the spanning element
     * @param {number} eventIndex - Event index for layering
     * @param {string} segment - Segment type: 'start', 'middle', 'end', or null
     */
    createSpanningBar(event, startCell, endCell, container, eventIndex, segment = null) {
        // Null checks
        if (!startCell || !endCell || !container || !container.parentElement) {
            console.log(`✗ Cannot create spanning bar for ${event.title}: missing elements`);
            console.log(`  startCell: ${!!startCell}, endCell: ${!!endCell}, container: ${!!container}, parent: ${!!container?.parentElement}`);
            return;
        }
        
        const spanningEl = document.createElement('div');
        spanningEl.className = 'calendar-spanning-bar';
        
        // Set background color
        if (event.backgroundColor) {
            spanningEl.style.backgroundColor = event.backgroundColor;
        } else if (event.color) {
            spanningEl.style.backgroundColor = event.color;
        } else {
            spanningEl.style.backgroundColor = '#3788d8'; // Default color
        }
        
        // Get positions of start and end cells
        const startRect = startCell.getBoundingClientRect();
        const endRect = endCell.getBoundingClientRect();
        const containerRect = container.parentElement.getBoundingClientRect();
        
        // Calculate position and size
        const left = startRect.left - containerRect.left;
        const top = startRect.top - containerRect.top + 30; // Offset for day header
        const width = endRect.right - startRect.left;
        const height = 20;
        
        // Position the spanning element
        spanningEl.style.position = 'absolute';
        spanningEl.style.left = left + 'px';
        spanningEl.style.top = (top + (eventIndex * 22)) + 'px'; // Stack events vertically
        spanningEl.style.width = width + 'px';
        spanningEl.style.height = height + 'px';
        spanningEl.style.borderRadius = '3px';
        spanningEl.style.display = 'flex';
        spanningEl.style.alignItems = 'center';
        spanningEl.style.padding = '2px 6px';
        spanningEl.style.fontSize = '0.8rem';
        spanningEl.style.color = 'white';
        spanningEl.style.overflow = 'hidden';
        spanningEl.style.whiteSpace = 'nowrap';
        spanningEl.style.textOverflow = 'ellipsis';
        spanningEl.style.pointerEvents = 'auto';
        spanningEl.style.zIndex = 10 + eventIndex;
        spanningEl.style.boxShadow = '0 1px 2px rgba(0,0,0,0.1)';
        
        // Add segment styling
        if (segment === 'start') {
            spanningEl.style.borderTopRightRadius = '0';
            spanningEl.style.borderBottomRightRadius = '0';
        } else if (segment === 'middle') {
            spanningEl.style.borderRadius = '0';
        } else if (segment === 'end') {
            spanningEl.style.borderTopLeftRadius = '0';
            spanningEl.style.borderBottomLeftRadius = '0';
        }
        
        // Add event content
        let eventContent = '';
        
        // Show time only on start segment
        if (!event.allDay && (!segment || segment === 'start')) {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            eventContent += `<span class="calendar-event-time">${this.formatTimeRange(eventStart, eventEnd)}</span> `;
        }
        
        // Show title
        const availableWidth = width - 20; // Account for padding
        const maxTitleLength = Math.max(5, Math.floor(availableWidth / 8)); // Roughly 8px per character
        const truncatedTitle = event.title.length > maxTitleLength 
            ? event.title.substring(0, maxTitleLength) + '...' 
            : event.title;
        
        eventContent += `<span class="calendar-event-title">${event.title}</span>`;
        
        spanningEl.innerHTML = eventContent;
        
        // Add click event
        spanningEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof this.options.onEventClick === 'function') {
                this.options.onEventClick(event);
            }
        });
        
        container.appendChild(spanningEl);
        
        console.log(`Created spanning bar for "${event.title}" at position: left=${left}px, top=${top + (eventIndex * 22)}px, width=${width}px`);
    }
    
    /**
     * Create a spanning event element for multi-day events
     * 
     * @param {Object} event - Event object
     * @param {number} row - Grid row position
     * @param {number} startCol - Starting column
     * @param {number} endCol - Ending column
     * @param {number} eventIndex - Event index for layering
     * @param {HTMLElement} container - Container element
     */
    createSpanningEventElement(event, row, startCol, endCol, eventIndex, container) {
        // Create container for spanning event if it doesn't exist
        let spanningContainer = container.querySelector('.calendar-multi-day-events');
        if (!spanningContainer) {
            spanningContainer = document.createElement('div');
            spanningContainer.className = 'calendar-multi-day-events';
            container.appendChild(spanningContainer);
        }
        
        const eventEl = document.createElement('div');
        eventEl.className = 'calendar-event calendar-spanning-event';
        
        // Set background color
        if (event.backgroundColor) {
            eventEl.style.backgroundColor = event.backgroundColor;
        } else if (event.color) {
            eventEl.style.backgroundColor = event.color;
        } else {
            eventEl.style.backgroundColor = '#3788d8'; // Default color
        }
        
        // Position the element using CSS Grid
        eventEl.style.gridRow = row;
        eventEl.style.gridColumn = `${startCol} / ${endCol + 1}`;
        eventEl.style.zIndex = 10 + eventIndex; // Layer events
        
        console.log(`Positioned event "${event.title}" at grid-row: ${row}, grid-column: ${startCol} / ${endCol + 1}`);
        
        // Add event content
        let eventContent = '';
        
        // Show time range if not all-day
        if (!event.allDay) {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            const startTime = this.formatTime(eventStart.getHours(), eventStart.getMinutes());
            const endTime = this.formatTime(eventEnd.getHours(), eventEnd.getMinutes());
            eventContent += `<span class="calendar-event-time">${startTime} - ${endTime}</span>&nbsp;&nbsp;`;
        }
        
        // Show full title - scrolling animation will handle overflow
        eventContent += `<span class="calendar-event-title">${event.title}</span>`;
        
        eventEl.innerHTML = eventContent;
        
        // Add click event
        eventEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof this.options.onEventClick === 'function') {
                this.options.onEventClick(event);
            }
        });
        
        // Add hover handlers
        this.addHoverHandlers(eventEl, event);
        
        spanningContainer.appendChild(eventEl);
    }

    /**
     * Create a time-based event element for week and day views
     * 
     * @param {Object} event - Event object
     * @param {Date} day - Day date
     * @returns {HTMLElement} Event element
     */
    createTimeEventElement(event, day) {
        console.log(`Creating time event element for: ${event.title} on ${day.toDateString()}`);
        
        const eventEl = document.createElement('div');
        eventEl.className = 'calendar-time-event';
        
        // Set background color with fallbacks
        if (event.backgroundColor) {
            eventEl.style.backgroundColor = event.backgroundColor;
        } else if (event.color) {
            eventEl.style.backgroundColor = event.color;
        } else {
            // Default color if none specified
            eventEl.style.backgroundColor = '#3788d8';
        }
        
        // Ensure we're working with Date objects
        const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        
        // For multi-day events, adjust start and end times for this specific day
        const dayStart = new Date(day);
        dayStart.setHours(0, 0, 0, 0);
        
        const dayEnd = new Date(day);
        dayEnd.setHours(23, 59, 59, 999);
        
        // Adjust start time if event starts before this day
        const displayStart = eventStart < dayStart ? dayStart : eventStart;
        
        // Adjust end time if event ends after this day
        const displayEnd = eventEnd > dayEnd ? dayEnd : eventEnd;
        
        // Calculate position and height based on hours
        // Each hour is 50px tall (from CSS)
        const hourHeight = 50;
        
        // Calculate start position in minutes since start of day
        const startMinutes = displayStart.getHours() * 60 + displayStart.getMinutes();
        
        // Calculate end position in minutes since start of day
        const endMinutes = displayEnd.getHours() * 60 + displayEnd.getMinutes();
        
        // Ensure minimum duration for visibility (30 minutes)
        const duration = Math.max(30, endMinutes - startMinutes);
        
        // Debug info
        console.log(`Event time calculations for ${event.title}:`, {
            eventStart: eventStart.toLocaleString(),
            eventEnd: eventEnd.toLocaleString(),
            displayStart: displayStart.toLocaleString(),
            displayEnd: displayEnd.toLocaleString(),
            startMinutes,
            endMinutes,
            duration
        });
        
        // Calculate top position (convert minutes to pixels)
        const top = (startMinutes / 60) * hourHeight;
        
        // Calculate height (convert duration to pixels)
        const height = (duration / 60) * hourHeight;
        
        // Apply position and size
        eventEl.style.top = `${top}px`;
        eventEl.style.height = `${height}px`;
        eventEl.style.left = '2px';  // Add a small margin from the left
        eventEl.style.right = '2px'; // Add a small margin from the right
        eventEl.style.width = 'calc(100% - 4px)'; // Ensure width is set correctly
        
        // Add event content
        let timeDisplay;
        if (eventStart < dayStart && eventEnd > dayEnd) {
            // Event spans multiple days including this entire day
            timeDisplay = 'All day';
        } else if (eventStart < dayStart) {
            // Event started before this day
            timeDisplay = `Until ${this.formatTime(eventEnd.getHours(), eventEnd.getMinutes())}`;
        } else if (eventEnd > dayEnd) {
            // Event continues after this day
            timeDisplay = `From ${this.formatTime(eventStart.getHours(), eventStart.getMinutes())}`;
        } else {
            // Event starts and ends on this day
            timeDisplay = `${this.formatTime(eventStart.getHours(), eventStart.getMinutes())} - ${this.formatTime(eventEnd.getHours(), eventEnd.getMinutes())}`;
        }
        
        eventEl.innerHTML = `
            <div class="calendar-event-title">${event.title}</div>
            <div class="calendar-event-time">${timeDisplay}</div>
        `;
        
        // Add click event
        eventEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof this.options.onEventClick === 'function') {
                this.options.onEventClick(event);
            }
        });
        
        // Add hover handlers
        this.addHoverHandlers(eventEl, event);
        
        return eventEl;
    }

    /**
     * Trigger event table update
     * 
     * @param {Array} events - Array of event objects
     */
    triggerEventTableUpdate(events) {
        // Dispatch custom event for event table update
        const eventTableEvent = new CustomEvent('calendarEventsLoaded', {
            detail: { events: events }
        });
        window.dispatchEvent(eventTableEvent);
    }

    /**
     * Add hover handlers to event element
     * 
     * @param {HTMLElement} eventEl - Event element
     * @param {Object} event - Event object
     */
    addHoverHandlers(eventEl, event) {
        if (typeof window.showEventPopup === 'function' && typeof window.hideEventPopup === 'function') {
            eventEl.addEventListener('mouseenter', () => {
                window.showEventPopup(event, eventEl);
            });
            
            eventEl.addEventListener('mouseleave', () => {
                window.hideEventPopup();
            });
        }
    }

    /**
     * Show more events for a day
     * 
     * @param {Array} events - Array of event objects
     * @param {Date} date - Day date
     */
    showMoreEvents(events, date) {
        // Create modal
        const modalEl = document.createElement('div');
        modalEl.className = 'calendar-event-modal';
        
        // Create modal content
        const contentEl = document.createElement('div');
        contentEl.className = 'calendar-event-modal-content';
        
        // Create modal header
        const headerEl = document.createElement('div');
        headerEl.className = 'calendar-event-modal-header';
        
        // Create modal title
        const titleEl = document.createElement('h3');
        titleEl.className = 'calendar-event-modal-title';
        titleEl.textContent = this.formatDateFull(date);
        headerEl.appendChild(titleEl);
        
        // Create close button
        const closeBtn = document.createElement('button');
        closeBtn.className = 'calendar-event-modal-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(modalEl);
        });
        headerEl.appendChild(closeBtn);
        
        contentEl.appendChild(headerEl);
        
        // Create modal body
        const bodyEl = document.createElement('div');
        bodyEl.className = 'calendar-event-modal-body';
        
        // Add events
        events.forEach(event => {
            const eventEl = document.createElement('div');
            eventEl.className = 'calendar-list-event';
            
            // Format time
            let timeText;
            if (event.allDay) {
                timeText = 'All day';
            } else {
                timeText = `${this.formatTime(event.start.getHours(), event.start.getMinutes())} - ${this.formatTime(event.end.getHours(), event.end.getMinutes())}`;
            }
            
            eventEl.innerHTML = `
                <div class="calendar-list-event-time">${timeText}</div>
                <div class="calendar-list-event-title">${event.title}</div>
                <div class="calendar-list-event-location">${event.location || ''}</div>
            `;
            
            // Add click event
            eventEl.addEventListener('click', () => {
                document.body.removeChild(modalEl);
                if (typeof this.options.onEventClick === 'function') {
                    this.options.onEventClick(event);
                }
            });
            
            bodyEl.appendChild(eventEl);
        });
        
        contentEl.appendChild(bodyEl);
        
        // Create modal footer
        const footerEl = document.createElement('div');
        footerEl.className = 'calendar-event-modal-footer';
        
        // Create close button
        const closeButton = document.createElement('button');
        closeButton.className = 'btn btn-secondary';
        closeButton.textContent = 'Close';
        closeButton.addEventListener('click', () => {
            document.body.removeChild(modalEl);
        });
        footerEl.appendChild(closeButton);
        
        contentEl.appendChild(footerEl);
        
        modalEl.appendChild(contentEl);
        
        // Add modal to body
        document.body.appendChild(modalEl);
        
        // Show modal
        setTimeout(() => {
            modalEl.classList.add('show');
        }, 10);
        
        // Close modal when clicking outside
        modalEl.addEventListener('click', (e) => {
            if (e.target === modalEl) {
                document.body.removeChild(modalEl);
            }
        });
    }

    /**
     * Get events for a specific day
     * 
     * @param {Date} date - Day date
     * @returns {Array} Array of event objects
     */
    getEventsForDay(date) {
        // Create a simple date string for comparison (YYYY-MM-DD)
        const dateStr = date.toISOString().split('T')[0];
        console.log(`Looking for events on date: ${dateStr}`);
        
        // Create date objects for start and end of the day
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);
        
        // Convert to timestamps for comparison
        const startOfDayTime = startOfDay.getTime();
        const endOfDayTime = endOfDay.getTime();
        
        // Simple function to check if a date is the same day (ignoring time)
        // Uses local time components to avoid timezone issues
        const isSameDay = (d1, d2) => {
            const year1 = d1.getFullYear();
            const month1 = d1.getMonth();
            const date1 = d1.getDate();
            
            const year2 = d2.getFullYear();
            const month2 = d2.getMonth();
            const date2 = d2.getDate();
            
            const isSame = year1 === year2 && month1 === month2 && date1 === date2;
            
            if (DEBUG_MODE) {
                console.log(`isSameDay comparison: ${d1.toString()} vs ${d2.toString()} = ${isSame}`);
                console.log(`  Date1: ${year1}-${month1+1}-${date1}, Date2: ${year2}-${month2+1}-${date2}`);
            }
            
            return isSame;
        };
        
        // Find all events that occur on this day
        return this.events.filter(event => {
            // Make sure we have valid date objects
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            if (DEBUG_MODE) {
                console.log(`=== CHECKING EVENT FOR DATE ${date.toDateString()} ===`);
                console.log(`Event: ${event.title}`);
                console.log(`Event Start: Local=${eventStart.toString()}, UTC=${eventStart.toISOString()}`);
                console.log(`Event End: Local=${eventEnd.toString()}, UTC=${eventEnd.toISOString()}`);
                console.log(`Calendar Date: ${date.toString()}`);
                console.log(`Event Start Date Components: ${eventStart.getFullYear()}-${eventStart.getMonth()+1}-${eventStart.getDate()}`);
                console.log(`Calendar Date Components: ${date.getFullYear()}-${date.getMonth()+1}-${date.getDate()}`);
            }
            
            // Simple check: does the event start on this day?
            if (isSameDay(eventStart, date)) {
                console.log(`Event ${event.title} starts on this day`);
                if (DEBUG_MODE) {
                    console.log(`✓ Event ${event.title} FOUND - starts on ${date.toDateString()}`);
                }
                return true;
            }
            
            // Simple check: does the event end on this day?
            if (isSameDay(eventEnd, date)) {
                console.log(`Event ${event.title} ends on this day`);
                if (DEBUG_MODE) {
                    console.log(`✓ Event ${event.title} FOUND - ends on ${date.toDateString()}`);
                }
                return true;
            }
            
            // Check if this day is between the start and end dates
            const eventStartTime = eventStart.getTime();
            const eventEndTime = eventEnd.getTime();
            
            // If event spans multiple days and this day is in between
            if (eventStartTime < startOfDayTime && eventEndTime > endOfDayTime) {
                console.log(`Event ${event.title} spans across this day`);
                return true;
            }
            
            // Check if event overlaps with this day
            if (eventStartTime <= endOfDayTime && eventEndTime >= startOfDayTime) {
                console.log(`Event ${event.title} overlaps with this day`);
                return true;
            }
            
            // For all-day events, do an additional check
            if (event.allDay) {
                // Get the date string for the event start (YYYY-MM-DD)
                const eventDateStr = eventStart.toISOString().split('T')[0];
                if (eventDateStr === dateStr) {
                    console.log(`All-day event ${event.title} is on this day`);
                    return true;
                }
            }
            
            return false;
        });
    }

    /**
     * Get events for a date range
     * 
     * @param {Date} startDate - Start date
     * @param {Date} endDate - End date
     * @returns {Array} Array of event objects
     */
    getEventsForRange(startDate, endDate) {
        console.log(`Getting events for range: ${startDate.toDateString()} to ${endDate.toDateString()}`);
        console.log(`Total events to check: ${this.events.length}`);
        
        const matchingEvents = this.events.filter(event => {
            // Make sure we have valid date objects
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            console.log(`Checking event: ${event.title}, Start: ${eventStart.toISOString()}, End: ${eventEnd.toISOString()}`);
            
            // An event is in the range if:
            // 1. It starts during the range
            // 2. OR it ends during the range
            // 3. OR it spans across the entire range
            
            // Convert to timestamps for more reliable comparison
            const startDateTime = startDate.getTime();
            const endDateTime = endDate.getTime();
            const eventStartTime = eventStart.getTime();
            const eventEndTime = eventEnd.getTime();
            
            const startsInRange = eventStartTime >= startDateTime && eventStartTime <= endDateTime;
            const endsInRange = eventEndTime >= startDateTime && eventEndTime <= endDateTime;
            const spansRange = eventStartTime <= startDateTime && eventEndTime >= endDateTime;
            
            // Also check if the event falls on any day in the range by date comparison
            let dateInRange = false;
            
            // Check each day in the range
            const checkDate = new Date(startDate);
            while (checkDate <= endDate) {
                // Check if event start date matches this day (ignoring time)
                if (
                    eventStart.getDate() === checkDate.getDate() && 
                    eventStart.getMonth() === checkDate.getMonth() && 
                    eventStart.getFullYear() === checkDate.getFullYear()
                ) {
                    dateInRange = true;
                    break;
                }
                
                // Move to next day
                checkDate.setDate(checkDate.getDate() + 1);
            }
            
            const isInRange = startsInRange || endsInRange || spansRange || dateInRange;
            console.log(`Event ${event.title} in range: ${isInRange} (starts: ${startsInRange}, ends: ${endsInRange}, spans: ${spansRange}, dateInRange: ${dateInRange})`);
            
            return isInRange;
        });
        
        console.log(`Found ${matchingEvents.length} events in range`);
        return matchingEvents;
    }

    /**
     * Get all-day events for a date range
     * 
     * @param {Date} startDate - Start date
     * @param {Date} endDate - End date
     * @returns {Array} Array of event objects
     */
    getAllDayEventsForRange(startDate, endDate) {
        return this.events.filter(event => {
            if (!event.allDay) return false;
            
            // Make sure we have valid date objects
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            // An all-day event is in the range if:
            // 1. It starts during the range
            // 2. OR it ends during the range
            // 3. OR it spans across the entire range
            const startsInRange = eventStart >= startDate && eventStart <= endDate;
            const endsInRange = eventEnd >= startDate && eventEnd <= endDate;
            const spansRange = eventStart <= startDate && eventEnd >= endDate;
            
            return startsInRange || endsInRange || spansRange;
        });
    }

    /**
     * Navigate to previous period
     */
    navigatePrevious() {
        console.log(`Navigating previous in ${this.currentView} view`);
        console.log(`Current date before: ${this.currentDate.toDateString()}`);
        
        // Create a new date object to avoid reference issues
        const newDate = new Date(this.currentDate);
        
        switch (this.currentView) {
            case 'month':
                newDate.setMonth(newDate.getMonth() - 1);
                break;
                
            case 'week':
                newDate.setDate(newDate.getDate() - 7);
                break;
                
            case 'day':
                newDate.setDate(newDate.getDate() - 1);
                break;
                
            case 'list':
                newDate.setDate(newDate.getDate() - 30);
                break;
        }
        
        // Update the current date
        this.currentDate = newDate;
        console.log(`Current date after: ${this.currentDate.toDateString()}`);
        
        // Clear any existing content to prevent overlapping
        this.contentEl.innerHTML = '';
        
        // Load events and render the view
        this.loadEvents();
    }

    /**
     * Navigate to next period
     */
    navigateNext() {
        console.log(`Navigating next in ${this.currentView} view`);
        console.log(`Current date before: ${this.currentDate.toDateString()}`);
        
        // Create a new date object to avoid reference issues
        const newDate = new Date(this.currentDate);
        
        switch (this.currentView) {
            case 'month':
                newDate.setMonth(newDate.getMonth() + 1);
                break;
                
            case 'week':
                newDate.setDate(newDate.getDate() + 7);
                break;
                
            case 'day':
                newDate.setDate(newDate.getDate() + 1);
                break;
                
            case 'list':
                newDate.setDate(newDate.getDate() + 30);
                break;
        }
        
        // Update the current date
        this.currentDate = newDate;
        console.log(`Current date after: ${this.currentDate.toDateString()}`);
        
        // Clear any existing content to prevent overlapping
        this.contentEl.innerHTML = '';
        
        // Load events and render the view
        this.loadEvents();
    }

    /**
     * Navigate to today
     */
    navigateToday() {
        console.log('Navigating to today');
        this.currentDate = new Date();
        console.log(`Current date set to: ${this.currentDate.toDateString()}`);
        
        // Clear any existing content to prevent overlapping
        this.contentEl.innerHTML = '';
        
        // Load events and render the view
        this.loadEvents();
    }

    /**
     * Change the current view
     * 
     * @param {string} view - View name ('month', 'week', 'day', 'list')
     */
    changeView(view) {
        console.log(`Changing view from ${this.currentView} to ${view}`);
        
        if (['month', 'week', 'day', 'list'].includes(view) && view !== this.currentView) {
            this.currentView = view;
            console.log(`Current date: ${this.currentDate.toDateString()}`);
            
            if (typeof this.options.onViewChange === 'function') {
                this.options.onViewChange(view);
            }
            
            // Clear any existing content to prevent overlapping
            this.contentEl.innerHTML = '';
            
            // Load events and render the view
            this.loadEvents();
        } else {
            console.log(`View not changed: ${view} is not valid or is already current view`);
        }
    }

    /**
     * Format a date as month and year
     * 
     * @param {Date} date - Date to format
     * @returns {string} Formatted date
     */
    formatMonthYear(date) {
        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        return `${months[date.getMonth()]} ${date.getFullYear()}`;
    }

    /**
     * Format a date (day and month)
     * 
     * @param {Date} date - Date to format
     * @returns {string} Formatted date
     */
    formatDate(date) {
        const months = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ];
        
        return `${months[date.getMonth()]} ${date.getDate()}`;
    }

    /**
     * Format a date (day, month, and year)
     * 
     * @param {Date} date - Date to format
     * @returns {string} Formatted date
     */
    formatDateFull(date) {
        const days = [
            'Sunday', 'Monday', 'Tuesday', 'Wednesday',
            'Thursday', 'Friday', 'Saturday'
        ];
        
        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        return `${days[date.getDay()]}, ${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
    }

    /**
     * Format a date as a key (YYYY-MM-DD)
     * 
     * @param {Date} date - Date to format
     * @returns {string} Formatted date key
     */
    formatDateKey(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    }

    /**
     * Format time
     * 
     * @param {number} hours - Hours (0-23)
     * @param {number} minutes - Minutes (0-59)
     * @returns {string} Formatted time
     */
    formatTime(hours, minutes) {
        if (this.options.timeFormat === '12') {
            const period = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours % 12 || 12;
            return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
        } else {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }
    }

    /**
     * Format time range for events
     * 
     * @param {Date} startDate - Event start date
     * @param {Date} endDate - Event end date
     * @returns {string} Formatted time range
     */
    formatTimeRange(startDate, endDate) {
        const startTime = this.formatTime(startDate.getHours(), startDate.getMinutes());
        const endTime = this.formatTime(endDate.getHours(), endDate.getMinutes());
        return `${startTime} - ${endTime} `;  // Added space at the end
    }

    /**
     * Get weekday names
     * 
     * @param {boolean} short - Whether to use short names
     * @returns {Array} Array of weekday names
     */
    getWeekdayNames(short = false, ultraShort = false) {
        const longNames = [
            'Sunday', 'Monday', 'Tuesday', 'Wednesday',
            'Thursday', 'Friday', 'Saturday'
        ];
        
        const shortNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        
        // Ultra-short names for mobile (just first letter)
        const ultraShortNames = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
        
        let names;
        if (ultraShort) {
            names = ultraShortNames;
        } else if (short) {
            names = shortNames;
        } else {
            names = longNames;
        }
        
        // Adjust for first day of week
        const adjustedNames = [];
        for (let i = this.options.firstDayOfWeek; i < this.options.firstDayOfWeek + 7; i++) {
            adjustedNames.push(names[i % 7]);
        }
        
        return adjustedNames;
    }

    /**
     * Check if a date is today
     *
     * @param {Date} date - Date to check
     * @returns {boolean} True if date is today
     */
    isToday(date) {
        const today = new Date();

        // Use timezone helper if available for consistent comparison
        if (window.TimezoneHelper && window.TimezoneHelper.isSameDay) {
            return window.TimezoneHelper.isSameDay(date, today);
        }

        // Fallback: Compare date components in local timezone
        return (
            date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear()
        );
    }

    /**
     * Check if a date is a weekend
     * 
     * @param {Date} date - Date to check
     * @returns {boolean} True if date is a weekend
     */
    isWeekend(date) {
        const day = date.getDay();
        return day === 0 || day === 6;
    }
}