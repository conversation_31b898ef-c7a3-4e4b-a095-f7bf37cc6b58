<?php
/**
 * Test Final Notification Fixes
 * 
 * Tests the final fixes:
 * 1. No count on mobile nav bar (just yellow bell)
 * 2. Desktop count as bright yellow number (better positioned)
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🔔 Test Final Notification Fixes</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test user
    $userId = 3;
    
    echo "<h2>🎯 Final Fixes Applied</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile Changes:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Removed mobile badge completely</strong> - No more red count circle</li>";
    echo "<li>✅ <strong>Bell color change only</strong> - Bright yellow when unread messages</li>";
    echo "<li>✅ <strong>Used !important</strong> - Should override Bootstrap styling</li>";
    echo "<li>✅ <strong>Added ID</strong> - mobile-bell-icon for better targeting</li>";
    echo "</ul>";
    
    echo "<h3>🖥️ Desktop Changes:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Simple bright yellow number</strong> - No circle background</li>";
    echo "<li>✅ <strong>Better positioning</strong> - Moved down and left (top: -5px, right: -10px)</li>";
    echo "<li>✅ <strong>Text shadow</strong> - Better visibility against backgrounds</li>";
    echo "<li>✅ <strong>Inline styles with !important</strong> - Override any Bootstrap conflicts</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Message Status</h2>";
    
    // Get current unread message count
    $db->query("SELECT COUNT(*) as unread_count 
                FROM unified_messages 
                WHERE to_user_id = :user_id 
                AND is_read = 0 
                AND is_archived = 0");
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult ? $unreadResult->unread_count : 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr>";
    echo "<td>Unread Messages for User {$userId}</td>";
    echo "<td style='color: " . ($unreadCount > 0 ? 'red' : 'green') . ";'>{$unreadCount}</td>";
    echo "<td>" . ($unreadCount > 0 ? '✅ Perfect for testing' : '⚠️ Need test message') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($unreadCount === 0) {
        echo "<h2>📤 Creating Test Message</h2>";
        echo "<p>Creating a test message to demonstrate the final fixes...</p>";
        
        $subject = "🔔 Final UI Test";
        $message = "This tests the final notification UI fixes: no mobile count, better desktop positioning.";
        
        $result = $messageModel->sendMessage($userId, $userId, $subject, $message);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! Message ID: {$result}</p>";
            $unreadCount = 1;
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    }
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🖥️ Desktop Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Look at the bell icon in header</strong></li>";
    echo "<li><strong>Expected:</strong> Bright yellow number positioned down and left of bell</li>";
    echo "<li><strong>No circle background</strong> - Just the number</li>";
    echo "<li><strong>Good visibility</strong> - Text shadow should make it readable</li>";
    echo "</ol>";
    
    echo "<h3>📱 Mobile PWA Testing:</h3>";
    echo "<ol>";
    echo "<li><strong>Open PWA on mobile</strong></li>";
    echo "<li><strong>Look at bottom navigation Messages tab</strong></li>";
    echo "<li><strong>Expected:</strong>";
    echo "<ul>";
    echo "<li>✅ Bell icon turns bright yellow when unread messages</li>";
    echo "<li>✅ NO red count badge/circle</li>";
    echo "<li>✅ Clean, simple appearance</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Platform</th><th>Element</th><th>Before</th><th>After</th></tr>";
    
    echo "<tr>";
    echo "<td rowspan='2'><strong>🖥️ Desktop</strong></td>";
    echo "<td>Count Display</td>";
    echo "<td style='color: red;'>❌ Circle badge, poor positioning</td>";
    echo "<td style='color: green;'>✅ Bright yellow number, better positioned</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Visibility</td>";
    echo "<td style='color: red;'>❌ Bootstrap conflicts, alignment issues</td>";
    echo "<td style='color: green;'>✅ !important styles, text shadow</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td rowspan='2'><strong>📱 Mobile</strong></td>";
    echo "<td>Bell Color</td>";
    echo "<td style='color: red;'>❌ Bootstrap overrides, not changing</td>";
    echo "<td style='color: green;'>✅ !important yellow, should work</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>Count Badge</td>";
    echo "<td style='color: red;'>❌ Red circle with number</td>";
    echo "<td style='color: green;'>✅ No badge (removed from HTML)</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Changes</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Mobile (footer.php):</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// REMOVED:
&lt;span class=\"mobile-notification-badge\" id=\"mobile-notification-badge\"&gt;&lt;/span&gt;

// ADDED:
&lt;i class=\"fas fa-bell\" id=\"mobile-bell-icon\"&gt;&lt;/i&gt;";
    echo "</pre>";
    
    echo "<h3>🖥️ Desktop (header.php):</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// CHANGED FROM:
class=\"position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger\"

// TO:
class=\"notification-count-simple\"
style=\"position: absolute; top: -5px; right: -10px; 
       color: #FFD700 !important; font-weight: bold !important;
       text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;\"";
    echo "</pre>";
    
    echo "<h3>📜 JavaScript (notification-center.js):</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// Mobile: Use !important to override Bootstrap
mobileBellIcon.style.setProperty('color', '#FFD700', 'important');

// Desktop: Apply bright yellow styling
badge.style.color = '#FFD700';
badge.style.background = 'none';";
    echo "</pre>";
    echo "</div>";
    
    if ($unreadCount > 0) {
        echo "<h2>✅ Ready for Final Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> You have {$unreadCount} unread message(s).</p>";
        echo "<p><strong>What you should see:</strong></p>";
        echo "<ul>";
        echo "<li>🖥️ <strong>Desktop:</strong> Bright yellow number near bell (no circle)</li>";
        echo "<li>📱 <strong>Mobile:</strong> Bright yellow bell icon (no count badge)</li>";
        echo "<li>✨ <strong>Clean UI:</strong> No alignment issues or Bootstrap conflicts</li>";
        echo "</ul>";
        echo "<p><strong>The !important styles should override any Bootstrap conflicts!</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files to Copy</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated files:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/includes/header.php</code> - Desktop badge styling</li>";
    echo "<li><code>views/includes/footer.php</code> - Removed mobile badge</li>";
    echo "<li><code>public/js/notification-center.js</code> - Updated badge handling</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🛠️ If Bootstrap Still Overrides</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<p>If the mobile bell still doesn't turn yellow, we can add CSS with higher specificity:</p>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "#mobile-bell-icon.has-unread {
    color: #FFD700 !important;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.6) !important;
}";
    echo "</pre>";
    echo "<p>But the current !important JavaScript approach should work.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Final notification fixes test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
