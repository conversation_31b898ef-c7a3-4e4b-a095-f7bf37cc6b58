<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-list me-2"></i>Notification Queue</h1>
            <p class="text-muted">Monitor and manage the notification processing queue.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/notification_settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">Status Filter</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" <?php echo $current_status === 'all' ? 'selected' : ''; ?>>All Statuses</option>
                        <option value="pending" <?php echo $current_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="sent" <?php echo $current_status === 'sent' ? 'selected' : ''; ?>>Sent</option>
                        <option value="failed" <?php echo $current_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        <option value="cancelled" <?php echo $current_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="limit" class="form-label">Limit</label>
                    <select class="form-select" id="limit" name="limit">
                        <option value="25" <?php echo $current_limit === 25 ? 'selected' : ''; ?>>25</option>
                        <option value="50" <?php echo $current_limit === 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $current_limit === 100 ? 'selected' : ''; ?>>100</option>
                        <option value="200" <?php echo $current_limit === 200 ? 'selected' : ''; ?>>200</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-2"></i>Filter
                    </button>
                    <?php if ($current_status === 'failed'): ?>
                        <form method="post" action="<?php echo BASE_URL; ?>/admin/clearFailed" class="d-inline">
                            <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                            <button type="submit" class="btn btn-danger" 
                                    onclick="return confirm('Are you sure you want to clear all failed notifications?')">
                                <i class="fas fa-trash me-2"></i>Clear Failed
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Notifications Table -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-bell me-2"></i>Notifications (<?php echo count($notifications); ?> shown)
            </h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Notifications Found</h5>
                    <p class="text-muted">No notifications match the current filter criteria.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Scheduled</th>
                                <th>Attempts</th>
                                <th>Last Attempt</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($notifications as $notification): ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">#<?php echo $notification->id; ?></small>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($notification->user_name); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($notification->email); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $typeIcons = [
                                            'email' => 'fas fa-envelope text-primary',
                                            'sms' => 'fas fa-sms text-success',
                                            'push' => 'fas fa-bell text-warning',
                                            'toast' => 'fas fa-comment text-info'
                                        ];
                                        $icon = $typeIcons[$notification->notification_type] ?? 'fas fa-question';
                                        ?>
                                        <i class="<?php echo $icon; ?> me-2"></i>
                                        <?php echo ucfirst($notification->notification_type); ?>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo ucfirst(str_replace('_', ' ', $notification->notification_category)); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" 
                                             title="<?php echo htmlspecialchars($notification->subject); ?>">
                                            <?php echo htmlspecialchars($notification->subject); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClasses = [
                                            'pending' => 'bg-warning',
                                            'sent' => 'bg-success',
                                            'failed' => 'bg-danger',
                                            'cancelled' => 'bg-secondary'
                                        ];
                                        $statusClass = $statusClasses[$notification->status] ?? 'bg-secondary';
                                        ?>
                                        <span class="badge <?php echo $statusClass; ?>">
                                            <?php echo ucfirst($notification->status); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <?php echo formatDateTimeForUser($notification->scheduled_for, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                                            <br>
                                            <?php echo formatDateTimeForUser($notification->scheduled_for, $_SESSION['user_id'] ?? null, 'g:i A'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $notification->attempts; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($notification->last_attempt): ?>
                                            <small>
                                                <?php echo formatDateTimeForUser($notification->last_attempt, $_SESSION['user_id'] ?? null, 'M j, g:i A'); ?>
                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted">Never</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewNotificationDetails(<?php echo $notification->id; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Notification Details Modal -->
<div class="modal fade" id="notificationDetailsModal" tabindex="-1" aria-labelledby="notificationDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationDetailsModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Notification Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="notificationDetailsContent">
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                    <p class="text-muted mt-2">Loading...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewNotificationDetails(notificationId) {
    const modal = new bootstrap.Modal(document.getElementById('notificationDetailsModal'));
    const content = document.getElementById('notificationDetailsContent');
    
    // Show loading state
    content.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
            <p class="text-muted mt-2">Loading...</p>
        </div>
    `;
    
    modal.show();
    
    // Find notification data from the table
    const notifications = <?php echo json_encode($notifications); ?>;
    const notification = notifications.find(n => n.id == notificationId);
    
    if (notification) {
        const typeIcons = {
            'email': 'fas fa-envelope text-primary',
            'sms': 'fas fa-sms text-success',
            'push': 'fas fa-bell text-warning',
            'toast': 'fas fa-comment text-info'
        };
        
        const statusClasses = {
            'pending': 'bg-warning',
            'sent': 'bg-success',
            'failed': 'bg-danger',
            'cancelled': 'bg-secondary'
        };
        
        const icon = typeIcons[notification.notification_type] || 'fas fa-question';
        const statusClass = statusClasses[notification.status] || 'bg-secondary';
        
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Basic Information</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>ID:</strong></td>
                            <td>#${notification.id}</td>
                        </tr>
                        <tr>
                            <td><strong>User:</strong></td>
                            <td>${notification.user_name}<br><small class="text-muted">${notification.email}</small></td>
                        </tr>
                        <tr>
                            <td><strong>Type:</strong></td>
                            <td><i class="${icon} me-2"></i>${notification.notification_type.charAt(0).toUpperCase() + notification.notification_type.slice(1)}</td>
                        </tr>
                        <tr>
                            <td><strong>Category:</strong></td>
                            <td>${notification.notification_category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td><span class="badge ${statusClass}">${notification.status.charAt(0).toUpperCase() + notification.status.slice(1)}</span></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Timing Information</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>${window.TimezoneHelper ? window.TimezoneHelper.formatDateTimeForUser(notification.created_at) : (() => {
                                const d = new Date(notification.created_at + 'Z');
                                return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0') + ' ' + String(d.getHours()).padStart(2, '0') + ':' + String(d.getMinutes()).padStart(2, '0');
                            })()}</td>
                        </tr>
                        <tr>
                            <td><strong>Scheduled:</strong></td>
                            <td>${window.TimezoneHelper ? window.TimezoneHelper.formatDateTimeForUser(notification.scheduled_for) : (() => {
                                const d = new Date(notification.scheduled_for + 'Z');
                                return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0') + ' ' + String(d.getHours()).padStart(2, '0') + ':' + String(d.getMinutes()).padStart(2, '0');
                            })()}</td>
                        </tr>
                        <tr>
                            <td><strong>Attempts:</strong></td>
                            <td>${notification.attempts}</td>
                        </tr>
                        <tr>
                            <td><strong>Last Attempt:</strong></td>
                            <td>${notification.last_attempt ? (window.TimezoneHelper ? window.TimezoneHelper.formatDateTimeForUser(notification.last_attempt) : new Date(notification.last_attempt + 'Z').toLocaleString()) : 'Never'}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>${window.TimezoneHelper ? window.TimezoneHelper.formatDateTimeForUser(notification.updated_at) : new Date(notification.updated_at + 'Z').toLocaleString()}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-12">
                    <h6>Subject</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        ${notification.subject}
                    </div>
                    
                    <h6>Message</h6>
                    <div class="bg-light p-3 rounded" style="white-space: pre-wrap;">
                        ${notification.message}
                    </div>
                    
                    ${notification.error_message ? `
                        <h6 class="mt-3 text-danger">Error Message</h6>
                        <div class="bg-danger bg-opacity-10 border border-danger p-3 rounded">
                            ${notification.error_message}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    } else {
        content.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                <p class="text-muted mt-2">Notification not found.</p>
            </div>
        `;
    }
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>