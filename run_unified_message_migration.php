<?php
/**
 * Unified Message System Migration Runner
 * 
 * This script runs the database migration to create the unified message system.
 * Run this once to set up the new system.
 */

// Initialize the system
require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Unified Message System Migration</h1>\n";
echo "<p>Starting migration...</p>\n";

try {
    $db = new Database();
    
    // Read the migration SQL file
    $migrationFile = __DIR__ . '/database/migrations/create_unified_message_system_fixed.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    if (!$sql) {
        throw new Exception("Could not read migration file");
    }
    
    echo "<p>Executing migration SQL...</p>\n";
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty statements and comments
        }
        
        try {
            $db->query($statement);
            $db->execute();
            $successCount++;
            echo "<p style='color: green;'>✓ Executed statement successfully</p>\n";
        } catch (Exception $e) {
            $errorCount++;
            echo "<p style='color: orange;'>⚠ Statement warning: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            // Continue with other statements
        }
    }
    
    echo "<h2>Migration Results</h2>\n";
    echo "<p><strong>Successful statements:</strong> $successCount</p>\n";
    echo "<p><strong>Warnings/Errors:</strong> $errorCount</p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold;'>✅ Migration completed successfully!</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠ Migration completed with warnings. Check the messages above.</p>\n";
    }
    
    echo "<h2>Next Steps</h2>\n";
    echo "<ul>\n";
    echo "<li>Test the judge messaging functionality</li>\n";
    echo "<li>Test push notifications</li>\n";
    echo "<li>Check the notification center</li>\n";
    echo "<li>Verify email delivery still works</li>\n";
    echo "</ul>\n";
    
    echo "<p><a href='/notification_center'>Go to Notification Center</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Migration failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check the error and try again.</p>\n";
}

echo "<hr>\n";
echo "<p><em>Migration script completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>