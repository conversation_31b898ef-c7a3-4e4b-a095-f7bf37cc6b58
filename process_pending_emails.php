<?php
/**
 * Process Pending Emails
 * 
 * Manually processes pending email notifications in the queue
 */

require_once 'config/config.php';
require_once 'core/Database.php';

// Load required models
require_once APPROOT . '/models/NotificationService.php';

echo "<h1>📧 Process Pending Emails</h1>";

try {
    echo "<h2>🔄 Processing Email Queue</h2>";
    
    // Load the NotificationService
    $notificationService = new NotificationService();
    
    // Process pending notifications
    $results = $notificationService->processPendingNotifications(50); // Process up to 50 emails
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📊 Processing Results:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th></tr>";
    echo "<tr><td><strong>Total Processed</strong></td><td>{$results['processed']}</td></tr>";
    echo "<tr><td><strong>Successfully Sent</strong></td><td style='color: green;'>{$results['sent']}</td></tr>";
    echo "<tr><td><strong>Failed</strong></td><td style='color: red;'>{$results['failed']}</td></tr>";
    echo "</table>";
    echo "</div>";
    
    if (!empty($results['errors'])) {
        echo "<h3>❌ Errors Encountered:</h3>";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        foreach ($results['errors'] as $error) {
            echo "<p>• " . htmlspecialchars($error) . "</p>";
        }
        echo "</div>";
    }
    
    if ($results['sent'] > 0) {
        echo "<h3>✅ Success!</h3>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>{$results['sent']} email(s) were successfully sent!</strong></p>";
        echo "<p>Check the recipient's email inbox for the notifications.</p>";
        echo "</div>";
    }
    
    if ($results['processed'] === 0) {
        echo "<h3>ℹ️ No Emails to Process</h3>";
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
        echo "<p>There are no pending email notifications in the queue.</p>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>No contact forms have been submitted recently</li>";
        echo "<li>Email notifications are disabled</li>";
        echo "<li>Emails have already been processed</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Check remaining pending emails
    $db = new Database();
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE notification_type = 'email' AND status = 'pending'");
    $pendingResult = $db->single();
    $remainingPending = $pendingResult->count ?? 0;
    
    echo "<h3>📋 Queue Status After Processing</h3>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Remaining pending emails:</strong> {$remainingPending}</p>";
    
    if ($remainingPending > 0) {
        echo "<p style='color: orange;'>⚠️ There are still {$remainingPending} pending email(s) in the queue.</p>";
        echo "<p><strong>Possible reasons:</strong></p>";
        echo "<ul>";
        echo "<li>Processing limit reached (processed max 50 emails)</li>";
        echo "<li>Some emails failed validation</li>";
        echo "<li>SMTP configuration issues</li>";
        echo "</ul>";
        echo "<p><a href='process_pending_emails.php'>Run this script again</a> to process more emails.</p>";
    } else {
        echo "<p style='color: green;'>✅ All email notifications have been processed!</p>";
    }
    echo "</div>";
    
    echo "<h2>🔧 Troubleshooting</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 If Emails Still Aren't Working:</h3>";
    echo "<ol>";
    echo "<li><strong>Check SMTP Configuration:</strong>";
    echo "<ul>";
    echo "<li>Go to /admin/settings_email</li>";
    echo "<li>Verify SMTP host, port, username, password</li>";
    echo "<li>Test the email configuration</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Check Global Settings:</strong>";
    echo "<ul>";
    echo "<li>Ensure email notifications are enabled globally</li>";
    echo "<li>Check admin notification settings</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Check User Preferences:</strong>";
    echo "<ul>";
    echo "<li>Ensure admin users have email notifications enabled</li>";
    echo "<li>Check individual user notification preferences</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Set Up Cron Job:</strong>";
    echo "<ul>";
    echo "<li>Configure cron to run /cron/process_notifications.php every 2 minutes</li>";
    echo "<li>This will automatically process emails without manual intervention</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Check Server Logs:</strong>";
    echo "<ul>";
    echo "<li>Look for PHP errors in server error logs</li>";
    echo "<li>Check for SMTP connection errors</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>📋 Recommended Cron Job</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⏰ Automatic Email Processing:</h3>";
    echo "<p>To ensure emails are sent automatically, add this cron job:</p>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "# Process email notifications every 2 minutes\n";
    echo "*/2 * * * * /usr/bin/php " . APPROOT . "/cron/process_notifications.php > /dev/null 2>&1";
    echo "</pre>";
    echo "<p><strong>This will:</strong></p>";
    echo "<ul>";
    echo "<li>Run every 2 minutes</li>";
    echo "<li>Process pending email notifications automatically</li>";
    echo "<li>Ensure contact form emails are sent promptly</li>";
    echo "<li>Handle all notification types (email, SMS, etc.)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Processing Error</h2>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
    
    echo "<h3>🔧 Possible Solutions:</h3>";
    echo "<ul>";
    echo "<li>Check if NotificationService class exists and is properly loaded</li>";
    echo "<li>Verify database connection</li>";
    echo "<li>Check file permissions</li>";
    echo "<li>Review server error logs for more details</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><em>Email processing completed at: " . date('Y-m-d H:i:s') . "</em></p>";
echo "<p><a href='debug_email_notifications.php'>← Back to Email Debug</a></p>";
?>
