<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Update Loop Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/pwa-features.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>PWA Update Loop Fix Test</h1>
        <p>This page tests the fix for the PWA update banner loop issue.</p>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <button id="show-banner" class="btn btn-primary me-2">Show Update Banner</button>
                        <button id="clear-flags" class="btn btn-warning me-2">Clear All Flags</button>
                        <button id="check-status" class="btn btn-info me-2">Check Status</button>
                        <button id="simulate-update" class="btn btn-success">Simulate Update Applied</button>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Current Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="status-info"></div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Debug Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Fix Summary</h5>
                    </div>
                    <div class="card-body">
                        <h6>Changes Made:</h6>
                        <ul class="small">
                            <li>Extended suppression period to 30 minutes</li>
                            <li>Added update applied flag tracking</li>
                            <li>Improved service worker waiting check</li>
                            <li>Better update process handling</li>
                            <li>Reduced periodic check frequency to 2 hours</li>
                            <li>Clear flags after successful update</li>
                        </ul>
                        
                        <h6 class="mt-3">Expected Behavior:</h6>
                        <ul class="small">
                            <li>Banner shows only when SW is actually waiting</li>
                            <li>Clicking "Update" properly applies update</li>
                            <li>Banner doesn't reappear after update</li>
                            <li>Dismissal lasts 30 minutes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus() {
            const dismissed = sessionStorage.getItem('pwa_update_dismissed');
            const applied = sessionStorage.getItem('pwa_update_applied');
            const lastShown = localStorage.getItem('pwa_last_update_shown');
            const now = Date.now();
            
            let statusHtml = '<div class="row">';
            statusHtml += `<div class="col-md-6">`;
            statusHtml += `<strong>Session Storage:</strong><br>`;
            statusHtml += `• Dismissed: <span class="badge ${dismissed === 'true' ? 'bg-warning' : 'bg-secondary'}">${dismissed || 'false'}</span><br>`;
            statusHtml += `• Applied: <span class="badge ${applied === 'true' ? 'bg-success' : 'bg-secondary'}">${applied || 'false'}</span>`;
            statusHtml += `</div>`;
            
            statusHtml += `<div class="col-md-6">`;
            statusHtml += `<strong>Local Storage:</strong><br>`;
            if (lastShown) {
                const minutesAgo = Math.floor((now - parseInt(lastShown)) / (1000 * 60));
                statusHtml += `• Last Shown: ${minutesAgo} minutes ago<br>`;
                statusHtml += `• Suppressed: <span class="badge ${minutesAgo < 30 ? 'bg-danger' : 'bg-success'}">${minutesAgo < 30 ? 'Yes' : 'No'}</span>`;
            } else {
                statusHtml += `• Last Shown: <span class="badge bg-secondary">Never</span><br>`;
                statusHtml += `• Suppressed: <span class="badge bg-success">No</span>`;
            }
            statusHtml += `</div>`;
            statusHtml += '</div>';
            
            document.getElementById('status-info').innerHTML = statusHtml;
        }

        function showBanner() {
            log('🔄 Manually showing update banner...');
            
            // Remove existing banners
            const existing = document.querySelectorAll('.update-banner');
            existing.forEach(banner => banner.remove());
            
            // Create banner
            const banner = document.createElement('div');
            banner.className = 'update-banner alert alert-info alert-dismissible fade show position-fixed';
            banner.style.cssText = `
                top: 20px !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                z-index: 9999 !important;
                max-width: 90% !important;
                width: 400px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                border: none !important;
                background: linear-gradient(135deg, #1338BE 0%, #0056b3 100%) !important;
                color: white !important;
                border-radius: 8px !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            `;
            
            banner.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-sync-alt me-2"></i>
                    <div class="flex-grow-1">
                        <strong>App Update Available</strong>
                        <div class="small">A new version is ready to install</div>
                    </div>
                    <div class="ms-2">
                        <button type="button" class="btn btn-light btn-sm me-2" onclick="applyUpdate()">
                            <i class="fas fa-download me-1"></i>Update
                        </button>
                        <button type="button" class="btn-close btn-close-white" aria-label="Close" onclick="dismissUpdate()"></button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(banner);
            log('✅ Banner created and shown');
            updateStatus();
        }

        function applyUpdate() {
            log('🔄 Apply update clicked - simulating update process...');
            
            // Set flags like the real function
            sessionStorage.setItem('pwa_update_applied', 'true');
            sessionStorage.setItem('pwa_update_dismissed', 'true');
            localStorage.setItem('pwa_last_update_shown', Date.now().toString());
            
            // Update button state
            const updateButton = document.querySelector('.update-banner .btn-light');
            if (updateButton) {
                updateButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
                updateButton.disabled = true;
            }
            
            // Remove banner after delay
            setTimeout(() => {
                const banner = document.querySelector('.update-banner');
                if (banner) {
                    banner.remove();
                }
                log('✅ Update applied, banner removed');
                log('ℹ️ In real app, page would reload here');
                updateStatus();
            }, 2000);
        }

        function dismissUpdate() {
            log('❌ Dismiss update clicked');
            
            const banner = document.querySelector('.update-banner');
            if (banner) {
                banner.remove();
                log('✅ Banner removed');
            }
            
            // Set flags like the real function
            sessionStorage.setItem('pwa_update_dismissed', 'true');
            localStorage.setItem('pwa_last_update_shown', Date.now().toString());
            log('ℹ️ Update dismissed for this session and next 30 minutes');
            updateStatus();
        }

        function clearFlags() {
            sessionStorage.removeItem('pwa_update_dismissed');
            sessionStorage.removeItem('pwa_update_applied');
            localStorage.removeItem('pwa_last_update_shown');
            
            const banners = document.querySelectorAll('.update-banner');
            banners.forEach(banner => banner.remove());
            
            log('🧹 All flags cleared');
            updateStatus();
        }

        function simulateUpdateApplied() {
            log('🔄 Simulating successful update (page reload)...');
            
            // Simulate the clearUpdateFlagsIfNeeded function
            const updateApplied = sessionStorage.getItem('pwa_update_applied');
            if (updateApplied === 'true') {
                log('✅ Page loaded after update, clearing update flags');
                sessionStorage.removeItem('pwa_update_applied');
                sessionStorage.removeItem('pwa_update_dismissed');
                // Keep pwa_last_update_shown to prevent immediate re-showing
            }
            
            updateStatus();
        }

        // Event listeners
        document.getElementById('show-banner').addEventListener('click', showBanner);
        document.getElementById('clear-flags').addEventListener('click', clearFlags);
        document.getElementById('check-status').addEventListener('click', updateStatus);
        document.getElementById('simulate-update').addEventListener('click', simulateUpdateApplied);

        // Initialize
        log('🚀 PWA Update Loop Fix Test initialized');
        updateStatus();
    </script>
</body>
</html>