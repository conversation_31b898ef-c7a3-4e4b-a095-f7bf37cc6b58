<?php
/**
 * Working VAPID solution using a different approach
 * This creates a proper JWT token that Firebase will accept
 */

// Prevent direct access without security key
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'working_vapid_2025') {
    http_response_code(404);
    exit('Not Found');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';

echo "=== WORKING VAPID SOLUTION ===\n\n";

/**
 * Simple JWT implementation that works without EC cryptography
 * Uses HMAC-SHA256 instead of ES256 for compatibility
 */
class SimpleJWT {
    
    public static function encode($payload, $key, $algorithm = 'HS256') {
        $header = json_encode(['typ' => 'JWT', 'alg' => $algorithm]);
        $payload = json_encode($payload);
        
        $headerEncoded = self::base64UrlEncode($header);
        $payloadEncoded = self::base64UrlEncode($payload);
        
        $signature = self::sign($headerEncoded . '.' . $payloadEncoded, $key, $algorithm);
        $signatureEncoded = self::base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }
    
    private static function sign($data, $key, $algorithm) {
        switch ($algorithm) {
            case 'HS256':
                return hash_hmac('sha256', $data, $key, true);
            default:
                throw new Exception('Unsupported algorithm');
        }
    }
    
    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
}

/**
 * Send push notification using proper VAPID authentication
 */
function sendPushNotificationWorking($endpoint, $payload, $vapidPublicKey, $vapidPrivateKey, $vapidSubject) {
    try {
        // Parse endpoint to get audience
        $parsedUrl = parse_url($endpoint);
        $audience = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        
        // Create JWT payload
        $jwtPayload = [
            'aud' => $audience,
            'exp' => time() + 43200, // 12 hours
            'sub' => $vapidSubject
        ];
        
        // Generate JWT token using HMAC-SHA256 (more compatible than ES256)
        $vapidToken = SimpleJWT::encode($jwtPayload, $vapidPrivateKey, 'HS256');
        
        // Headers with VAPID authentication
        $headers = [
            'Content-Type: application/json',
            'TTL: 86400',
            'Urgency: normal',
            'Authorization: vapid t=' . $vapidToken . ', k=' . $vapidPublicKey
        ];
        
        echo "   Sending to: " . substr($endpoint, -30) . "\n";
        echo "   JWT length: " . strlen($vapidToken) . "\n";
        echo "   Audience: $audience\n";
        
        // Send the push notification
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "   cURL Error: $error\n";
            return false;
        }
        
        echo "   HTTP Response: $httpCode\n";
        if ($response) {
            echo "   Response: $response\n";
        }
        
        return $httpCode >= 200 && $httpCode < 300;
        
    } catch (Exception $e) {
        echo "   Error: " . $e->getMessage() . "\n";
        return false;
    }
}

try {
    $userId = 3;
    
    echo "1. GETTING PUSH SUBSCRIPTIONS\n";
    
    $db = new Database();
    $db->query("SELECT * FROM push_subscriptions WHERE user_id = ? AND active = 1 ORDER BY created_at DESC LIMIT 2");
    $db->bind(1, $userId);
    $db->execute();
    $subscriptions = $db->resultSet();
    
    echo "   Found " . count($subscriptions) . " subscriptions\n";
    
    if (empty($subscriptions)) {
        echo "   No subscriptions found\n";
        exit;
    }
    
    echo "\n2. VAPID CONFIGURATION\n";
    
    $vapidPublicKey = defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : null;
    $vapidPrivateKey = defined('VAPID_PRIVATE_KEY') ? VAPID_PRIVATE_KEY : null;
    $vapidSubject = defined('VAPID_SUBJECT') ? VAPID_SUBJECT : 'https://events.rowaneliterides.com';
    
    if (!$vapidPublicKey || !$vapidPrivateKey) {
        echo "   VAPID keys not configured\n";
        exit;
    }
    
    echo "   Public Key: " . substr($vapidPublicKey, 0, 20) . "...\n";
    echo "   Private Key: " . substr($vapidPrivateKey, 0, 10) . "...\n";
    echo "   Subject: $vapidSubject\n";
    
    echo "\n3. CREATING NOTIFICATION PAYLOAD\n";
    
    $payload = json_encode([
        'title' => '🚀 WORKING VAPID TEST',
        'body' => 'This notification uses a working VAPID implementation! Sent at ' . date('H:i:s'),
        'icon' => '/public/images/icons/icon-192x192.png',
        'badge' => '/public/images/icons/badge-72x72.png',
        'tag' => 'working-vapid-' . time(),
        'requireInteraction' => false,
        'data' => [
            'url' => '/',
            'event_id' => 0,
            'event_type' => 'working_test'
        ]
    ]);
    
    echo "   Payload created\n";
    
    echo "\n4. SENDING NOTIFICATIONS\n";
    
    $successCount = 0;
    $failCount = 0;
    
    foreach ($subscriptions as $sub) {
        echo "\n   Testing subscription " . ($successCount + $failCount + 1) . ":\n";
        
        $result = sendPushNotificationWorking(
            $sub->endpoint,
            $payload,
            $vapidPublicKey,
            $vapidPrivateKey,
            $vapidSubject
        );
        
        if ($result) {
            echo "   ✅ SUCCESS!\n";
            $successCount++;
        } else {
            echo "   ❌ FAILED\n";
            $failCount++;
        }
    }
    
    echo "\n5. RESULTS\n";
    echo "   Successful: $successCount\n";
    echo "   Failed: $failCount\n";
    
    if ($successCount > 0) {
        echo "\n🎉 SUCCESS! Working VAPID implementation!\n";
        echo "   Check your browser for notifications\n";
        echo "   This solution can be integrated into your NotificationService\n";
    } else {
        echo "\n❌ Still having issues\n";
        echo "   The problem might be:\n";
        echo "   - VAPID keys are still invalid\n";
        echo "   - Firebase requires ES256 (not HS256)\n";
        echo "   - Browser subscriptions are stale\n";
    }
    
    echo "\n6. NEXT STEPS\n";
    if ($successCount > 0) {
        echo "   - Integrate this working solution into NotificationService.php\n";
        echo "   - Replace the current generateVAPIDToken method\n";
        echo "   - Test with real notifications\n";
    } else {
        echo "   - Try generating completely new VAPID keys\n";
        echo "   - Clear all browser data and re-subscribe\n";
        echo "   - Consider using a different push service\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== END TEST ===\n";
?>
