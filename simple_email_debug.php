<?php
/**
 * Simple Email Debug
 * 
 * Basic debugging without relying on specific model classes
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🔍 Simple Email Debug</h1>";

try {
    $db = new Database();
    
    echo "<h2>📊 Database Tables Check</h2>";
    
    // Check if notification_queue table exists
    try {
        $db->query("SHOW TABLES LIKE 'notification_queue'");
        $queueTableExists = $db->single();
        
        if ($queueTableExists) {
            echo "<p style='color: green;'>✅ notification_queue table exists</p>";
            
            // Check queue contents
            $db->query("SELECT COUNT(*) as total FROM notification_queue");
            $totalNotifications = $db->single();
            
            $db->query("SELECT COUNT(*) as pending FROM notification_queue WHERE status = 'pending'");
            $pendingNotifications = $db->single();
            
            $db->query("SELECT COUNT(*) as email_pending FROM notification_queue WHERE notification_type = 'email' AND status = 'pending'");
            $emailPending = $db->single();
            
            echo "<table border='1' cellpadding='5'>";
            echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th></tr>";
            echo "<tr><td>Total Notifications</td><td>{$totalNotifications->total}</td></tr>";
            echo "<tr><td>Pending Notifications</td><td>{$pendingNotifications->pending}</td></tr>";
            echo "<tr><td>Pending Email Notifications</td><td style='color: " . ($emailPending->email_pending > 0 ? 'orange' : 'green') . ";'>{$emailPending->email_pending}</td></tr>";
            echo "</table>";
            
            if ($emailPending->email_pending > 0) {
                echo "<h3>📋 Recent Pending Email Notifications</h3>";
                $db->query("SELECT id, user_id, subject, created_at, error_message FROM notification_queue WHERE notification_type = 'email' AND status = 'pending' ORDER BY created_at DESC LIMIT 5");
                $pendingEmails = $db->resultSet();
                
                echo "<table border='1' cellpadding='5' style='width: 100%;'>";
                echo "<tr style='background: #f0f0f0;'><th>ID</th><th>User ID</th><th>Subject</th><th>Created</th><th>Error</th></tr>";
                foreach ($pendingEmails as $email) {
                    echo "<tr>";
                    echo "<td>{$email->id}</td>";
                    echo "<td>{$email->user_id}</td>";
                    echo "<td>" . htmlspecialchars(substr($email->subject, 0, 50)) . "</td>";
                    echo "<td>{$email->created_at}</td>";
                    echo "<td>" . htmlspecialchars($email->error_message ?? 'None') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ notification_queue table does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking notification_queue table: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>👤 Admin Users Check</h2>";
    
    // Check admin users
    try {
        $db->query("SELECT id, name, email, status FROM users WHERE role = 'admin'");
        $adminUsers = $db->resultSet();
        
        if (!empty($adminUsers)) {
            echo "<table border='1' cellpadding='5' style='width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Name</th><th>Email</th><th>Status</th></tr>";
            foreach ($adminUsers as $admin) {
                $statusColor = $admin->status === 'active' ? 'green' : 'red';
                echo "<tr>";
                echo "<td>{$admin->id}</td>";
                echo "<td>" . htmlspecialchars($admin->name) . "</td>";
                echo "<td>" . htmlspecialchars($admin->email) . "</td>";
                echo "<td style='color: {$statusColor};'>{$admin->status}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            $activeAdmins = array_filter($adminUsers, function($admin) { return $admin->status === 'active'; });
            echo "<p><strong>Active admins who should receive contact form emails: " . count($activeAdmins) . "</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ No admin users found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking admin users: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>📧 Email Settings Check</h2>";
    
    // Check if settings table exists and get email settings
    try {
        $db->query("SHOW TABLES LIKE 'settings'");
        $settingsTableExists = $db->single();
        
        if ($settingsTableExists) {
            echo "<p style='color: green;'>✅ settings table exists</p>";
            
            $emailSettings = [
                'email_smtp_host',
                'email_smtp_port', 
                'email_smtp_username',
                'email_smtp_password',
                'email_from_address',
                'email_from_name'
            ];
            
            echo "<table border='1' cellpadding='5' style='width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'><th>Setting</th><th>Value</th><th>Status</th></tr>";
            
            foreach ($emailSettings as $setting) {
                $db->query("SELECT setting_value FROM settings WHERE setting_key = ?", [$setting]);
                $result = $db->single();
                $value = $result->setting_value ?? '';
                
                $status = !empty($value) ? '✅ Set' : '❌ Not Set';
                $statusColor = !empty($value) ? 'green' : 'red';
                $displayValue = $setting === 'email_smtp_password' ? (empty($value) ? 'Not Set' : '••••••••') : ($value ?: 'Not Set');
                
                echo "<tr>";
                echo "<td><strong>" . str_replace('_', ' ', ucwords($setting, '_')) . "</strong></td>";
                echo "<td>" . htmlspecialchars($displayValue) . "</td>";
                echo "<td style='color: {$statusColor};'>{$status}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ settings table does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking email settings: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>🔍 Recent Messages Check</h2>";
    
    // Check recent messages
    try {
        $db->query("SHOW TABLES LIKE 'messages'");
        $messagesTableExists = $db->single();
        
        if ($messagesTableExists) {
            echo "<p style='color: green;'>✅ messages table exists</p>";
            
            $db->query("SELECT COUNT(*) as total FROM messages WHERE subject LIKE 'Contact Form:%'");
            $contactMessages = $db->single();
            
            echo "<p><strong>Contact form messages in database: {$contactMessages->total}</strong></p>";
            
            if ($contactMessages->total > 0) {
                $db->query("SELECT id, from_user_id, to_user_id, subject, created_at FROM messages WHERE subject LIKE 'Contact Form:%' ORDER BY created_at DESC LIMIT 5");
                $recentMessages = $db->resultSet();
                
                echo "<table border='1' cellpadding='5' style='width: 100%;'>";
                echo "<tr style='background: #f0f0f0;'><th>ID</th><th>From</th><th>To</th><th>Subject</th><th>Created</th></tr>";
                foreach ($recentMessages as $msg) {
                    echo "<tr>";
                    echo "<td>{$msg->id}</td>";
                    echo "<td>{$msg->from_user_id}</td>";
                    echo "<td>{$msg->to_user_id}</td>";
                    echo "<td>" . htmlspecialchars($msg->subject) . "</td>";
                    echo "<td>{$msg->created_at}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p style='color: red;'>❌ messages table does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking messages: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>🎯 Diagnosis</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Based on the checks above:</h3>";
    
    // Get the data for diagnosis
    $db->query("SELECT COUNT(*) as email_pending FROM notification_queue WHERE notification_type = 'email' AND status = 'pending'");
    $emailPendingResult = $db->single();
    $emailPendingCount = $emailPendingResult->email_pending ?? 0;
    
    if ($emailPendingCount > 0) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
        echo "<strong>⚠️ ISSUE FOUND: {$emailPendingCount} pending email(s) in queue</strong><br>";
        echo "<strong>Diagnosis:</strong> Contact form emails are being queued but not sent<br>";
        echo "<strong>Cause:</strong> Email processing cron job is not running<br>";
        echo "<strong>Solution:</strong> Set up cron job or process emails manually";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
        echo "<strong>✅ No pending emails in queue</strong><br>";
        echo "This could mean emails are being processed, or they're not being queued at all.";
        echo "</div>";
    }
    
    echo "</div>";
    
    echo "<h2>🧪 Manual Test</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Next Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Submit a contact form</strong> and then refresh this page to see if emails are queued</li>";
    echo "<li><strong>Check SMTP settings</strong> at /admin/settings_email</li>";
    echo "<li><strong>Set up cron job</strong> to process notifications automatically:</li>";
    echo "</ol>";
    
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "# Add this to your crontab:\n";
    echo "*/2 * * * * /usr/bin/php " . APPROOT . "/cron/process_notifications.php > /dev/null 2>&1";
    echo "</pre>";
    
    if ($emailPendingCount > 0) {
        echo "<h4>🔄 Manual Email Processing</h4>";
        echo "<p>You can try to process the pending emails manually by running:</p>";
        echo "<p><code>" . APPROOT . "/cron/process_notifications.php</code></p>";
        echo "<p>Or check if there's a web-based processor available.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Debug Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Simple email debug completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
