/**
 * Notification Permission Manager v1.0.0
 * 
 * Proactively manages FCM notification permissions throughout the app
 * Shows permission prompts at strategic moments when users are most likely to accept
 */

class NotificationPermissionManager {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.debugMode = localStorage.getItem('notification_debug') === 'true' ||
                         document.body.dataset.debugMode === 'true' ||
                         window.location.search.includes('debug=1');
        
        this.isSupported = this.checkSupport();
        this.hasShownPrompt = sessionStorage.getItem('fcm_prompt_shown') === 'true';
        this.lastPromptTime = localStorage.getItem('fcm_last_prompt_time');
        
        // Don't show prompts more than once per day
        this.promptCooldown = 24 * 60 * 60 * 1000; // 24 hours
        
        if (this.debugMode) {
            console.log('[FCM-Permission] Manager initialized');
        }
        
        this.init();
    }
    
    checkSupport() {
        return 'serviceWorker' in navigator && 
               'PushManager' in window && 
               'Notification' in window &&
               !this.isIOS();
    }
    
    isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    }
    
    async init() {
        if (!this.isSupported) {
            if (this.debugMode) {
                console.log('[FCM-Permission] Not supported on this device');
            }
            return;
        }
        
        // Wait for FCM manager to be available
        this.waitForFCMManager();
        
        // Set up strategic prompt triggers
        this.setupPromptTriggers();
    }
    
    async waitForFCMManager() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 40; // 20 seconds max wait
            
            const checkFCM = () => {
                if (window.fcmManager) {
                    if (this.debugMode) {
                        console.log('[FCM-Permission] FCM Manager found');
                    }
                    this.fcmManager = window.fcmManager;
                    resolve(true);
                    return true;
                }
                return false;
            };
            
            if (checkFCM()) {
                return;
            }
            
            const interval = setInterval(() => {
                attempts++;
                if (checkFCM()) {
                    clearInterval(interval);
                } else if (attempts >= maxAttempts) {
                    clearInterval(interval);
                    if (this.debugMode) {
                        console.warn('[FCM-Permission] FCM Manager not found after waiting');
                    }
                    resolve(false);
                }
            }, 500);
        });
    }
    
    setupPromptTriggers() {
        // Trigger 1: After user registers for an event
        this.setupEventRegistrationTrigger();
        
        // Trigger 2: When viewing event details
        this.setupEventViewTrigger();
        
        // Trigger 3: After successful login (delayed)
        this.setupLoginTrigger();
        
        // Trigger 4: When user subscribes to event notifications
        this.setupEventSubscriptionTrigger();
        
        // Trigger 5: Dashboard visit (for returning users)
        this.setupDashboardTrigger();
    }
    
    setupEventRegistrationTrigger() {
        // Listen for successful event registrations
        document.addEventListener('eventRegistrationSuccess', () => {
            setTimeout(() => {
                this.showContextualPrompt('registration', {
                    title: 'Stay Updated!',
                    message: 'Get notified about event updates, reminders, and important announcements.',
                    context: 'You just registered for an event'
                });
            }, 2000);
        });
    }
    
    setupEventViewTrigger() {
        // Show prompt when viewing event details (if user seems engaged)
        if (window.location.pathname.includes('/events/') || 
            window.location.pathname.includes('/car-shows/')) {
            
            // Wait for user to spend some time on the page
            setTimeout(() => {
                if (!document.hidden) { // Only if page is still active
                    this.showContextualPrompt('event-view', {
                        title: 'Never Miss an Event!',
                        message: 'Get reminders and updates for events you\'re interested in.',
                        context: 'Viewing event details'
                    });
                }
            }, 15000); // 15 seconds
        }
    }
    
    setupLoginTrigger() {
        // Check if user just logged in
        const justLoggedIn = sessionStorage.getItem('just_logged_in') === 'true';
        if (justLoggedIn) {
            sessionStorage.removeItem('just_logged_in');
            
            // Wait a bit after login
            setTimeout(() => {
                this.showContextualPrompt('login', {
                    title: 'Welcome Back!',
                    message: 'Enable notifications to stay updated on your events and registrations.',
                    context: 'Just logged in'
                });
            }, 5000);
        }
    }
    
    setupEventSubscriptionTrigger() {
        // Listen for event subscription attempts
        document.addEventListener('eventSubscriptionAttempt', () => {
            this.showContextualPrompt('subscription', {
                title: 'Enable Browser Notifications',
                message: 'To receive event reminders, please allow browser notifications.',
                context: 'Subscribing to event notifications',
                urgent: true
            });
        });
    }
    
    setupDashboardTrigger() {
        // Show on dashboard for users who haven't enabled notifications
        if (window.location.pathname.includes('/dashboard') || 
            window.location.pathname.includes('/user/')) {
            
            setTimeout(() => {
                this.showContextualPrompt('dashboard', {
                    title: 'Enhance Your Experience',
                    message: 'Enable notifications to get timely updates about your events.',
                    context: 'Using dashboard'
                });
            }, 10000); // 10 seconds
        }
    }
    
    shouldShowPrompt() {
        // Don't show if already shown in this session
        if (this.hasShownPrompt) {
            return false;
        }
        
        // Don't show if permission already granted
        if (Notification.permission === 'granted') {
            return false;
        }
        
        // Don't show if permission permanently denied
        if (Notification.permission === 'denied') {
            return false;
        }
        
        // Don't show if shown recently (cooldown period)
        if (this.lastPromptTime) {
            const timeSinceLastPrompt = Date.now() - parseInt(this.lastPromptTime);
            if (timeSinceLastPrompt < this.promptCooldown) {
                return false;
            }
        }
        
        return true;
    }
    
    async showContextualPrompt(trigger, options = {}) {
        if (!this.shouldShowPrompt()) {
            if (this.debugMode) {
                console.log('[FCM-Permission] Skipping prompt for trigger:', trigger);
            }
            return;
        }
        
        if (this.debugMode) {
            console.log('[FCM-Permission] Showing prompt for trigger:', trigger, options);
        }
        
        // Mark as shown for this session
        this.hasShownPrompt = true;
        sessionStorage.setItem('fcm_prompt_shown', 'true');
        
        // Update last prompt time
        localStorage.setItem('fcm_last_prompt_time', Date.now().toString());
        
        // Show the prompt
        this.displayPromptModal(options);
    }
    
    displayPromptModal(options) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'fcm-permission-modal';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('data-bs-backdrop', 'static');
        
        const isUrgent = options.urgent || false;
        const urgentClass = isUrgent ? 'border-warning' : '';
        const urgentIcon = isUrgent ? 'fas fa-exclamation-triangle text-warning' : 'fas fa-bell text-primary';
        
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content ${urgentClass}">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="${urgentIcon} me-2"></i>
                            ${options.title || 'Enable Notifications'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
                        </div>
                        <p class="mb-3">${options.message || 'Stay updated with important notifications.'}</p>
                        ${options.context ? `<small class="text-muted"><i class="fas fa-info-circle me-1"></i>${options.context}</small>` : ''}
                        
                        <div class="mt-4">
                            <h6 class="mb-2">You'll receive notifications for:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Event reminders</li>
                                <li><i class="fas fa-check text-success me-2"></i>Registration updates</li>
                                <li><i class="fas fa-check text-success me-2"></i>Important announcements</li>
                                <li><i class="fas fa-check text-success me-2"></i>Judging notifications</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            Maybe Later
                        </button>
                        <button type="button" class="btn btn-primary" onclick="window.notificationPermissionManager.enableNotifications()">
                            <i class="fas fa-bell me-2"></i>Enable Notifications
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Show modal
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }
    
    async enableNotifications() {
        // Wait for FCM manager to be ready if not already available
        if (!this.fcmManager) {
            console.log('[FCM-Permission] FCM Manager not ready, waiting...');
            await this.waitForFCMManager();
        }
        
        if (!this.fcmManager) {
            alert('Notification system not ready. Please refresh the page and try again.');
            return;
        }
        
        try {
            // Close the modal first
            const modal = document.getElementById('fcm-permission-modal');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
            
            // This is now called from a user gesture (button click), so it's safe
            if (this.debugMode) {
                console.log('[FCM-Permission] Requesting permission from user gesture');
            }
            
            // Request permission and get token
            const token = await this.fcmManager.requestPermissionAndGetToken();
            
            if (token) {
                // Show success message
                this.showSuccessToast('Notifications enabled successfully! You\'ll now receive important updates.');
                
                // Update user preferences to reflect enabled state
                this.updateUserPreferences(true);
                
                if (this.debugMode) {
                    console.log('[FCM-Permission] Successfully enabled notifications');
                }
            } else {
                throw new Error('Failed to get notification token');
            }
            
        } catch (error) {
            console.error('[FCM-Permission] Error enabling notifications:', error);
            
            if (error.message.includes('denied')) {
                alert('Notifications were blocked. Please enable them in your browser settings and refresh the page.');
            } else {
                alert('Failed to enable notifications. Please try again or refresh the page.');
            }
        }
    }
    
    async updateUserPreferences(enabled) {
        try {
            const response = await fetch(`${this.baseUrl}/api/user/notification-preferences`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    push_notifications: enabled ? 1 : 0
                })
            });
            
            if (!response.ok) {
                throw new Error('Failed to update preferences');
            }
            
        } catch (error) {
            console.error('[FCM-Permission] Error updating preferences:', error);
        }
    }
    
    showSuccessToast(message) {
        // Create toast container if it doesn't exist
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        
        // Create toast
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        container.appendChild(toast);
        
        // Show toast
        const bsToast = new bootstrap.Toast(toast, { delay: 5000 });
        bsToast.show();
        
        // Clean up
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
    
    // Public method to manually trigger prompt
    triggerPrompt(context = 'manual') {
        this.showContextualPrompt(context, {
            title: 'Enable Notifications',
            message: 'Stay updated with important notifications about your events and activities.',
            context: 'Manually triggered'
        });
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is logged in
    if (document.body.dataset.userId) {
        window.notificationPermissionManager = new NotificationPermissionManager();
    }
});