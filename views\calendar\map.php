<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <!-- Mobile-First Responsive Header -->
    <div class="row mb-4">
        <!-- Title Section -->
        <div class="col-12 col-lg-6 mb-3 mb-lg-0">
            <h1 class="mb-0">Event Map</h1>
        </div>
        
        <!-- Navigation Buttons Section -->
        <div class="col-12 col-lg-6">
            <div class="header-nav-buttons d-flex flex-column flex-sm-row gap-2 justify-content-lg-end">
                <!-- View Toggle Buttons -->
                <div class="btn-group flex-fill flex-sm-auto" role="group" aria-label="View toggle">
                    <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-outline-primary d-flex align-items-center justify-content-center">
                        <i class="fas fa-calendar me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Event </span>View
                    </a>
                    <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-primary active d-flex align-items-center justify-content-center">
                        <i class="fas fa-map-marker-alt me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Map </span>View
                    </a>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-flex gap-2">
                    <?php if (isLoggedIn()): ?>
                    <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary flex-fill flex-sm-auto d-flex align-items-center justify-content-center">
                        <i class="fas fa-plus me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Add </span>Event
                    </a>
                    <?php endif; ?>
                    <?php if (isAdmin()): ?>
                    <a href="<?php echo URLROOT; ?>/calendar/mapSettings" class="btn btn-outline-secondary flex-fill flex-sm-auto d-flex align-items-center justify-content-center" title="Map Settings">
                        <i class="fas fa-cog me-1 me-sm-2"></i>
                        <span class="d-none d-sm-inline">Settings</span>
                        <span class="d-sm-none">Config</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content Column (Full Width) -->
        <div class="col-12 mb-4">
            <!-- Advanced Filter -->
            <?php include APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
            
            <!-- Map Container -->
            <div class="card mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-muted">Interactive Map</h6>
                    <button id="reset-map-view" class="btn btn-sm btn-outline-primary" title="Fit map to show all current events">
                        <i class="fas fa-expand-arrows-alt me-1"></i>Fit to Events
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="map" style="height: 500px;"></div>
                </div>
            </div>

            <!-- Event List -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Events</h5>
                    <div class="d-flex align-items-center" style="gap: 1.5rem;">
                        <span id="event-count" class="badge bg-light text-dark px-2 py-1 me-2">0 events</span>
                        <!-- Pagination Mode Toggle -->
                        <div class="form-check form-switch mb-0 ms-2">
                            <input class="form-check-input" type="checkbox" id="pagination-mode-toggle" checked>
                            <label class="form-check-label text-white" for="pagination-mode-toggle" style="font-size: 0.85rem; white-space: nowrap;">
                                Show all pins
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Pagination Controls Top -->
                    <div id="pagination-controls-top" class="d-flex justify-content-between align-items-center mb-3" style="display: none;">
                        <div class="d-flex align-items-center gap-2">
                            <label for="page-size-select-top" class="form-label mb-0">Show:</label>
                            <select id="page-size-select-top" class="form-select form-select-sm" style="width: auto;">
                                <option value="10">10</option>
                                <option value="25" selected>25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span class="text-muted">per page</span>
                        </div>
                        <div id="pagination-info-top" class="text-muted">
                            <!-- Pagination info will be populated via JavaScript -->
                        </div>
                    </div>
                    
                    <!-- Top Pagination Navigation -->
                    <div id="pagination-nav-top" class="d-flex justify-content-center mb-3" style="display: none;">
                        <nav aria-label="Event pagination top">
                            <ul id="pagination-list-top" class="pagination pagination-sm mb-0">
                                <!-- Pagination buttons will be populated via JavaScript -->
                            </ul>
                        </nav>
                    </div>
                    
                    <div id="event-list" class="list-group">
                        <!-- Events will be populated via JavaScript -->
                        <div class="text-center py-5" id="no-events-message">
                            <i class="fas fa-map-marker-alt fa-3x mb-3 text-muted"></i>
                            <p class="lead text-muted">No events to display. Try adjusting your filters.</p>
                        </div>
                    </div>
                    
                    <!-- Pagination Controls Bottom -->
                    <div id="pagination-controls-bottom" class="d-flex justify-content-between align-items-center mt-3" style="display: none;">
                        <div class="d-flex align-items-center gap-2">
                            <label for="page-size-select-bottom" class="form-label mb-0">Show:</label>
                            <select id="page-size-select-bottom" class="form-select form-select-sm" style="width: auto;">
                                <option value="10">10</option>
                                <option value="25" selected>25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span class="text-muted">per page</span>
                        </div>
                        <div id="pagination-info-bottom" class="text-muted">
                            <!-- Pagination info will be populated via JavaScript -->
                        </div>
                    </div>
                    
                    <!-- Bottom Pagination Navigation -->
                    <div id="pagination-nav-bottom" class="d-flex justify-content-center mt-3" style="display: none;">
                        <nav aria-label="Event pagination bottom">
                            <ul id="pagination-list-bottom" class="pagination pagination-sm mb-0">
                                <!-- Pagination buttons will be populated via JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Details Modal -->
<div class="modal fade" id="event-details-modal" tabindex="-1" aria-labelledby="event-details-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="event-details-modal-label">Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4 id="modal-event-title"></h4>
                        <p class="text-muted">
                            <i class="fas fa-calendar-alt me-2"></i> <span id="modal-event-date"></span>
                        </p>
                        <p class="text-muted">
                            <i class="fas fa-map-marker-alt me-2"></i> <span id="modal-event-location"></span>
                        </p>
                        <div id="modal-event-description" class="mt-3"></div>
                    </div>
                    <div class="col-md-4">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">Calendar</h6>
                                <p id="modal-event-calendar" class="mb-0"></p>
                            </div>
                        </div>
                        <div id="modal-event-show-card" class="card mb-3 d-none">
                            <div class="card-body">
                                <h6 class="card-title">Show</h6>
                                <p id="modal-event-show" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="modal-event-link" class="btn btn-primary" target="_blank">View Event</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Mobile-First Responsive Header Styles */
.header-nav-buttons {
    min-height: 44px; /* Minimum touch target size for mobile */
}

.header-nav-buttons .btn {
    min-height: 44px;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.header-nav-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-nav-buttons .btn:active {
    transform: translateY(0);
}

/* Mobile button adjustments */
@media (max-width: 575.98px) {
    .header-nav-buttons .btn {
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
    }
    
    .header-nav-buttons .btn-group {
        width: 100%;
    }
    
    .header-nav-buttons .btn-group .btn {
        flex: 1;
    }
    
    h1 {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }
}

/* Tablet adjustments */
@media (min-width: 576px) and (max-width: 991.98px) {
    .header-nav-buttons .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

/* Desktop adjustments */
@media (min-width: 992px) {
    .header-nav-buttons .btn {
        font-size: 0.95rem;
        padding: 0.5rem 1.25rem;
    }
}

/* Button group improvements */
.header-nav-buttons .btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.header-nav-buttons .btn-group .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.header-nav-buttons .btn-group .btn:not(:first-child):not(:last-child) {
    border-radius: 0;
}

/* Icon spacing improvements */
.header-nav-buttons .btn i {
    font-size: 0.9em;
}

/* Active state improvements */
.header-nav-buttons .btn.active {
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

/* Focus states for accessibility */
.header-nav-buttons .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    outline: none;
}

.color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.event-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.event-item:hover {
    background-color: #f8f9fa;
}

.event-item .event-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.event-item .event-location {
    font-size: 0.85rem;
    color: #6c757d;
}

.map-marker-label {
    position: absolute;
    background-color: white;
    border: 1px solid #ccc;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    transform: translate(-50%, -100%);
    margin-top: -8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.event-number-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    margin-right: 10px;
    flex-shrink: 0;
}

.event-item {
    display: flex;
    align-items: flex-start;
}

.event-item-content {
    flex: 1;
}

/* Pagination Styles */
.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

.pagination .page-link {
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease-in-out;
}

.pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.event-item.highlighted {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107;
    animation: highlightPulse 2s ease-in-out;
}

.event-item.highlighted h5,
.event-item.highlighted .event-date,
.event-item.highlighted .event-location {
    color: #212529 !important;
}

.event-item.active {
    background-color: #e3f2fd !important;
    border-left: 4px solid #2196f3;
}

.event-item.active h5,
.event-item.active .event-date,
.event-item.active .event-location {
    color: #212529 !important;
}

@keyframes highlightPulse {
    0% { background-color: #fff3cd; }
    50% { background-color: #ffeaa7; }
    100% { background-color: #fff3cd; }
}

.pagination-mode-info {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Event details button styling */
.event-item .btn-outline-primary {
    border-color: #dee2e6;
    color: #6c757d;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    line-height: 1;
}

.event-item .btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* Improved active event styling */
.event-item.active {
    background-color: #e3f2fd !important;
    border-left: 4px solid #2196f3;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

.event-item.active h5,
.event-item.active .event-date,
.event-item.active .event-location {
    color: #1976d2 !important;
}
</style>

<script>
    // Define constants for JavaScript
    const URLROOT = '<?php echo URLROOT; ?>';
    const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
    
    // Cache busting information for debugging
    if (DEBUG_MODE) {
        console.log('=== MAP VIEW CACHE BUSTING INFO ===');
        console.log('Calendar Filters JS:', '<?php echo filemtime(APPROOT . '/public/js/calendar-filters.js'); ?>');
        console.log('=== END CACHE BUSTING INFO ===');
    }
</script>
<!-- Timezone Helper - MUST be loaded BEFORE other calendar scripts -->
<script src="<?php echo URLROOT; ?>/public/js/timezone-helper.js?v=<?php echo filemtime(APPROOT . '/public/js/timezone-helper.js'); ?>"></script>

<!-- Calendar Filters with cache-busting -->
<script src="<?php echo URLROOT; ?>/public/js/calendar-filters.js?v=<?php echo filemtime(APPROOT . '/public/js/calendar-filters.js'); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Map initialization variables
    let map;
    let markers = [];
    let events = [];
    let allEvents = []; // Store all events for pagination
    let listEvents = []; // Store current page events for list
    let paginationData = {};
    let infoWindow;
    let currentInfoWindow = null;
    let geocoder;
    let bounds;
    let mapProvider = '<?php echo $data['mapSettings']['provider']; ?>';
    
    // Original map settings for reset functionality
    const originalMapSettings = {
        lat: <?php echo $data['mapSettings']['default_lat']; ?>,
        lng: <?php echo $data['mapSettings']['default_lng']; ?>,
        zoom: <?php echo $data['mapSettings']['default_zoom']; ?>
    };
    
    // Current filtered view settings (updated when events are loaded)
    let currentFilteredView = {
        lat: <?php echo $data['mapSettings']['default_lat']; ?>,
        lng: <?php echo $data['mapSettings']['default_lng']; ?>,
        zoom: <?php echo $data['mapSettings']['default_zoom']; ?>,
        hasBounds: false
    };
    
    // Pagination variables
    let currentPage = 1;
    let pageSize = 25;
    let paginationMode = 'all_pins'; // 'all_pins' or 'current_page'
    
    // Focus event data (if provided)
    const focusEvent = <?php echo isset($data['focusEvent']) && $data['focusEvent'] ? json_encode($data['focusEvent']) : 'null'; ?>;
    const focusShow = <?php echo isset($data['focusShow']) && $data['focusShow'] ? json_encode($data['focusShow']) : 'null'; ?>;

    // Debug focus data
    if (DEBUG_MODE) {
        console.log('=== MAP VIEW: Focus data ===');
        console.log('focusEvent:', focusEvent);
        console.log('focusShow:', focusShow);
    }
    
    // Helper function to get activeFilters object consistently
    function getActiveFilters() {
        if (window.calendarFilters && window.calendarFilters.activeFilters) {
            return window.calendarFilters.activeFilters;
        } else {
            // Fallback: create global activeFilters if not available
            if (typeof window.activeFilters === 'undefined') {
                window.activeFilters = {
                    calendars: [],
                    state: '',
                    city: '',
                    venue: '',
                    clubs: [],
                    radius: 50,
                    lat: null,
                    lng: null,
                    keyword: '',
                    categories: [],
                    tags: [],
                    priceMin: 0,
                    priceMax: 500,
                    currentView: 'map'
                };
            }
            return window.activeFilters;
        }
    }
    
    // Set the current view to 'map' for the filter system
    const activeFilters = getActiveFilters();
    activeFilters.currentView = 'map';

    // Ensure the global calendarFilters object knows we're in map view
    if (window.calendarFilters) {
        window.calendarFilters.activeFilters.currentView = 'map';
    }

    // Initialize the map based on the selected provider
    function initMap() {
        // Default map center and zoom
        const defaultLat = <?php echo $data['mapSettings']['default_lat']; ?>;
        const defaultLng = <?php echo $data['mapSettings']['default_lng']; ?>;
        const defaultZoom = <?php echo $data['mapSettings']['default_zoom']; ?>;
        
        // Create the map based on the provider
        switch (mapProvider) {
            case 'google':
                initGoogleMap(defaultLat, defaultLng, defaultZoom);
                break;
            case 'openstreetmap':
                initOpenStreetMap(defaultLat, defaultLng, defaultZoom);
                break;
            case 'mapbox':
                initMapboxMap(defaultLat, defaultLng, defaultZoom);
                break;
            case 'here':
                initHereMap(defaultLat, defaultLng, defaultZoom);
                break;
            default:
                // Fallback to OpenStreetMap if provider is not recognized
                initOpenStreetMap(defaultLat, defaultLng, defaultZoom);
        }
        
        // Load events
        loadEvents();
        
        // Load states for filter
        loadStates();
        
        // Initialize event listeners
        initEventListeners();
    }
    
    // Initialize Google Maps
    function initGoogleMap(defaultLat, defaultLng, defaultZoom) {
        try {
            // Check if Google Maps API is available
            if (!window.google || !window.google.maps) {
                throw new Error('Google Maps API not available');
            }
            
            if (DEBUG_MODE) {
                console.log('=== GOOGLE MAPS INITIALIZATION ===');
                console.log('Default coordinates:', defaultLat, defaultLng);
                console.log('Default zoom:', defaultZoom);
            }
            
            // Create the map with mapId for AdvancedMarkerElement support
            map = new google.maps.Map(document.getElementById('map'), {
                center: { lat: defaultLat, lng: defaultLng },
                zoom: defaultZoom,
                mapTypeControl: true,
                fullscreenControl: true,
                streetViewControl: false,
                scrollwheel: true,
                gestureHandling: 'auto',
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.DROPDOWN_MENU
                },
                // Use a default map ID for AdvancedMarkerElement support
                mapId: 'DEMO_MAP_ID'
            });
            
            // Create info window for markers
            infoWindow = new google.maps.InfoWindow();
            
            // Create geocoder for location search
            geocoder = new google.maps.Geocoder();
            
            // Create bounds object for auto-zooming
            bounds = new google.maps.LatLngBounds();
            
            if (DEBUG_MODE) {
                console.log('Google Maps initialized successfully');
            }
            
        } catch (error) {
            console.error('Error initializing Google Maps:', error);
            console.log('Falling back to OpenStreetMap...');
            mapProvider = 'openstreetmap';
            initOpenStreetMap(defaultLat, defaultLng, defaultZoom);
        }
    }
    
    // Initialize OpenStreetMap with Leaflet
    function initOpenStreetMap(defaultLat, defaultLng, defaultZoom) {
        // Create the map with scroll wheel zoom enabled
        map = L.map('map', {
            scrollWheelZoom: true,
            doubleClickZoom: true,
            touchZoom: true,
            boxZoom: true,
            keyboard: true
        }).setView([defaultLat, defaultLng], defaultZoom);
        
        // Add tile layer
        L.tileLayer('<?php echo $data['mapSettings']['tile_url']; ?>', {
            attribution: '<?php echo $data['mapSettings']['attribution']; ?>'
        }).addTo(map);
        
        // Create custom info window functionality
        infoWindow = {
            marker: null,
            popup: null,
            setContent: function(content) {
                this.content = content;
            },
            open: function(map, marker) {
                if (this.popup) {
                    this.popup.remove();
                }
                this.marker = marker;
                this.popup = L.popup()
                    .setLatLng(marker.getLatLng())
                    .setContent(this.content)
                    .openOn(map);
            },
            close: function() {
                if (this.popup) {
                    this.popup.remove();
                    this.popup = null;
                }
            }
        };
        
        // Create custom geocoder
        geocoder = {
            geocode: function(request, callback) {
                const address = request.address;
                
                // Use Nominatim for geocoding
                fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.length > 0) {
                            const result = data[0];
                            callback([{
                                geometry: {
                                    location: {
                                        lat: function() { return parseFloat(result.lat); },
                                        lng: function() { return parseFloat(result.lon); }
                                    }
                                },
                                address_components: []
                            }], 'OK');
                        } else {
                            callback([], 'ZERO_RESULTS');
                        }
                    })
                    .catch(error => {
                        console.error('Geocoding error:', error);
                        callback([], 'ERROR');
                    });
            }
        };
        
        // Create custom bounds
        bounds = {
            extend: function(latLng) {
                if (!this._bounds) {
                    this._bounds = L.latLngBounds([latLng.lat, latLng.lng], [latLng.lat, latLng.lng]);
                } else {
                    this._bounds.extend([latLng.lat, latLng.lng]);
                }
            },
            getCenter: function() {
                return this._bounds ? this._bounds.getCenter() : null;
            }
        };
        
        // Define constants
        L.GeocoderStatus = {
            OK: 'OK',
            ZERO_RESULTS: 'ZERO_RESULTS',
            ERROR: 'ERROR'
        };
    }
    
    // Initialize Mapbox
    function initMapboxMap(defaultLat, defaultLng, defaultZoom) {
        // Load Mapbox GL JS if not already loaded
        if (!window.mapboxgl) {
            const script = document.createElement('script');
            script.src = 'https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js';
            script.onload = function() {
                // Set access token
                mapboxgl.accessToken = '<?php echo $data['mapSettings']['api_key']; ?>';
                
                // Create the map
                map = new mapboxgl.Map({
                    container: 'map',
                    style: 'mapbox://styles/mapbox/streets-v12',
                    center: [defaultLng, defaultLat],
                    zoom: defaultZoom
                });
                
                // Add navigation controls
                map.addControl(new mapboxgl.NavigationControl());
                
                // Create custom info window functionality
                infoWindow = {
                    marker: null,
                    popup: null,
                    setContent: function(content) {
                        this.content = content;
                    },
                    open: function(map, marker) {
                        if (this.popup) {
                            this.popup.remove();
                        }
                        this.marker = marker;
                        this.popup = new mapboxgl.Popup()
                            .setLngLat(marker.getLngLat())
                            .setHTML(this.content)
                            .addTo(map);
                    },
                    close: function() {
                        if (this.popup) {
                            this.popup.remove();
                            this.popup = null;
                        }
                    }
                };
                
                // Create custom geocoder
                geocoder = {
                    geocode: function(request, callback) {
                        const address = request.address;
                        
                        // Use Mapbox Geocoding API
                        fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=${mapboxgl.accessToken}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data && data.features && data.features.length > 0) {
                                    const result = data.features[0];
                                    callback([{
                                        geometry: {
                                            location: {
                                                lat: function() { return result.center[1]; },
                                                lng: function() { return result.center[0]; }
                                            }
                                        },
                                        address_components: []
                                    }], 'OK');
                                } else {
                                    callback([], 'ZERO_RESULTS');
                                }
                            })
                            .catch(error => {
                                console.error('Geocoding error:', error);
                                callback([], 'ERROR');
                            });
                    }
                };
                
                // Create custom bounds
                bounds = {
                    _sw: null,
                    _ne: null,
                    extend: function(latLng) {
                        if (!this._sw && !this._ne) {
                            this._sw = [latLng.lng, latLng.lat];
                            this._ne = [latLng.lng, latLng.lat];
                        } else {
                            this._sw = [
                                Math.min(this._sw[0], latLng.lng),
                                Math.min(this._sw[1], latLng.lat)
                            ];
                            this._ne = [
                                Math.max(this._ne[0], latLng.lng),
                                Math.max(this._ne[1], latLng.lat)
                            ];
                        }
                    },
                    getCenter: function() {
                        if (!this._sw || !this._ne) return null;
                        return {
                            lng: (this._sw[0] + this._ne[0]) / 2,
                            lat: (this._sw[1] + this._ne[1]) / 2
                        };
                    }
                };
                
                // Define constants
                mapboxgl.GeocoderStatus = {
                    OK: 'OK',
                    ZERO_RESULTS: 'ZERO_RESULTS',
                    ERROR: 'ERROR'
                };
                
                // Load events after map is loaded
                map.on('load', function() {
                    loadEvents();
                    loadStates();
                    initEventListeners();
                });
            };
            document.head.appendChild(script);
            
            // Add CSS
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.css';
            document.head.appendChild(link);
            
            // Don't continue with the rest of the initialization
            return;
        } else {
            // Mapbox is already loaded
            mapboxgl.accessToken = '<?php echo $data['mapSettings']['api_key']; ?>';
            
            // Create the map
            map = new mapboxgl.Map({
                container: 'map',
                style: 'mapbox://styles/mapbox/streets-v12',
                center: [defaultLng, defaultLat],
                zoom: defaultZoom
            });
            
            // Add navigation controls
            map.addControl(new mapboxgl.NavigationControl());
        }
    }
    
    // Initialize HERE Maps
    function initHereMap(defaultLat, defaultLng, defaultZoom) {
        // Load HERE Maps if not already loaded
        if (!window.H) {
            const script = document.createElement('script');
            script.src = 'https://js.api.here.com/v3/3.1/mapsjs-core.js';
            script.onload = function() {
                // Load additional HERE Maps scripts
                const scripts = [
                    'https://js.api.here.com/v3/3.1/mapsjs-service.js',
                    'https://js.api.here.com/v3/3.1/mapsjs-mapevents.js',
                    'https://js.api.here.com/v3/3.1/mapsjs-ui.js'
                ];
                
                let loadedScripts = 0;
                scripts.forEach(src => {
                    const script = document.createElement('script');
                    script.src = src;
                    script.onload = function() {
                        loadedScripts++;
                        if (loadedScripts === scripts.length) {
                            // All scripts loaded, initialize map
                            initHereMapInstance(defaultLat, defaultLng, defaultZoom);
                        }
                    };
                    document.head.appendChild(script);
                });
                
                // Add CSS
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://js.api.here.com/v3/3.1/mapsjs-ui.css';
                document.head.appendChild(link);
            };
            document.head.appendChild(script);
            
            // Don't continue with the rest of the initialization
            return;
        } else {
            // HERE Maps is already loaded
            initHereMapInstance(defaultLat, defaultLng, defaultZoom);
        }
    }
    
    // Initialize HERE Maps instance
    function initHereMapInstance(defaultLat, defaultLng, defaultZoom) {
        // Initialize the platform
        const platform = new H.service.Platform({
            apikey: '<?php echo $data['mapSettings']['api_key']; ?>'
        });
        
        // Get default layers
        const defaultLayers = platform.createDefaultLayers();
        
        // Create the map
        map = new H.Map(
            document.getElementById('map'),
            defaultLayers.vector.normal.map,
            {
                center: { lat: defaultLat, lng: defaultLng },
                zoom: defaultZoom,
                pixelRatio: window.devicePixelRatio || 1
            }
        );
        
        // Add event listeners for map interactions
        const behavior = new H.mapevents.Behavior(new H.mapevents.MapEvents(map));
        
        // Create UI controls
        const ui = H.ui.UI.createDefault(map, defaultLayers);
        
        // Create custom info window functionality
        infoWindow = {
            bubble: null,
            setContent: function(content) {
                this.content = content;
            },
            open: function(map, marker) {
                if (this.bubble) {
                    ui.removeBubble(this.bubble);
                }
                this.bubble = new H.ui.InfoBubble(marker.getGeometry(), {
                    content: this.content
                });
                ui.addBubble(this.bubble);
            },
            close: function() {
                if (this.bubble) {
                    ui.removeBubble(this.bubble);
                    this.bubble = null;
                }
            }
        };
        
        // Create geocoder
        const geocodingService = platform.getSearchService();
        geocoder = {
            geocode: function(request, callback) {
                const address = request.address;
                
                geocodingService.geocode({
                    q: address
                }, (result) => {
                    if (result.items && result.items.length > 0) {
                        const item = result.items[0];
                        callback([{
                            geometry: {
                                location: {
                                    lat: function() { return item.position.lat; },
                                    lng: function() { return item.position.lng; }
                                }
                            },
                            address_components: []
                        }], 'OK');
                    } else {
                        callback([], 'ZERO_RESULTS');
                    }
                }, (error) => {
                    console.error('Geocoding error:', error);
                    callback([], 'ERROR');
                });
            }
        };
        
        // Create custom bounds
        bounds = {
            _bounds: new H.geo.Rect(90, -180, -90, 180),
            extend: function(latLng) {
                this._bounds.mergeLatLng(latLng.lat, latLng.lng);
            },
            getCenter: function() {
                const center = this._bounds.getCenter();
                return { lat: center.lat, lng: center.lng };
            }
        };
        
        // Define constants
        H.GeocoderStatus = {
            OK: 'OK',
            ZERO_RESULTS: 'ZERO_RESULTS',
            ERROR: 'ERROR'
        };
        
        // Load events
        loadEvents();
        loadStates();
        initEventListeners();
    }
    
    // Load events from the server
    function loadEvents(page = 1) {
        return new Promise((resolve, reject) => {
        if (DEBUG_MODE) {
            console.log('=== MAP VIEW: Loading events ===', 'Page:', page, 'PageSize:', pageSize, 'Mode:', paginationMode);
        }
        
        // Show loading indicator
        document.getElementById('event-list').innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-3 text-muted">Loading events...</p></div>';
        
        // Build the URL with filters
        let url = '<?php echo URLROOT; ?>/calendar/mapEvents';
        
        // Get filter parameters from the filter system
        let filterParams = '';
        if (window.calendarFilters && typeof window.calendarFilters.getFilterParams === 'function') {
            filterParams = window.calendarFilters.getFilterParams();
            if (DEBUG_MODE) {
                console.log('=== MAP VIEW: Using filter params from calendarFilters ===', filterParams);
            }
        } else {
            // Fallback: build basic filter params
            const activeFilters = getActiveFilters();
            const params = [];
            
            if (activeFilters.calendars && activeFilters.calendars.length > 0) {
                params.push(`calendars=${activeFilters.calendars.join(',')}`);
            }
            if (activeFilters.state) {
                params.push(`state=${encodeURIComponent(activeFilters.state)}`);
            }
            if (activeFilters.city) {
                params.push(`city=${encodeURIComponent(activeFilters.city)}`);
            }
            if (activeFilters.keyword) {
                params.push(`keyword=${encodeURIComponent(activeFilters.keyword)}`);
            }
            
            filterParams = params.join('&');
            if (DEBUG_MODE) {
                console.log('=== MAP VIEW: Using fallback filter params ===', filterParams);
            }
        }

        // If we have a focus event, modify the date range to include it
        if (focusEvent && focusEvent.start_date) {
            const focusDate = new Date(focusEvent.start_date);
            const today = new Date();

            // If the focus event is in the past, extend the start date to include it
            if (focusDate < today) {
                // Set start date to 30 days before the focus event to give some context
                const extendedStart = new Date(focusDate);
                extendedStart.setDate(extendedStart.getDate() - 30);
                const startParam = `start=${extendedStart.toISOString().split('T')[0]}`;

                if (filterParams) {
                    filterParams += '&' + startParam;
                } else {
                    filterParams = startParam;
                }

                if (DEBUG_MODE) {
                    console.log('=== MAP VIEW: Extended date range for focus event ===', {
                        focusDate: focusDate.toISOString().split('T')[0],
                        extendedStart: extendedStart.toISOString().split('T')[0]
                    });
                }
            }
        }

        // Add pagination parameters
        const paginationParams = [
            `page=${page}`,
            `page_size=${pageSize}`,
            `pagination_mode=${paginationMode}`
        ];
        
        if (filterParams) {
            filterParams += '&' + paginationParams.join('&');
        } else {
            filterParams = paginationParams.join('&');
        }
        
        // Add cache busting parameter
        filterParams += '&_cb=' + Date.now();
        
        if (filterParams) {
            url += '?' + filterParams;
        }
        
        if (DEBUG_MODE) {
            console.log('=== MAP VIEW: Fetching events from ===', url);
        }
        
        // Fetch events
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (DEBUG_MODE) {
                    console.log('=== MAP VIEW: Events loaded ===', data);
                }
                
                // Handle new pagination response format
                if (data.pagination) {
                    paginationData = data.pagination;
                    allEvents = data.all_events || [];
                    listEvents = data.list_events || [];
                    events = paginationMode === 'all_pins' ? allEvents : listEvents;
                    currentPage = data.pagination.current_page;
                } else {
                    // Fallback for old format
                    events = data;
                    allEvents = data;
                    listEvents = data;
                    paginationData = {
                        current_page: 1,
                        total_pages: 1,
                        total_events: data.length,
                        has_previous: false,
                        has_next: false
                    };
                }
                
                displayEvents();
                updatePaginationControls();
                
                // Handle focus event if provided
                if (focusEvent && focusEvent.id) {
                    if (DEBUG_MODE) {
                        console.log('=== MAP VIEW: Attempting to focus on event ===', focusEvent.id);
                        console.log('=== MAP VIEW: Focus event data ===', focusEvent);
                    }
                    setTimeout(() => {
                        focusOnEvent(focusEvent.id);
                    }, 500); // Wait for map to finish loading
                } else {
                    if (DEBUG_MODE) {
                        console.log('=== MAP VIEW: No focus event provided or missing ID ===');
                    }
                }
                
                resolve(data);
            })
            .catch(error => {
                console.error('Error loading events:', error);
                document.getElementById('event-list').innerHTML = '<div class="alert alert-danger">Error loading events. Please try again later.</div>';
                reject(error);
            });
        });
    }
    
    // Load states for the filter
    function loadStates() {
        fetch('<?php echo URLROOT; ?>/calendar/getStates?_cb=' + Date.now())
            .then(response => response.json())
            .then(data => {
                const stateFilter = document.getElementById('state-filter');
                
                // Clear existing options except the first one
                while (stateFilter.options.length > 1) {
                    stateFilter.remove(1);
                }
                
                // Add states to the dropdown
                data.forEach(state => {
                    const option = document.createElement('option');
                    option.value = state.state;
                    option.textContent = `${state.state} (${state.event_count})`;
                    stateFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading states:', error);
            });
    }
    
    // Display events on the map and in the list
    function displayEvents() {
        // Clear existing markers
        clearMarkers();
        
        // Reset bounds based on provider
        if (mapProvider === 'google') {
            bounds = new google.maps.LatLngBounds();
        } else if (mapProvider === 'openstreetmap') {
            bounds._bounds = null;
        } else if (mapProvider === 'mapbox') {
            bounds._sw = null;
            bounds._ne = null;
        } else if (mapProvider === 'here') {
            bounds._bounds = new H.geo.Rect(90, -180, -90, 180);
        }
        
        // Update event count
        const totalEvents = paginationData.total_events || events.length;
        document.getElementById('event-count').textContent = `${totalEvents} events`;
        
        // Clear event list efficiently
        const eventList = document.getElementById('event-list');
        // Use replaceChildren for better performance than innerHTML = ''
        eventList.replaceChildren();
        
        // Show message if no events
        if (listEvents.length === 0) {
            eventList.innerHTML = '<div class="text-center py-5" id="no-events-message"><i class="fas fa-map-marker-alt fa-3x mb-3 text-muted"></i><p class="lead text-muted">No events to display. Try adjusting your filters.</p></div>';
            return;
        }
        
        // Sort events by start date (soonest first) for map markers
        const sortedMapEvents = [...events].sort((a, b) => {
            // Parse dates directly - controller already converted to correct timezone
            let dateA = new Date(a.start);
            let dateB = new Date(b.start);
            return dateA.getTime() - dateB.getTime();
        });
        
        // Sort events by start date for list display
        const sortedListEvents = [...listEvents].sort((a, b) => {
            // Parse dates directly - controller already converted to correct timezone
            let dateA = new Date(a.start);
            let dateB = new Date(b.start);
            return dateA.getTime() - dateB.getTime();
        });
        
        if (DEBUG_MODE) {
            console.log('=== MAP VIEW: Events sorted by date ===');
            console.log('Map events count:', sortedMapEvents.length);
            console.log('List events count:', sortedListEvents.length);
        }
        
        // Create map markers from sorted map events
        sortedMapEvents.forEach((event, index) => {
            // Skip events without location data
            if (!event.extendedProps.lat || !event.extendedProps.lng) {
                if (DEBUG_MODE) {
                    console.log(`Skipping event "${event.title}" - no location data`);
                }
                return;
            }
            
            // Calculate the correct pin number based on pagination mode
            let pinNumber;
            if (paginationMode === 'all_pins') {
                // In "show all pins" mode, use global position among all events with location
                pinNumber = index + 1;
            } else {
                // In "current page pins" mode, find the position of this event in the current page list
                const eventIndexInList = sortedListEvents.findIndex(listEvent => listEvent.id === event.id);
                if (eventIndexInList !== -1) {
                    pinNumber = ((currentPage - 1) * pageSize) + eventIndexInList + 1;
                } else {
                    // Fallback: use index + 1 if not found in list (shouldn't happen)
                    pinNumber = index + 1;
                }
            }
            
            if (DEBUG_MODE) {
                console.log(`Event "${event.title}" - Pin number: ${pinNumber}, Pagination mode: ${paginationMode}`);
            }
            
            // Create marker based on provider
            const position = {
                lat: parseFloat(event.extendedProps.lat),
                lng: parseFloat(event.extendedProps.lng)
            };
            
            let marker;
            
            // Add marker to map based on provider with event number
            switch (mapProvider) {
                case 'google':
                    marker = createGoogleMarker(event, position, pinNumber);
                    break;
                case 'openstreetmap':
                    marker = createLeafletMarker(event, position, pinNumber);
                    break;
                case 'mapbox':
                    marker = createMapboxMarker(event, position, pinNumber);
                    break;
                case 'here':
                    marker = createHereMarker(event, position, pinNumber);
                    break;
                default:
                    // Fallback to OpenStreetMap
                    marker = createLeafletMarker(event, position, pinNumber);
            }
            
            // Extend bounds to include this marker
            bounds.extend(position);
            
            // Add marker to array
            markers.push(marker);
        });
        
        // Create list items from sorted list events using DocumentFragment for better performance
        const fragment = document.createDocumentFragment();
        let listEventNumber = ((currentPage - 1) * pageSize) + 1;
        
        sortedListEvents.forEach((event, index) => {
            // Create event list item with number badge
            const eventItem = document.createElement('div');
            eventItem.className = 'list-group-item event-item';
            eventItem.dataset.eventId = event.id;
            eventItem.innerHTML = `
                <div class="event-number-badge">${listEventNumber}</div>
                <div class="event-item-content">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">${event.title}</h5>
                        <div class="d-flex align-items-center gap-2">
                            <span class="color-dot" style="background-color: ${event.color || '#3788d8'}"></span>
                            <a href="${URLROOT}/calendar/event/${event.id}" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation();" title="View Event Details">
                                <i class="fas fa-info-circle"></i>
                            </a>
                        </div>
                    </div>
                    <p class="mb-1 event-date">
                        <i class="fas fa-calendar-alt me-1"></i> ${formatDate(event.start, event.end, event.allDay)}
                    </p>
                    <p class="mb-0 event-location">
                        <i class="fas fa-map-marker-alt me-1"></i> ${event.extendedProps.location || 'No location'}
                    </p>
                </div>
            `;
            
            // Add click listener to event item
            eventItem.addEventListener('click', () => {
                // Find corresponding marker and center map on it
                const position = {
                    lat: parseFloat(event.extendedProps.lat),
                    lng: parseFloat(event.extendedProps.lng)
                };
                
                // Find the marker for this event
                const marker = markers.find(m => m.eventId === event.id);
                if (marker) {
                    centerMapOnMarker(position, marker);
                    
                    // Highlight the event in the list (no scroll)
                    document.querySelectorAll('.event-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    eventItem.classList.add('active');
                }
            });
            
            // Add event item to fragment (no reflow yet)
            fragment.appendChild(eventItem);
            
            // Increment list event number
            listEventNumber++;
        });
        
        // Single DOM operation - append all items at once
        eventList.appendChild(fragment);
        
        // Fit map to bounds if we have markers
        if (markers.length > 0) {
            fitMapToBounds();
            
            // Capture current filtered view after fitting bounds
            // Use requestAnimationFrame for better performance than setTimeout
            requestAnimationFrame(() => {
                setTimeout(() => {
                    captureCurrentFilteredView();
                }, 100); // Reduced delay since DOM operations are now optimized
            });
        } else {
            // No markers, use default view
            currentFilteredView.hasBounds = false;
        }
    }
    
    // Focus on a specific event by ID
    function focusOnEvent(eventId) {
        if (DEBUG_MODE) {
            console.log('=== MAP VIEW: Focusing on event ===', eventId);
        }

        // Find the event in the current events first, then in all events
        let event = events.find(e => e.id == eventId);
        if (!event && allEvents) {
            event = allEvents.find(e => e.id == eventId);
            if (DEBUG_MODE && event) {
                console.log('=== MAP VIEW: Event found in allEvents array ===', eventId);
            }
        }

        if (!event) {
            console.warn('Event not found in current events or allEvents:', eventId);
            if (DEBUG_MODE) {
                console.log('=== MAP VIEW: Available events ===', events.map(e => e.id));
                console.log('=== MAP VIEW: Available allEvents ===', allEvents ? allEvents.map(e => e.id) : 'null');
            }
            return;
        }
        
        // Check if event has location data
        if (!event.extendedProps.lat || !event.extendedProps.lng) {
            console.warn('Event has no location data:', eventId);
            return;
        }
        
        const position = {
            lat: parseFloat(event.extendedProps.lat),
            lng: parseFloat(event.extendedProps.lng)
        };
        
        // Find the marker for this event
        const marker = markers.find(m => m.eventId == eventId);
        if (marker) {
            // Center map on the marker with higher zoom
            centerMapOnMarker(position, marker, 15); // Higher zoom level for focus

            // Highlight the event in the list (but don't scroll to it)
            const eventItem = document.querySelector(`[data-event-id="${eventId}"]`);
            if (eventItem) {
                document.querySelectorAll('.event-item').forEach(item => {
                    item.classList.remove('active');
                });
                eventItem.classList.add('active');

                if (DEBUG_MODE) {
                    console.log('=== MAP VIEW: Event highlighted in list (no scroll) ===', eventId);
                }
            }
        }
    }
    
    // Update pagination controls
    function updatePaginationControls() {
        const topControls = document.getElementById('pagination-controls-top');
        const bottomControls = document.getElementById('pagination-controls-bottom');
        const topNavigation = document.getElementById('pagination-nav-top');
        const bottomNavigation = document.getElementById('pagination-nav-bottom');
        const paginationInfoTop = document.getElementById('pagination-info-top');
        const paginationInfoBottom = document.getElementById('pagination-info-bottom');
        const paginationListTop = document.getElementById('pagination-list-top');
        const paginationListBottom = document.getElementById('pagination-list-bottom');
        
        if (!paginationData || paginationData.total_pages <= 1) {
            // Batch style changes to avoid multiple reflows
            requestAnimationFrame(() => {
                topControls.style.display = 'none';
                bottomControls.style.display = 'none';
                topNavigation.style.display = 'none';
                bottomNavigation.style.display = 'none';
            });
            return;
        }
        
        // Show pagination controls - batch style changes
        requestAnimationFrame(() => {
            topControls.style.display = 'flex';
            bottomControls.style.display = 'flex';
            topNavigation.style.display = 'flex';
            bottomNavigation.style.display = 'flex';
        });
        
        // Update pagination info
        const startItem = ((paginationData.current_page - 1) * paginationData.page_size) + 1;
        const endItem = Math.min(paginationData.current_page * paginationData.page_size, paginationData.total_events);
        const infoText = `Showing ${startItem}-${endItem} of ${paginationData.total_events} events`;
        paginationInfoTop.textContent = infoText;
        paginationInfoBottom.textContent = infoText;
        
        // Update page size selectors
        const pageSizeSelectTop = document.getElementById('page-size-select-top');
        const pageSizeSelectBottom = document.getElementById('page-size-select-bottom');
        pageSizeSelectTop.value = paginationData.page_size;
        pageSizeSelectBottom.value = paginationData.page_size;
        
        // Build pagination buttons for both top and bottom
        buildPaginationButtons(paginationListTop);
        buildPaginationButtons(paginationListBottom);
        
        // Add click listeners to both pagination lists
        [paginationListTop, paginationListBottom].forEach(paginationList => {
            // Remove existing listeners
            paginationList.replaceWith(paginationList.cloneNode(true));
        });
        
        // Re-get the elements after cloning
        const newPaginationListTop = document.getElementById('pagination-list-top');
        const newPaginationListBottom = document.getElementById('pagination-list-bottom');
        
        [newPaginationListTop, newPaginationListBottom].forEach(paginationList => {
            paginationList.addEventListener('click', function(e) {
                e.preventDefault();
                if (e.target.classList.contains('page-link') && e.target.dataset.page) {
                    const page = parseInt(e.target.dataset.page);
                    const totalPages = paginationData.total_pages;
                    const currentPage = paginationData.current_page;
                    if (page !== currentPage && page >= 1 && page <= totalPages) {
                        loadEvents(page);
                    }
                }
            });
        });
    }
    
    // Helper function to build pagination buttons
    function buildPaginationButtons(paginationList) {
        paginationList.innerHTML = '';
        
        const totalPages = paginationData.total_pages;
        const currentPage = paginationData.current_page;
        
        // Previous button
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${!paginationData.has_previous ? 'disabled' : ''}`;
        prevItem.innerHTML = `<a class="page-link" href="#" data-page="${paginationData.current_page - 1}">Previous</a>`;
        paginationList.appendChild(prevItem);
        
        // Calculate page range to show
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);
        
        // Adjust range if we're near the beginning or end
        if (endPage - startPage < 4) {
            if (startPage === 1) {
                endPage = Math.min(totalPages, startPage + 4);
            } else if (endPage === totalPages) {
                startPage = Math.max(1, endPage - 4);
            }
        }
        
        // First page and ellipsis
        if (startPage > 1) {
            const firstItem = document.createElement('li');
            firstItem.className = 'page-item';
            firstItem.innerHTML = '<a class="page-link" href="#" data-page="1">1</a>';
            paginationList.appendChild(firstItem);
            
            if (startPage > 2) {
                const ellipsisItem = document.createElement('li');
                ellipsisItem.className = 'page-item disabled';
                ellipsisItem.innerHTML = '<span class="page-link">...</span>';
                paginationList.appendChild(ellipsisItem);
            }
        }
        
        // Page numbers in range
        for (let i = startPage; i <= endPage; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageItem.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            paginationList.appendChild(pageItem);
        }
        
        // Last page and ellipsis
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsisItem = document.createElement('li');
                ellipsisItem.className = 'page-item disabled';
                ellipsisItem.innerHTML = '<span class="page-link">...</span>';
                paginationList.appendChild(ellipsisItem);
            }
            
            const lastItem = document.createElement('li');
            lastItem.className = 'page-item';
            lastItem.innerHTML = `<a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>`;
            paginationList.appendChild(lastItem);
        }
        
        // Next button
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${!paginationData.has_next ? 'disabled' : ''}`;
        nextItem.innerHTML = `<a class="page-link" href="#" data-page="${paginationData.current_page + 1}">Next</a>`;
        paginationList.appendChild(nextItem);
    }
    
    // Jump to page containing specific event
    function jumpToEventPage(eventId) {
        if (paginationMode === 'all_pins') {
            // Find the event in allEvents array
            const eventIndex = allEvents.findIndex(event => event.id == eventId);
            if (eventIndex !== -1) {
                const targetPage = Math.floor(eventIndex / pageSize) + 1;
                if (targetPage !== currentPage) {
                    loadEvents(targetPage).then(() => {
                        // Highlight the event in the list
                        setTimeout(() => {
                            highlightEventInList(eventId);
                        }, 100);
                    });
                } else {
                    highlightEventInList(eventId);
                }
            }
        } else {
            // In current page mode, the event should already be visible
            highlightEventInList(eventId);
        }
    }
    
    // Highlight event in the list
    function highlightEventInList(eventId) {
        // Use requestAnimationFrame to avoid forced reflows
        requestAnimationFrame(() => {
            // Remove any existing highlights
            document.querySelectorAll('.event-item.highlighted').forEach(item => {
                item.classList.remove('highlighted');
            });
            
            // Find and highlight the target event
            const eventItem = document.querySelector(`.event-item[data-event-id="${eventId}"]`);
            if (eventItem) {
                eventItem.classList.add('highlighted');
                
                // Remove highlight after 3 seconds
                setTimeout(() => {
                    eventItem.classList.remove('highlighted');
                }, 3000);
                
                if (DEBUG_MODE) {
                    console.log('=== MAP VIEW: Event highlighted in list ===', eventId);
                }
            } else {
                if (DEBUG_MODE) {
                    console.log('=== MAP VIEW: Event not found in current list ===', eventId);
                }
            }
        });
    }
    
    // Create Google Maps marker using AdvancedMarkerElement
    function createGoogleMarker(event, position, eventNumber) {
        // Get marker settings from PHP
        const markerType = '<?php echo isset($data['mapSettings']['marker_type']) ? $data['mapSettings']['marker_type'] : 'default'; ?>';
        const markerSize = parseInt('<?php echo isset($data['mapSettings']['marker_size']) ? $data['mapSettings']['marker_size'] : 16; ?>');
        const showBorder = '<?php echo isset($data['mapSettings']['marker_border']) ? $data['mapSettings']['marker_border'] : 'true'; ?>' === 'true';
        const borderColor = '<?php echo isset($data['mapSettings']['marker_border_color']) ? $data['mapSettings']['marker_border_color'] : '#FFFFFF'; ?>';
        const borderWidth = parseInt('<?php echo isset($data['mapSettings']['marker_border_width']) ? $data['mapSettings']['marker_border_width'] : 2; ?>');
        const useCalendarColors = '<?php echo isset($data['mapSettings']['use_calendar_colors']) ? $data['mapSettings']['use_calendar_colors'] : 'true'; ?>' === 'true';
        const defaultColor = '<?php echo isset($data['mapSettings']['default_marker_color']) ? $data['mapSettings']['default_marker_color'] : '#3788d8'; ?>';
        const customMarkerUrl = '<?php echo isset($data['mapSettings']['custom_marker_url']) ? $data['mapSettings']['custom_marker_url'] : ''; ?>';
        
        // Determine marker color
        const markerColor = useCalendarColors ? (event.color || defaultColor) : defaultColor;
        
        let markerElement;
        
        // Check if AdvancedMarkerElement is available (requires Maps API v3.56+)
        if (google.maps.marker && google.maps.marker.AdvancedMarkerElement) {
            // Create marker content based on marker type
            let content;
            
            if (markerType === 'custom' && customMarkerUrl) {
                // Custom image marker
                const img = document.createElement('img');
                img.src = customMarkerUrl;
                img.style.width = markerSize + 'px';
                img.style.height = markerSize + 'px';
                img.style.borderRadius = '50%';
                if (showBorder) {
                    img.style.border = `${borderWidth}px solid ${borderColor}`;
                }
                
                // Add number overlay
                const container = document.createElement('div');
                container.style.position = 'relative';
                container.style.display = 'inline-block';
                
                const numberOverlay = document.createElement('div');
                numberOverlay.style.position = 'absolute';
                numberOverlay.style.top = '50%';
                numberOverlay.style.left = '50%';
                numberOverlay.style.transform = 'translate(-50%, -50%)';
                numberOverlay.style.backgroundColor = 'rgba(0,0,0,0.7)';
                numberOverlay.style.color = 'white';
                numberOverlay.style.borderRadius = '50%';
                numberOverlay.style.width = '16px';
                numberOverlay.style.height = '16px';
                numberOverlay.style.display = 'flex';
                numberOverlay.style.alignItems = 'center';
                numberOverlay.style.justifyContent = 'center';
                numberOverlay.style.fontSize = '10px';
                numberOverlay.style.fontWeight = 'bold';
                numberOverlay.textContent = eventNumber;
                
                container.appendChild(img);
                container.appendChild(numberOverlay);
                content = container;
                
            } else if (markerType === 'pin') {
                // Pin marker with number
                const pinSize = Math.max(markerSize, 24); // Minimum size for pins
                const borderStyle = showBorder ? `stroke="${borderColor}" stroke-width="${borderWidth}"` : '';
                
                const pinDiv = document.createElement('div');
                pinDiv.innerHTML = `<svg width="${pinSize}" height="${pinSize * 1.5}" viewBox="0 0 24 36">
                    <path d="M12 0C5.4 0 0 5.4 0 12c0 7.2 12 24 12 24s12-16.8 12-24c0-6.6-5.4-12-12-12z" 
                          fill="${markerColor}" ${borderStyle} />
                    <circle cx="12" cy="12" r="6" fill="white" />
                    <text x="12" y="16" text-anchor="middle" font-size="10" font-weight="bold" fill="${markerColor}">${eventNumber}</text>
                </svg>`;
                content = pinDiv.firstElementChild;
                
            } else {
                // Default circle marker with number
                const adjustedSize = Math.max(markerSize, 20); // Minimum size to fit number
                const borderStyle = showBorder ? `border: ${borderWidth}px solid ${borderColor};` : '';
                
                const circleDiv = document.createElement('div');
                circleDiv.style.cssText = `
                    background-color: ${markerColor};
                    width: ${adjustedSize}px;
                    height: ${adjustedSize}px;
                    border-radius: 50%;
                    ${borderStyle}
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: bold;
                    font-size: ${Math.max(10, adjustedSize * 0.5)}px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                `;
                circleDiv.textContent = eventNumber;
                content = circleDiv;
            }
            
            // Create AdvancedMarkerElement
            markerElement = new google.maps.marker.AdvancedMarkerElement({
                position: position,
                map: map,
                title: event.title,
                content: content
            });
            
        } else {
            // Fallback to legacy Marker for older API versions
            console.warn('AdvancedMarkerElement not available, falling back to legacy Marker');
            markerElement = new google.maps.Marker({
                position: position,
                map: map,
                title: event.title,
                label: {
                    text: eventNumber.toString(),
                    color: 'white',
                    fontWeight: 'bold',
                    fontSize: '12px'
                },
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    fillColor: markerColor,
                    fillOpacity: 0.9,
                    strokeWeight: showBorder ? borderWidth : 0,
                    strokeColor: showBorder ? borderColor : '#ffffff',
                    scale: Math.max(markerSize * 0.75, 12)
                }
            });
        }
        
        // Add event ID and number to marker
        markerElement.eventId = event.id;
        markerElement.eventNumber = eventNumber;
        
        // Add click listener to marker (use 'gmp-click' for AdvancedMarkerElement, 'click' for legacy Marker)
        const eventType = (google.maps.marker && google.maps.marker.AdvancedMarkerElement && markerElement instanceof google.maps.marker.AdvancedMarkerElement) ? 'gmp-click' : 'click';
        markerElement.addListener(eventType, () => {
            // Close any open info window
            if (currentInfoWindow) {
                currentInfoWindow.close();
            }
            
            // Create info window content
            const content = createInfoWindowContent(event);
            
            // Set info window content and open it
            infoWindow.setContent(content);
            infoWindow.open(map, markerElement);
            currentInfoWindow = infoWindow;
            
            // Jump to the page containing this event and highlight it
            jumpToEventPage(event.id);
        });
        
        return markerElement;
    }
    
    // Create Leaflet marker for OpenStreetMap
    function createLeafletMarker(event, position, eventNumber) {
        // Get marker settings from PHP
        const markerType = '<?php echo isset($data['mapSettings']['marker_type']) ? $data['mapSettings']['marker_type'] : 'default'; ?>';
        const markerSize = parseInt('<?php echo isset($data['mapSettings']['marker_size']) ? $data['mapSettings']['marker_size'] : 16; ?>');
        const showBorder = '<?php echo isset($data['mapSettings']['marker_border']) ? $data['mapSettings']['marker_border'] : 'true'; ?>' === 'true';
        const borderColor = '<?php echo isset($data['mapSettings']['marker_border_color']) ? $data['mapSettings']['marker_border_color'] : '#FFFFFF'; ?>';
        const borderWidth = parseInt('<?php echo isset($data['mapSettings']['marker_border_width']) ? $data['mapSettings']['marker_border_width'] : 2; ?>');
        const useCalendarColors = '<?php echo isset($data['mapSettings']['use_calendar_colors']) ? $data['mapSettings']['use_calendar_colors'] : 'true'; ?>' === 'true';
        const defaultColor = '<?php echo isset($data['mapSettings']['default_marker_color']) ? $data['mapSettings']['default_marker_color'] : '#3788d8'; ?>';
        const customMarkerUrl = '<?php echo isset($data['mapSettings']['custom_marker_url']) ? $data['mapSettings']['custom_marker_url'] : ''; ?>';
        
        // Determine marker color
        const markerColor = useCalendarColors ? (event.color || defaultColor) : defaultColor;
        
        let icon;
        
        // Create icon based on marker type
        if (markerType === 'custom' && customMarkerUrl) {
            // Custom image marker
            icon = L.icon({
                iconUrl: customMarkerUrl,
                iconSize: [markerSize, markerSize],
                iconAnchor: [markerSize/2, markerSize/2]
            });
        } else if (markerType === 'pin') {
            // Pin marker with number
            const pinSize = Math.max(markerSize, 24); // Minimum size for pins
            const borderStyle = showBorder ? `stroke="${borderColor}" stroke-width="${borderWidth}"` : '';
            
            icon = L.divIcon({
                className: 'custom-marker',
                html: `<svg width="${pinSize}" height="${pinSize * 1.5}" viewBox="0 0 24 36">
                    <path d="M12 0C5.4 0 0 5.4 0 12c0 7.2 12 24 12 24s12-16.8 12-24c0-6.6-5.4-12-12-12z" 
                          fill="${markerColor}" ${borderStyle} />
                    <circle cx="12" cy="12" r="6" fill="white" />
                    <text x="12" y="16" text-anchor="middle" font-size="10" font-weight="bold" fill="${markerColor}">${eventNumber}</text>
                </svg>`,
                iconSize: [pinSize, pinSize * 1.5],
                iconAnchor: [pinSize/2, pinSize * 1.5]
            });
        } else {
            // Default circle marker with number
            const borderStyle = showBorder ? `border: ${borderWidth}px solid ${borderColor};` : '';
            const adjustedSize = Math.max(markerSize, 20); // Minimum size to fit number
            
            icon = L.divIcon({
                className: 'custom-marker',
                html: `<div style="background-color: ${markerColor}; width: ${adjustedSize}px; height: ${adjustedSize}px; border-radius: 50%; ${borderStyle} display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: ${Math.max(10, adjustedSize * 0.5)}px;">${eventNumber}</div>`,
                iconSize: [adjustedSize + (showBorder ? borderWidth * 2 : 0), adjustedSize + (showBorder ? borderWidth * 2 : 0)],
                iconAnchor: [(adjustedSize + (showBorder ? borderWidth * 2 : 0))/2, (adjustedSize + (showBorder ? borderWidth * 2 : 0))/2]
            });
        }
        
        // Create marker
        const marker = L.marker([position.lat, position.lng], {
            icon: icon,
            title: event.title
        }).addTo(map);
        
        // Add event ID and number to marker
        marker.eventId = event.id;
        marker.eventNumber = eventNumber;
        
        // Add click listener to marker
        marker.on('click', () => {
            // Close any open info window
            if (currentInfoWindow) {
                currentInfoWindow.close();
            }
            
            // Create info window content
            const content = createInfoWindowContent(event);
            
            // Set info window content and open it
            infoWindow.setContent(content);
            infoWindow.open(map, marker);
            currentInfoWindow = infoWindow;
            
            // Jump to the page containing this event and highlight it
            jumpToEventPage(event.id);
        });
        
        // Add methods to make the marker compatible with other providers
        marker.getPosition = function() {
            return this.getLatLng();
        };
        
        return marker;
    }
    
    // Create Mapbox marker
    function createMapboxMarker(event, position, eventNumber) {
        // Create marker element with number
        const el = document.createElement('div');
        el.className = 'mapbox-marker';
        el.style.backgroundColor = event.color || '#3788d8';
        el.style.width = '24px';
        el.style.height = '24px';
        el.style.borderRadius = '50%';
        el.style.border = '2px solid white';
        el.style.display = 'flex';
        el.style.alignItems = 'center';
        el.style.justifyContent = 'center';
        el.style.color = 'white';
        el.style.fontWeight = 'bold';
        el.style.fontSize = '12px';
        el.textContent = eventNumber;
        
        // Create marker
        const marker = new mapboxgl.Marker(el)
            .setLngLat([position.lng, position.lat])
            .addTo(map);
        
        // Add event ID and number to marker
        marker.eventId = event.id;
        marker.eventNumber = eventNumber;
        
        // Add click listener to marker
        el.addEventListener('click', () => {
            // Close any open info window
            if (currentInfoWindow) {
                currentInfoWindow.close();
            }
            
            // Create info window content
            const content = createInfoWindowContent(event);
            
            // Set info window content and open it
            infoWindow.setContent(content);
            infoWindow.open(map, marker);
            currentInfoWindow = infoWindow;
            
            // Jump to the page containing this event and highlight it
            jumpToEventPage(event.id);
        });
        
        // Add methods to make the marker compatible with other providers
        marker.getPosition = function() {
            return this.getLngLat();
        };
        
        return marker;
    }
    
    // Create HERE Maps marker
    function createHereMarker(event, position, eventNumber) {
        // Create custom icon with number
        const icon = new H.map.Icon(
            `<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" fill="${event.color || '#3788d8'}" stroke="white" stroke-width="2"/>
                <text x="12" y="16" text-anchor="middle" font-size="12" font-weight="bold" fill="white">${eventNumber}</text>
            </svg>`,
            {anchor: {x: 12, y: 12}}
        );
        
        // Create marker
        const marker = new H.map.Marker(position, {
            icon: icon,
            data: { eventId: event.id, eventNumber: eventNumber }
        });
        
        // Add marker to map
        map.addObject(marker);
        
        // Add event ID and number to marker
        marker.eventId = event.id;
        marker.eventNumber = eventNumber;
        
        // Add tap event listener to marker
        marker.addEventListener('tap', (evt) => {
            // Close any open info window
            if (currentInfoWindow) {
                currentInfoWindow.close();
            }
            
            // Create info window content
            const content = createInfoWindowContent(event);
            
            // Set info window content and open it
            infoWindow.setContent(content);
            infoWindow.open(map, marker);
            currentInfoWindow = infoWindow;
            
            // Jump to the page containing this event and highlight it
            jumpToEventPage(event.id);
        });
        
        // Add methods to make the marker compatible with other providers
        marker.getPosition = function() {
            return this.getGeometry();
        };
        
        return marker;
    }
    
    // Create info window content
    function createInfoWindowContent(event) {
        return `
            <div class="info-window">
                <h6>${event.title}</h6>
                <p class="mb-1"><small>${formatDate(event.start, event.end, event.allDay)}</small></p>
                <p class="mb-1"><small>${event.extendedProps.location || 'No location'}</small></p>
                <a href="${event.url}" class="btn btn-sm btn-primary mt-2">View Details</a>
            </div>
        `;
    }
    
    // Center map on marker
    function centerMapOnMarker(position, marker, zoomLevel = 12) {
        switch (mapProvider) {
            case 'google':
                map.setCenter(position);
                map.setZoom(zoomLevel);
                // Use correct event type for AdvancedMarkerElement vs legacy Marker
                const eventType = (google.maps.marker && google.maps.marker.AdvancedMarkerElement && marker instanceof google.maps.marker.AdvancedMarkerElement) ? 'gmp-click' : 'click';
                google.maps.event.trigger(marker, eventType);
                break;
            case 'openstreetmap':
                map.setView([position.lat, position.lng], zoomLevel);
                marker.fire('click');
                break;
            case 'mapbox':
                map.flyTo({
                    center: [position.lng, position.lat],
                    zoom: zoomLevel
                });
                marker.getElement().click();
                break;
            case 'here':
                map.setCenter(position);
                map.setZoom(zoomLevel);
                marker.dispatchEvent('tap');
                break;
        }
    }
    
    // Fit map to bounds
    function fitMapToBounds() {
        switch (mapProvider) {
            case 'google':
                map.fitBounds(bounds);
                const listener = google.maps.event.addListener(map, 'idle', function() {
                    if (map.getZoom() > 15) {
                        map.setZoom(15);
                    }
                    google.maps.event.removeListener(listener);
                });
                break;
            case 'openstreetmap':
                if (bounds._bounds) {
                    map.fitBounds(bounds._bounds);
                    if (map.getZoom() > 15) {
                        map.setZoom(15);
                    }
                }
                break;
            case 'mapbox':
                if (bounds._sw && bounds._ne) {
                    map.fitBounds([bounds._sw, bounds._ne], {
                        padding: 50,
                        maxZoom: 15
                    });
                }
                break;
            case 'here':
                map.getViewModel().setLookAtData({
                    bounds: bounds._bounds
                });
                if (map.getZoom() > 15) {
                    map.setZoom(15);
                }
                break;
        }
    }
    
    // Clear all markers from the map
    function clearMarkers() {
        switch (mapProvider) {
            case 'google':
                markers.forEach(marker => {
                    marker.setMap(null);
                });
                break;
            case 'openstreetmap':
                markers.forEach(marker => {
                    map.removeLayer(marker);
                });
                break;
            case 'mapbox':
                markers.forEach(marker => {
                    marker.remove();
                });
                break;
            case 'here':
                markers.forEach(marker => {
                    map.removeObject(marker);
                });
                break;
        }
        
        markers = [];
        
        if (currentInfoWindow) {
            currentInfoWindow.close();
            currentInfoWindow = null;
        }
    }
    
    // Format date for display
    function formatDate(start, end, allDay) {
        // Convert UTC database dates to local timezone for display
        let startDate, endDate;
        if (window.TimezoneHelper && window.TimezoneHelper.parseMySQLDateTime) {
            startDate = window.TimezoneHelper.parseMySQLDateTime(start);
            endDate = window.TimezoneHelper.parseMySQLDateTime(end);
        } else {
            startDate = new Date(start + (start.includes('T') ? '' : 'T00:00:00Z'));
            endDate = new Date(end + (end.includes('T') ? '' : 'T00:00:00Z'));
        }
        
        if (allDay) {
            // Format as date only
            const options = { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' };
            
            if (isSameDay(startDate, endDate)) {
                return startDate.toLocaleDateString('en-US', options);
            } else {
                return `${startDate.toLocaleDateString('en-US', options)} - ${endDate.toLocaleDateString('en-US', options)}`;
            }
        } else {
            // Format with time
            const dateOptions = { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' };
            const timeOptions = { hour: 'numeric', minute: '2-digit', hour12: true };
            
            if (isSameDay(startDate, endDate)) {
                return `${startDate.toLocaleDateString('en-US', dateOptions)} ${startDate.toLocaleTimeString('en-US', timeOptions)} - ${endDate.toLocaleTimeString('en-US', timeOptions)}`;
            } else {
                return `${startDate.toLocaleDateString('en-US', dateOptions)} ${startDate.toLocaleTimeString('en-US', timeOptions)} - ${endDate.toLocaleDateString('en-US', dateOptions)} ${endDate.toLocaleTimeString('en-US', timeOptions)}`;
            }
        }
    }
    
    // Check if two dates are the same day
    function isSameDay(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }
    
    // Highlight an event in the list
    function highlightEventInList(eventId) {
        // Remove highlight from all events
        document.querySelectorAll('.event-item').forEach(item => {
            item.classList.remove('active', 'bg-light');
        });
        
        // Add highlight to the selected event (no scroll)
        const eventItem = document.querySelector(`.event-item[data-event-id="${eventId}"]`);
        if (eventItem) {
            eventItem.classList.add('active', 'bg-light');
        }
    }
    
    // Navigate directly to event details page (legacy function kept for compatibility)
    function showEventDetails(event) {
        window.location.href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
    }
    
    // Initialize event listeners
    function initEventListeners() {
        // Make the updateMapWithLocation function available to the filter system
        window.updateMapWithLocation = function(lat, lng) {
            // Remove any existing search markers and circles
            removeSearchMarkers();
            
            const filters = getActiveFilters();
            
            // Add search marker and radius circle based on provider
            switch (mapProvider) {
                case 'google':
                    // Center map on location
                    map.setCenter({lat: lat, lng: lng});
                    map.setZoom(10);
                    
                    // Add a special marker for the searched location
                    window.searchMarker = new google.maps.Marker({
                        position: {lat: lat, lng: lng},
                        map: map,
                        title: 'Searched Location',
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            fillColor: '#FF0000',
                            fillOpacity: 0.6,
                            strokeWeight: 2,
                            strokeColor: '#FFFFFF',
                            scale: 12
                        },
                        zIndex: 1000
                    });
                    
                    // Add circle to show radius
                    window.radiusCircle = new google.maps.Circle({
                        map: map,
                        radius: filters.radius * 1609.34, // Convert miles to meters
                        fillColor: '#FF0000',
                        fillOpacity: 0.1,
                        strokeColor: '#FF0000',
                        strokeOpacity: 0.8,
                        strokeWeight: 1,
                        center: {lat: lat, lng: lng}
                    });
                    break;
                
                case 'openstreetmap':
                    // Center map on location
                    map.setView([lat, lng], 10);
                    
                    // Add a special marker for the searched location
                    window.searchMarker = L.circleMarker([lat, lng], {
                        radius: 8,
                        fillColor: '#FF0000',
                        color: '#FFFFFF',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.6
                    }).addTo(map);
                    
                    // Add circle to show radius
                    window.radiusCircle = L.circle([lat, lng], {
                        radius: filters.radius * 1609.34, // Convert miles to meters
                        fillColor: '#FF0000',
                        fillOpacity: 0.1,
                        color: '#FF0000',
                        weight: 1
                    }).addTo(map);
                    break;
                
                case 'mapbox':
                    // Center map on location
                    map.flyTo({
                        center: [lng, lat],
                        zoom: 10
                    });
                    
                    // Add a special marker for the searched location
                    const el = document.createElement('div');
                    el.className = 'mapbox-search-marker';
                    el.style.backgroundColor = '#FF0000';
                    el.style.width = '16px';
                    el.style.height = '16px';
                    el.style.borderRadius = '50%';
                    el.style.border = '2px solid #FFFFFF';
                    
                    window.searchMarker = new mapboxgl.Marker(el)
                        .setLngLat([lng, lat])
                        .addTo(map);
                    
                    // Add circle to show radius (using a custom layer)
                    // This is more complex for Mapbox and would require a custom layer
                    break;
                
                case 'here':
                    // Center map on location
                    map.setCenter({lat: lat, lng: lng});
                    map.setZoom(10);
                    
                    // Add a special marker for the searched location
                    window.searchMarker = new H.map.Circle(
                        {lat: lat, lng: lng},
                        8,
                        {
                            style: {
                                fillColor: '#FF0000',
                                strokeColor: '#FFFFFF',
                                lineWidth: 2,
                                fillOpacity: 0.6
                            }
                        }
                    );
                    map.addObject(window.searchMarker);
                    
                    // Add circle to show radius
                    window.radiusCircle = new H.map.Circle(
                        {lat: lat, lng: lng},
                        filters.radius * 1609.34, // Convert miles to meters
                        {
                            style: {
                                fillColor: '#FF0000',
                                strokeColor: '#FF0000',
                                lineWidth: 1,
                                fillOpacity: 0.1
                            }
                        }
                    );
                    map.addObject(window.radiusCircle);
                    break;
            }
        };
    }
    
    // Update calendar filters based on checkboxes (delegated to calendar-filters.js)
    function updateCalendarFilters() {
        // This function is now handled by the calendar-filters.js system
        // Just ensure the global filter system is aware of changes
        if (window.calendarFilters && typeof window.calendarFilters.updateCalendarFilters === 'function') {
            window.calendarFilters.updateCalendarFilters();
        }
    }
    
    // Search for a location
    function searchLocation() {
        const locationInput = document.getElementById('location-search').value;
        
        if (!locationInput) {
            return;
        }
        
        // Remove any existing search markers and circles
        removeSearchMarkers();
        
        geocoder.geocode({ address: locationInput }, function(results, status) {
            const okStatus = mapProvider === 'google' ? google.maps.GeocoderStatus.OK : 
                            (mapProvider === 'openstreetmap' ? 'OK' : 
                            (mapProvider === 'mapbox' ? 'OK' : 'OK'));
            
            if (status === okStatus && results[0]) {
                const location = results[0].geometry.location;
                const lat = typeof location.lat === 'function' ? location.lat() : location.lat;
                const lng = typeof location.lng === 'function' ? location.lng() : location.lng;
                
                // Update filters with location
                const filters = getActiveFilters();
                filters.lat = lat;
                filters.lng = lng;
                
                // Add search marker and radius circle based on provider
                switch (mapProvider) {
                    case 'google':
                        // Center map on location
                        map.setCenter(location);
                        map.setZoom(10);
                        
                        // Add a special marker for the searched location
                        window.searchMarker = new google.maps.Marker({
                            position: location,
                            map: map,
                            title: 'Searched Location',
                            icon: {
                                path: google.maps.SymbolPath.CIRCLE,
                                fillColor: '#FF0000',
                                fillOpacity: 0.6,
                                strokeWeight: 2,
                                strokeColor: '#FFFFFF',
                                scale: 12
                            },
                            zIndex: 1000
                        });
                        
                        // Add circle to show radius
                        window.radiusCircle = new google.maps.Circle({
                            map: map,
                            radius: filters.radius * 1609.34, // Convert miles to meters
                            fillColor: '#FF0000',
                            fillOpacity: 0.1,
                            strokeColor: '#FF0000',
                            strokeOpacity: 0.8,
                            strokeWeight: 1,
                            center: location
                        });
                        break;
                    
                    case 'openstreetmap':
                        // Center map on location
                        map.setView([lat, lng], 10);
                        
                        // Add a special marker for the searched location
                        window.searchMarker = L.circleMarker([lat, lng], {
                            radius: 8,
                            fillColor: '#FF0000',
                            color: '#FFFFFF',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.6
                        }).addTo(map);
                        
                        // Add circle to show radius
                        window.radiusCircle = L.circle([lat, lng], {
                            radius: filters.radius * 1609.34, // Convert miles to meters
                            fillColor: '#FF0000',
                            fillOpacity: 0.1,
                            color: '#FF0000',
                            weight: 1
                        }).addTo(map);
                        break;
                    
                    case 'mapbox':
                        // Center map on location
                        map.flyTo({
                            center: [lng, lat],
                            zoom: 10
                        });
                        
                        // Add a special marker for the searched location
                        const el = document.createElement('div');
                        el.className = 'mapbox-search-marker';
                        el.style.backgroundColor = '#FF0000';
                        el.style.width = '16px';
                        el.style.height = '16px';
                        el.style.borderRadius = '50%';
                        el.style.border = '2px solid white';
                        
                        window.searchMarker = new mapboxgl.Marker(el)
                            .setLngLat([lng, lat])
                            .addTo(map);
                        
                        // Add circle to show radius (using a custom layer)
                        if (!map.getSource('radius-circle')) {
                            map.addSource('radius-circle', {
                                type: 'geojson',
                                data: {
                                    type: 'Feature',
                                    geometry: {
                                        type: 'Point',
                                        coordinates: [lng, lat]
                                    },
                                    properties: {
                                        radius: filters.radius * 1609.34 // Convert miles to meters
                                    }
                                }
                            });
                            
                            map.addLayer({
                                id: 'radius-circle',
                                type: 'circle',
                                source: 'radius-circle',
                                paint: {
                                    'circle-radius': ['get', 'radius'],
                                    'circle-color': '#FF0000',
                                    'circle-opacity': 0.1,
                                    'circle-stroke-color': '#FF0000',
                                    'circle-stroke-width': 1
                                }
                            });
                            
                            window.radiusCircle = true;
                        } else {
                            map.getSource('radius-circle').setData({
                                type: 'Feature',
                                geometry: {
                                    type: 'Point',
                                    coordinates: [lng, lat]
                                },
                                properties: {
                                    radius: filters.radius * 1609.34
                                }
                            });
                        }
                        break;
                    
                    case 'here':
                        // Center map on location
                        map.setCenter({lat: lat, lng: lng});
                        map.setZoom(10);
                        
                        // Add a special marker for the searched location
                        const searchIcon = new H.map.Icon(
                            '<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">' +
                            '<circle cx="12" cy="12" r="8" fill="#FF0000" stroke="#FFFFFF" stroke-width="2"/>' +
                            '</svg>',
                            {anchor: {x: 12, y: 12}}
                        );
                        
                        window.searchMarker = new H.map.Marker({lat: lat, lng: lng}, {
                            icon: searchIcon,
                            zIndex: 1000
                        });
                        map.addObject(window.searchMarker);
                        
                        // Add circle to show radius
                        window.radiusCircle = new H.map.Circle(
                            {lat: lat, lng: lng},
                            filters.radius * 1609.34, // Convert miles to meters
                            {
                                style: {
                                    fillColor: 'rgba(255, 0, 0, 0.1)',
                                    strokeColor: '#FF0000',
                                    lineWidth: 1
                                }
                            }
                        );
                        map.addObject(window.radiusCircle);
                        break;
                }
                
                // Load events with the new location filter
                loadEvents();
            } else {
                alert('Location not found. Please try a different search.');
            }
        });
    }
    
    // Remove search markers and circles
    function removeSearchMarkers() {
        if (window.searchMarker) {
            switch (mapProvider) {
                case 'google':
                    window.searchMarker.setMap(null);
                    if (window.radiusCircle) {
                        window.radiusCircle.setMap(null);
                    }
                    break;
                case 'openstreetmap':
                    map.removeLayer(window.searchMarker);
                    if (window.radiusCircle) {
                        map.removeLayer(window.radiusCircle);
                    }
                    break;
                case 'mapbox':
                    window.searchMarker.remove();
                    if (window.radiusCircle && map.getLayer('radius-circle')) {
                        map.removeLayer('radius-circle');
                        map.removeSource('radius-circle');
                    }
                    break;
                case 'here':
                    map.removeObject(window.searchMarker);
                    if (window.radiusCircle) {
                        map.removeObject(window.radiusCircle);
                    }
                    break;
            }
            window.searchMarker = null;
            window.radiusCircle = null;
        }
    }
    
    // Reset all filters
    function resetFilters() {
        // Use the reset filters function from the filter system if available
        if (window.calendarFilters && typeof window.calendarFilters.resetFilters === 'function') {
            window.calendarFilters.resetFilters();
            
            // Remove any search markers and circles
            removeSearchMarkers();
            
            // Reset map view based on provider
            const defaultLat = <?php echo $data['mapSettings']['default_lat']; ?>;
            const defaultLng = <?php echo $data['mapSettings']['default_lng']; ?>;
            const defaultZoom = <?php echo $data['mapSettings']['default_zoom']; ?>;
            
            switch (mapProvider) {
                case 'google':
                    map.setCenter({ lat: defaultLat, lng: defaultLng });
                    map.setZoom(defaultZoom);
                    break;
                case 'openstreetmap':
                    map.setView([defaultLat, defaultLng], defaultZoom);
                    break;
                case 'mapbox':
                    map.flyTo({
                        center: [defaultLng, defaultLat],
                        zoom: defaultZoom
                    });
                    break;
                case 'here':
                    map.setCenter({ lat: defaultLat, lng: defaultLng });
                    map.setZoom(defaultZoom);
                    break;
            }
            
            return;
        }
        
        // Fallback to original reset logic if filter system is not available
        const filters = getActiveFilters();
        
        // Reset date filters
        document.getElementById('start-date').value = '';
        document.getElementById('end-date').value = '';
        filters.startDate = null;
        filters.endDate = null;
        
        // Reset calendar filters
        document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        updateCalendarFilters();
        
        // Reset state filter
        document.getElementById('state-filter').value = '';
        filters.state = '';
        
        // Reset location search
        document.getElementById('location-search').value = '';
        filters.lat = null;
        filters.lng = null;
        
        // Remove any search markers and circles
        removeSearchMarkers();
        
        // Reset radius filter
        const defaultRadius = <?php echo $data['mapSettings']['filter_radius']; ?>;
        document.getElementById('radius-filter').value = defaultRadius;
        document.getElementById('radius-value').textContent = defaultRadius;
        filters.radius = defaultRadius;
        
        // Reset map view based on provider
        const defaultLat = <?php echo $data['mapSettings']['default_lat']; ?>;
        const defaultLng = <?php echo $data['mapSettings']['default_lng']; ?>;
        const defaultZoom = <?php echo $data['mapSettings']['default_zoom']; ?>;
        
        switch (mapProvider) {
            case 'google':
                map.setCenter({ lat: defaultLat, lng: defaultLng });
                map.setZoom(defaultZoom);
                break;
            case 'openstreetmap':
                map.setView([defaultLat, defaultLng], defaultZoom);
                break;
            case 'mapbox':
                map.flyTo({
                    center: [defaultLng, defaultLat],
                    zoom: defaultZoom
                });
                break;
            case 'here':
                map.setCenter({ lat: defaultLat, lng: defaultLng });
                map.setZoom(defaultZoom);
                break;
        }
        
        // Reload events
        loadEvents();
    }
    
    // Reset map view to current filtered view (without affecting filters)
    function resetMapView() {
        // Remove any search markers and circles
        removeSearchMarkers();
        
        // Close any open info windows
        if (currentInfoWindow) {
            currentInfoWindow.close();
            currentInfoWindow = null;
        }
        
        // If we have markers and bounds, fit to those bounds, otherwise use filtered view settings
        if (markers.length > 0 && currentFilteredView.hasBounds) {
            fitMapToBounds();
            if (DEBUG_MODE) {
                console.log('=== MAP VIEW: Reset to fit current markers bounds ===');
            }
        } else {
            // Reset map view to current filtered view based on provider
            switch (mapProvider) {
                case 'google':
                    map.setCenter({ lat: currentFilteredView.lat, lng: currentFilteredView.lng });
                    map.setZoom(currentFilteredView.zoom);
                    break;
                case 'openstreetmap':
                    map.setView([currentFilteredView.lat, currentFilteredView.lng], currentFilteredView.zoom);
                    break;
                case 'mapbox':
                    map.flyTo({
                        center: [currentFilteredView.lng, currentFilteredView.lat],
                        zoom: currentFilteredView.zoom
                    });
                    break;
                case 'here':
                    map.setCenter({ lat: currentFilteredView.lat, lng: currentFilteredView.lng });
                    map.setZoom(currentFilteredView.zoom);
                    break;
            }
            
            if (DEBUG_MODE) {
                console.log('=== MAP VIEW: Reset to current filtered view ===', currentFilteredView);
            }
        }
    }
    
    // Capture current map view as the filtered view for reset functionality
    function captureCurrentFilteredView() {
        switch (mapProvider) {
            case 'google':
                const center = map.getCenter();
                currentFilteredView = {
                    lat: center.lat(),
                    lng: center.lng(),
                    zoom: map.getZoom(),
                    hasBounds: true
                };
                break;
            case 'openstreetmap':
                const leafletCenter = map.getCenter();
                currentFilteredView = {
                    lat: leafletCenter.lat,
                    lng: leafletCenter.lng,
                    zoom: map.getZoom(),
                    hasBounds: true
                };
                break;
            case 'mapbox':
                const mapboxCenter = map.getCenter();
                currentFilteredView = {
                    lat: mapboxCenter.lat,
                    lng: mapboxCenter.lng,
                    zoom: map.getZoom(),
                    hasBounds: true
                };
                break;
            case 'here':
                const hereCenter = map.getCenter();
                currentFilteredView = {
                    lat: hereCenter.lat,
                    lng: hereCenter.lng,
                    zoom: map.getZoom(),
                    hasBounds: true
                };
                break;
        }
        
        if (DEBUG_MODE) {
            console.log('=== MAP VIEW: Captured current filtered view ===', currentFilteredView);
        }
    }
    
    // Expose loadEvents function globally for the filter system
    window.loadEvents = loadEvents;
    
    // Wait for calendar filters to be ready, then initialize map
    function waitForFiltersAndInitMap() {
        if (window.calendarFilters) {
            // Set the current view to map in the filter system
            window.calendarFilters.activeFilters.currentView = 'map';

            if (DEBUG_MODE) {
                console.log('=== MAP VIEW: Calendar filters ready, initializing map ===');
                console.log('Filter system active filters:', window.calendarFilters.activeFilters);
                console.log('=== MAP VIEW: loadEvents function exposed globally ===');
            }

            // Start loading the appropriate map API
            loadMapAPI();
        } else {
            // Wait a bit more for the filter system to load
            setTimeout(waitForFiltersAndInitMap, 100);
        }
    }
    
    // Load map API based on provider
    function loadMapAPI() {
        switch (mapProvider) {
            case 'google':
                loadGoogleMapsAPI();
                break;
            case 'openstreetmap':
                loadLeafletAPI();
                break;
            case 'mapbox':
                // Mapbox is loaded in the initMapboxMap function
                initMap();
                break;
            case 'here':
                // HERE Maps is loaded in the initHereMap function
                initMap();
                break;
            default:
                // Fallback to OpenStreetMap
                loadLeafletAPI();
        }
    }
    
    // Load Google Maps API with error handling
    function loadGoogleMapsAPI() {
        const apiKey = '<?php echo $data['mapSettings']['api_key']; ?>';
        
        // Check if API key is provided
        if (!apiKey || apiKey.trim() === '') {
            console.warn('Google Maps API key not provided. Falling back to OpenStreetMap.');
            mapProvider = 'openstreetmap';
            loadLeafletAPI();
            return;
        }
        
        const script = document.createElement('script');
        // Use latest stable version (3.60 is current as of 2024) with marker library for AdvancedMarkerElement
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,marker&callback=initMap&v=3.60&loading=async`;
        script.async = true;
        script.defer = true;
        
        // Handle script loading errors
        script.onerror = function() {
            console.error('Failed to load Google Maps API. Falling back to OpenStreetMap.');
            mapProvider = 'openstreetmap';
            loadLeafletAPI();
        };
        
        // Set up global callback
        window.initMap = initMap;
        
        // Handle Google Maps API errors
        window.gm_authFailure = function() {
            console.error('Google Maps API authentication failed. Check your API key and billing settings.');
            mapProvider = 'openstreetmap';
            loadLeafletAPI();
        };
        
        // Additional error handling for CORS issues
        window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.toString().includes('googleapis.com')) {
                console.error('Google Maps CORS/Network Error:', event.reason);
                if (DEBUG_MODE) {
                    console.log('Attempting fallback to OpenStreetMap due to Google Maps network error');
                }
                mapProvider = 'openstreetmap';
                loadLeafletAPI();
            }
        });
        
        // Note: Google Maps may show CORS errors for internal diagnostic calls
        // These are cosmetic and don't affect map functionality
        if (DEBUG_MODE) {
            console.log('Note: Google Maps CORS errors for gen_204 and RPC calls are cosmetic and can be ignored');
        }
        
        document.head.appendChild(script);
        
        // Timeout fallback in case Google Maps doesn't load
        setTimeout(function() {
            if (!window.google || !window.google.maps) {
                console.warn('Google Maps API failed to load within timeout. Falling back to OpenStreetMap.');
                mapProvider = 'openstreetmap';
                loadLeafletAPI();
            }
        }, 10000); // 10 second timeout
    }
    
    // Load Leaflet API for OpenStreetMap
    function loadLeafletAPI() {
        // Load Leaflet CSS
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
        link.crossOrigin = '';
        document.head.appendChild(link);
        
        // Load Leaflet JS
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        script.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=';
        script.crossOrigin = '';
        script.onload = initMap;
        document.head.appendChild(script);
    }
    
    // Initialize pagination event listeners
    function initPaginationEventListeners() {
        // Load saved page size from localStorage
        const savedPageSize = localStorage.getItem('mapViewPageSize');
        if (savedPageSize) {
            pageSize = parseInt(savedPageSize);
        }
        
        // Page size selectors (both top and bottom)
        const pageSizeSelectTop = document.getElementById('page-size-select-top');
        const pageSizeSelectBottom = document.getElementById('page-size-select-bottom');
        
        // Set initial values from saved preference
        if (savedPageSize) {
            pageSizeSelectTop.value = savedPageSize;
            pageSizeSelectBottom.value = savedPageSize;
        }
        
        // Function to handle page size change
        function handlePageSizeChange(newSize) {
            pageSize = parseInt(newSize);
            currentPage = 1; // Reset to first page when changing page size
            
            // Save to localStorage
            localStorage.setItem('mapViewPageSize', pageSize.toString());
            
            // Sync both selectors
            pageSizeSelectTop.value = pageSize;
            pageSizeSelectBottom.value = pageSize;
            
            // Reload events
            loadEvents(1);
        }
        
        // Add event listeners to both selectors
        pageSizeSelectTop.addEventListener('change', function() {
            handlePageSizeChange(this.value);
        });
        
        pageSizeSelectBottom.addEventListener('change', function() {
            handlePageSizeChange(this.value);
        });
        
        // Pagination mode toggle
        const paginationToggle = document.getElementById('pagination-mode-toggle');
        paginationToggle.addEventListener('change', function() {
            paginationMode = this.checked ? 'all_pins' : 'current_page';
            
            // Update toggle label
            const label = this.nextElementSibling;
            label.textContent = this.checked ? 'Show all pins' : 'Current page pins';
            
            if (DEBUG_MODE) {
                console.log('=== MAP VIEW: Pagination mode changed to ===', paginationMode);
            }
            
            // Reload events with new pagination mode
            loadEvents(currentPage);
        });
        
        // Set initial pagination mode
        paginationMode = paginationToggle.checked ? 'all_pins' : 'current_page';
        
        // Reset map view button
        const resetMapButton = document.getElementById('reset-map-view');
        if (resetMapButton) {
            resetMapButton.addEventListener('click', function() {
                resetMapView();
                
                if (DEBUG_MODE) {
                    console.log('=== MAP VIEW: Reset button clicked ===');
                }
            });
        }
        
        if (DEBUG_MODE) {
            console.log('=== MAP VIEW: Pagination event listeners initialized ===');
            console.log('=== MAP VIEW: Initial page size ===', pageSize);
        }
    }
    
    // Initialize pagination event listeners
    initPaginationEventListeners();
    
    // Start the initialization process
    waitForFiltersAndInitMap();
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>