<?php
/**
 * Debug FCM Delivery Chain
 * 
 * Test each step of FCM delivery to find where it breaks
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'helpers/csrf_helper.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die('Please log in first.');
}

$userId = $_SESSION['user_id'];

echo "<h2>🔍 Debug FCM Delivery Chain</h2>";
echo "<p><strong>User ID:</strong> $userId</p>";

try {
    // Get the latest subscription
    $db = new Database();
    $db->query('SELECT * FROM push_subscriptions WHERE user_id = :user_id AND active = 1 ORDER BY created_at DESC LIMIT 1');
    $db->bind(':user_id', $userId);
    $subscription = $db->single();
    
    if (!$subscription) {
        echo "<p style='color: red;'>❌ No active subscriptions found</p>";
        exit;
    }
    
    echo "<h3>1. Subscription Details</h3>";
    echo "<p><strong>ID:</strong> {$subscription->id}</p>";
    echo "<p><strong>Endpoint:</strong> " . substr($subscription->endpoint, 0, 80) . "...</p>";
    echo "<p><strong>Created:</strong> {$subscription->created_at}</p>";
    
    // Extract FCM token
    require_once 'helpers/fcm_v1_helper.php';
    $fcmToken = FCMv1Helper::extractFCMToken($subscription->endpoint);
    
    echo "<p><strong>FCM Token:</strong> " . ($fcmToken ? substr($fcmToken, 0, 40) . '...' : 'NULL') . "</p>";
    
    if (!$fcmToken) {
        echo "<p style='color: red;'>❌ Could not extract FCM token</p>";
        exit;
    }
    
    echo "<h3>2. FCM Service Account Check</h3>";
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    
    if (!file_exists($serviceAccountPath)) {
        echo "<p style='color: red;'>❌ Service account file not found</p>";
        exit;
    }
    
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    echo "<p><strong>Project ID:</strong> {$serviceAccount['project_id']}</p>";
    echo "<p><strong>Client Email:</strong> {$serviceAccount['client_email']}</p>";
    echo "<p style='color: green;'>✅ Service account file valid</p>";
    
    echo "<h3>3. Test FCM API Access</h3>";
    
    $fcm = new FCMv1Helper($serviceAccountPath);
    
    // Test 1: Simple notification
    echo "<h4>Test 1: Basic Notification</h4>";
    $result1 = $fcm->sendNotification(
        $fcmToken, 
        'FCM Test 1',
        'Basic notification test at ' . date('H:i:s')
    );
    
    echo "<p><strong>Result:</strong> " . ($result1 ? '✅ Success' : '❌ Failed') . "</p>";
    
    // Test 2: Notification with data
    echo "<h4>Test 2: Notification with Data</h4>";
    $result2 = $fcm->sendNotification(
        $fcmToken, 
        'FCM Test 2',
        'Notification with data at ' . date('H:i:s'),
        [
            'test' => 'data_test',
            'url' => '/dashboard',
            'timestamp' => (string)time()
        ]
    );
    
    echo "<p><strong>Result:</strong> " . ($result2 ? '✅ Success' : '❌ Failed') . "</p>";
    
    // Test 3: Check if sendDataMessage method exists
    echo "<h4>Test 3: Data-Only Message</h4>";
    if (method_exists($fcm, 'sendDataMessage')) {
        $result3 = $fcm->sendDataMessage($fcmToken, [
            'title' => 'FCM Test 3 - Data Only',
            'body' => 'Data-only message at ' . date('H:i:s'),
            'test' => 'data_only',
            'url' => '/dashboard'
        ]);
        echo "<p><strong>Result:</strong> " . ($result3 ? '✅ Success' : '❌ Failed') . "</p>";
    } else {
        echo "<p><strong>Result:</strong> ⚠️ sendDataMessage method not available</p>";
        
        // Try sending data-only via regular method
        $result3 = $fcm->sendNotification(
            $fcmToken,
            '', // Empty title for data-only
            '', // Empty body for data-only  
            [
                'title' => 'FCM Test 3 - Data Only',
                'body' => 'Data-only message at ' . date('H:i:s'),
                'test' => 'data_only',
                'url' => '/dashboard'
            ]
        );
        echo "<p><strong>Alternative Result:</strong> " . ($result3 ? '✅ Success' : '❌ Failed') . "</p>";
    }
    
    // Test 4: Token validation via simple notification
    echo "<h4>Test 4: Token Validation</h4>";
    
    // Use a simple notification to validate the token
    $validationResult = $fcm->sendNotification(
        $fcmToken,
        'Token Validation Test',
        'Testing if FCM token is valid at ' . date('H:i:s'),
        ['validation' => 'true']
    );
    
    if ($validationResult) {
        echo "<p style='color: green;'>✅ Token is valid - FCM accepted the message</p>";
    } else {
        echo "<p style='color: red;'>❌ Token validation failed - FCM rejected the message</p>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>Invalid FCM token format</li>";
        echo "<li>Token belongs to different Firebase project</li>";
        echo "<li>Token has expired</li>";
        echo "<li>Network/authentication issues</li>";
        echo "</ul>";
    }
    
    echo "<h3>4. Subscription Endpoint Analysis</h3>";
    echo "<p><strong>Full Endpoint:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; word-break: break-all;'>";
    echo htmlspecialchars($subscription->endpoint);
    echo "</pre>";
    
    // Check if endpoint format is correct
    if (strpos($subscription->endpoint, 'https://fcm.googleapis.com/fcm/send/') === 0) {
        echo "<p style='color: green;'>✅ Endpoint format is correct</p>";
        
        $tokenPart = str_replace('https://fcm.googleapis.com/fcm/send/', '', $subscription->endpoint);
        echo "<p><strong>Extracted Token:</strong> " . substr($tokenPart, 0, 50) . "...</p>";
        
        if ($tokenPart === $fcmToken) {
            echo "<p style='color: green;'>✅ Token extraction is correct</p>";
        } else {
            echo "<p style='color: red;'>❌ Token extraction mismatch!</p>";
            echo "<p><strong>Expected:</strong> " . substr($tokenPart, 0, 50) . "...</p>";
            echo "<p><strong>Got:</strong> " . substr($fcmToken, 0, 50) . "...</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Endpoint format is incorrect</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='/debug_notification_delivery.php'>← Back to Delivery Debug</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>FCM Delivery Debug</title>
    <meta name="csrf-token" content="<?php echo generateCsrfToken(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
</body>
</html>