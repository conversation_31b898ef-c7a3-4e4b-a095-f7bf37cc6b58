<!DOCTYPE html>
<html>
<head>
    <title>Fresh FCM Subscription Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .token { word-break: break-all; font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Fresh FCM Subscription Test</h1>
        
        <div class="info status">
            <h3>This test will:</h3>
            <ul>
                <li>Remove any existing push subscription</li>
                <li>Create a completely fresh subscription</li>
                <li>Extract the FCM token</li>
                <li>Test it immediately with FCM v1 API</li>
                <li>Send you a push notification if successful</li>
            </ul>
        </div>
        
        <div id="status" class="status info">
            <strong>Status:</strong> Ready to start
        </div>
        
        <div>
            <button id="startTest" class="btn">🔄 Create Fresh Subscription & Test</button>
            <button id="testPermission" class="btn">🔔 Test Permission Only</button>
            <button id="clearLog" class="btn" onclick="clearLog()">Clear Log</button>
        </div>
        
        <h3>📊 Test Log:</h3>
        <div id="log" class="log"></div>
        
        <script>
            const BASE_URL = 'https://events.rowaneliterides.com';
            let logElement = document.getElementById('log');
            let statusElement = document.getElementById('status');
            let startButton = document.getElementById('startTest');

            // Register service worker first
            async function registerServiceWorker() {
                if ('serviceWorker' in navigator) {
                    try {
                        const registration = await navigator.serviceWorker.register('/sw.js', {
                            scope: '/'
                        });
                        log('✅ Service Worker registered successfully');
                        return registration;
                    } catch (error) {
                        log('❌ Service Worker registration failed: ' + error.message, 'error');
                        throw error;
                    }
                } else {
                    throw new Error('Service Worker not supported');
                }
            }
            
            function log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
                
                if (type === 'error') {
                    logEntry.style.color = 'red';
                } else if (type === 'success') {
                    logEntry.style.color = 'green';
                } else if (type === 'warning') {
                    logEntry.style.color = 'orange';
                }
                
                logElement.appendChild(logEntry);
                logElement.scrollTop = logElement.scrollHeight;
            }
            
            function updateStatus(message, type = 'info') {
                statusElement.className = `status ${type}`;
                statusElement.innerHTML = `<strong>Status:</strong> ${message}`;
            }
            
            function clearLog() {
                logElement.innerHTML = '';
            }
            
            async function createFreshSubscriptionAndTest() {
                try {
                    startButton.disabled = true;
                    updateStatus('Creating fresh subscription...', 'info');

                    log('🚀 Starting fresh subscription test...');

                    // Step 1: Check browser support
                    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
                        throw new Error('Browser does not support push notifications');
                    }
                    log('✅ Browser supports push notifications');

                    // Step 2: Register service worker if needed
                    let registration = await navigator.serviceWorker.getRegistration();
                    if (!registration) {
                        log('⚠️ No service worker found, registering...');
                        registration = await registerServiceWorker();
                        // Wait for service worker to be ready
                        await navigator.serviceWorker.ready;
                    }
                    log('✅ Service worker registration found');
                    
                    // Step 3: Remove existing subscription
                    const existingSub = await registration.pushManager.getSubscription();
                    if (existingSub) {
                        await existingSub.unsubscribe();
                        log('🗑️ Removed existing subscription');
                    } else {
                        log('ℹ️ No existing subscription to remove');
                    }
                    
                    // Step 4: Check current permission
                    log('📋 Current permission: ' + Notification.permission);

                    // Step 5: Request permission with detailed logging
                    log('🔔 Requesting notification permission...');

                    let permission;
                    try {
                        permission = await Notification.requestPermission();
                        log('📋 Permission result: ' + permission);
                    } catch (permError) {
                        log('❌ Permission request failed: ' + permError.message, 'error');
                        throw new Error('Permission request failed: ' + permError.message);
                    }

                    if (permission !== 'granted') {
                        log('❌ Permission denied. Possible reasons:', 'warning');
                        log('   - Browser security settings', 'warning');
                        log('   - Site not served over HTTPS', 'warning');
                        log('   - Browser blocking notifications globally', 'warning');
                        log('   - Incognito mode restrictions', 'warning');

                        // Check if we're in a secure context
                        log('📋 Secure context: ' + window.isSecureContext, 'info');
                        log('📋 Protocol: ' + window.location.protocol, 'info');
                        log('📋 User agent: ' + navigator.userAgent.substring(0, 50) + '...', 'info');

                        throw new Error('Notification permission not granted: ' + permission);
                    }
                    log('✅ Notification permission granted');
                    
                    // Step 5: Get VAPID public key
                    const vapidResponse = await fetch(`${BASE_URL}/api/notifications/vapid-key.php`);
                    const vapidData = await vapidResponse.json();
                    
                    if (!vapidData.success || !vapidData.publicKey) {
                        throw new Error('Could not get VAPID public key');
                    }
                    log('✅ VAPID public key retrieved');
                    
                    // Step 6: Create new subscription
                    const newSubscription = await registration.pushManager.subscribe({
                        userVisibleOnly: true,
                        applicationServerKey: vapidData.publicKey
                    });
                    
                    log('✅ New subscription created');
                    
                    // Step 7: Extract FCM token
                    const endpoint = newSubscription.endpoint;
                    const fcmToken = endpoint.split('/').pop();
                    
                    log(`📱 FCM Token: <span class="token">${fcmToken}</span>`);
                    
                    // Step 8: Send subscription to server
                    const subscribeResponse = await fetch(`${BASE_URL}/notification/subscribe`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            subscription: newSubscription
                        })
                    });
                    
                    if (subscribeResponse.ok) {
                        log('✅ Subscription sent to server');
                    } else {
                        log('⚠️ Failed to send subscription to server', 'warning');
                    }
                    
                    // Step 9: Test with FCM v1 API immediately
                    log('🔄 Testing with FCM v1 API...');
                    
                    const testResponse = await fetch(`${BASE_URL}/test_single_fcm_token.php?test_key=single_fcm_2025&token=${encodeURIComponent(fcmToken)}`);
                    const testResult = await testResponse.text();
                    
                    log('📋 FCM Test Result:');
                    log(testResult);
                    
                    if (testResult.includes('SUCCESS')) {
                        updateStatus('🎉 Fresh subscription working! Check for notification.', 'success');
                        log('🎉 SUCCESS! You should receive a push notification now!', 'success');
                    } else {
                        updateStatus('❌ Test failed - check log for details', 'error');
                        log('❌ Test failed - see details above', 'error');
                    }
                    
                } catch (error) {
                    log(`❌ Error: ${error.message}`, 'error');
                    updateStatus('❌ Error occurred - check log', 'error');
                } finally {
                    startButton.disabled = false;
                }
            }
            
            // Test permission only
            async function testPermissionOnly() {
                try {
                    log('🔔 Testing notification permission...');

                    log('📋 Current permission: ' + Notification.permission);
                    log('📋 Secure context: ' + window.isSecureContext);
                    log('📋 Protocol: ' + window.location.protocol);
                    log('📋 Browser: ' + navigator.userAgent.split(' ').pop());

                    if (Notification.permission === 'denied') {
                        log('❌ Notifications are blocked. To fix:', 'error');
                        log('   1. Click the lock icon (🔒) in address bar', 'info');
                        log('   2. Set Notifications to "Allow"', 'info');
                        log('   3. Reload page and try again', 'info');
                        return;
                    }

                    if (Notification.permission === 'default') {
                        log('⚠️ Requesting permission...', 'warning');
                        const permission = await Notification.requestPermission();
                        log('📋 Result: ' + permission);

                        if (permission === 'granted') {
                            log('✅ Permission granted! You can now test subscriptions.', 'success');
                        } else {
                            log('❌ Permission denied: ' + permission, 'error');
                        }
                    } else {
                        log('✅ Permission already granted!', 'success');
                    }

                } catch (error) {
                    log('❌ Error testing permission: ' + error.message, 'error');
                }
            }

            startButton.addEventListener('click', createFreshSubscriptionAndTest);
            document.getElementById('testPermission').addEventListener('click', testPermissionOnly);

            // Auto-start after 2 seconds
            setTimeout(() => {
                log('ℹ️ Click "Test Permission Only" first, then "Create Fresh Subscription & Test"');
            }, 1000);
        </script>
    </div>
</body>
</html>
