<?php
/**
 * Debug Client-Side Notification Issues
 * 
 * Check browser permissions, service worker, and notification settings
 */

require_once 'config/config.php';
require_once 'helpers/csrf_helper.php';

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    die('Please log in first.');
}

$userId = $_SESSION['user_id'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Notification Debug</title>
    <meta name="csrf-token" content="<?php echo generateCsrfToken(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔍 Client-Side Notification Debug</h2>
        <p><strong>User ID:</strong> <?php echo $userId; ?></p>
        
        <div class="debug-section">
            <h3>1. Browser Support Check</h3>
            <div id="browser-support"></div>
        </div>
        
        <div class="debug-section">
            <h3>2. Notification Permission</h3>
            <div id="notification-permission"></div>
            <button id="request-permission" class="btn btn-primary mt-2" style="display: none;">Request Permission</button>
        </div>
        
        <div class="debug-section">
            <h3>3. Service Worker Status</h3>
            <div id="service-worker-status"></div>
        </div>
        
        <div class="debug-section">
            <h3>4. Firebase Configuration</h3>
            <div id="firebase-status"></div>
        </div>
        
        <div class="debug-section">
            <h3>5. FCM Token Status</h3>
            <div id="fcm-token-status"></div>
            <button id="refresh-token" class="btn btn-secondary mt-2">Refresh FCM Token</button>
        </div>
        
        <div class="debug-section">
            <h3>6. Test Notifications</h3>
            <div id="test-results"></div>
            <button id="test-browser-notification" class="btn btn-success mt-2">Test Browser Notification</button>
            <button id="test-fcm-notification" class="btn btn-warning mt-2">Test FCM Notification</button>
        </div>
        
        <div class="debug-section">
            <h3>7. Console Logs</h3>
            <div id="console-logs" style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; max-height: 300px; overflow-y: auto;"></div>
        </div>
        
        <hr>
        <p>
            <a href="/debug_fcm_sending.php" class="btn btn-link">← FCM Sending Debug</a>
            <a href="/test_push_fix.php" class="btn btn-link">← Push Test</a>
        </p>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>

    <script>
        // Console logging capture
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logs = [];
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateConsoleLogs();
        }
        
        console.log = function(...args) {
            addLog('log', args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addLog('error', args.join(' '));
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addLog('warn', args.join(' '));
            originalWarn.apply(console, args);
        };
        
        function updateConsoleLogs() {
            document.getElementById('console-logs').innerHTML = logs.slice(-20).join('<br>');
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="${type}">${message}</span>`;
        }
        
        // 1. Browser Support Check
        function checkBrowserSupport() {
            let support = [];
            
            if ('serviceWorker' in navigator) {
                support.push('✅ Service Worker supported');
            } else {
                support.push('❌ Service Worker not supported');
            }
            
            if ('Notification' in window) {
                support.push('✅ Notifications supported');
            } else {
                support.push('❌ Notifications not supported');
            }
            
            if ('PushManager' in window) {
                support.push('✅ Push Manager supported');
            } else {
                support.push('❌ Push Manager not supported');
            }
            
            updateStatus('browser-support', support.join('<br>'));
        }
        
        // 2. Notification Permission
        function checkNotificationPermission() {
            const permission = Notification.permission;
            let message = `Current permission: <strong>${permission}</strong><br>`;
            
            switch (permission) {
                case 'granted':
                    message += '<span class="success">✅ Notifications are allowed</span>';
                    break;
                case 'denied':
                    message += '<span class="error">❌ Notifications are blocked</span>';
                    break;
                case 'default':
                    message += '<span class="warning">⚠️ Permission not requested yet</span>';
                    document.getElementById('request-permission').style.display = 'block';
                    break;
            }
            
            updateStatus('notification-permission', message);
        }
        
        // 3. Service Worker Status
        async function checkServiceWorker() {
            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    updateStatus('service-worker-status', 
                        `✅ Service Worker registered<br>` +
                        `Scope: ${registration.scope}<br>` +
                        `State: ${registration.active ? registration.active.state : 'No active worker'}`
                    );
                } else {
                    updateStatus('service-worker-status', '❌ No Service Worker registered', 'error');
                }
            } catch (error) {
                updateStatus('service-worker-status', `❌ Error checking Service Worker: ${error.message}`, 'error');
            }
        }
        
        // 4. Firebase Configuration
        function checkFirebaseConfig() {
            try {
                const firebaseConfig = {
                    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
                    authDomain: "rowaneliterides.firebaseapp.com",
                    projectId: "rowaneliterides",
                    storageBucket: "rowaneliterides.firebasestorage.app",
                    messagingSenderId: "310533125467",
                    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
                };
                
                firebase.initializeApp(firebaseConfig);
                const messaging = firebase.messaging();
                
                updateStatus('firebase-status', '✅ Firebase initialized successfully');
                return messaging;
            } catch (error) {
                updateStatus('firebase-status', `❌ Firebase initialization failed: ${error.message}`, 'error');
                return null;
            }
        }
        
        // 5. FCM Token Status
        async function checkFCMToken(messaging) {
            if (!messaging) {
                updateStatus('fcm-token-status', '❌ Firebase messaging not available', 'error');
                return;
            }
            
            try {
                const token = await messaging.getToken({
                    vapidKey: '<?php echo VAPID_PUBLIC_KEY; ?>'
                });
                
                if (token) {
                    updateStatus('fcm-token-status', 
                        `✅ FCM Token obtained<br>` +
                        `Token: ${token.substring(0, 30)}...`
                    );
                    console.log('FCM Token:', token);
                } else {
                    updateStatus('fcm-token-status', '❌ No FCM token available', 'error');
                }
            } catch (error) {
                updateStatus('fcm-token-status', `❌ Error getting FCM token: ${error.message}`, 'error');
            }
        }
        
        // 6. Test Notifications
        function testBrowserNotification() {
            if (Notification.permission === 'granted') {
                const notification = new Notification('Browser Test', {
                    body: 'This is a direct browser notification test',
                    icon: '/public/images/icon-192x192.png',
                    tag: 'test'
                });
                
                notification.onclick = function() {
                    console.log('Browser notification clicked');
                    notification.close();
                };
                
                updateStatus('test-results', '✅ Browser notification sent');
            } else {
                updateStatus('test-results', '❌ Browser notifications not permitted', 'error');
            }
        }
        
        async function testFCMNotification() {
            try {
                const response = await fetch('/debug_fcm_sending.php');
                if (response.ok) {
                    updateStatus('test-results', '✅ FCM test notification triggered');
                } else {
                    updateStatus('test-results', '❌ FCM test failed', 'error');
                }
            } catch (error) {
                updateStatus('test-results', `❌ FCM test error: ${error.message}`, 'error');
            }
        }
        
        // Event Listeners
        document.getElementById('request-permission').addEventListener('click', async () => {
            const permission = await Notification.requestPermission();
            checkNotificationPermission();
        });
        
        document.getElementById('test-browser-notification').addEventListener('click', testBrowserNotification);
        document.getElementById('test-fcm-notification').addEventListener('click', testFCMNotification);
        
        document.getElementById('refresh-token').addEventListener('click', async () => {
            const messaging = firebase.messaging();
            await checkFCMToken(messaging);
        });
        
        // Initialize checks
        window.addEventListener('load', async () => {
            console.log('Starting client-side notification debug...');
            
            checkBrowserSupport();
            checkNotificationPermission();
            await checkServiceWorker();
            const messaging = checkFirebaseConfig();
            await checkFCMToken(messaging);
            
            // Set up FCM message listener
            if (messaging) {
                messaging.onMessage((payload) => {
                    console.log('FCM message received:', payload);
                    addLog('fcm', `Message received: ${payload.notification?.title || 'No title'}`);
                    
                    // Show notification manually if needed
                    if (Notification.permission === 'granted') {
                        new Notification(payload.notification?.title || 'FCM Message', {
                            body: payload.notification?.body || 'FCM notification received',
                            icon: payload.notification?.icon || '/public/images/icon-192x192.png'
                        });
                    }
                });
            }
        });
    </script>
</body>
</html>