<?php

/**
 * Email Reminder Management Model
 * Handles email reminder operations for admin email management
 */
class EmailReminderModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Create email reminder
     */
    public function createReminder($messageId, $userId, $reminderDate, $reminderText = '') {
        // First update the message to mark it has a reminder
        $this->db->query("UPDATE messages SET has_reminder = 1, reminder_date = :reminder_date WHERE id = :message_id");
        $this->db->bind(':reminder_date', $reminderDate);
        $this->db->bind(':message_id', $messageId);
        $this->db->execute();
        
        // Then create the reminder record
        $this->db->query("INSERT INTO email_reminders (message_id, user_id, reminder_date, reminder_text) 
                         VALUES (:message_id, :user_id, :reminder_date, :reminder_text)");
        $this->db->bind(':message_id', $messageId);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':reminder_date', $reminderDate);
        $this->db->bind(':reminder_text', $reminderText);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    /**
     * Get reminder by ID
     */
    public function getReminderById($reminderId) {
        $this->db->query("SELECT r.*, m.subject as message_subject, u.name as user_name
                         FROM email_reminders r
                         LEFT JOIN messages m ON r.message_id = m.id
                         LEFT JOIN users u ON r.user_id = u.id
                         WHERE r.id = :reminder_id");
        $this->db->bind(':reminder_id', $reminderId);
        return $this->db->single();
    }
    
    /**
     * Get reminders for user
     */
    public function getUserReminders($userId, $includeCompleted = false) {
        $sql = "SELECT r.*, m.subject as message_subject, m.ticket_number
                FROM email_reminders r
                LEFT JOIN messages m ON r.message_id = m.id
                WHERE r.user_id = :user_id";
        
        if (!$includeCompleted) {
            $sql .= " AND r.is_completed = 0";
        }
        
        $sql .= " ORDER BY r.reminder_date ASC";
        
        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        return $this->db->resultSet();
    }
    
    /**
     * Get due reminders
     */
    public function getDueReminders($userId = null) {
        $sql = "SELECT r.*, m.subject as message_subject, m.ticket_number, u.name as user_name
                FROM email_reminders r
                LEFT JOIN messages m ON r.message_id = m.id
                LEFT JOIN users u ON r.user_id = u.id
                WHERE r.reminder_date <= NOW() AND r.is_completed = 0";
        
        if ($userId) {
            $sql .= " AND r.user_id = :user_id";
        }
        
        $sql .= " ORDER BY r.reminder_date ASC";
        
        $this->db->query($sql);
        if ($userId) {
            $this->db->bind(':user_id', $userId);
        }
        return $this->db->resultSet();
    }
    
    /**
     * Update reminder
     */
    public function updateReminder($reminderId, $reminderDate, $reminderText) {
        $this->db->query("UPDATE email_reminders 
                         SET reminder_date = :reminder_date, reminder_text = :reminder_text
                         WHERE id = :reminder_id");
        $this->db->bind(':reminder_id', $reminderId);
        $this->db->bind(':reminder_date', $reminderDate);
        $this->db->bind(':reminder_text', $reminderText);
        
        return $this->db->execute();
    }
    
    /**
     * Complete reminder
     */
    public function completeReminder($reminderId) {
        // Mark reminder as completed
        $this->db->query("UPDATE email_reminders SET is_completed = 1 WHERE id = :reminder_id");
        $this->db->bind(':reminder_id', $reminderId);
        $this->db->execute();
        
        // Check if this was the last reminder for the message
        $this->db->query("SELECT message_id FROM email_reminders WHERE id = :reminder_id");
        $this->db->bind(':reminder_id', $reminderId);
        $reminder = $this->db->single();
        
        if ($reminder) {
            $this->db->query("SELECT COUNT(*) as count FROM email_reminders 
                             WHERE message_id = :message_id AND is_completed = 0");
            $this->db->bind(':message_id', $reminder->message_id);
            $result = $this->db->single();
            
            // If no more active reminders, update message
            if ($result && $result->count == 0) {
                $this->db->query("UPDATE messages SET has_reminder = 0, reminder_date = NULL WHERE id = :message_id");
                $this->db->bind(':message_id', $reminder->message_id);
                $this->db->execute();
            }
        }
        
        return true;
    }
    
    /**
     * Delete reminder
     */
    public function deleteReminder($reminderId) {
        // Get reminder info first
        $reminder = $this->getReminderById($reminderId);
        if (!$reminder) {
            return false;
        }
        
        // Delete the reminder
        $this->db->query("DELETE FROM email_reminders WHERE id = :reminder_id");
        $this->db->bind(':reminder_id', $reminderId);
        $this->db->execute();
        
        // Check if this was the last reminder for the message
        $this->db->query("SELECT COUNT(*) as count FROM email_reminders WHERE message_id = :message_id");
        $this->db->bind(':message_id', $reminder->message_id);
        $result = $this->db->single();
        
        // If no more reminders, update message
        if ($result && $result->count == 0) {
            $this->db->query("UPDATE messages SET has_reminder = 0, reminder_date = NULL WHERE id = :message_id");
            $this->db->bind(':message_id', $reminder->message_id);
            $this->db->execute();
        }
        
        return true;
    }
    
    /**
     * Get reminder statistics for user
     */
    public function getReminderStats($userId) {
        $this->db->query("SELECT 
                         COUNT(*) as total_reminders,
                         COUNT(CASE WHEN is_completed = 0 THEN 1 END) as active_reminders,
                         COUNT(CASE WHEN reminder_date <= NOW() AND is_completed = 0 THEN 1 END) as overdue_reminders,
                         COUNT(CASE WHEN DATE(reminder_date) = CURDATE() AND is_completed = 0 THEN 1 END) as today_reminders
                         FROM email_reminders 
                         WHERE user_id = :user_id");
        $this->db->bind(':user_id', $userId);
        return $this->db->single();
    }
    
    /**
     * Snooze reminder (postpone by specified hours)
     */
    public function snoozeReminder($reminderId, $hours = 1) {
        $this->db->query("UPDATE email_reminders 
                         SET reminder_date = DATE_ADD(reminder_date, INTERVAL :hours HOUR)
                         WHERE id = :reminder_id");
        $this->db->bind(':reminder_id', $reminderId);
        $this->db->bind(':hours', $hours);
        
        return $this->db->execute();
    }
    
    /**
     * Get upcoming reminders (next 24 hours)
     */
    public function getUpcomingReminders($userId) {
        $this->db->query("SELECT r.*, m.subject as message_subject, m.ticket_number
                         FROM email_reminders r
                         LEFT JOIN messages m ON r.message_id = m.id
                         WHERE r.user_id = :user_id 
                         AND r.is_completed = 0
                         AND r.reminder_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 24 HOUR)
                         ORDER BY r.reminder_date ASC");
        $this->db->bind(':user_id', $userId);
        return $this->db->resultSet();
    }
}
