<?php
/**
 * Desktop Toast Notification Test
 * 
 * This script specifically tests toast notifications on desktop browsers
 */

require_once 'config/config.php';
require_once 'core/Database.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$userId = $_SESSION['user_id'];

// Create a test toast notification if requested
if (isset($_POST['create_toast'])) {
    try {
        $db = new Database();
        $db->query('INSERT INTO user_toast_notifications (user_id, title, message, event_type, is_read, created_at) 
                   VALUES (:user_id, :title, :message, :event_type, 0, NOW())');
        $db->bind(':user_id', $userId);
        $db->bind(':title', 'Desktop Test Toast');
        $db->bind(':message', 'This is a desktop-specific test toast notification created at ' . date('H:i:s'));
        $db->bind(':event_type', 'test');
        
        if ($db->execute()) {
            $success = true;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Desktop Toast Test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-button {
            margin: 10px 5px;
        }
        
        /* Ensure toast container is visible */
        #toast-container {
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 99999 !important;
            pointer-events: none !important;
        }
        
        .toast {
            pointer-events: auto !important;
            margin-bottom: 10px !important;
        }
    </style>
</head>
<body data-debug-mode="true">

<div class="container mt-4">
    <h2>Desktop Toast Notification Test</h2>
    
    <?php if (isset($success)): ?>
        <div class="alert alert-success">✅ Test toast notification created! It should appear in the top-right corner.</div>
    <?php endif; ?>
    
    <?php if (isset($error)): ?>
        <div class="alert alert-danger">❌ Error: <?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-6">
            <h3>Test Actions</h3>
            
            <form method="post">
                <button type="submit" name="create_toast" class="btn btn-primary test-button">
                    Create Desktop Test Toast
                </button>
            </form>
            
            <button onclick="testManualToast()" class="btn btn-success test-button">
                Test Manual Toast (JavaScript)
            </button>
            
            <button onclick="testAPICall()" class="btn btn-info test-button">
                Test API Call
            </button>
            
            <button onclick="forceReload()" class="btn btn-warning test-button">
                Force Notification Reload
            </button>
        </div>
        
        <div class="col-md-6">
            <h3>Debug Information</h3>
            <div id="debug-output" class="debug-info">
                <div>Loading debug information...</div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <h3>What to Look For</h3>
            <ul>
                <li><strong>Toast Container:</strong> Should be created in top-right corner</li>
                <li><strong>Console Messages:</strong> Look for [NotificationManager] messages in browser console (F12)</li>
                <li><strong>API Responses:</strong> Check if /notification/getUnread returns data</li>
                <li><strong>Toast Display:</strong> Notifications should slide in from the right</li>
            </ul>
        </div>
    </div>
</div>

<script>
    const BASE_URL = '<?php echo BASE_URL; ?>';
    let debugOutput = document.getElementById('debug-output');
    
    function addDebugMessage(message) {
        const timestamp = new Date().toLocaleTimeString();
        debugOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        debugOutput.scrollTop = debugOutput.scrollHeight;
    }
    
    function testManualToast() {
        addDebugMessage('Testing manual toast creation...');
        
        if (window.notificationManager) {
            const testNotification = {
                id: Date.now(),
                title: 'Manual Test Toast',
                message: 'This is a manually created toast notification for desktop testing',
                created_at: new Date().toISOString(),
                event_type: 'test'
            };
            
            window.notificationManager.showToastNotifications([testNotification]);
            addDebugMessage('✅ Manual toast created via NotificationManager');
        } else {
            addDebugMessage('❌ NotificationManager not found');
        }
    }
    
    async function testAPICall() {
        addDebugMessage('Testing API call to /notification/getUnread...');
        
        try {
            const response = await fetch(`${BASE_URL}/notification/getUnread`);
            const data = await response.json();
            
            addDebugMessage(`API Response: ${JSON.stringify(data)}`);
            
            if (data.toast && data.toast.length > 0) {
                addDebugMessage(`✅ Found ${data.toast.length} toast notifications`);
                
                if (window.notificationManager) {
                    window.notificationManager.showToastNotifications(data.toast);
                    addDebugMessage('✅ Displayed toast notifications');
                } else {
                    addDebugMessage('❌ NotificationManager not available to display toasts');
                }
            } else {
                addDebugMessage('ℹ️ No toast notifications found');
            }
        } catch (error) {
            addDebugMessage(`❌ API Error: ${error.message}`);
        }
    }
    
    function forceReload() {
        addDebugMessage('Forcing notification reload...');
        
        if (window.notificationManager) {
            window.notificationManager.loadUnreadNotifications();
            addDebugMessage('✅ Notification reload triggered');
        } else {
            addDebugMessage('❌ NotificationManager not found');
        }
    }
    
    // Check system status on load
    document.addEventListener('DOMContentLoaded', function() {
        addDebugMessage('DOM loaded, checking system status...');
        
        // Check if we're on mobile or desktop
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         window.innerWidth <= 768 ||
                         ('ontouchstart' in window);
        
        addDebugMessage(`Device type: ${isMobile ? 'Mobile' : 'Desktop'}`);
        addDebugMessage(`Screen width: ${window.innerWidth}px`);
        addDebugMessage(`User agent: ${navigator.userAgent}`);
        
        // Check for notification manager
        setTimeout(() => {
            if (window.notificationManager) {
                addDebugMessage('✅ NotificationManager found');
                addDebugMessage(`Polling interval: ${window.notificationManager.pollingInterval}ms`);
                addDebugMessage(`Debug mode: ${window.notificationManager.debugMode}`);
            } else {
                addDebugMessage('❌ NotificationManager not found');
            }
            
            // Check for toast container
            const toastContainer = document.getElementById('toast-container');
            if (toastContainer) {
                addDebugMessage('✅ Toast container found');
                addDebugMessage(`Container position: ${getComputedStyle(toastContainer).position}`);
                addDebugMessage(`Container z-index: ${getComputedStyle(toastContainer).zIndex}`);
            } else {
                addDebugMessage('❌ Toast container not found');
            }
        }, 1000);
        
        // Test automatic loading
        setTimeout(() => {
            addDebugMessage('Testing automatic notification loading...');
            testAPICall();
        }, 2000);
    });
</script>

<!-- Include the notification system -->
<script src="<?php echo BASE_URL; ?>/public/js/notifications.js"></script>
<script src="<?php echo BASE_URL; ?>/public/js/desktop-notifications-fix.js"></script>

</body>
</html>