<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Web Push API Test (No Firebase SDK)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Direct Web Push API Test</h1>
    <p>This test bypasses Firebase SDK and uses Web Push API directly to isolate the pushManager error.</p>
    
    <div id="logs"></div>
    
    <button onclick="testDirectWebPush()">Test Direct Web Push API</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <script>
        function log(message, type = 'log') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function urlBase64ToUint8Array(base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding)
                .replace(/-/g, '+')
                .replace(/_/g, '/');
            
            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);
            
            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }
        
        async function testDirectWebPush() {
            log('Starting Direct Web Push API Test (No Firebase SDK)...', 'log');
            
            try {
                // Step 1: Check basic support
                if (!window.isSecureContext) {
                    throw new Error('Not in secure context (HTTPS required)');
                }
                log('✅ Secure context confirmed', 'success');
                
                if (!('serviceWorker' in navigator)) {
                    throw new Error('Service Worker not supported');
                }
                log('✅ Service Worker supported', 'success');
                
                if (!('PushManager' in window)) {
                    throw new Error('Push Manager not supported');
                }
                log('✅ Push Manager supported', 'success');
                
                // Step 2: Check notification permission
                if (Notification.permission !== 'granted') {
                    log('⚠️ Requesting notification permission...', 'warning');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        throw new Error('Notification permission denied');
                    }
                }
                log('✅ Notification permission granted', 'success');
                
                // Step 3: Register/get service worker
                let registration = await navigator.serviceWorker.getRegistration('/');
                if (!registration) {
                    log('⚠️ Registering service worker...', 'warning');
                    registration = await navigator.serviceWorker.register('/sw.js', { scope: '/' });
                }
                
                registration = await navigator.serviceWorker.ready;
                log('✅ Service worker ready', 'success');
                
                // Step 4: Critical test - check pushManager availability
                log('🔍 Checking pushManager availability...', 'log');
                log(`Registration object: ${!!registration}`, 'log');
                log(`Registration.active: ${!!registration.active}`, 'log');
                log(`Registration.pushManager: ${!!registration.pushManager}`, 'log');
                log(`PushManager type: ${typeof registration.pushManager}`, 'log');
                
                if (!registration.pushManager) {
                    throw new Error('PushManager not available on service worker registration - THIS IS THE ERROR!');
                }
                log('✅ PushManager is available', 'success');
                
                // Step 5: Get VAPID key
                log('⚠️ Fetching VAPID key...', 'warning');
                const vapidResponse = await fetch('/api/pwa/vapid-key');
                const vapidData = await vapidResponse.json();
                if (!vapidData.success) {
                    throw new Error('VAPID key not available: ' + vapidData.message);
                }
                log('✅ VAPID key obtained', 'success');
                
                // Step 6: Test pushManager methods
                log('🔍 Testing pushManager methods...', 'log');
                
                try {
                    // Test getSubscription
                    const existingSubscription = await registration.pushManager.getSubscription();
                    log(`✅ getSubscription() works - existing: ${!!existingSubscription}`, 'success');
                    
                    if (existingSubscription) {
                        log('Found existing subscription:', 'log');
                        log(`- Endpoint: ${existingSubscription.endpoint.substring(0, 50)}...`, 'log');
                        log(`- Keys: ${!!existingSubscription.keys}`, 'log');
                    }
                    
                } catch (getSubError) {
                    log(`❌ getSubscription() failed: ${getSubError.message}`, 'error');
                    throw getSubError;
                }
                
                // Step 7: Test subscribe (the critical operation)
                log('🔄 Testing pushManager.subscribe() - THIS IS WHERE FIREBASE FAILS', 'warning');
                
                try {
                    const subscription = await registration.pushManager.subscribe({
                        userVisibleOnly: true,
                        applicationServerKey: urlBase64ToUint8Array(vapidData.publicKey)
                    });
                    
                    log('✅ SUCCESS: pushManager.subscribe() worked!', 'success');
                    log('Subscription details:', 'log');
                    log(`- Endpoint: ${subscription.endpoint.substring(0, 50)}...`, 'log');
                    log(`- Keys available: ${!!subscription.keys}`, 'log');
                    
                    if (subscription.endpoint.includes('fcm.googleapis.com')) {
                        const urlParts = subscription.endpoint.split('/');
                        const token = urlParts[urlParts.length - 1];
                        log(`- Extracted FCM token: ${token.substring(0, 20)}...`, 'log');
                    }
                    
                } catch (subscribeError) {
                    log(`❌ pushManager.subscribe() failed: ${subscribeError.message}`, 'error');
                    log(`Error stack: ${subscribeError.stack}`, 'error');
                    
                    // This is likely where the Firebase SDK is failing
                    if (subscribeError.message.includes('pushManager')) {
                        log('🔍 This is the same error Firebase SDK encounters!', 'warning');
                    }
                    
                    throw subscribeError;
                }
                
                log('✅ All tests passed! The issue is not with pushManager availability.', 'success');
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                log(`Stack trace: ${error.stack}`, 'error');
                
                // Provide specific guidance based on error
                if (error.message.includes('pushManager')) {
                    log('🔍 DIAGNOSIS: The pushManager is undefined when Firebase tries to access it.', 'warning');
                    log('This suggests a timing issue or service worker state problem.', 'warning');
                } else if (error.message.includes('subscribe')) {
                    log('🔍 DIAGNOSIS: pushManager exists but subscribe() fails.', 'warning');
                    log('This could be a VAPID key issue or browser restriction.', 'warning');
                }
            }
        }
        
        // Global error handlers
        window.addEventListener('error', (event) => {
            log(`Global Error: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`Unhandled Promise Rejection: ${event.reason}`, 'error');
            if (event.reason && event.reason.stack) {
                log(`Stack: ${event.reason.stack}`, 'error');
            }
        });
        
        // Auto-run test on load
        document.addEventListener('DOMContentLoaded', () => {
            log('Direct Web Push test page loaded', 'success');
            log('Click "Test Direct Web Push API" to run the test', 'log');
        });
    </script>
</body>
</html>