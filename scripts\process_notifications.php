<?php
/**
 * Notification Processing Cron Script (Scripts Version)
 * 
 * This script processes pending notifications and should be run every 2-5 minutes via cron.
 * This is the version that includes all required classes for EmailService integration.
 * 
 * Usage:
 * */2 * * * * /usr/bin/php /path/to/your/site/scripts/process_notifications.php
 */

// Set the script to run from command line or web
if (php_sapi_name() !== 'cli') {
    // If running via web, check for security key
    $cronKey = $_GET['key'] ?? '';
    $expectedKey = hash('sha256', 'notification_cron_' . gmdate('Y-m-d'));
    
    if ($cronKey !== $expectedKey) {
        http_response_code(403);
        echo "Unauthorized access";
        exit;
    }
}

// Include the application bootstrap with ALL required classes
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/core/Database.php';
require_once dirname(__DIR__) . '/models/NotificationModel.php';
require_once dirname(__DIR__) . '/models/NotificationService.php';
require_once dirname(__DIR__) . '/models/EmailService.php';
require_once dirname(__DIR__) . '/models/SettingsModel.php';

// Set error reporting for cron jobs
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', dirname(__DIR__) . '/logs/cron_errors.log');
}

// Function to log messages
function logMessage($message, $level = 'INFO') {
    $timestamp = gmdate('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
    
    if (DEBUG_MODE) {
        echo $logMessage;
    }
    
    // Ensure logs directory exists
    $logsDir = dirname(__DIR__) . '/logs';
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    
    // Log to file
    $logFile = $logsDir . '/notification_cron.log';
    
    // Check if we can write to the log file
    if (!is_writable($logsDir)) {
        error_log("Cannot write to logs directory: $logsDir");
        return;
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

try {
    logMessage("Starting notification processing cron job");
    logMessage("PHP Version: " . PHP_VERSION);
    logMessage("Memory Limit: " . ini_get('memory_limit'));
    logMessage("Max Execution Time: " . ini_get('max_execution_time'));
    logMessage("Current Working Directory: " . getcwd());
    logMessage("Script Path: " . __FILE__);
    
    // Test class loading
    $requiredClasses = ['Database', 'NotificationModel', 'NotificationService', 'EmailService', 'SettingsModel'];
    foreach ($requiredClasses as $className) {
        if (!class_exists($className)) {
            throw new Exception("Required class '$className' not found");
        }
    }
    logMessage("All required classes loaded successfully");
    
    // Test database connection
    $db = new Database();
    $db->query("SELECT 1");
    $db->single();
    logMessage("Database connection test successful");
    
    // Initialize the notification service
    logMessage("Initializing NotificationService");
    $notificationService = new NotificationService();
    logMessage("NotificationService initialized successfully");
    
    // Check for pending notifications before processing
    $db = new Database();
    $db->query('SELECT COUNT(*) as count FROM notification_queue WHERE status = ?');
    $db->bind(1, 'pending');
    $db->execute();
    $result = $db->single();
    $pendingCount = $result->count ?? 0;
    logMessage("Found $pendingCount pending notifications");
    
    // Initialize results array
    $results = [
        'processed' => 0,
        'sent' => 0,
        'failed' => 0,
        'errors' => []
    ];
    
    if ($pendingCount == 0) {
        logMessage("No pending notifications to process");
    } else {
        // Process pending notifications (limit to 100 per run to avoid timeouts)
        logMessage("Processing up to 100 pending notifications");
        $results = $notificationService->processPendingNotifications(100);
        
        logMessage("Processing complete - Processed: {$results['processed']}, Sent: {$results['sent']}, Failed: {$results['failed']}");
        
        // Log any errors
        if (!empty($results['errors'])) {
            logMessage("Errors encountered during processing:");
            foreach ($results['errors'] as $error) {
                logMessage($error, 'ERROR');
            }
        }
    }
    
    // Clean up old notifications (older than 30 days)
    $db = new Database();
    $db->query('DELETE FROM notification_queue WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)');
    $cleanupResult = $db->execute();
    
    if ($cleanupResult) {
        $affectedRows = $db->rowCount();
        if ($affectedRows > 0) {
            logMessage("Cleaned up $affectedRows old notifications");
        }
    }
    
    // Create heartbeat file to indicate the cron is running
    $heartbeatFile = dirname(__DIR__) . '/logs/notification_cron_heartbeat.json';
    $heartbeatData = [
        'last_run' => gmdate('Y-m-d H:i:s'),
        'status' => 'success',
        'pending_count' => $pendingCount,
        'processed' => $results['processed'],
        'sent' => $results['sent'],
        'failed' => $results['failed'],
        'php_version' => PHP_VERSION,
        'memory_usage' => memory_get_peak_usage(true),
        'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
    ];
    
    file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
    
    logMessage("Notification processing cron job completed successfully");
    logMessage("Heartbeat file path: " . $heartbeatFile);
    logMessage("Heartbeat data: " . json_encode($heartbeatData));
    
    // Verify heartbeat file was created
    if (file_exists($heartbeatFile)) {
        logMessage("Heartbeat file created successfully");
    } else {
        logMessage("Warning: Heartbeat file was not created", 'WARNING');
    }
    
} catch (Exception $e) {
    $errorMessage = "Cron job failed: " . $e->getMessage();
    logMessage($errorMessage, 'ERROR');
    logMessage("Stack trace: " . $e->getTraceAsString(), 'ERROR');
    
    // Create error heartbeat
    $heartbeatFile = dirname(__DIR__) . '/logs/notification_cron_heartbeat.json';
    $heartbeatData = [
        'last_run' => gmdate('Y-m-d H:i:s'),
        'status' => 'error',
        'error' => $e->getMessage(),
        'php_version' => PHP_VERSION,
        'memory_usage' => memory_get_peak_usage(true)
    ];
    
    file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
    
    // Exit with error code
    exit(1);
}

exit(0);
?>
