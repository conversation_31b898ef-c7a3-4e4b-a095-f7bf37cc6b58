<?php
/**
 * Test script to verify ticket number reuse is working correctly
 */

// Start session
session_start();

// Define the application root directory
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Ticket Number Reuse Test</h1>";

echo "<h2>Testing Database Lookup Logic</h2>";

// Test the database lookup logic that should be used in createMessage
function testDatabaseLookup($ticketNumber) {
    try {
        // Load database
        require_once APPROOT . '/core/Database.php';
        $db = new Database();

        // Check if this ticket number already exists in the database
        $db->query("SELECT ticket_number, security_token FROM messages WHERE ticket_number = :ticket_number AND security_token IS NOT NULL LIMIT 1");
        $db->bind(':ticket_number', $ticketNumber);
        $existingMessage = $db->single();

        if ($existingMessage) {
            return [
                'found_in_db' => true,
                'ticket_number' => $existingMessage->ticket_number,
                'security_token' => $existingMessage->security_token
            ];
        } else {
            return [
                'found_in_db' => false,
                'ticket_number' => null,
                'security_token' => null
            ];
        }
    } catch (Exception $e) {
        return [
            'found_in_db' => false,
            'error' => $e->getMessage(),
            'ticket_number' => null,
            'security_token' => null
        ];
    }
}

// Get some actual ticket numbers from the database to test with
try {
    require_once APPROOT . '/core/Database.php';
    $db = new Database();

    // Get some existing tickets from the database
    $db->query("SELECT DISTINCT ticket_number FROM messages WHERE ticket_number IS NOT NULL AND security_token IS NOT NULL ORDER BY created_at DESC LIMIT 5");
    $existingTickets = $db->resultSet();

    $testTickets = [];
    foreach ($existingTickets as $ticket) {
        $testTickets[] = $ticket->ticket_number;
    }

    // Add some test cases that shouldn't exist
    $testTickets[] = 'RER-9999-999-NOEXIST';  // Should not exist
    $testTickets[] = 'INVALID-TICKET';        // Should not exist

} catch (Exception $e) {
    // Fallback test cases if database is not available
    $testTickets = [
        'RER-2025-001',             // Test ticket
        'RER-A25-22-001',           // Test ticket
        'RER-C25-22-001',           // Test ticket
        'RER-9999-999-NOEXIST',     // Should not exist
        'INVALID-TICKET',           // Should not exist
    ];
}

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Ticket Number</th><th>Found in DB?</th><th>DB Ticket Number</th><th>DB Security Token</th><th>Action</th></tr>";

foreach ($testTickets as $ticket) {
    $result = testDatabaseLookup($ticket);

    echo "<tr>";
    echo "<td>" . htmlspecialchars($ticket) . "</td>";

    if (isset($result['error'])) {
        echo "<td style='color: red;'>Error: " . htmlspecialchars($result['error']) . "</td>";
        echo "<td colspan='3'>-</td>";
    } else {
        echo "<td style='color: " . ($result['found_in_db'] ? 'green' : 'blue') . ";'>" .
             ($result['found_in_db'] ? '✓ Yes' : '✗ No') . "</td>";
        echo "<td>" . htmlspecialchars($result['ticket_number'] ?: 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($result['security_token'] ?: 'N/A') . "</td>";

        if ($result['found_in_db']) {
            echo "<td style='color: green;'>Reuse exact same ticket_number and security_token</td>";
        } else {
            echo "<td style='color: blue;'>Generate new ticket and token</td>";
        }
    }
    echo "</tr>";
}

echo "</table>";

echo "<h2>Expected Behavior</h2>";
echo "<ul>";
echo "<li><strong>Found in Database (Green):</strong> When an email reply contains a ticket number that exists in the messages table:</li>";
echo "<ul>";
echo "<li>Query the database for existing ticket_number and security_token</li>";
echo "<li>Pass the exact same values to createEmailMessage()</li>";
echo "<li>Reuse the same ticket number and security token in the new message</li>";
echo "<li>Thread the message with the existing conversation</li>";
echo "</ul>";
echo "<li><strong>Not Found in Database (Blue):</strong> When an email doesn't contain a ticket that exists in the database:</li>";
echo "<ul>";
echo "<li>Generate a new ticket number based on sender role</li>";
echo "<li>Generate a new security token</li>";
echo "<li>Start a new conversation thread</li>";
echo "</ul>";
echo "</ul>";

echo "<h2>Files Modified</h2>";
echo "<ul>";
echo "<li><strong>models/UnifiedMessageModel.php:</strong> Updated createEmailMessage() to accept existing ticket/token parameters</li>";
echo "<li><strong>models/EmailProcessingEngine.php:</strong> Updated createMessage() to check database for existing ticket/token</li>";
echo "</ul>";

echo "<h2>Key Changes</h2>";
echo "<ol>";
echo "<li><strong>UnifiedMessageModel::createEmailMessage():</strong> Added parameters for existingTicketNumber and existingSecurityToken</li>";
echo "<li><strong>EmailProcessingEngine::createMessage():</strong> Added database query to check for existing messages with same ticket_number</li>";
echo "<li><strong>Logic Flow:</strong> If ticket found in database → reuse exact values; otherwise → generate new</li>";
echo "</ol>";

echo "<h2>Testing</h2>";
echo "<p>To test this fix:</p>";
echo "<ol>";
echo "<li>Send an email to the system (will get a new ticket like RER-2025-001-ABC123)</li>";
echo "<li>Reply to that email with the ticket number in the subject</li>";
echo "<li>The reply should reuse the same ticket number and security token</li>";
echo "<li>Both messages should appear in the same conversation thread</li>";
echo "</ol>";

echo "<p><strong>Before Fix:</strong> Every email got a new ticket number</p>";
echo "<p><strong>After Fix:</strong> Replies reuse the existing ticket number and are properly threaded</p>";
?>
