<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Precise Service Worker Fix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .sw-item { padding: 10px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 Precise Service Worker Fix</h2>
        
        <div class="alert alert-danger">
            <h5>⚠️ IMPORTANT:</h5>
            <p>We need to keep <strong>firebase-messaging-sw.js</strong> and remove <strong>sw.js</strong></p>
            <p>Let's be very precise about which one to unregister!</p>
        </div>
        
        <div class="step">
            <h3>Current Service Workers</h3>
            <div id="current-workers">Loading...</div>
            <button id="refresh-list" class="btn btn-info mt-2">Refresh List</button>
        </div>
        
        <div class="step">
            <h3>Re-register FCM Service Worker</h3>
            <div id="fcm-register-status">Ready to register FCM...</div>
            <button id="register-fcm-now" class="btn btn-success mt-2">Register FCM Service Worker</button>
        </div>
        
        <div class="step">
            <h3>Test Notifications</h3>
            <div id="test-status">Ready to test...</div>
            <button id="test-now" class="btn btn-primary mt-2">Test FCM Notification</button>
        </div>
        
        <div class="step">
            <h3>Console Output</h3>
            <div id="console-output" style="background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        const logs = [];
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            document.getElementById('console-output').innerHTML = logs.slice(-20).join('<br>');
            console.log(message);
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="${type}">${message}</span>`;
        }
        
        // Show current service workers with precise controls
        async function showCurrentWorkers() {
            try {
                addLog('Loading current service workers...');
                
                const registrations = await navigator.serviceWorker.getRegistrations();
                addLog(`Found ${registrations.length} service worker registrations`);
                
                let workersHtml = '';
                
                if (registrations.length === 0) {
                    workersHtml = '<div class="alert alert-warning">No service workers found!</div>';
                } else {
                    for (let i = 0; i < registrations.length; i++) {
                        const reg = registrations[i];
                        const scriptURL = reg.active?.scriptURL || reg.installing?.scriptURL || reg.waiting?.scriptURL || 'Unknown';
                        const state = reg.active?.state || reg.installing?.state || reg.waiting?.state || 'Unknown';
                        const scope = reg.scope;
                        
                        const fileName = scriptURL.split('/').pop();
                        const isSwJs = scriptURL.includes('sw.js') && !scriptURL.includes('firebase-messaging-sw.js');
                        const isFcmSw = scriptURL.includes('firebase-messaging-sw.js');
                        
                        let badgeClass = 'secondary';
                        let badgeText = 'Unknown';
                        let actionButton = '';
                        
                        if (isFcmSw) {
                            badgeClass = 'success';
                            badgeText = 'FCM (KEEP)';
                            actionButton = '<button class="btn btn-sm btn-outline-success" disabled>✅ Keep This</button>';
                        } else if (isSwJs) {
                            badgeClass = 'danger';
                            badgeText = 'PWA (REMOVE)';
                            actionButton = `<button class="btn btn-sm btn-danger" onclick="unregisterSpecific(${i})">❌ Remove This</button>`;
                        } else {
                            badgeClass = 'warning';
                            badgeText = 'Other';
                            actionButton = `<button class="btn btn-sm btn-warning" onclick="unregisterSpecific(${i})">⚠️ Remove This</button>`;
                        }
                        
                        workersHtml += `
                            <div class="sw-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${fileName}</strong> 
                                        <span class="badge bg-${badgeClass}">${badgeText}</span>
                                        <br>
                                        <small>State: ${state} | Scope: ${scope}</small>
                                        <br>
                                        <small>URL: ${scriptURL}</small>
                                    </div>
                                    <div>
                                        ${actionButton}
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        addLog(`SW ${i}: ${fileName} - ${state} - ${isFcmSw ? 'FCM' : isSwJs ? 'PWA' : 'Other'}`);
                    }
                }
                
                document.getElementById('current-workers').innerHTML = workersHtml;
                
                // Store registrations globally for unregister function
                window.currentRegistrations = registrations;
                
            } catch (error) {
                addLog('Error loading service workers: ' + error.message);
                document.getElementById('current-workers').innerHTML = '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            }
        }
        
        // Unregister specific service worker
        window.unregisterSpecific = async function(index) {
            try {
                const registration = window.currentRegistrations[index];
                const scriptURL = registration.active?.scriptURL || registration.installing?.scriptURL || registration.waiting?.scriptURL;
                
                addLog(`Unregistering service worker: ${scriptURL}`);
                
                const success = await registration.unregister();
                
                if (success) {
                    addLog('Service worker unregistered successfully');
                    // Refresh the list
                    setTimeout(showCurrentWorkers, 1000);
                } else {
                    addLog('Failed to unregister service worker');
                }
                
            } catch (error) {
                addLog('Error unregistering service worker: ' + error.message);
            }
        };
        
        // Register FCM service worker
        document.getElementById('register-fcm-now').addEventListener('click', async () => {
            try {
                updateStatus('fcm-register-status', '🔄 Registering FCM service worker...', 'warning');
                addLog('Registering FCM service worker...');
                
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                addLog('FCM service worker registered: ' + registration.scope);
                
                // Wait for it to be ready
                await navigator.serviceWorker.ready;
                addLog('FCM service worker is ready');
                
                updateStatus('fcm-register-status', '✅ FCM service worker registered and ready', 'success');
                
                // Refresh the list
                setTimeout(showCurrentWorkers, 1000);
                
            } catch (error) {
                addLog('Error registering FCM service worker: ' + error.message);
                updateStatus('fcm-register-status', '❌ Error: ' + error.message, 'error');
            }
        });
        
        // Test notification
        document.getElementById('test-now').addEventListener('click', async () => {
            try {
                updateStatus('test-status', '🔄 Sending test notification...', 'warning');
                addLog('Sending test FCM notification...');
                
                const response = await fetch('/test_fcm_now_working.php');
                const text = await response.text();
                
                if (text.includes('SUCCESS! Notification sent!')) {
                    updateStatus('test-status', '✅ Test notification sent! Check your device.', 'success');
                    addLog('Test notification sent successfully');
                } else {
                    updateStatus('test-status', '⚠️ Server response unclear', 'warning');
                    addLog('Server response: ' + text.substring(0, 100) + '...');
                }
                
            } catch (error) {
                addLog('Error sending test notification: ' + error.message);
                updateStatus('test-status', '❌ Error: ' + error.message, 'error');
            }
        });
        
        // Refresh button
        document.getElementById('refresh-list').addEventListener('click', showCurrentWorkers);
        
        // Initialize
        window.addEventListener('load', () => {
            addLog('Precise service worker fix tool loaded');
            showCurrentWorkers();
        });
    </script>
</body>
</html>