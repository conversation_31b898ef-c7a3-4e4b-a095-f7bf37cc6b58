<?php
/**
 * Test Toast Timestamp Fix
 * 
 * This script tests if the "NaN days ago" issue is fixed in toast notifications
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🧪 Test Toast Timestamp Fix</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    $db = new Database();
    
    // Test parameters
    $fromUserId = 3;
    $toUserId = 3;
    $subject = "🕐 Timestamp Test";
    $message = "This message tests if toast notifications show proper timestamps instead of 'NaN days ago'.";
    
    echo "<h2>📋 Test Information</h2>";
    echo "<p><strong>Purpose:</strong> Test if toast notifications show proper timestamps</p>";
    echo "<p><strong>Expected Result:</strong> Toast should show 'Just now' or '1 minute ago' instead of 'NaN days ago'</p>";
    
    echo "<h2>🔍 JavaScript File Status</h2>";
    
    // Check if notifications.js has the fix
    $jsFile = 'public/js/notifications.js';
    if (file_exists($jsFile)) {
        $content = file_get_contents($jsFile);
        $hasNullCheck = strpos($content, 'if (!dateString || dateString === null') !== false;
        $hasErrorHandling = strpos($content, 'console.warn(\'Invalid date in formatTimeAgo\'') !== false;
        $hasTimestampFix = strpos($content, 'date.replace(\' \', \'T\') + \'Z\'') !== false;
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Fix Component</th><th>Status</th></tr>";
        echo "<tr><td>Null/undefined check</td><td>" . ($hasNullCheck ? '✅ Present' : '❌ Missing') . "</td></tr>";
        echo "<tr><td>Error handling</td><td>" . ($hasErrorHandling ? '✅ Present' : '❌ Missing') . "</td></tr>";
        echo "<tr><td>MySQL datetime parsing</td><td>" . ($hasTimestampFix ? '✅ Present' : '❌ Missing') . "</td></tr>";
        echo "</table>";
        
        if ($hasNullCheck && $hasErrorHandling && $hasTimestampFix) {
            echo "<p style='color: green;'>✅ All timestamp fixes are present in the JavaScript file</p>";
        } else {
            echo "<p style='color: red;'>❌ Some timestamp fixes are missing from the JavaScript file</p>";
        }
        
        // Check file modification time
        $lastModified = date('Y-m-d H:i:s', filemtime($jsFile));
        echo "<p><strong>File last modified:</strong> {$lastModified}</p>";
        
    } else {
        echo "<p style='color: red;'>❌ notifications.js file not found!</p>";
    }
    
    echo "<h2>📤 Sending Test Message</h2>";
    
    // Send a test message to trigger toast notification
    $result = $messageModel->sendMessage($fromUserId, $toUserId, $subject, $message);
    
    if ($result) {
        echo "<p style='color: green;'>✅ Test message sent successfully! Message ID: {$result}</p>";
        
        // Get the message details to show what timestamp will be used
        $storedMessage = $messageModel->getMessageById($result);
        if ($storedMessage) {
            echo "<h3>📊 Message Timestamp Details</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Field</th><th>Value</th></tr>";
            echo "<tr><td>Created At</td><td>{$storedMessage->created_at}</td></tr>";
            echo "<tr><td>Format</td><td>MySQL DATETIME (YYYY-MM-DD HH:MM:SS)</td></tr>";
            echo "<tr><td>Expected Display</td><td>Just now (or 1-2 minutes ago)</td></tr>";
            echo "</table>";
        }
        
        echo "<h3>🧪 Testing Instructions</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>To test the fix:</strong></p>";
        echo "<ol>";
        echo "<li>🔄 <strong>Refresh your PWA</strong> (pull down to refresh or close/reopen app)</li>";
        echo "<li>👀 <strong>Look for toast notification</strong> that should appear automatically</li>";
        echo "<li>⏰ <strong>Check the timestamp</strong> at the bottom of the toast</li>";
        echo "<li>✅ <strong>Verify it shows</strong> 'Just now' or 'X minutes ago' instead of 'NaN days ago'</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<h3>🔧 Cache Refresh Status</h3>";
        echo "<p>The notifications.js file now includes a cache-busting parameter: <code>?v=" . time() . "</code></p>";
        echo "<p>This forces the PWA to load the updated JavaScript file with the timestamp fix.</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to send test message</p>";
    }
    
    echo "<h2>🎯 Expected vs Actual Results</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Scenario</th><th>Before Fix</th><th>After Fix</th></tr>";
    echo "<tr>";
    echo "<td><strong>Valid timestamp</strong><br><small>2024-12-19 15:30:00</small></td>";
    echo "<td style='color: red;'>❌ 'NaN days ago'</td>";
    echo "<td style='color: green;'>✅ 'Just now' or '2 minutes ago'</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Null timestamp</strong><br><small>null or empty</small></td>";
    echo "<td style='color: red;'>❌ 'NaN days ago'</td>";
    echo "<td style='color: green;'>✅ 'Unknown time'</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><strong>Invalid timestamp</strong><br><small>malformed date</small></td>";
    echo "<td style='color: red;'>❌ 'NaN days ago'</td>";
    echo "<td style='color: green;'>✅ 'Invalid date'</td>";
    echo "</tr>";
    echo "</table>";
    
    echo "<h2>🛠️ Troubleshooting</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
    echo "<h4>If you still see 'NaN days ago':</h4>";
    echo "<ol>";
    echo "<li><strong>Hard refresh:</strong> Close PWA completely and reopen</li>";
    echo "<li><strong>Clear cache:</strong> Go to browser settings → Site settings → Clear storage</li>";
    echo "<li><strong>Check console:</strong> Open dev tools and look for JavaScript errors</li>";
    echo "<li><strong>Verify file:</strong> Check if notifications.js was actually updated on server</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>✅ Success Indicators</h2>";
    echo "<ul>";
    echo "<li>✅ Toast notification appears with proper timestamp</li>";
    echo "<li>✅ No 'NaN days ago' text visible</li>";
    echo "<li>✅ Timestamp shows relative time (e.g., 'Just now', '2 minutes ago')</li>";
    echo "<li>✅ No JavaScript errors in browser console</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
