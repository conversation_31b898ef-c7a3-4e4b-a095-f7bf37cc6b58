<?php

/**
 * Show Role Assignment Cleanup Cron Job
 * 
 * This script automatically removes expired show role assignments
 * Run this script daily via cron job
 * 
 * Example cron entry (run daily at 2 AM):
 * 0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_show_roles.php
 * 
 * @version 1.0
 * @date 2025-07-10
 */

// Include the application bootstrap
require_once dirname(__DIR__) . '/app/bootstrap.php';

// Set up error reporting for cron job
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/logs/cron_errors.log');

// Create log directory if it doesn't exist
$logDir = dirname(__DIR__) . '/logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Log file for this cron job
$logFile = $logDir . '/show_role_cleanup.log';

/**
 * Log message with timestamp
 */
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

/**
 * Send email notification about cleanup results
 */
function sendCleanupNotification($results) {
    try {
        // Get admin email from settings
        $db = new Database();
        $db->query('SELECT setting_value FROM system_settings WHERE setting_key = "admin_email"');
        $adminEmailSetting = $db->single();
        
        if (!$adminEmailSetting) {
            logMessage("Warning: No admin email configured for cleanup notifications");
            return false;
        }
        
        $adminEmail = $adminEmailSetting->setting_value;
        
        // Prepare email content
        $subject = "Show Role Assignment Cleanup Report - " . date('Y-m-d');
        $message = "Show Role Assignment Cleanup Report\n";
        $message .= "Date: " . date('Y-m-d H:i:s') . "\n\n";
        $message .= "Results:\n";
        $message .= "- Expired assignments cleaned: " . $results['expired_assignments'] . "\n";
        $message .= "- Expired requests cleaned: " . $results['expired_requests'] . "\n";
        
        if (!empty($results['errors'])) {
            $message .= "\nErrors encountered:\n";
            foreach ($results['errors'] as $error) {
                $message .= "- " . $error . "\n";
            }
        }
        
        $message .= "\nThis is an automated message from the Events and Shows Management System.";
        
        // Send email
        $headers = "From: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
        $headers .= "Reply-To: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
        
        $success = mail($adminEmail, $subject, $message, $headers);
        
        if ($success) {
            logMessage("Cleanup notification sent to: $adminEmail");
        } else {
            logMessage("Failed to send cleanup notification to: $adminEmail");
        }
        
        return $success;
        
    } catch (Exception $e) {
        logMessage("Error sending cleanup notification: " . $e->getMessage());
        return false;
    }
}

// Start cleanup process
logMessage("Starting show role assignment cleanup process");

try {
    // Initialize the ShowRoleModel
    $showRoleModel = new ShowRoleModel();
    
    // Run cleanup
    $results = $showRoleModel->cleanupExpiredAssignments();
    
    // Log results
    logMessage("Cleanup completed successfully");
    logMessage("Expired assignments cleaned: " . $results['expired_assignments']);
    logMessage("Expired requests cleaned: " . $results['expired_requests']);
    
    if (!empty($results['errors'])) {
        logMessage("Errors encountered during cleanup:");
        foreach ($results['errors'] as $error) {
            logMessage("Error: " . $error);
        }
    }
    
    // Send notification if there were any changes or errors
    if ($results['expired_assignments'] > 0 || $results['expired_requests'] > 0 || !empty($results['errors'])) {
        sendCleanupNotification($results);
    }
    
    // Output results for cron log
    echo "Show role cleanup completed:\n";
    echo "- Expired assignments: " . $results['expired_assignments'] . "\n";
    echo "- Expired requests: " . $results['expired_requests'] . "\n";
    
    if (!empty($results['errors'])) {
        echo "- Errors: " . count($results['errors']) . "\n";
        foreach ($results['errors'] as $error) {
            echo "  * " . $error . "\n";
        }
    }
    
} catch (Exception $e) {
    $errorMessage = "Fatal error during show role cleanup: " . $e->getMessage();
    logMessage($errorMessage);
    
    // Try to send error notification
    try {
        $results = [
            'expired_assignments' => 0,
            'expired_requests' => 0,
            'errors' => [$errorMessage]
        ];
        sendCleanupNotification($results);
    } catch (Exception $notificationError) {
        logMessage("Failed to send error notification: " . $notificationError->getMessage());
    }
    
    // Output error for cron log
    echo "ERROR: " . $errorMessage . "\n";
    exit(1);
}

logMessage("Show role assignment cleanup process completed");

// Optional: Clean up old log entries (keep last 30 days)
try {
    $logLines = file($logFile);
    if ($logLines && count($logLines) > 1000) { // If log file is getting large
        $cutoffDate = date('Y-m-d', strtotime('-30 days'));
        $filteredLines = [];
        
        foreach ($logLines as $line) {
            if (preg_match('/^\[(\d{4}-\d{2}-\d{2})/', $line, $matches)) {
                if ($matches[1] >= $cutoffDate) {
                    $filteredLines[] = $line;
                }
            }
        }
        
        if (count($filteredLines) < count($logLines)) {
            file_put_contents($logFile, implode('', $filteredLines), LOCK_EX);
            logMessage("Cleaned up old log entries, kept " . count($filteredLines) . " recent entries");
        }
    }
} catch (Exception $e) {
    logMessage("Warning: Failed to clean up old log entries: " . $e->getMessage());
}

exit(0);
?>
