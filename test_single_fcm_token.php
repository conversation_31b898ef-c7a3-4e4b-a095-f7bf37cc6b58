<?php
/**
 * Test a single FCM token immediately after creation
 */

// Set proper UTF-8 encoding
header('Content-Type: text/plain; charset=utf-8');

// Prevent direct access without security key
if (!isset($_GET['test_key']) || $_GET['test_key'] !== 'single_fcm_2025') {
    http_response_code(404);
    exit('Not Found');
}

if (!isset($_GET['token'])) {
    exit('No token provided');
}

// Initialize the application properly
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load core configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/helpers/fcm_v1_helper.php';

$fcmToken = $_GET['token'];

echo "=== SINGLE FCM TOKEN TEST ===\n\n";

try {
    echo "1. TOKEN VALIDATION\n";
    echo "   Token: " . substr($fcmToken, 0, 30) . "...\n";
    echo "   Length: " . strlen($fcmToken) . " characters\n";
    
    if (strlen($fcmToken) < 100) {
        echo "   ⚠️ Token seems too short\n";
    } else {
        echo "   ✅ Token length looks good\n";
    }
    
    echo "\n2. INITIALIZING FCM v1 API\n";
    
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    
    if (!file_exists($serviceAccountPath)) {
        echo "   ❌ Service account file not found\n";
        exit;
    }
    
    $fcm = new FCMv1Helper($serviceAccountPath);
    echo "   ✅ FCM v1 Helper initialized\n";
    
    echo "\n3. SENDING TEST NOTIFICATION\n";
    
    $title = "🎉 FRESH TOKEN SUCCESS!";
    $body = "This notification was sent to a brand new FCM token at " . date('H:i:s') . "!";
    $data = [
        'url' => '/',
        'event_id' => '0',
        'event_type' => 'fresh_token_success',
        'timestamp' => (string)time()
    ];
    
    echo "   Title: $title\n";
    echo "   Body: $body\n";
    echo "   Sending...\n";
    
    $result = $fcm->sendNotification($fcmToken, $title, $body, $data);
    
    echo "\n4. RESULT\n";
    
    if ($result['success']) {
        echo "   ✅ SUCCESS! Notification sent successfully!\n";
        echo "   🔔 Check your browser for the notification\n";
        echo "   📱 FCM v1 API is working perfectly\n";
        echo "   🚀 Ready to integrate into NotificationService\n";
        
        echo "\n5. INTEGRATION READY\n";
        echo "   The FCM v1 API solution is working!\n";
        echo "   Next steps:\n";
        echo "   1. Replace VAPID implementation in NotificationService.php\n";
        echo "   2. Use FCMv1Helper for all push notifications\n";
        echo "   3. Clean up old subscriptions regularly\n";
        echo "   4. Test with real notifications\n";
        
    } else {
        echo "   ❌ FAILED: " . $result['error'] . "\n";
        
        if (strpos($result['error'], 'UNREGISTERED') !== false) {
            echo "   📝 Token is still unregistered\n";
            echo "   This might mean:\n";
            echo "   - Token was created with wrong VAPID key\n";
            echo "   - Firebase project mismatch\n";
            echo "   - Token needs time to propagate\n";
        } else {
            echo "   📝 Different error - check Firebase setup\n";
        }
    }
    
    echo "\n6. TROUBLESHOOTING\n";
    
    if (!$result['success']) {
        echo "   If this fresh token still fails:\n";
        echo "   1. Verify Firebase project ID in service account\n";
        echo "   2. Check VAPID keys match Firebase project\n";
        echo "   3. Ensure FCM is enabled in Firebase console\n";
        echo "   4. Try waiting 1-2 minutes for token to propagate\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== END TEST ===\n";
?>
