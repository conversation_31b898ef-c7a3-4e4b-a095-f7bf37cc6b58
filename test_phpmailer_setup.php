<?php
/**
 * Test PHPMailer Setup
 * 
 * This file tests if PHPMailer is properly installed and configured.
 */

// Start session and include necessary files
session_start();

// Define constants
define('APPROOT', dirname(__FILE__));
define('BASE_URL', 'https://events.rowaneliterides.com');
define('DEBUG_MODE', true);

echo "<h1>📧 PHPMailer Setup Test</h1>";

echo "<h2>🔍 Checking PHPMailer Installation</h2>";

// Check if vendor directory exists
$vendorDir = APPROOT . '/vendor';
$autoloadPath = $vendorDir . '/autoload.php';
$phpmailerDir = $vendorDir . '/phpmailer';
$phpmailerSrcDir = $phpmailerDir . '/src';

echo "<p><strong>Vendor directory:</strong> " . $vendorDir . "</p>";
echo "<p><strong>Vendor directory exists:</strong> " . (is_dir($vendorDir) ? '✅ Yes' : '❌ No') . "</p>";

echo "<p><strong>PHPMailer directory:</strong> " . $phpmailerDir . "</p>";
echo "<p><strong>PHPMailer directory exists:</strong> " . (is_dir($phpmailerDir) ? '✅ Yes' : '❌ No') . "</p>";

echo "<p><strong>PHPMailer src directory:</strong> " . $phpmailerSrcDir . "</p>";
echo "<p><strong>PHPMailer src directory exists:</strong> " . (is_dir($phpmailerSrcDir) ? '✅ Yes' : '❌ No') . "</p>";

echo "<p><strong>Autoload file:</strong> " . $autoloadPath . "</p>";
echo "<p><strong>Autoload file exists:</strong> " . (file_exists($autoloadPath) ? '✅ Yes' : '❌ No') . "</p>";

if (is_dir($phpmailerSrcDir)) {
    echo "<h3>📁 PHPMailer Files Found:</h3>";
    $files = scandir($phpmailerSrcDir);
    $phpFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'php';
    });
    
    echo "<ul>";
    foreach ($phpFiles as $file) {
        echo "<li>" . htmlspecialchars($file) . "</li>";
    }
    echo "</ul>";
}

echo "<h2>🧪 Testing Autoloader</h2>";

if (file_exists($autoloadPath)) {
    echo "<p>⏳ Loading autoloader...</p>";
    require_once $autoloadPath;
    echo "<p>✅ Autoloader loaded successfully</p>";
    
    // Test if PHPMailer classes can be loaded
    echo "<h3>Testing PHPMailer Class Loading:</h3>";
    
    $testClasses = [
        'PHPMailer\\PHPMailer\\PHPMailer',
        'PHPMailer\\PHPMailer\\SMTP',
        'PHPMailer\\PHPMailer\\Exception'
    ];
    
    $allClassesAvailable = true;
    
    foreach ($testClasses as $className) {
        $exists = class_exists($className);
        echo "<p><strong>{$className}:</strong> " . ($exists ? '✅ Available' : '❌ Not Available') . "</p>";
        if (!$exists) {
            $allClassesAvailable = false;
        }
    }
    
    if ($allClassesAvailable) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
        echo "<h3>🎉 PHPMailer Successfully Installed!</h3>";
        echo "<p>All PHPMailer classes are available and ready to use.</p>";
        echo "</div>";
        
        echo "<h2>🧪 Testing PHPMailer Instantiation</h2>";
        
        try {
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            echo "<p>✅ PHPMailer instance created successfully</p>";
            echo "<p><strong>PHPMailer Version:</strong> " . $mail::VERSION . "</p>";
            
            // Test basic configuration
            $mail->isSMTP();
            echo "<p>✅ SMTP mode set successfully</p>";
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
            echo "<h3>✅ PHPMailer is Working Perfectly!</h3>";
            echo "<p>Your email system can now use PHPMailer for enhanced functionality.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
            echo "<h3>❌ PHPMailer Instantiation Failed</h3>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
            $allClassesAvailable = false;
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
        echo "<h3>❌ PHPMailer Classes Not Available</h3>";
        echo "<p>Some PHPMailer classes could not be loaded. Please check the installation.</p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
    echo "<h3>❌ Autoloader Not Found</h3>";
    echo "<p>The autoloader file is missing. Please make sure vendor/autoload.php exists.</p>";
    echo "</div>";
    $allClassesAvailable = false;
}

echo "<h2>🔧 Testing EmailService Integration</h2>";

try {
    // Load configuration and required classes
    require_once APPROOT . '/config/config.php';
    require_once APPROOT . '/core/Database.php';
    require_once APPROOT . '/models/SettingsModel.php';
    require_once APPROOT . '/models/EmailService.php';
    
    echo "<p>⏳ Creating EmailService instance...</p>";
    $emailService = new EmailService();
    echo "<p>✅ EmailService created successfully</p>";
    
    // Check if email is configured
    $isConfigured = $emailService->isConfigured();
    echo "<p><strong>Email Configuration Status:</strong> " . ($isConfigured ? '✅ Configured' : '❌ Not Configured') . "</p>";
    
    if ($allClassesAvailable) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
        echo "<h3>🎉 Complete Setup Success!</h3>";
        echo "<p>PHPMailer is installed and EmailService can use it for enhanced email functionality.</p>";
        echo "<p><strong>Benefits you now have:</strong></p>";
        echo "<ul>";
        echo "<li>✅ SMTP authentication support</li>";
        echo "<li>✅ Better email formatting and HTML support</li>";
        echo "<li>✅ Attachment support</li>";
        echo "<li>✅ Improved deliverability</li>";
        echo "<li>✅ Better error handling and debugging</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
    echo "<h3>❌ EmailService Integration Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🚀 Next Steps</h2>";

if ($allClassesAvailable) {
    echo "<ol>";
    echo "<li>✅ PHPMailer is properly installed and working</li>";
    echo "<li>🔧 Configure your SMTP settings at <code>/admin/settings_email</code></li>";
    echo "<li>📧 Test the email functionality</li>";
    echo "<li>🗑️ Delete this test file</li>";
    echo "</ol>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ Setup Issues Found</h3>";
    echo "<p>Please check the following:</p>";
    echo "<ol>";
    echo "<li>Make sure PHPMailer is extracted to <code>/vendor/phpmailer/</code></li>";
    echo "<li>Ensure the <code>src</code> directory exists in <code>/vendor/phpmailer/src/</code></li>";
    echo "<li>Verify that <code>vendor/autoload.php</code> exists</li>";
    echo "<li>Check file permissions</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<h2>📁 Current Directory Structure</h2>";
echo "<pre>";
echo "vendor/\n";
echo "├── autoload.php (✅ Created)\n";
echo "├── phpmailer/\n";
echo "│   ├── src/\n";
echo "│   │   ├── PHPMailer.php\n";
echo "│   │   ├── SMTP.php\n";
echo "│   │   ├── Exception.php\n";
echo "│   │   └── ...\n";
echo "│   └── ...\n";
echo "└── ...\n";
echo "</pre>";

// Self-delete after 5 minutes
if (file_exists(__FILE__) && (time() - filemtime(__FILE__)) > 300) {
    unlink(__FILE__);
}
?>
