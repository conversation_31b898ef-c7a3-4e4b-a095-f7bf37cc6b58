<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase getToken Isolated Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Firebase getToken Isolated Test</h1>
    <p>This test isolates the Firebase getToken call to identify the pushManager error.</p>
    
    <div id="logs"></div>
    
    <button onclick="runIsolatedTest()">Run Isolated Firebase Test</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.14.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.14.0/firebase-messaging-compat.js"></script>
    
    <script>
        function log(message, type = 'log') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        async function runIsolatedTest() {
            log('Starting isolated Firebase getToken test...', 'log');
            
            try {
                // Step 1: Check Firebase availability
                if (typeof firebase === 'undefined') {
                    throw new Error('Firebase not loaded');
                }
                log('✅ Firebase SDK loaded', 'success');
                
                // Step 2: Initialize Firebase
                const firebaseConfig = {
                    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
                    authDomain: "rowaneliterides.firebaseapp.com",
                    projectId: "rowaneliterides",
                    storageBucket: "rowaneliterides.firebasestorage.app",
                    messagingSenderId: "310533125467",
                    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
                };
                
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                log('✅ Firebase initialized', 'success');
                
                // Step 3: Initialize messaging
                const messaging = firebase.messaging();
                log('✅ Firebase messaging initialized', 'success');
                
                // Step 4: Check notification permission
                if (Notification.permission !== 'granted') {
                    log('⚠️ Requesting notification permission...', 'warning');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        throw new Error('Notification permission denied');
                    }
                }
                log('✅ Notification permission granted', 'success');
                
                // Step 5: Register service worker
                if (!('serviceWorker' in navigator)) {
                    throw new Error('Service Worker not supported');
                }
                
                let registration = await navigator.serviceWorker.getRegistration('/');
                if (!registration) {
                    log('⚠️ Registering service worker...', 'warning');
                    registration = await navigator.serviceWorker.register('/sw.js', { scope: '/' });
                }
                log('✅ Service worker registered', 'success');
                
                // Step 6: Wait for service worker to be ready
                registration = await navigator.serviceWorker.ready;
                log('✅ Service worker ready', 'success');
                
                // Step 7: Validate pushManager
                if (!registration.pushManager) {
                    throw new Error('PushManager not available on registration');
                }
                log('✅ PushManager available', 'success');
                
                // Step 8: Get VAPID key
                log('⚠️ Fetching VAPID key...', 'warning');
                const vapidResponse = await fetch('/api/pwa/vapid-key');
                const vapidData = await vapidResponse.json();
                if (!vapidData.success) {
                    throw new Error('VAPID key not available: ' + vapidData.message);
                }
                log('✅ VAPID key obtained', 'success');
                
                // Step 9: The critical call - this is where the error likely occurs
                log('🔄 Calling messaging.getToken() - THIS IS WHERE THE ERROR LIKELY OCCURS', 'warning');
                log('Registration details before getToken call:', 'log');
                log(`- Scope: ${registration.scope}`, 'log');
                log(`- Active: ${!!registration.active}`, 'log');
                log(`- PushManager: ${!!registration.pushManager}`, 'log');
                
                const token = await messaging.getToken({
                    vapidKey: vapidData.publicKey,
                    serviceWorkerRegistration: registration
                });
                
                if (token) {
                    log('✅ SUCCESS: Token obtained: ' + token.substring(0, 20) + '...', 'success');
                } else {
                    log('❌ No token returned from getToken()', 'error');
                }
                
            } catch (error) {
                log('❌ ERROR: ' + error.message, 'error');
                log('Error stack trace:', 'error');
                log('<pre>' + error.stack + '</pre>', 'error');
                
                // Additional debugging for pushManager errors
                if (error.message && error.message.includes('pushManager')) {
                    log('🔍 PushManager error detected - additional debugging:', 'warning');
                    try {
                        const reg = await navigator.serviceWorker.ready;
                        log(`Registration after error: ${!!reg}`, 'log');
                        log(`Active after error: ${reg ? !!reg.active : 'N/A'}`, 'log');
                        log(`PushManager after error: ${reg ? !!reg.pushManager : 'N/A'}`, 'log');
                    } catch (debugError) {
                        log('Could not get debug info: ' + debugError.message, 'error');
                    }
                }
            }
        }
        
        // Global error handlers
        window.addEventListener('error', (event) => {
            log(`Global Error: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`Unhandled Promise Rejection: ${event.reason}`, 'error');
            if (event.reason && event.reason.stack) {
                log(`Stack: ${event.reason.stack}`, 'error');
            }
        });
        
        // Auto-run test on load
        document.addEventListener('DOMContentLoaded', () => {
            log('Isolated test page loaded', 'success');
        });
    </script>
</body>
</html>