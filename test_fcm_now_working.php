<?php
/**
 * Test FCM Notifications - Now Working Version
 * 
 * Test notifications now that FCM token is working
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'helpers/csrf_helper.php';

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    die('Please log in first.');
}

$userId = $_SESSION['user_id'];

echo "<h2>🎉 Test FCM Notifications - Working Version</h2>";
echo "<p><strong>User ID:</strong> $userId</p>";

try {
    // Get the latest subscription (should have the new token)
    $db = new Database();
    $db->query('SELECT * FROM push_subscriptions WHERE user_id = :user_id AND active = 1 ORDER BY created_at DESC LIMIT 1');
    $db->bind(':user_id', $userId);
    $subscription = $db->single();
    
    if (!$subscription) {
        echo "<p style='color: red;'>❌ No active subscriptions found</p>";
        echo "<p>Please refresh the page to register a new subscription.</p>";
        exit;
    }
    
    echo "<h3>Latest Subscription</h3>";
    echo "<p><strong>ID:</strong> {$subscription->id}</p>";
    echo "<p><strong>Endpoint:</strong> " . substr($subscription->endpoint, 0, 50) . "...</p>";
    echo "<p><strong>Created:</strong> {$subscription->created_at}</p>";
    
    // Extract FCM token
    require_once 'helpers/fcm_v1_helper.php';
    $fcmToken = FCMv1Helper::extractFCMToken($subscription->endpoint);
    
    echo "<p><strong>FCM Token:</strong> " . ($fcmToken ? substr($fcmToken, 0, 30) . '...' : 'NULL') . "</p>";
    
    if (!$fcmToken) {
        echo "<p style='color: red;'>❌ Could not extract FCM token</p>";
        exit;
    }
    
    // Test FCM sending
    echo "<h3>Sending Test Notification</h3>";
    
    $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
    if (!file_exists($serviceAccountPath)) {
        echo "<p style='color: red;'>❌ Service account file not found</p>";
        exit;
    }
    
    $fcm = new FCMv1Helper($serviceAccountPath);
    
    $result = $fcm->sendNotification($fcmToken, [
        'title' => '🎉 FCM Test Success!',
        'body' => 'Your push notifications are now working! Sent at ' . date('H:i:s')
    ], [
        'test' => 'success',
        'timestamp' => time(),
        'url' => '/dashboard'
    ]);
    
    if ($result) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ SUCCESS! Notification sent!</strong></p>";
        echo "<p>🔔 Check your device for the notification.</p>";
        
        // Also test the NotificationService directly
        echo "<h3>Testing NotificationService</h3>";
        
        require_once 'models/NotificationService.php';
        $notificationService = new NotificationService();
        
        // Test sending notification directly via NotificationService
        $payload = json_encode([
            'title' => 'NotificationService Test',
            'body' => 'Testing the NotificationService method! 🚀 Sent at ' . date('H:i:s'),
            'data' => [
                'test' => 'notificationservice',
                'url' => '/dashboard',
                'timestamp' => time()
            ]
        ]);
            // Use reflection to access the private sendPushToSubscription method
        try {
            $reflection = new ReflectionClass($notificationService);
            $method = $reflection->getMethod('sendPushToSubscription');
            $method->setAccessible(true);
            
            $serviceResult = $method->invoke($notificationService, $subscription, $payload);
    
            
            if ($serviceResult) {
                echo "<p style='color: green;'><strong>✅ NotificationService test successful!</strong></p>";
                echo "<p>🔔 You should receive a second notification via NotificationService.</p>";
            } else {
                echo "<p style='color: red;'><strong>❌ NotificationService test failed</strong></p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'><strong>NotificationService Error:</strong> " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ Failed to send notification</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/debug_client_notifications.php'>← Back to Client Debug</a></p>";
echo "<p><a href='/test_push_fix.php'>← Back to Push Test</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>FCM Test - Working</title>
    <meta name="csrf-token" content="<?php echo generateCsrfToken(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <script>
        // Listen for FCM messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', event => {
                console.log('Service Worker message:', event.data);
                
                if (event.data && event.data.type === 'FCM_MESSAGE') {
                    console.log('FCM message received in page:', event.data.payload);
                }
            });
        }
        
        // Set up Firebase messaging to listen for foreground messages
        if (typeof firebase !== 'undefined') {
            const firebaseConfig = {
                apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
                authDomain: "rowaneliterides.firebaseapp.com",
                projectId: "rowaneliterides",
                storageBucket: "rowaneliterides.firebasestorage.app",
                messagingSenderId: "310533125467",
                appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
            };
            
            if (!firebase.apps.length) {
                firebase.initializeApp(firebaseConfig);
                
                const messaging = firebase.messaging();
                
                messaging.onMessage((payload) => {
                    console.log('Foreground FCM message received:', payload);
                    
                    // Show notification manually for foreground messages
                    if (Notification.permission === 'granted') {
                        new Notification(payload.notification?.title || 'New Message', {
                            body: payload.notification?.body || 'You have a new notification',
                            icon: payload.notification?.icon || '/public/images/icon-192x192.png'
                        });
                    }
                });
            }
        }
    </script>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>
</body>
</html>