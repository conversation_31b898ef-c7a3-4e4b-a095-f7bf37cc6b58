<?php
// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load notification helper
require_once APPROOT . '/helpers/notification_helper.php';

// Send test notifications if requested
if (isset($_POST['send_test'])) {
    $userId = 3; // Test user ID
    
    $toastResult = sendToastNotification($userId, "Frontend Test Toast", "This toast should appear in the browser!");
    $pushResult = sendPushNotification($userId, "Frontend Test Push", "This push should appear in the browser!");
    
    $message = "Notifications sent! Toast: " . ($toastResult ? "✅" : "❌") . " Push: " . ($pushResult ? "✅" : "❌");
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Frontend Notification Test</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-button { 
            background: #007cba; 
            color: white; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover { background: #005a87; }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            background: #f0f8ff; 
            border: 1px solid #007cba; 
        }
        .log { 
            background: #f5f5f5; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-family: monospace; 
            max-height: 300px; 
            overflow-y: auto; 
        }
        
        /* Toast notification styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        }
        
        .toast-notification {
            margin-bottom: 10px;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <h1>🔔 Frontend Notification Test</h1>
    
    <?php if (isset($message)): ?>
        <div class="status"><?php echo $message; ?></div>
    <?php endif; ?>
    
    <div class="status">
        <h3>📊 Test Status</h3>
        <p id="notification-status">Initializing notification system...</p>
        <p id="api-status">Testing API connection...</p>
        <p id="toast-count">Toast notifications: <span id="toast-count-value">0</span></p>
        <p id="push-count">Push notifications: <span id="push-count-value">0</span></p>
    </div>
    
    <h2>🧪 Test Actions</h2>
    
    <form method="post" style="display: inline;">
        <button type="submit" name="send_test" class="test-button">📤 Send Test Notifications</button>
    </form>
    
    <button onclick="testAPI()" class="test-button">🔄 Test API Manually</button>
    <button onclick="forceReload()" class="test-button">⚡ Force Reload Notifications</button>
    <button onclick="showTestToast()" class="test-button">🍞 Show Test Toast</button>
    
    <h2>📝 Debug Log</h2>
    <div id="debug-log" class="log">
        <div>Initializing...</div>
    </div>
    
    <div id="toast-container" class="toast-container"></div>
    
    <script>
        // Set BASE_URL for the notification system
        window.BASE_URL = '<?php echo BASE_URL; ?>';
        
        // Debug logging
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[NotificationTest] ${message}`);
        }
        
        // Test API endpoint directly
        async function testAPI() {
            log('Testing /notification/getUnread endpoint...');
            
            try {
                const response = await fetch(`${window.BASE_URL}/notification/getUnread`);
                const data = await response.json();
                
                log(`API Response: ${JSON.stringify(data)}`);
                
                document.getElementById('toast-count-value').textContent = data.toast ? data.toast.length : 0;
                document.getElementById('push-count-value').textContent = data.push ? data.push.length : 0;
                
                document.getElementById('api-status').innerHTML = '✅ API working correctly';
                
                // Show notifications if any
                if (data.toast && data.toast.length > 0) {
                    log(`Found ${data.toast.length} toast notifications`);
                    showToastNotifications(data.toast);
                }
                
            } catch (error) {
                log(`API Error: ${error.message}`);
                document.getElementById('api-status').innerHTML = '❌ API error: ' + error.message;
            }
        }
        
        // Force reload notifications using notification manager
        function forceReload() {
            log('Force reloading notifications...');
            
            if (window.notificationManager) {
                window.notificationManager.loadUnreadNotifications();
                log('Called notificationManager.loadUnreadNotifications()');
            } else {
                log('❌ NotificationManager not found - loading manually');
                testAPI();
            }
        }
        
        // Show test toast manually
        function showTestToast() {
            log('Showing manual test toast...');
            
            const notification = {
                id: Date.now(),
                title: 'Manual Test Toast',
                message: 'This is a manually triggered toast notification',
                created_at: new Date().toISOString(),
                event_type: 'test'
            };
            
            showToastNotifications([notification]);
        }
        
        // Simple toast display function
        function showToastNotifications(notifications) {
            const container = document.getElementById('toast-container');
            
            notifications.forEach(notification => {
                // Check if toast already exists
                if (document.querySelector(`[data-notification-id="${notification.id}"]`)) {
                    return;
                }
                
                const toast = document.createElement('div');
                toast.className = 'toast-notification alert alert-info alert-dismissible fade show';
                toast.setAttribute('data-notification-id', notification.id);
                
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body flex-grow-1">
                            <strong>${notification.title}</strong><br>
                            ${notification.message}
                            <small class="text-muted d-block mt-1">
                                ${new Date(notification.created_at).toLocaleString()}
                            </small>
                        </div>
                        <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
                    </div>
                `;
                
                container.appendChild(toast);
                
                log(`Displayed toast: ${notification.title}`);
                
                // Auto-dismiss after 8 seconds
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                        log(`Auto-dismissed toast: ${notification.title}`);
                    }
                }, 8000);
            });
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, initializing tests...');
            
            // Check if notification manager exists
            setTimeout(() => {
                if (window.notificationManager) {
                    log('✅ NotificationManager found');
                    document.getElementById('notification-status').innerHTML = '✅ NotificationManager loaded';
                } else {
                    log('❌ NotificationManager not found');
                    document.getElementById('notification-status').innerHTML = '❌ NotificationManager not loaded';
                }
                
                // Test API after a short delay
                setTimeout(testAPI, 1000);
            }, 500);
        });
    </script>
    
    <!-- Load the notification system -->
    <script src="<?php echo BASE_URL; ?>/public/js/notifications.js"></script>
    
    <!-- Bootstrap CSS for styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
</body>
</html>
