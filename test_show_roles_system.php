<?php
/**
 * Test script for Show Roles System
 * 
 * This script tests the basic functionality of the show roles system
 * to ensure all components are working correctly.
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Auth.php';
require_once APPROOT . '/models/ShowRoleModel.php';
require_once APPROOT . '/models/ShowModel.php';
require_once APPROOT . '/models/UserModel.php';

// Only allow running by admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

echo "<h1>Show Roles System Test</h1>";

try {
    // Test database connection
    echo "<h2>1. Database Connection Test</h2>";
    $db = new Database();
    echo "✅ Database connection successful<br>";
    
    // Test Auth class
    echo "<h2>2. Auth Class Test</h2>";
    $auth = new Auth();
    echo "✅ Auth class loaded successfully<br>";
    echo "User logged in: " . ($auth->isLoggedIn() ? "Yes" : "No") . "<br>";
    echo "User role: " . ($auth->hasRole('admin') ? "Admin" : "Other") . "<br>";
    
    // Test models
    echo "<h2>3. Model Classes Test</h2>";
    
    $showRoleModel = new ShowRoleModel();
    echo "✅ ShowRoleModel loaded successfully<br>";
    
    $showModel = new ShowModel();
    echo "✅ ShowModel loaded successfully<br>";
    
    $userModel = new UserModel();
    echo "✅ UserModel loaded successfully<br>";
    
    // Test database tables
    echo "<h2>4. Database Tables Test</h2>";
    
    // Check if show_role_requests table exists
    $db->query("SHOW TABLES LIKE 'show_role_requests'");
    $result = $db->single();
    if ($result) {
        echo "✅ show_role_requests table exists<br>";
        
        // Check table structure
        $db->query("DESCRIBE show_role_requests");
        $columns = $db->resultSet();
        echo "Table has " . count($columns) . " columns<br>";
    } else {
        echo "❌ show_role_requests table does not exist<br>";
    }
    
    // Check if show_role_assignments table exists
    $db->query("SHOW TABLES LIKE 'show_role_assignments'");
    $result = $db->single();
    if ($result) {
        echo "✅ show_role_assignments table exists<br>";
        
        // Check table structure
        $db->query("DESCRIBE show_role_assignments");
        $columns = $db->resultSet();
        echo "Table has " . count($columns) . " columns<br>";
    } else {
        echo "❌ show_role_assignments table does not exist<br>";
    }
    
    // Check notification_queue table
    $db->query("SHOW TABLES LIKE 'notification_queue'");
    $result = $db->single();
    if ($result) {
        echo "✅ notification_queue table exists<br>";
        
        // Check notification_category enum values
        $db->query("SHOW COLUMNS FROM notification_queue LIKE 'notification_category'");
        $column = $db->single();
        if ($column) {
            echo "notification_category enum: " . $column->Type . "<br>";
        }
    } else {
        echo "❌ notification_queue table does not exist<br>";
    }
    
    // Test basic functionality
    echo "<h2>5. Basic Functionality Test</h2>";
    
    // Get a sample show
    $db->query("SELECT * FROM shows LIMIT 1");
    $show = $db->single();
    
    if ($show) {
        echo "✅ Sample show found: " . htmlspecialchars($show->name) . " (ID: {$show->id})<br>";
        
        // Test getting show roles
        try {
            $roles = $showRoleModel->getShowRoles($show->id);
            echo "✅ getShowRoles() method works - found " . count($roles) . " role assignments<br>";
        } catch (Exception $e) {
            echo "❌ getShowRoles() method failed: " . $e->getMessage() . "<br>";
        }
        
        // Test getting pending requests
        try {
            $requests = $showRoleModel->getPendingRequests($show->id);
            echo "✅ getPendingRequests() method works - found " . count($requests) . " pending requests<br>";
        } catch (Exception $e) {
            echo "❌ getPendingRequests() method failed: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ No shows found in database<br>";
    }
    
    // Test controller loading
    echo "<h2>6. Controller Test</h2>";
    
    if (file_exists(APPROOT . '/controllers/ShowRolesController.php')) {
        echo "✅ ShowRolesController.php file exists<br>";
        
        require_once APPROOT . '/controllers/ShowRolesController.php';
        
        if (class_exists('ShowRolesController')) {
            echo "✅ ShowRolesController class exists<br>";
        } else {
            echo "❌ ShowRolesController class not found<br>";
        }
    } else {
        echo "❌ ShowRolesController.php file not found<br>";
    }
    
    // Test view files
    echo "<h2>7. View Files Test</h2>";
    
    $viewFiles = [
        'views/show_roles/manage.php',
        'views/show_roles/admin_overview.php',
        'views/show_roles/my_requests.php'
    ];
    
    foreach ($viewFiles as $viewFile) {
        if (file_exists(APPROOT . '/' . $viewFile)) {
            echo "✅ {$viewFile} exists<br>";
        } else {
            echo "❌ {$viewFile} not found<br>";
        }
    }
    
    echo "<h2>8. System Status</h2>";
    echo "<div style='padding: 20px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<strong>✅ Show Roles System Test Complete!</strong><br>";
    echo "The system appears to be properly configured and ready for use.";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error During Testing</h2>";
    echo "<div style='padding: 20px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Line:</strong> " . $e->getLine();
    echo "</div>";
}

echo "<br><br><a href='/admin/dashboard'>← Back to Admin Dashboard</a>";
?>
