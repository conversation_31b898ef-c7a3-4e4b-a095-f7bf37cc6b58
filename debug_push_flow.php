<?php
/**
 * Debug the complete push notification flow
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load core classes
require_once APPROOT . '/core/Database.php';

// Load notification helper
require_once APPROOT . '/helpers/notification_helper.php';

echo "<h1>🔍 Push Notification Flow Debug</h1>";

$userId = 3; // Test user ID

try {
    echo "<h2>1. Testing Cron Job</h2>";
    
    // Test the cron job manually
    $cronKey = hash('sha256', 'notification_cron_' . gmdate('Y-m-d'));
    $cronUrl = BASE_URL . "/cron.php?task=process_notifications&key=" . $cronKey;
    
    echo "<h3>🔄 Manual Cron Execution:</h3>";
    echo "<p>Cron URL: <code>$cronUrl</code></p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $cronUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $cronResponse = curl_exec($ch);
    $cronHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $cronError = curl_error($ch);
    curl_close($ch);
    
    if ($cronError) {
        echo "<p>❌ Cron cURL Error: $cronError</p>";
    } else {
        echo "<p>✅ Cron HTTP Status: $cronHttpCode</p>";
        echo "<h4>Cron Response:</h4>";
        echo "<pre>" . htmlspecialchars($cronResponse) . "</pre>";
    }
    
    echo "<h2>2. Testing Direct Push Sending</h2>";
    
    require_once APPROOT . '/models/NotificationService.php';
    require_once APPROOT . '/models/NotificationModel.php';
    
    $notificationService = new NotificationService();
    $notificationModel = new NotificationModel();
    
    // Check push subscriptions first
    $subscriptions = $notificationModel->getUserPushSubscriptions($userId);
    echo "<h3>📱 Push Subscriptions Check:</h3>";
    echo "<p>Found " . count($subscriptions) . " active subscriptions for user $userId</p>";
    
    if (empty($subscriptions)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ No Push Subscriptions Found</h4>";
        echo "<p>The user needs to enable push notifications first:</p>";
        echo "<ol>";
        echo "<li>Visit the main site</li>";
        echo "<li>Allow notifications when prompted by the browser</li>";
        echo "<li>Check that the PWA service worker is registered</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        foreach ($subscriptions as $sub) {
            echo "<p>📱 Subscription ID {$sub->id}: " . substr($sub->endpoint, 0, 50) . "...</p>";
        }
    }
    
    echo "<h3>🔔 Sending Test Push Notification:</h3>";
    
    // Enable debug mode temporarily
    $originalDebug = defined('DEBUG_MODE') ? DEBUG_MODE : false;
    if (!defined('DEBUG_MODE')) {
        define('DEBUG_MODE', true);
    }
    
    // Send push notification with detailed logging
    echo "<p>Calling sendPushNotification()...</p>";
    $result = sendPushNotification($userId, "Debug Test Push", "This is a debug test push notification - " . date('H:i:s'));
    echo "<p>Result: " . ($result ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
    
    echo "<h2>3. Checking Database Storage</h2>";
    
    $db = new Database();
    
    // Check recent push notifications
    echo "<h3>💾 Recent Push Notifications:</h3>";
    $db->query("SELECT * FROM user_push_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
    $db->bind(':user_id', $userId);
    $recentPush = $db->resultSet();
    
    if (!empty($recentPush)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Title</th><th>Message</th><th>Created</th></tr>";
        foreach ($recentPush as $notification) {
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->title}</td>";
            echo "<td>{$notification->message}</td>";
            echo "<td>{$notification->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No push notifications found in database</p>";
    }
    
    // Check notification queue
    echo "<h3>📋 Notification Queue:</h3>";
    $db->query("SELECT * FROM notification_queue WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
    $db->bind(':user_id', $userId);
    $queuedNotifications = $db->resultSet();
    
    if (!empty($queuedNotifications)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Type</th><th>Subject</th><th>Status</th><th>Scheduled</th></tr>";
        foreach ($queuedNotifications as $notification) {
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>{$notification->notification_type}</td>";
            echo "<td>{$notification->subject}</td>";
            echo "<td>{$notification->status}</td>";
            echo "<td>{$notification->scheduled_for}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>ℹ️ No queued notifications found</p>";
    }
    
    echo "<h2>4. Testing Service Worker Registration</h2>";
    
    echo "<h3>🔧 Service Worker Check:</h3>";
    echo "<p>Visit your main site and check the browser console for:</p>";
    echo "<ul>";
    echo "<li>Service worker registration messages</li>";
    echo "<li>Push subscription success/failure</li>";
    echo "<li>VAPID key configuration</li>";
    echo "</ul>";
    
    echo "<h2>5. Manual Push Test</h2>";
    
    if (!empty($subscriptions)) {
        echo "<h3>🧪 Testing Direct Push to Browser:</h3>";
        
        // Test sending to first subscription directly
        $subscription = $subscriptions[0];
        
        $payload = json_encode([
            'title' => 'Manual Push Test',
            'body' => 'This is a manual push test sent directly to your browser at ' . date('H:i:s'),
            'icon' => '/public/images/icons/icon-192x192.png',
            'tag' => 'manual-test-' . time(),
            'data' => ['url' => '/']
        ]);
        
        echo "<p>Sending to endpoint: " . substr($subscription->endpoint, 0, 50) . "...</p>";
        
        $headers = [
            'Content-Type: application/json',
            'TTL: 86400',
            'Urgency: normal'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $subscription->endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $pushResponse = curl_exec($ch);
        $pushHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $pushError = curl_error($ch);
        curl_close($ch);
        
        if ($pushError) {
            echo "<p>❌ Push cURL Error: $pushError</p>";
        } else {
            echo "<p>Push HTTP Status: $pushHttpCode</p>";
            echo "<p>Push Response: " . htmlspecialchars($pushResponse) . "</p>";
            
            if ($pushHttpCode >= 200 && $pushHttpCode < 300) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>✅ Manual Push Sent Successfully!</h4>";
                echo "<p>Check your browser/system notifications now!</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>❌ Manual Push Failed</h4>";
                echo "<p>HTTP Status: $pushHttpCode</p>";
                echo "</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<details><summary>Stack Trace</summary><pre>" . $e->getTraceAsString() . "</pre></details>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Push Notification Flow Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        h1, h2, h3 { color: #333; }
        h1 { border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 12px; }
        details { margin: 10px 0; }
        summary { cursor: pointer; font-weight: bold; }
    </style>
</head>
<body>
    <hr>
    <h2>🎯 What This Debug Shows</h2>
    <ol>
        <li><strong>Cron Job Status:</strong> Whether the notification processing cron is working</li>
        <li><strong>Push Subscriptions:</strong> Whether your browser is registered for push notifications</li>
        <li><strong>Database Storage:</strong> Whether notifications are being stored correctly</li>
        <li><strong>Direct Push Test:</strong> Manual test of sending push to browser endpoint</li>
    </ol>
    
    <h2>🔧 Next Steps</h2>
    <p>Based on the results above:</p>
    <ul>
        <li>If no push subscriptions: Enable notifications on the main site</li>
        <li>If manual push test fails: Check VAPID keys and browser compatibility</li>
        <li>If everything looks good but still no notifications: Check browser notification settings</li>
    </ul>
    
</body>
</html>
