/**
 * BACKUP: Original Firebase Messaging Service Worker
 * Backed up during FCM unification process
 * Date: 2024
 */

/**
 * Firebase Messaging Service Worker
 * 
 * This file is required by Firebase SDK but we redirect all functionality
 * to the main service worker to avoid conflicts.
 */

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
    authDomain: "rowaneliterides.firebaseapp.com",
    projectId: "rowaneliterides",
    storageBucket: "rowaneliterides.firebasestorage.app",
    messagingSenderId: "310533125467",
    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
    console.log('[FCM-SW] Received background message:', payload);
    
    const notificationTitle = payload.notification?.title || 'New Notification';
    const notificationOptions = {
        body: payload.notification?.body || 'You have a new message',
        icon: payload.notification?.icon || '/public/images/icon-192x192.png',
        badge: '/public/images/icon-72x72.png',
        tag: payload.data?.tag || 'general',
        data: payload.data || {},
        actions: [
            {
                action: 'view',
                title: 'View',
                icon: '/public/images/icon-72x72.png'
            },
            {
                action: 'dismiss',
                title: 'Dismiss'
            }
        ],
        requireInteraction: true,
        vibrate: [200, 100, 200]
    };
    
    return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    console.log('[FCM-SW] Notification click received:', event);
    
    event.notification.close();
    
    if (event.action === 'view' || !event.action) {
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
                for (const client of clientList) {
                    if (client.url.includes(self.location.origin) && 'focus' in client) {
                        return client.focus();
                    }
                }
                
                if (clients.openWindow) {
                    const targetUrl = event.notification.data?.url || '/';
                    return clients.openWindow(targetUrl);
                }
            })
        );
    }
});