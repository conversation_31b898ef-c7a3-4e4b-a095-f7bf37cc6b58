<?php
/**
 * Test Push Notification Fix
 * 
 * This script tests the complete push notification flow
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'helpers/csrf_helper.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$userId = $_SESSION['user_id'];

echo "<h2>Push Notification Fix Test</h2>";
echo "<p><strong>User ID:</strong> $userId</p>";

// Create a test push notification if requested
if (isset($_POST['create_push'])) {
    try {
        $db = new Database();
        $db->query('INSERT INTO user_push_notifications (user_id, title, message, event_type, is_read, created_at) 
                   VALUES (:user_id, :title, :message, :event_type, 0, NOW())');
        $db->bind(':user_id', $userId);
        $db->bind(':title', 'Push Fix Test');
        $db->bind(':message', 'This is a test push notification to verify the fix is working - ' . date('H:i:s'));
        $db->bind(':event_type', 'test');
        
        if ($db->execute()) {
            echo "<p style='color: green;'>✅ Test push notification created in database!</p>";
            echo "<p><strong>Next:</strong> The JavaScript should automatically detect this and send it via FCM.</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create test push notification.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

// Check current push notifications
try {
    $db = new Database();
    $db->query('SELECT * FROM user_push_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 10');
    $db->bind(':user_id', $userId);
    $pushNotifications = $db->resultSet();
    
    echo "<h3>Current Push Notifications in Database</h3>";
    echo "<p><strong>Total:</strong> " . count($pushNotifications) . "</p>";
    
    if (!empty($pushNotifications)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Title</th><th>Message</th><th>Read</th><th>Created</th></tr>";
        foreach ($pushNotifications as $push) {
            $readStatus = $push->is_read ? 'Yes' : 'No';
            echo "<tr>";
            echo "<td>{$push->id}</td>";
            echo "<td>" . htmlspecialchars($push->title) . "</td>";
            echo "<td>" . htmlspecialchars(substr($push->message, 0, 50)) . "...</td>";
            echo "<td>{$readStatus}</td>";
            echo "<td>{$push->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check unread count
    $db->query('SELECT COUNT(*) as unread_count FROM user_push_notifications WHERE user_id = :user_id AND is_read = 0');
    $db->bind(':user_id', $userId);
    $unreadResult = $db->single();
    $unreadCount = $unreadResult->unread_count ?? 0;
    
    echo "<p><strong>Unread Count:</strong> $unreadCount</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Push Notification Fix Test</title>
    <meta name="csrf-token" content="<?php echo generateCsrfToken(); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        
        console.log('[PushTest] Page loaded, BASE_URL:', BASE_URL);
        
        // Test the API endpoint directly
        async function testPushAPI() {
            try {
                console.log('[PushTest] Testing /notification/getUnread endpoint...');
                const response = await fetch(`${BASE_URL}/notification/getUnread`);
                const data = await response.json();
                console.log('[PushTest] API Response:', data);
                
                if (data.push && data.push.length > 0) {
                    console.log('[PushTest] ✅ Found', data.push.length, 'push notifications');
                    console.log('[PushTest] Push data:', data.push);
                    
                    // Test the sendPush endpoint
                    const pushIds = data.push.map(p => p.id);
                    console.log('[PushTest] Testing sendPush with IDs:', pushIds);
                    
                    const formData = new FormData();
                    formData.append('notification_ids', JSON.stringify(pushIds));
                    formData.append('csrf_token', document.querySelector('meta[name="csrf-token"]')?.content || '');
                    
                    const sendResponse = await fetch(`${BASE_URL}/notification/sendPush`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    const sendResult = await sendResponse.json();
                    console.log('[PushTest] SendPush response:', sendResult);
                    
                } else {
                    console.log('[PushTest] ❌ No push notifications found');
                }
            } catch (error) {
                console.error('[PushTest] API Error:', error);
            }
        }
        
        // Check if notification manager loads and processes push notifications
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[PushTest] DOM ready');
            
            // Test API immediately
            testPushAPI();
            
            // Check for notification manager
            setTimeout(() => {
                if (window.notificationManager) {
                    console.log('[PushTest] ✅ NotificationManager found');
                    console.log('[PushTest] Forcing notification reload...');
                    window.notificationManager.loadUnreadNotifications();
                } else {
                    console.log('[PushTest] ❌ NotificationManager not found');
                }
            }, 2000);
        });
    </script>
</head>
<body data-debug-mode="true">
    
    <div style="margin: 20px;">
        <form method="post">
            <button type="submit" name="create_push" class="btn btn-primary">Create Test Push Notification</button>
        </form>
        
        <hr>
        
        <h3>Expected Behavior</h3>
        <ol>
            <li>Click "Create Test Push Notification" to add a push notification to the database</li>
            <li>The JavaScript should automatically detect it within 30 seconds (or immediately if you refresh)</li>
            <li>The JavaScript should call the server to send the push notification via FCM</li>
            <li>You should receive a push notification in your browser</li>
            <li>Check the browser console (F12 → Console) for debug messages</li>
        </ol>
        
        <h3>Console Messages to Look For</h3>
        <ul>
            <li><code>[NotificationManager] Received notifications: {push_count: X, ...}</code></li>
            <li><code>[NotificationManager] Triggering server to send X push notifications via FCM...</code></li>
            <li><code>[NotificationManager] Server successfully sent X push notifications</code></li>
            <li><code>[FCM] Push notification received</code> (in service worker)</li>
        </ul>
        
        <p><a href="/admin/dashboard">← Back to Admin Dashboard</a></p>
    </div>
    
    <!-- Include the notification system -->
    <script src="<?php echo BASE_URL; ?>/public/js/notifications.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/desktop-notifications-fix.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/fcm-notifications.js"></script>
</body>
</html>