<?php
/**
 * Auto Reset Tasks Cron Script
 * 
 * This script automatically resets tasks that have been stuck in 'running' status
 * for too long. It should be run via a system cron job every 15 minutes.
 * 
 * Cron schedule example:
 * */15 * * * * /usr/bin/php /path/to/your/site/cron/auto_reset_tasks.php
 */

// Prevent direct web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line');
}

// Include the application bootstrap
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/core/Database.php';

// Set error reporting for cron jobs
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', dirname(__DIR__) . '/logs/cron_errors.log');
}

// Log file path
$logFile = dirname(__DIR__) . '/logs/auto_reset_tasks.log';

// Ensure logs directory exists
$logsDir = dirname($logFile);
if (!is_dir($logsDir)) {
    mkdir($logsDir, 0755, true);
}

// Log function
function logToFile($message, $level = 'INFO') {
    global $logFile;
    $timestamp = gmdate('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    // Write to log file
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running in CLI
    if (php_sapi_name() === 'cli') {
        echo $logMessage;
    }
}

// Start logging
logToFile("Auto Reset Tasks Script Started");
logToFile("PHP Version: " . PHP_VERSION);
logToFile("Script Path: " . __FILE__);

try {
    // Initialize database
    $db = new Database();
    logToFile("Database connection successful");
    
    // Get server time
    $db->query("SELECT NOW() as server_time");
    $result = $db->single();
    $serverTime = $result->server_time;
    logToFile("Server time: {$serverTime}");
    
    // Check if scheduled_tasks table exists
    $db->query("SHOW TABLES LIKE 'scheduled_tasks'");
    if (!$db->single()) {
        logToFile("No scheduled_tasks table found, skipping reset", 'WARNING');
        exit(0);
    }
    
    // Find tasks that have been stuck in 'running' status for more than 30 minutes
    $db->query("SELECT * FROM scheduled_tasks WHERE status = 'running' AND last_run < DATE_SUB(NOW(), INTERVAL 30 MINUTE)");
    $stuckTasks = $db->resultSet();
    
    if (empty($stuckTasks)) {
        logToFile("No stuck tasks found");
    } else {
        logToFile("Found " . count($stuckTasks) . " stuck tasks");
        
        foreach ($stuckTasks as $task) {
            logToFile("Resetting stuck task: ID {$task->id}, Name: {$task->task_name}, Last run: {$task->last_run}");
            
            // Reset the task status to 'pending'
            $db->query("UPDATE scheduled_tasks SET status = 'pending', error_message = 'Auto-reset: Task was stuck in running status' WHERE id = :id");
            $db->bind(':id', $task->id);
            
            if ($db->execute()) {
                logToFile("Successfully reset task ID {$task->id}");
            } else {
                logToFile("Failed to reset task ID {$task->id}", 'ERROR');
            }
        }
    }
    
    // Also check for tasks that have been pending for too long (over 24 hours)
    $db->query("SELECT * FROM scheduled_tasks WHERE status = 'pending' AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $oldPendingTasks = $db->resultSet();
    
    if (!empty($oldPendingTasks)) {
        logToFile("Found " . count($oldPendingTasks) . " old pending tasks (over 24 hours)");
        
        foreach ($oldPendingTasks as $task) {
            logToFile("Old pending task: ID {$task->id}, Name: {$task->task_name}, Created: {$task->created_at}", 'WARNING');
            
            // Optionally reset these too or mark them as failed
            $db->query("UPDATE scheduled_tasks SET error_message = 'Auto-reset: Task was pending for over 24 hours' WHERE id = :id");
            $db->bind(':id', $task->id);
            $db->execute();
        }
    }
    
    // Get final statistics
    $db->query("SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                FROM scheduled_tasks");
    $stats = $db->single();
    
    logToFile("Task statistics - Total: {$stats->total}, Pending: {$stats->pending}, Running: {$stats->running}, Completed: {$stats->completed}, Failed: {$stats->failed}");
    
    // Create heartbeat file
    $heartbeatFile = dirname(__DIR__) . '/logs/auto_reset_tasks_heartbeat.json';
    $heartbeatData = [
        'last_run' => gmdate('Y-m-d H:i:s'),
        'status' => 'success',
        'stuck_tasks_reset' => count($stuckTasks),
        'old_pending_tasks' => count($oldPendingTasks ?? []),
        'task_statistics' => [
            'total' => $stats->total,
            'pending' => $stats->pending,
            'running' => $stats->running,
            'completed' => $stats->completed,
            'failed' => $stats->failed
        ],
        'php_version' => PHP_VERSION,
        'memory_usage' => memory_get_peak_usage(true)
    ];
    
    file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
    logToFile("Heartbeat file updated: {$heartbeatFile}");
    
    logToFile("Auto Reset Tasks Script Completed Successfully");
    
} catch (Exception $e) {
    $errorMessage = "Auto Reset Tasks Script Failed: " . $e->getMessage();
    logToFile($errorMessage, 'ERROR');
    
    if (DEBUG_MODE) {
        logToFile("Stack trace: " . $e->getTraceAsString(), 'ERROR');
    }
    
    // Create error heartbeat
    $heartbeatFile = dirname(__DIR__) . '/logs/auto_reset_tasks_heartbeat.json';
    $heartbeatData = [
        'last_run' => gmdate('Y-m-d H:i:s'),
        'status' => 'error',
        'error' => $e->getMessage(),
        'php_version' => PHP_VERSION,
        'memory_usage' => memory_get_peak_usage(true)
    ];
    
    file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
    
    exit(1);
}

exit(0);
?>
