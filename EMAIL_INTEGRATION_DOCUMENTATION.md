# Email Integration System Documentation

## Overview

The Email Integration System is a comprehensive solution that automatically retrieves emails from POP3/IMAP servers, processes them through an advanced email processing engine, creates messages in the unified messaging system, and provides a powerful admin dashboard for email management.

## Features

### 🚀 Core Features
- **POP3/IMAP Email Retrieval**: Full support for both protocols with SSL/TLS encryption
- **Automatic Ticket Generation**: RER-2025-001 format with uniqueness checking
- **Email Threading**: Smart conversation tracking using ticket numbers and email headers
- **Spam Filtering**: Advanced spam detection with configurable scoring
- **Attachment Handling**: Full support for email attachments with virus scanning capability
- **Auto-Reply Prevention**: Detects and ignores auto-replies to prevent loops

### 📧 Admin Dashboard Features
- **Folder System**: Personal admin folders with color coding and drag-and-drop
- **Message Ownership**: Admin ownership tracking with conflict warnings
- **Bulk Operations**: Select multiple messages for batch processing
- **Advanced Sorting**: Sort by date, subject, priority, ticket number, read status
- **Search Functionality**: Full-text search across subjects, content, and ticket numbers
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile

### ⏰ Reminder System
- **Flexible Scheduling**: Set reminders with custom dates/times
- **Quick Options**: 15min, 1hr, 1day, 1week, 1month presets
- **Notification Integration**: Uses unified messaging system for alerts
- **Snooze Functionality**: Postpone reminders as needed
- **Auto-Cleanup**: Removes old completed reminders

### 📝 Template System
- **Email Templates**: Create reusable templates for quick responses
- **Variable Substitution**: Dynamic content with template variables
- **Preview Functionality**: See how templates will look before sending
- **WYSIWYG Editor**: Rich text editing capabilities
- **Template Categories**: Organize templates by type/purpose

## Installation

### Prerequisites
- PHP 7.4+ with IMAP extension enabled
- MySQL/MariaDB database
- Web server (Apache/Nginx)
- Cron job capability

### Required PHP Extensions
```bash
php -m | grep -E "(imap|openssl|json|mbstring)"
```

### Setup Steps

1. **Run the setup script**:
   ```bash
   php setup_email_integration.php
   ```

2. **Configure email server settings**:
   - Go to Admin > Settings > Email Server
   - Enter your POP3/IMAP server details
   - Test the connection

3. **Set up cron jobs**:
   ```bash
   # Add to crontab (crontab -e)
   */5 * * * * /usr/bin/php /path/to/your/app/cron/process_incoming_emails.php
   */2 * * * * /usr/bin/php /path/to/your/app/cron/process_reminders.php
   ```

4. **Enable email processing**:
   - In Email Server Settings, check "Enable Email Processing"
   - Save settings

## Configuration

### Email Server Settings

| Setting | Description | Default |
|---------|-------------|---------|
| Protocol | IMAP or POP3 | IMAP |
| Host | Email server hostname | - |
| Port | Server port | 993 (IMAP SSL) |
| Username | Email account username | - |
| Password | Email account password | - |
| Encryption | SSL, TLS, or None | SSL |
| Delete After Processing | Remove emails from server | Yes |
| Spam Filtering | Enable basic spam detection | Yes |
| Max Email Size | Maximum email size (MB) | 10 |
| Attachment Support | Allow email attachments | Yes |
| Max Attachment Size | Maximum attachment size (MB) | 5 |
| Auto-Reply | Send confirmation emails | Yes |

### Ticket Number Format

Tickets use the format: `PREFIX-YEAR-SEQUENCE`
- Default prefix: `RER`
- Example: `RER-2025-001`, `RER-2025-002`, etc.
- Sequence resets each year
- Prefix can be customized (2-5 uppercase letters)

## Usage

### Admin Dashboard

Access the email dashboard at `/admin/email_dashboard`

#### Folder Management
- Create custom folders with color coding
- Drag and drop messages between folders
- System folders: Inbox, Sent, Archive, Trash
- Personal folders are private to each admin

#### Message Actions
- **View**: Open full message with thread history
- **Move**: Transfer to different folder
- **Remind**: Set reminder notifications
- **Transfer**: Change ownership to another admin
- **Reply**: Quick reply with template support

#### Bulk Operations
- Select multiple messages
- Bulk move to folders
- Bulk mark as read
- Bulk set reminders

### Email Templates

Access templates at `/admin/settings_email_templates`

#### Available Variables
```
{{subject}}         - Original email subject
{{ticket_number}}   - Generated ticket number
{{date}}            - Current date and time
{{name}}            - Sender name
{{email}}           - Sender email address
{{admin_name}}      - Admin name (for responses)
{{site_name}}       - Site/application name
{{response_content}} - Admin response content
```

#### Default Templates
- **Auto Reply Confirmation**: Sent automatically when emails are processed
- **Welcome Message**: For new user communications
- **General Response**: Standard reply template

### Reminder System

#### Setting Reminders
1. Open a message
2. Click "Set Reminder"
3. Choose time (quick options or custom)
4. Add optional note
5. Save

#### Reminder Notifications
- Sent via unified messaging system
- Includes link to original message
- Shows reminder note and context
- Automatic cleanup after completion

## API Integration

### Email Processing Engine

The system provides several classes for programmatic access:

```php
// Email retrieval
$emailService = new EmailRetrievalService();
$emails = $emailService->fetchEmails(50);

// Email processing
$processor = new EmailProcessingEngine();
$results = $processor->processEmails($emails);

// Ticket management
$ticketService = new TicketNumberService();
$ticket = $ticketService->generateTicketNumber();

// Reminder management
$reminderService = new ReminderService();
$reminderService->createReminder($messageId, $adminId, $time, $note);
```

### Database Schema

#### Key Tables
- `messages`: Extended unified messaging table with email fields
- `email_processing_log`: Processing history and statistics
- `ticket_numbers`: Ticket number tracking and uniqueness
- `admin_email_folders`: Personal folder system
- `message_reminders`: Reminder scheduling
- `email_templates`: Template management
- `message_attachments`: Attachment metadata

## Monitoring

### Processing Statistics
- Daily/total processed emails
- Failed processing attempts
- Spam detection rates
- Attachment processing stats

### Health Monitoring
- Email server connection status
- Last processing run information
- Cron job heartbeat tracking
- Error logging and alerts

### Log Files
- `logs/email_processing_heartbeat.txt`: Processing status
- `logs/reminder_processing_heartbeat.txt`: Reminder status
- Standard PHP error logs for detailed debugging

## Troubleshooting

### Common Issues

#### Email Connection Fails
1. Verify server settings (host, port, credentials)
2. Check firewall/network connectivity
3. Ensure PHP IMAP extension is enabled
4. Test with different encryption settings

#### Emails Not Processing
1. Check cron job is running
2. Verify email processing is enabled
3. Check processing logs for errors
4. Ensure sufficient disk space for attachments

#### Reminders Not Sending
1. Verify reminder cron job is active
2. Check unified messaging system is working
3. Ensure reminder times are in the future
4. Check for database connection issues

### Debug Mode
Enable debug logging by setting:
```php
define('DEBUG_MODE', true);
```

## Security Considerations

### Email Security
- Passwords stored encrypted in database
- SSL/TLS encryption for email connections
- Attachment type validation
- File size limits to prevent abuse

### Access Control
- Admin-only access to email dashboard
- Message ownership prevents conflicts
- CSRF protection on all forms
- Input validation and sanitization

### Data Protection
- Automatic cleanup of old logs
- Secure attachment storage
- Email deletion after processing (configurable)
- Audit trail for all actions

## Performance Optimization

### Email Processing
- Configurable fetch limits
- Batch processing for efficiency
- Connection pooling for IMAP
- Automatic cleanup of old data

### Database Optimization
- Proper indexing on all search fields
- Partitioning for large message tables
- Regular cleanup of old records
- Query optimization for dashboard

### Caching
- Template caching for quick responses
- Folder structure caching
- Statistics caching for dashboard
- Connection state caching

## Maintenance

### Regular Tasks
- Monitor disk space for attachments
- Review and update spam filtering rules
- Clean up old processing logs
- Update email templates as needed

### Backup Considerations
- Include attachment files in backups
- Export email templates
- Backup processing logs for auditing
- Document custom configuration

## Support

For technical support or feature requests, please refer to the system documentation or contact your system administrator.

---

**Version**: 1.0  
**Last Updated**: 2025-01-16  
**Compatibility**: PHP 7.4+, MySQL 5.7+
