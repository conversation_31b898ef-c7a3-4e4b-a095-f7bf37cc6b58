<?php
/**
 * Fix Server Time Difference Issue
 * 
 * Addresses the time difference between PHP server and database server
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🕐 Fix Server Time Difference Issue</h1>";

try {
    $db = new Database();
    
    echo "<h2>🎯 Server Time Difference Analysis</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Root Cause Identified:</h3>";
    echo "<ul>";
    echo "<li>❌ <strong>Problem:</strong> Database server and PHP server have different times</li>";
    echo "<li>🕐 <strong>Effect:</strong> Database NOW() creates future timestamps relative to PHP time</li>";
    echo "<li>⚡ <strong>Result:</strong> Notifications scheduled in \"future\" and not processed</li>";
    echo "<li>✅ <strong>Solution:</strong> Use PHP server time instead of database NOW()</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>⏰ Time Comparison</h2>";
    
    // Get current times from both servers
    $phpTime = date('Y-m-d H:i:s');
    $phpTimestamp = time();
    
    $db->query("SELECT NOW() as db_time, UNIX_TIMESTAMP() as db_timestamp");
    $db->execute();
    $dbTime = $db->single();
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📊 Server Time Comparison:</h3>";
    
    echo "<table border='1' cellpadding='10' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Server</th><th>Current Time</th><th>Timestamp</th><th>Timezone</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>PHP Server</strong></td>";
    echo "<td style='color: blue;'><strong>{$phpTime}</strong></td>";
    echo "<td>{$phpTimestamp}</td>";
    echo "<td>" . date_default_timezone_get() . "</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Database Server</strong></td>";
    echo "<td style='color: red;'><strong>{$dbTime->db_time}</strong></td>";
    echo "<td>{$dbTime->db_timestamp}</td>";
    echo "<td>Database timezone</td>";
    echo "</tr>";
    
    echo "</table>";
    
    $timeDifference = $dbTime->db_timestamp - $phpTimestamp;
    $minutesDifference = round($timeDifference / 60);
    
    echo "<div style='background: " . (abs($timeDifference) > 60 ? '#f8d7da' : '#d4edda') . "; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
    echo "<h4>⏱️ Time Difference Analysis:</h4>";
    echo "<p><strong>Difference:</strong> {$timeDifference} seconds ({$minutesDifference} minutes)</p>";
    
    if (abs($timeDifference) > 300) { // 5 minutes
        echo "<p style='color: red;'><strong>❌ CRITICAL:</strong> Large time difference detected!</p>";
    } elseif (abs($timeDifference) > 60) { // 1 minute
        echo "<p style='color: orange;'><strong>⚠️ WARNING:</strong> Significant time difference detected!</p>";
    } else {
        echo "<p style='color: green;'><strong>✅ OK:</strong> Time difference is minimal</p>";
    }
    
    if ($timeDifference > 0) {
        echo "<p><strong>Issue:</strong> Database time is {$minutesDifference} minutes AHEAD of PHP time</p>";
        echo "<p><strong>Effect:</strong> Database NOW() creates \"future\" timestamps that PHP considers not ready</p>";
    } elseif ($timeDifference < 0) {
        echo "<p><strong>Issue:</strong> Database time is " . abs($minutesDifference) . " minutes BEHIND PHP time</p>";
        echo "<p><strong>Effect:</strong> Less problematic, but still inconsistent</p>";
    } else {
        echo "<p><strong>Status:</strong> Times are synchronized</p>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<h2>🔧 Code Fix Applied</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📝 Solution: Use PHP Time Instead of Database NOW()</h3>";
    
    echo "<h4>❌ Before (Using Database NOW()):</h4>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
    echo "// UnifiedMessageModel::addToNotificationQueue()\n";
    echo "INSERT INTO notification_queue \n";
    echo "(user_id, notification_type, subject, message, status, scheduled_for, created_at, updated_at)\n";
    echo "VALUES (:user_id, :type, :subject, :message, 'pending', NOW(), NOW(), NOW())\n\n";
    echo "// Problem: NOW() uses database server time!";
    echo "</pre>";
    
    echo "<h4>✅ After (Using PHP Time):</h4>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
    echo "// UnifiedMessageModel::addToNotificationQueue()\n";
    echo "\$currentTime = date('Y-m-d H:i:s'); // PHP server time\n\n";
    echo "INSERT INTO notification_queue \n";
    echo "(user_id, notification_type, subject, message, status, scheduled_for, created_at, updated_at)\n";
    echo "VALUES (:user_id, :type, :subject, :message, 'pending', :scheduled_for, :created_at, :updated_at)\n\n";
    echo "// Solution: Use PHP server time for consistency!";
    echo "</pre>";
    
    echo "<h4>🎯 Benefits of Using PHP Time:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Consistency:</strong> All time comparisons use same server time</li>";
    echo "<li>✅ <strong>Immediate processing:</strong> scheduled_for always <= current PHP time</li>";
    echo "<li>✅ <strong>No timezone issues:</strong> PHP and processing logic use same time source</li>";
    echo "<li>✅ <strong>Predictable behavior:</strong> No mysterious \"future\" scheduling</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🧹 Fix Existing Future-Scheduled Notifications</h2>";
    
    // Check for notifications still scheduled in the future
    $db->query("SELECT id, user_id, subject, scheduled_for, created_at FROM notification_queue WHERE status = 'pending' AND scheduled_for > ?");
    $db->bind(1, $phpTime);
    $db->execute();
    $futureNotifications = $db->resultSet();
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🕐 Notifications Still Scheduled in Future:</h3>";
    
    if (empty($futureNotifications)) {
        echo "<p style='color: green;'>✅ No notifications scheduled in the future (relative to PHP time)</p>";
    } else {
        echo "<p><strong>Found " . count($futureNotifications) . " notification(s) still scheduled in the future</strong></p>";
        
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Subject</th><th>Scheduled For</th><th>Minutes in Future</th></tr>";
        
        foreach ($futureNotifications as $notification) {
            $scheduledTime = strtotime($notification->scheduled_for);
            $minutesInFuture = round(($scheduledTime - $phpTimestamp) / 60);
            
            echo "<tr>";
            echo "<td>{$notification->id}</td>";
            echo "<td>" . htmlspecialchars(substr($notification->subject, 0, 40)) . "...</td>";
            echo "<td>{$notification->scheduled_for}</td>";
            echo "<td style='color: red;'>+{$minutesInFuture} min</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<form method='post' style='margin: 15px 0;'>";
        echo "<button type='submit' name='fix_future_times' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Fix All Future Times (Set to PHP Time)</button>";
        echo "</form>";
        
        if (isset($_POST['fix_future_times'])) {
            echo "<h4>🔄 Fixing Results:</h4>";
            
            $db->query("UPDATE notification_queue SET scheduled_for = ?, updated_at = ? WHERE status = 'pending' AND scheduled_for > ?");
            $db->bind(1, $phpTime);
            $db->bind(2, $phpTime);
            $db->bind(3, $phpTime);
            $success = $db->execute();
            $affectedRows = $db->rowCount();
            
            if ($success) {
                echo "<p style='color: green;'>✅ Successfully fixed {$affectedRows} notification(s)</p>";
                echo "<p><strong>All notifications now use PHP server time and are ready for processing!</strong></p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to fix notifications</p>";
            }
        }
    }
    echo "</div>";
    
    echo "<h2>🧪 Test Email Processing</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📧 Test Notification Processing with Fixed Times:</h3>";
    
    // Check current pending notifications (using PHP time for comparison)
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE status = 'pending' AND notification_type = 'email' AND scheduled_for <= ?");
    $db->bind(1, $phpTime);
    $db->execute();
    $readyEmails = $db->single();
    $readyCount = $readyEmails->count ?? 0;
    
    echo "<p><strong>Emails ready for processing (using PHP time):</strong> {$readyCount}</p>";
    
    if ($readyCount > 0) {
        echo "<form method='post'>";
        echo "<button type='submit' name='test_processing' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Email Processing Now</button>";
        echo "</form>";
        
        if (isset($_POST['test_processing'])) {
            echo "<h4>🔄 Processing Test Results:</h4>";
            
            try {
                // Load required classes
                require_once APPROOT . '/models/NotificationService.php';
                require_once APPROOT . '/models/NotificationModel.php';
                require_once APPROOT . '/models/EmailService.php';
                require_once APPROOT . '/models/SettingsModel.php';
                
                $notificationService = new NotificationService();
                $results = $notificationService->processPendingNotifications(5);
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Processing Results:</strong><br>";
                echo "Processed: {$results['processed']}<br>";
                echo "Sent: {$results['sent']}<br>";
                echo "Failed: {$results['failed']}<br>";
                
                if (!empty($results['errors'])) {
                    echo "<strong>Errors:</strong><br>";
                    foreach ($results['errors'] as $error) {
                        echo "• " . htmlspecialchars($error) . "<br>";
                    }
                }
                echo "</div>";
                
                if ($results['processed'] > 0) {
                    echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Server time fix is working! Notifications processed correctly!</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Still no notifications processed. Check other settings (email enabled, user preferences, etc.)</p>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
        }
    } else {
        echo "<p>No emails ready for processing. Submit a contact form to test.</p>";
    }
    echo "</div>";
    
    echo "<h2>📋 Files Updated</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Files to copy to server:</strong></p>";
    echo "<ul>";
    echo "<li><code>models/UnifiedMessageModel.php</code> - Now uses PHP time instead of database NOW()</li>";
    echo "<li><code>models/EmailService.php</code> - Queue fallback also uses PHP time</li>";
    echo "</ul>";
    
    echo "<p><strong>Changes made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>addToNotificationQueue():</strong> Uses date('Y-m-d H:i:s') instead of NOW()</li>";
    echo "<li>✅ <strong>queueEmailForLater():</strong> Uses PHP time for consistency</li>";
    echo "<li>✅ <strong>All timestamps:</strong> scheduled_for, created_at, updated_at use PHP time</li>";
    echo "<li>✅ <strong>Immediate processing:</strong> No more \"future\" scheduling issues</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Fix Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Server time difference fix completed at: " . date('Y-m-d H:i:s') . " (PHP time)</em></p>";
?>
