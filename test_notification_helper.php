<?php
/**
 * Test the notification helper
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

// Load Database class
require_once APPROOT . '/core/Database.php';

// Load notification helper
require_once APPROOT . '/helpers/notification_helper.php';

// Example usage of the notification helper
if (isset($_GET['test']) || $_SERVER['REQUEST_METHOD'] === 'POST') {
    $userId = 3; // Replace with actual user ID
    
    echo "<h1>Testing Notification Helper</h1>";
    
    // Test 1: Send all enabled notification types
    echo "<h2>Test 1: Send All Enabled Types</h2>";
    $result = sendNotification($userId, "Test Notification", "This respects user preferences and sends all enabled types.");
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    // Test 2: Send specific toast notification
    echo "<h2>Test 2: Send Toast Only</h2>";
    $success = sendToastNotification($userId, "Toast Test", "This is a toast notification");
    echo "Toast sent: " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    // Test 3: Send specific push notification
    echo "<h2>Test 3: Send Push Only</h2>";
    $success = sendPushNotification($userId, "Push Test", "This is a push notification");
    echo "Push sent: " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    // Test 4: Send email notification
    echo "<h2>Test 4: Send Email Only</h2>";
    $success = sendEmailNotification($userId, "Email Test", "This is an email notification");
    echo "Email sent: " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
    
    // Test 5: Using the helper class directly
    echo "<h2>Test 5: Using Helper Class Directly</h2>";
    $helper = new NotificationHelper();
    $result = $helper->sendToUser($userId, "Direct Helper Test", "Using the helper class directly");
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    echo "<hr>";
    echo "<p><strong>Check your browser for toast notifications and push notifications!</strong></p>";
    echo "<p>Email notifications will be sent if configured.</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Notification Helper Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-button { 
            background: #007cba; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
    </style>
</head>
<body>
    <h1>Notification Helper Test Page</h1>
    
    <p>This page tests the notification helper functions. Click the button below to send test notifications.</p>
    
    <form method="post">
        <button type="submit" class="test-button">🔔 Send Test Notifications</button>
    </form>
    
    <p><a href="?test=1" class="test-button" style="text-decoration: none; display: inline-block;">🔗 Or click here to test via GET</a></p>
    
    <hr>
    
    <h2>How to Use in Your Code</h2>
    
    <h3>1. Include the helper:</h3>
    <pre><code>require_once 'helpers/notification_helper.php';</code></pre>
    
    <h3>2. Send notification respecting user preferences:</h3>
    <pre><code>$result = sendNotification($userId, "Your Title", "Your message");
if ($result['success']) {
    echo "Sent via: " . implode(', ', $result['sent_types']);
}</code></pre>
    
    <h3>3. Send specific notification types:</h3>
    <pre><code>// Send toast notification
$success = sendToastNotification($userId, "Toast Title", "Toast message");

// Send push notification
$success = sendPushNotification($userId, "Push Title", "Push message");

// Send email notification
$success = sendEmailNotification($userId, "Email Title", "Email message");</code></pre>
    
    <h3>4. Using the helper class directly:</h3>
    <pre><code>$helper = new NotificationHelper();
$result = $helper->sendToUser($userId, $title, $message);

// Or send specific types
$helper->sendToast($userId, $title, $message);
$helper->sendPush($userId, $title, $message);</code></pre>
    
    <h3>5. Real-world example:</h3>
    <pre><code>// In your controller or business logic
function notifyUserOfApproval($userId, $itemName) {
    $title = "Approval Notification";
    $message = "Your $itemName has been approved!";
    
    $result = sendNotification($userId, $title, $message);
    
    if ($result['success']) {
        error_log("Notification sent via: " . implode(', ', $result['sent_types']));
        return true;
    } else {
        error_log("Failed to send notification: " . implode(', ', $result['errors']));
        return false;
    }
}</code></pre>
    
</body>
</html>
